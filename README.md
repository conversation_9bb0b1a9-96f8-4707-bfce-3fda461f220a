# SelfMirror 🪞

> 一个基于AI的智能记忆与洞察系统

## 🚀 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 📋 核心功能

- **智能聊天** - 基于新AI工厂架构的对话系统
- **记忆管理** - 本地化的记忆存储与检索
- **每日洞察** - 自动生成的个人洞察报告
- **调试控制台** - 完整的开发调试工具

## 🏗️ 技术架构

- **前端**: Next.js 15 + TypeScript + Tailwind CSS + shadcn/ui
- **AI集成**: 统一AI工厂 (支持Gemini/Doubao)
- **存储**: 本地化存储抽象层
- **状态管理**: React Context + Hooks

## 📁 项目结构

```
selfmirror2025/
├── app/                    # Next.js应用目录
├── components/             # React组件
├── lib/                    # 核心库文件
│   ├── ai/                # AI工厂模块
│   ├── services/          # 业务服务
│   └── storage/           # 存储抽象层
├── memory/                 # 记忆数据目录
├── docs-archive/           # 历史文档归档
└── SelfMirror MVP 技术架构白皮书/  # 核心技术文档
```

## 🔧 环境配置

创建 `.env.local` 文件：

```env
# AI提供商配置
AI_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key
DOUBAO_API_KEY=your_doubao_api_key

# 代理配置（可选）
PROXY_URL=http://127.0.0.1:7897
```

## 📖 文档

- **核心架构**: `SelfMirror MVP 技术架构白皮书/`
- **历史文档**: `docs-archive/` - 包含所有历史分析报告和技术文档
- **快速开始**: `docs-archive/QUICK_START.md`
- **API文档**: `docs-archive/docs/`

## 🛠️ 开发

```bash
# 启动调试控制台
npm run dev
# 访问 http://localhost:3000/debug-console

# 运行测试
npm test

# 代码检查
npm run lint
```

## 📊 项目状态

- ✅ AI模块重构完成 (新AI工厂架构)
- ✅ 存储抽象层实现
- ✅ 调试控制台完整
- ✅ 项目空间优化 (从10GB减少到517MB)
- ✅ 文档归档整理

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

---

**SelfMirror** - 让AI成为你的智能镜像 🪞✨
