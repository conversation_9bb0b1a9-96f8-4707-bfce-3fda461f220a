@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Han+Sans:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-sans: system-ui, sans-serif;
    --font-mono: 'Fira Code', 'JetBrains Mono', monospace;
  }

  .dark {
    --background: 222 84% 5%; /* #0D1117 */
    --foreground: 210 40% 98%; /* #E6EDF3 */
    --card: 222 47% 11%; /* #161B22 */
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 5%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222 47% 11%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%; /* #8B949E */
    --accent: 212 100% 66%; /* #58A6FF */
    --accent-foreground: 222 47% 11%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 20% 24%; /* #30363D */
    --input: 217 20% 24%;
    --ring: 212 100% 66%;
    --radius: 0.5rem;

    /* Custom variables for direct use */
    --background-custom: #0D1117;
    --content-background-custom: #161B22;
    --border-custom: #30363D;
    --primary-text-custom: #E6EDF3;
    --secondary-text-custom: #8B949E;
    --accent-custom: #58A6FF;
    --editor-background-custom: #101418;

    /* === Spacing System === */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-2xl: 3rem;      /* 48px */
    --spacing-3xl: 4rem;      /* 64px */
    --spacing-4xl: 6rem;      /* 96px */

    /* === Border Radius === */
    --radius-sm: 0.375rem;    /* 6px */
    --radius-md: 0.5rem;      /* 8px */
    --radius-lg: 0.75rem;     /* 12px */
    --radius-xl: 1rem;        /* 16px */
    --radius-2xl: 1.5rem;     /* 24px */

    /* === Animation Duration === */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    --duration-slower: 800ms;



    /* === Z-index Tiers === */
    --z-background: -1;
    --z-base: 0;
    --z-elevated: 10;
    --z-overlay: 100;
    --z-modal: 1000;
  }
}

@layer base {
  * {
    @apply border-border;
  }
}

/* ===== 基础重置与全局样式 ===== */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

html {
  height: 100%;
  scroll-behavior: smooth;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-text-size-adjust: 100%;
}

body {
  height: 100%;
  font-family: var(--font-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* 添加微妙的背景纹理 - 侘寂美学的不完美 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(245, 245, 220, 0.015) 1px, transparent 0);
  background-size: 24px 24px;
  pointer-events: none;
  z-index: var(--z-background);
  opacity: 0.6;
}

/* 防止移动端键盘弹出时的布局问题 */
@supports (height: 100dvh) {
  html, body {
    height: 100dvh;
  }
}

/* ===== 滚动条美化 - 纤细优雅 ===== */

/* 主聊天界面 - 完全隐藏滚动条，营造纯净空间 */
.emotional-space ::-webkit-scrollbar {
  display: none;
}

.emotional-space {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 调试台界面 - 美化滚动条，保持专业感 */
.debug-console ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.debug-console ::-webkit-scrollbar-track {
  background: transparent;
}

.debug-console ::-webkit-scrollbar-thumb {
  background: rgba(96, 165, 250, 0.2);
  border-radius: 3px;
  transition: all var(--duration-normal) ease;
}

.debug-console ::-webkit-scrollbar-thumb:hover {
  background: rgba(96, 165, 250, 0.4);
}

/* 默认滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(42, 43, 66, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(160, 160, 160, 0.3);
  border-radius: 4px;
  transition: all var(--duration-normal) ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(160, 160, 160, 0.5);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(96, 165, 250, 0.3) transparent;
}

/* ===== 动画效果 ===== */

/* 移除复杂的呼吸动画 - 已用简单状态指示器替代 */

/* 基础动画效果 - 仅保留MVP必需的 */

/* 简单脉冲效果 - 用于加载状态 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* 基础悬停效果 */
.hover-lift {
  transition: transform var(--duration-normal) ease;
}

.hover-lift:hover {
  transform: translateY(-1px);
}

/* ===== 输入框样式 ===== */

/* 隐形输入框 - 情感空间专用 */
.input-invisible {
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: 1.125rem;
  line-height: 1.75;
  caret-color: var(--text-primary);
  width: 100%;
  resize: none;
}

.input-invisible::placeholder {
  color: var(--text-placeholder);
  opacity: 0.6;
  transition: opacity var(--duration-normal) ease;
}

.input-invisible:focus::placeholder {
  opacity: 0.3;
}

/* ===== 代码编辑器样式 ===== */

.code-editor {
  font-family: var(--font-mono);
  background: rgba(42, 43, 66, 0.6);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  line-height: 1.6;
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 120px;
  transition: all var(--duration-normal) ease;
  font-size: 0.875rem;
}

.code-editor:focus {
  border-color: var(--border-focus);
  box-shadow: var(--shadow-glow);
  background: rgba(42, 43, 66, 0.8);
  outline: none;
}

.code-editor::placeholder {
  color: var(--text-placeholder);
  opacity: 0.7;
}

/* ===== 按钮样式系统 (已移除，使用 shadcn/ui Button 组件) ===== */
/*
注意：以下按钮样式已被 shadcn/ui Button 组件替代：
- .btn-outline -> <Button variant="outline">
- .btn-primary -> <Button variant="default">
- .btn-danger -> <Button variant="destructive">

如需自定义按钮样式，请在组件级别使用 className 或扩展 shadcn/ui 主题
*/

/* ===== 卡片样式系统 (已移除，使用 shadcn/ui Card 组件) ===== */
/*
注意：以下卡片样式已被 shadcn/ui Card 组件替代：
- .card-subtle -> <Card className="backdrop-blur-sm">
- .card-debug -> <Card>
- .card-memory -> <Card className="border-l-4 border-l-blue-500">

如需自定义卡片样式，请在组件级别使用 className 或扩展 shadcn/ui 主题
*/

/* 保留特殊用途的卡片样式 */
.card-memory {
  background: rgba(42, 43, 66, 0.3);
  border-left: 3px solid var(--accent-blue);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* ===== 标签页样式 ===== */

.tab-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  font-size: 0.875rem;
  font-weight: 500;
  font-family: var(--font-primary);
  border-bottom: 2px solid transparent;
  transition: all var(--duration-normal) ease;
  cursor: pointer;
  position: relative;
}

.tab-button:hover {
  color: var(--text-secondary);
  background: rgba(96, 165, 250, 0.05);
}

.tab-button.active {
  color: var(--accent-blue);
  border-bottom-color: var(--accent-blue);
  background: rgba(96, 165, 250, 0.1);
}

.tab-button.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-blue);
  border-radius: 0 0 2px 2px;
}

/* ===== 文本样式层级系统 ===== */

.text-display {
  font-size: 2.25rem;
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

.text-title {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
}

.text-subtitle {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.4;
  color: var(--text-primary);
}

.text-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-primary);
}

.text-caption {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-tertiary);
}

.text-small {
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.4;
  color: var(--text-muted);
}

/* 特殊文本样式 */
.text-mono {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.6;
}

.text-gradient {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

/* ===== 响应式设计 ===== */

/* 移动端适配 */
@media (max-width: 768px) {
  :root {
    --spacing-md: 0.75rem;      /* 减小间距 */
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
  }

  .text-display {
    font-size: 1.875rem;
  }

  .text-title {
    font-size: 1.25rem;
  }

  .input-invisible {
    font-size: 1rem;
  }

  .code-editor {
    font-size: 0.8rem;
    min-height: 100px;
  }
}



/* ===== 实用工具类 ===== */

.font-system {
  font-family: var(--font-primary);
}

.font-mono {
  font-family: var(--font-mono);
}

.center-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.full-height {
  height: 100vh;
}

.full-width {
  width: 100%;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* ===== 聊天气泡样式 ===== */

/* 用户消息气泡 */
.chat-bubble-user {
  background: var(--accent-blue);
  color: white;
  border-radius: 1.5rem 1.5rem 0.5rem 1.5rem;
  padding: 0.75rem 1rem;
  max-width: 75%;
  margin-left: auto;
  box-shadow: var(--shadow-soft);
  word-wrap: break-word;
  transition: all var(--duration-normal) ease;
}

.chat-bubble-user:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-glow);
}

/* AI消息气泡 */
.chat-bubble-ai {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
  border-radius: 1.5rem 1.5rem 1.5rem 0.5rem;
  padding: 0.75rem 1rem;
  max-width: 75%;
  margin-right: auto;
  box-shadow: var(--shadow-soft);
  word-wrap: break-word;
  transition: all var(--duration-normal) ease;
}

.chat-bubble-ai:hover {
  border-color: var(--border-hover);
  background: var(--bg-card-hover);
}

/* 气泡内代码块样式 */
.chat-bubble-user .code-block,
.chat-bubble-ai .code-block {
  margin: 0.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  font-family: var(--font-mono);
}

.chat-bubble-user .code-block {
  background: rgba(0, 0, 0, 0.2);
}

.chat-bubble-ai .code-block {
  background: var(--bg-primary);
}

/* ===== 调试台增强样式 ===== */

/* 大文本域样式 */
.large-textarea {
  font-family: var(--font-mono);
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 400px;
  max-height: 80vh;
  transition: all var(--duration-normal) ease;
  line-height: 1.6;
  font-size: 14px;
  tab-size: 2;
}

.large-textarea:focus {
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  outline: none;
}

/* 增强的标签页样式 */
.enhanced-tab {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: var(--font-primary);
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  border-bottom: 2px solid transparent;
  transition: all var(--duration-normal) ease;
  cursor: pointer;
  white-space: nowrap;
}

.enhanced-tab:hover {
  background: rgba(96, 165, 250, 0.05);
  color: var(--text-secondary);
}

.enhanced-tab.active {
  background: var(--bg-card);
  color: var(--accent-blue);
  border-bottom-color: var(--accent-blue);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}