'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  TestTube,
  Activity,
  Heart,
  Settings,
  Database,
  RefreshCw
} from 'lucide-react';

// 导入新的调试组件
import TestingDashboard from '@/components/debug/testing/TestingDashboard';
import PerformanceDashboard from '@/components/debug/performance/PerformanceDashboard';
import HealthDashboard from '@/components/debug/health/HealthDashboard';

export default function DebugPage() {
  const [isArchiving, setIsArchiving] = useState(false);
  const [message, setMessage] = useState('');

  const handleArchive = async () => {
    setIsArchiving(true);
    setMessage('');

    try {
      const response = await fetch('/api/memory/archive', {
        method: 'POST'
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('✅ ' + data.message);
      } else {
        setMessage('❌ ' + (data.error || '归档失败'));
      }
    } catch (error) {
      setMessage('❌ 网络错误：' + (error as Error).message);
    } finally {
      setIsArchiving(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">SelfMirror 调试控制台</h1>
        <p className="text-muted-foreground">
          系统监控、测试管理和性能优化的统一控制台
        </p>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>概览</span>
          </TabsTrigger>
          <TabsTrigger value="testing" className="flex items-center space-x-2">
            <TestTube className="h-4 w-4" />
            <span>测试框架</span>
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>性能监控</span>
          </TabsTrigger>
          <TabsTrigger value="health" className="flex items-center space-x-2">
            <Heart className="h-4 w-4" />
            <span>健康检查</span>
          </TabsTrigger>
          <TabsTrigger value="legacy" className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>记忆管理</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">测试框架</CardTitle>
                <TestTube className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">
                  个测试套件
                </p>
                <Badge variant="default" className="mt-2">运行中</Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">性能监控</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">95</div>
                <p className="text-xs text-muted-foreground">
                  健康分数
                </p>
                <Badge variant="default" className="mt-2">优秀</Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">系统健康</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-xs text-muted-foreground">
                  个组件监控
                </p>
                <Badge variant="default" className="mt-2">健康</Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">记忆管理</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.2K</div>
                <p className="text-xs text-muted-foreground">
                  条记忆条目
                </p>
                <Badge variant="secondary" className="mt-2">正常</Badge>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>系统状态概览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">API响应时间</span>
                  <Badge variant="default">正常</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">内存使用率</span>
                  <Badge variant="default">65%</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">缓存命中率</span>
                  <Badge variant="default">85%</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">错误率</span>
                  <Badge variant="default">0.1%</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing">
          <TestingDashboard />
        </TabsContent>

        <TabsContent value="performance">
          <PerformanceDashboard />
        </TabsContent>

        <TabsContent value="health">
          <HealthDashboard />
        </TabsContent>

        <TabsContent value="legacy" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>记忆管理</CardTitle>
              <p className="text-sm text-muted-foreground">管理AI的记忆系统</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">归档今天的洞察</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  将"每日洞察今天.md"的内容移动到"每日洞察归档.md"
                </p>
                <Button
                  onClick={handleArchive}
                  disabled={isArchiving}
                  className="space-x-2"
                >
                  {isArchiving ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Database className="h-4 w-4" />
                  )}
                  <span>{isArchiving ? '归档中...' : '开始归档'}</span>
                </Button>
                {message && (
                  <div className="mt-4 p-3 bg-muted rounded-md">
                    {message}
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">系统信息</h3>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <p>• 每日沉淀触发频率：每6轮对话</p>
                  <p>• 日常对话上下文窗口：正常6轮，沉淀时8轮</p>
                  <p>• 记忆文件位置：/memory/</p>
                  <p>• 热日志：每日洞察今天.md（当天的洞察）</p>
                  <p>• 冷日志：每日洞察归档.md（历史洞察）</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 