'use client';

import { useState } from 'react';

export default function DebugPage() {
  const [isArchiving, setIsArchiving] = useState(false);
  const [message, setMessage] = useState('');

  const handleArchive = async () => {
    setIsArchiving(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/memory/archive', {
        method: 'POST'
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setMessage('✅ ' + data.message);
      } else {
        setMessage('❌ ' + (data.error || '归档失败'));
      }
    } catch (error) {
      setMessage('❌ 网络错误：' + (error as Error).message);
    } finally {
      setIsArchiving(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-3xl font-bold mb-6">调试工具</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold mb-2">记忆管理</h2>
          <p className="text-gray-600">管理AI的记忆系统</p>
        </div>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">归档今天的洞察</h3>
            <p className="text-sm text-gray-600 mb-4">
              将"每日洞察今天.md"的内容移动到"每日洞察归档.md"
            </p>
            <button 
              onClick={handleArchive} 
              disabled={isArchiving}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
            >
              {isArchiving ? (
                <>
                  <span className="mr-2">⏳</span>
                  归档中...
                </>
              ) : (
                <>
                  <span className="mr-2">📁</span>
                  手动归档当天洞察
                </>
              )}
            </button>
            {message && (
              <p className={`mt-2 text-sm ${message.startsWith('✅') ? 'text-green-600' : 'text-red-600'}`}>
                {message}
              </p>
            )}
          </div>
          
          <div className="pt-4 border-t">
            <h3 className="text-lg font-semibold mb-2">系统信息</h3>
            <div className="space-y-1 text-sm text-gray-700">
              <p>• 每日沉淀触发频率：每6轮对话</p>
              <p>• 日常对话上下文窗口：正常6轮，沉淀时8轮</p>
              <p>• 记忆文件位置：/memory/</p>
              <p>• 热日志：每日洞察今天.md（当天的洞察）</p>
              <p>• 冷日志：每日洞察归档.md（历史洞察）</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 