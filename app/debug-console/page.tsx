"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import ChatInterface from "@/components/chat/ChatInterface"
import { PromptEditor } from "@/components/debug/PromptEditor"
import { DailyInsightViewer } from "@/components/debug/DailyInsightViewer"
import { UnifiedRAGDebugger } from "@/components/debug/UnifiedRAGDebugger"
import { IndexingPanel } from "@/components/debug/meaning-rag/IndexingPanel"
import { RetrievalPanel } from "@/components/debug/meaning-rag/RetrievalPanel"
import { UnifiedDataManager } from "@/components/debug/UnifiedDataManager"
import { SimpleAIModelManager } from "@/components/debug/SimpleAIModelManager"
import { UnifiedConfigManager } from "@/components/debug/UnifiedConfigManager"
// TODO: 重构后重新启用
// import { ThreeEngineMonitor } from "@/components/debug/three-engine/ThreeEngineMonitor"
// import { ThreeE<PERSON><PERSON>Config } from "@/components/debug/three-engine/ThreeEngineConfig"
// import { ThreeEngineChatTest } from "@/components/debug/three-engine/ThreeEngineChatTest"
// import { DualCoreDebugPanel } from "@/components/debug/dual-core/DualCoreDebugPanel"
// import { dualCoreDebugController } from "@/lib/services/dual-core-debug-controller"
import { RAGDebugProvider } from "@/lib/contexts/RAGDebugContext"

// 提示词文件配置
const promptFiles = [
  { name: "小镜人设提示词", path: "小镜人设提示词.md" },
  { name: "用户画像", path: "用户画像.md" },
  { name: "心智要素结构", path: "心智要素结构.md" },
  { name: "日常对话模式", path: "prompts/日常对话模式提示词.md" },
  { name: "每日洞察模式", path: "prompts/每日洞察模式提示词.md" },
]

export default function DebugConsolePage() {
  return (
    <RAGDebugProvider>
      <div className="h-screen flex bg-[#18181B] text-gray-100">
      {/* Left Panel: Three Engine Chat Test (50%) */}
      <div className="w-1/2 flex flex-col border-r border-[#27272A]">
        <div className="p-4 border-b border-[#27272A]">
          <h2 className="text-lg font-semibold text-white mb-2">三引擎聊天测试</h2>
          <p className="text-sm text-gray-400">测试三引擎协作工作流和传统模式的对比</p>
        </div>
        <div className="flex-1 overflow-hidden">
          {/* TODO: 重构后重新启用 */}
          {/* <ThreeEngineChatTest /> */}
          <div className="p-4 text-center text-gray-400">
            三引擎聊天测试功能重构中...
          </div>
        </div>
      </div>

      {/* Right Panel: Debug Console (50%) */}
      <div className="w-1/2 flex flex-col bg-[#18181B]">
        <div className="p-4 border-b border-[#27272A]">
          <h2 className="text-lg font-semibold text-white">调试控制台</h2>
          <p className="text-sm text-gray-400">系统提示词编辑、洞察日志、RAG调试和数据管理</p>
        </div>

        <div className="flex-1 overflow-auto">
          <Tabs defaultValue="ai-models" className="h-full flex flex-col">
            <TabsList className="bg-[#27272A] border-b border-[#27272A] rounded-none w-full justify-start">
              {/* TODO: 重构后重新启用 */}
              {/* <TabsTrigger value="dual-core-debug" className="text-sm">双核抽象层</TabsTrigger> */}
              {/* <TabsTrigger value="three-engine-monitor" className="text-sm">三引擎监控</TabsTrigger> */}
              {/* <TabsTrigger value="three-engine-config" className="text-sm">三引擎配置</TabsTrigger> */}
              <TabsTrigger value="ai-models" className="text-sm">AI模型切换</TabsTrigger>
              <TabsTrigger value="config" className="text-sm">基础设置</TabsTrigger>
              <TabsTrigger value="prompts" className="text-sm">系统提示词</TabsTrigger>
              <TabsTrigger value="insights" className="text-sm">每日洞察</TabsTrigger>
              <TabsTrigger value="rag" className="text-sm">RAG调试</TabsTrigger>
              <TabsTrigger value="meaning-rag" className="text-sm">意义RAG调试</TabsTrigger>
              <TabsTrigger value="meaning-rag-workshop" className="text-sm">意义RAG炼金工房</TabsTrigger>
              <TabsTrigger value="data" className="text-sm">数据管理</TabsTrigger>
            </TabsList>

            {/* TODO: 重构后重新启用 */}
            {/* 双核抽象层调试面板 */}
            {/* <TabsContent value="dual-core-debug" className="flex-1 overflow-hidden m-0">
              <DualCoreDebugPanel />
            </TabsContent> */}

            {/* 三引擎监控面板 */}
            {/* <TabsContent value="three-engine-monitor" className="flex-1 overflow-hidden m-0">
              <ThreeEngineMonitor />
            </TabsContent> */}

            {/* 三引擎配置面板 */}
            {/* <TabsContent value="three-engine-config" className="flex-1 overflow-hidden m-0">
              <ThreeEngineConfig />
            </TabsContent>

            {/* AI 模型切换面板 */}
            <TabsContent value="ai-models" className="flex-1 overflow-auto m-0 p-4">
              <SimpleAIModelManager />
            </TabsContent>

            {/* 基础设置面板 */}
            <TabsContent value="config" className="flex-1 overflow-auto m-0 p-4">
              <UnifiedConfigManager />
            </TabsContent>

            {/* 系统提示词编辑器 */}
            <TabsContent value="prompts" className="flex-1 overflow-hidden m-0 p-4">
              <Tabs defaultValue={promptFiles[0].path} className="h-full flex flex-col">
                <TabsList className="bg-[#27272A] mb-4">
                  {promptFiles.map((file) => (
                    <TabsTrigger key={file.path} value={file.path} className="text-xs">
                      {file.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
                {promptFiles.map((file) => (
                  <TabsContent key={file.path} value={file.path} className="flex-1 m-0">
                    <PromptEditor fileName={file.name} filePath={file.path} />
                  </TabsContent>
                ))}
              </Tabs>
            </TabsContent>

            {/* 每日洞察日志 */}
            <TabsContent value="insights" className="flex-1 overflow-hidden m-0 p-4">
              <DailyInsightViewer />
            </TabsContent>

            {/* RAG知识库调试器 */}
            <TabsContent value="rag" className="flex-1 overflow-hidden m-0 p-4">
              <UnifiedRAGDebugger />
            </TabsContent>

            {/* 意义RAG调试器 */}
            <TabsContent value="meaning-rag" className="flex-1 overflow-hidden m-0 p-4">
              <UnifiedRAGDebugger />
            </TabsContent>

            {/* 意义RAG炼金工房 */}
            <TabsContent value="meaning-rag-workshop" className="flex-1 overflow-hidden m-0 p-0">
              <div className="h-full flex">
                {/* 左侧面板 - 索引构建控制区 (40%) */}
                <div className="w-[40%] border-r border-[#30363D] p-4 overflow-y-auto">
                  <IndexingPanel />
                </div>

                {/* 右侧面板 - 检索逻辑调试区 (60%) */}
                <div className="w-[60%] p-4 overflow-y-auto">
                  <RetrievalPanel />
                </div>
              </div>
            </TabsContent>

            {/* 数据管理控制台 */}
            <TabsContent value="data" className="flex-1 overflow-hidden m-0 p-4">
              <UnifiedDataManager />
            </TabsContent>
          </Tabs>
        </div>
      </div>
      </div>
    </RAGDebugProvider>
  )
}
