/**
 * 测试三引擎工作流协调器的API端点
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试三引擎工作流协调器...');
    
    const results: any = {
      success: true,
      tests: [],
      workflowStats: null
    };
    
    // 1. 初始化工作流协调器
    console.log('🔄 初始化工作流协调器...');
    const { ThreeEngineWorkflowCoordinator } = await import("@/lib/services/three-engine/workflow-coordinator");
    
    const coordinator = new ThreeEngineWorkflowCoordinator();
    await coordinator.initialize();
    
    results.tests.push({
      name: '工作流协调器初始化',
      success: true,
      message: '工作流协调器初始化成功'
    });
    
    // 2. 测试简单问答工作流
    console.log('🔄 测试简单问答工作流...');
    
    const simpleRequest = {
      userMessage: "今天天气怎么样？",
      userProfile: "关注生活质量的用户",
      conversationHistory: [
        "用户: 早上好",
        "AI: 早上好！今天有什么可以帮助您的吗？"
      ],
      generationConfig: {
        temperature: 0.7,
        maxTokens: 300,
        streamResponse: false
      }
    };
    
    const simpleResponse = await coordinator.executeWorkflow(simpleRequest);
    const isValidSimple = coordinator.validateWorkflow(simpleResponse);
    
    results.tests.push({
      name: '简单问答工作流',
      success: isValidSimple,
      workflow: {
        workflowId: simpleResponse.workflowId,
        finalResponseLength: simpleResponse.finalResponse.length,
        totalTime: simpleResponse.executionStats.totalTime,
        navigatorTime: simpleResponse.executionStats.navigatorTime,
        retrieverTime: simpleResponse.executionStats.retrieverTime,
        generatorTime: simpleResponse.executionStats.generatorTime,
        overallQuality: simpleResponse.qualityMetrics.overallQuality,
        navigatorConfidence: simpleResponse.qualityMetrics.navigatorConfidence,
        retrievalRelevance: simpleResponse.qualityMetrics.retrievalRelevance,
        generationCoherence: simpleResponse.qualityMetrics.generationCoherence
      },
      validation: isValidSimple
    });
    
    // 3. 测试情感支持工作流
    console.log('🔄 测试情感支持工作流...');
    
    const emotionalRequest = {
      userMessage: "我最近感觉很焦虑，工作压力很大，不知道该怎么办",
      userProfile: "职场新人，容易焦虑",
      conversationHistory: [
        "用户: 最近工作很忙",
        "AI: 理解你的感受，工作忙碌确实会带来压力",
        "用户: 是的，感觉有点吃不消"
      ],
      generationConfig: {
        temperature: 0.6,
        maxTokens: 500,
        streamResponse: false
      }
    };
    
    const emotionalResponse = await coordinator.executeWorkflow(emotionalRequest);
    const isValidEmotional = coordinator.validateWorkflow(emotionalResponse);
    
    results.tests.push({
      name: '情感支持工作流',
      success: isValidEmotional,
      workflow: {
        workflowId: emotionalResponse.workflowId,
        finalResponseLength: emotionalResponse.finalResponse.length,
        totalTime: emotionalResponse.executionStats.totalTime,
        navigatorTime: emotionalResponse.executionStats.navigatorTime,
        retrieverTime: emotionalResponse.executionStats.retrieverTime,
        generatorTime: emotionalResponse.executionStats.generatorTime,
        overallQuality: emotionalResponse.qualityMetrics.overallQuality,
        navigatorConfidence: emotionalResponse.qualityMetrics.navigatorConfidence
      },
      validation: isValidEmotional
    });
    
    // 4. 测试技术问题工作流
    console.log('🔄 测试技术问题工作流...');
    
    const technicalRequest = {
      userMessage: "如何提高React应用的性能？",
      userProfile: "前端开发者",
      conversationHistory: [
        "用户: 我在学习React",
        "AI: React是一个很棒的前端框架！有什么具体问题吗？"
      ],
      generationConfig: {
        temperature: 0.5,
        maxTokens: 400,
        streamResponse: false
      }
    };
    
    const technicalResponse = await coordinator.executeWorkflow(technicalRequest);
    const isValidTechnical = coordinator.validateWorkflow(technicalResponse);
    
    results.tests.push({
      name: '技术问题工作流',
      success: isValidTechnical,
      workflow: {
        workflowId: technicalResponse.workflowId,
        finalResponseLength: technicalResponse.finalResponse.length,
        totalTime: technicalResponse.executionStats.totalTime,
        overallQuality: technicalResponse.qualityMetrics.overallQuality
      },
      validation: isValidTechnical
    });
    
    // 5. 测试并行处理配置
    console.log('🔄 测试并行处理配置...');
    
    const parallelCoordinator = new ThreeEngineWorkflowCoordinator({
      enableParallelProcessing: true,
      workflowTimeout: 20000,
      enableCaching: true
    });
    await parallelCoordinator.initialize();
    
    const parallelRequest = {
      userMessage: "请给我一些学习建议",
      userProfile: "学习者",
      conversationHistory: [],
      generationConfig: {
        temperature: 0.7,
        maxTokens: 200,
        streamResponse: false
      }
    };
    
    const parallelResponse = await parallelCoordinator.executeWorkflow(parallelRequest);
    const isValidParallel = parallelCoordinator.validateWorkflow(parallelResponse);
    
    results.tests.push({
      name: '并行处理配置测试',
      success: isValidParallel,
      workflow: {
        workflowId: parallelResponse.workflowId,
        totalTime: parallelResponse.executionStats.totalTime,
        coordinationOverhead: parallelResponse.executionStats.coordinationOverhead,
        overallQuality: parallelResponse.qualityMetrics.overallQuality
      },
      validation: isValidParallel
    });
    
    // 6. 测试缓存功能
    console.log('🔄 测试缓存功能...');
    
    const cacheCoordinator = new ThreeEngineWorkflowCoordinator({
      enableCaching: true,
      qualityThreshold: 0.5
    });
    await cacheCoordinator.initialize();
    
    const cacheRequest = {
      userMessage: "今天是个好日子",
      userProfile: "乐观用户",
      conversationHistory: [],
      generationConfig: {
        temperature: 0.7,
        maxTokens: 150,
        streamResponse: false
      }
    };
    
    // 第一次执行
    const firstResponse = await cacheCoordinator.executeWorkflow(cacheRequest);
    const firstTime = firstResponse.executionStats.totalTime;
    
    // 第二次执行（应该使用缓存）
    const secondResponse = await cacheCoordinator.executeWorkflow(cacheRequest);
    const secondTime = secondResponse.executionStats.totalTime;
    
    results.tests.push({
      name: '缓存功能测试',
      success: true,
      caching: {
        firstExecutionTime: firstTime,
        secondExecutionTime: secondTime,
        cacheUsed: secondTime < firstTime * 0.5, // 如果第二次明显更快，说明使用了缓存
        firstWorkflowId: firstResponse.workflowId,
        secondWorkflowId: secondResponse.workflowId
      },
      message: '缓存功能测试完成'
    });
    
    // 7. 测试质量验证功能
    console.log('🔄 测试质量验证功能...');
    
    // 创建一个低质量的模拟响应
    const lowQualityResponse = {
      ...simpleResponse,
      finalResponse: "好的",
      qualityMetrics: {
        overallQuality: 0.2,
        navigatorConfidence: 0.3,
        retrievalRelevance: 0.1,
        generationCoherence: 0.2
      }
    };
    
    const validationResults = {
      highQualityValid: coordinator.validateWorkflow(simpleResponse),
      lowQualityValid: coordinator.validateWorkflow(lowQualityResponse),
      emptyResponseValid: coordinator.validateWorkflow({
        ...simpleResponse,
        finalResponse: ""
      })
    };
    
    results.tests.push({
      name: '质量验证测试',
      success: validationResults.highQualityValid && !validationResults.lowQualityValid && !validationResults.emptyResponseValid,
      validationResults,
      message: '质量验证功能正常工作'
    });
    
    // 8. 测试错误处理和降级
    console.log('🔄 测试错误处理和降级...');
    
    const fallbackCoordinator = new ThreeEngineWorkflowCoordinator({
      enableFallback: true,
      workflowTimeout: 1, // 极短的超时时间来触发错误
      retryAttempts: 1
    });
    await fallbackCoordinator.initialize();
    
    try {
      const fallbackResponse = await fallbackCoordinator.executeWorkflow({
        userMessage: "测试错误处理",
        userProfile: "测试用户",
        conversationHistory: [],
        generationConfig: { temperature: 0.7, maxTokens: 100, streamResponse: false }
      });
      
      results.tests.push({
        name: '错误处理和降级测试',
        success: true,
        fallback: {
          fallbackUsed: fallbackResponse.workflowMetadata?.fallbackUsed || false,
          responseLength: fallbackResponse.finalResponse.length,
          overallQuality: fallbackResponse.qualityMetrics.overallQuality
        },
        message: '错误处理和降级功能正常'
      });
    } catch (error) {
      results.tests.push({
        name: '错误处理和降级测试',
        success: false,
        error: error instanceof Error ? error.message : '降级处理失败',
        message: '降级处理未能正常工作'
      });
    }
    
    // 9. 测试工作流统计功能
    console.log('🔄 测试工作流统计功能...');
    
    const workflowStats = coordinator.getWorkflowStats();
    results.workflowStats = {
      totalExecutions: workflowStats.totalExecutions,
      successRate: Math.round(workflowStats.successRate * 100) / 100,
      averageExecutionTime: Math.round(workflowStats.averageExecutionTime),
      enginePerformance: {
        navigator: {
          averageTime: Math.round(workflowStats.enginePerformance.navigator.averageTime),
          errorCount: workflowStats.enginePerformance.navigator.errorCount
        },
        retriever: {
          averageTime: Math.round(workflowStats.enginePerformance.retriever.averageTime),
          errorCount: workflowStats.enginePerformance.retriever.errorCount
        },
        generator: {
          averageTime: Math.round(workflowStats.enginePerformance.generator.averageTime),
          errorCount: workflowStats.enginePerformance.generator.errorCount
        }
      },
      lastExecutionTime: workflowStats.lastExecutionTime,
      cacheStats: workflowStats.cacheStats
    };
    
    results.tests.push({
      name: '工作流统计测试',
      success: workflowStats.totalExecutions > 0,
      stats: results.workflowStats,
      message: '工作流统计功能正常'
    });
    
    // 10. 综合性能基准测试
    console.log('🔄 执行综合性能基准测试...');
    
    const benchmarkMessages = [
      "今天心情不错",
      "工作遇到了困难",
      "想要学习新技能",
      "感觉有点迷茫"
    ];
    
    const benchmarkResults = [];
    const benchmarkStart = Date.now();
    
    for (const message of benchmarkMessages) {
      const start = Date.now();
      try {
        const response = await coordinator.executeWorkflow({
          userMessage: message,
          userProfile: "基准测试用户",
          conversationHistory: [],
          generationConfig: { temperature: 0.7, maxTokens: 150, streamResponse: false }
        });
        
        const time = Date.now() - start;
        benchmarkResults.push({
          message: message.slice(0, 15) + '...',
          success: true,
          totalTime: time,
          navigatorTime: response.executionStats.navigatorTime,
          retrieverTime: response.executionStats.retrieverTime,
          generatorTime: response.executionStats.generatorTime,
          coordinationOverhead: response.executionStats.coordinationOverhead,
          overallQuality: response.qualityMetrics.overallQuality,
          responseLength: response.finalResponse.length
        });
      } catch (error) {
        benchmarkResults.push({
          message: message.slice(0, 15) + '...',
          success: false,
          error: error instanceof Error ? error.message : '执行失败'
        });
      }
    }
    
    const totalBenchmarkTime = Date.now() - benchmarkStart;
    
    results.tests.push({
      name: '综合性能基准测试',
      success: benchmarkResults.every(r => r.success),
      totalTime: totalBenchmarkTime,
      averageTime: Math.round(totalBenchmarkTime / benchmarkMessages.length),
      results: benchmarkResults,
      message: `完成${benchmarkMessages.length}个工作流，平均耗时${Math.round(totalBenchmarkTime / benchmarkMessages.length)}ms`
    });
    
    // 11. 综合功能评估
    console.log('🔄 综合功能评估...');
    
    const successfulTests = results.tests.filter(test => test.success).length;
    const totalTests = results.tests.length;
    const successRate = Math.round((successfulTests / totalTests) * 100);
    
    results.tests.push({
      name: '综合功能评估',
      success: successRate >= 80,
      evaluation: {
        successfulTests,
        totalTests,
        successRate: `${successRate}%`,
        coordinatorReady: successRate >= 80
      },
      message: `三引擎工作流协调器功能完整性: ${successRate}%`
    });
    
    console.log('✅ 三引擎工作流协调器测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { userMessage, userProfile, conversationHistory, generationConfig, coordinatorConfig } = await req.json();
    
    const { ThreeEngineWorkflowCoordinator } = await import("@/lib/services/three-engine/workflow-coordinator");
    
    const coordinator = new ThreeEngineWorkflowCoordinator(coordinatorConfig);
    await coordinator.initialize();
    
    const workflowRequest = {
      userMessage,
      userProfile,
      conversationHistory,
      generationConfig
    };
    
    const response = await coordinator.executeWorkflow(workflowRequest);
    const isValid = coordinator.validateWorkflow(response);
    const stats = coordinator.getWorkflowStats();
    
    return new Response(JSON.stringify({
      success: true,
      workflow: response,
      validation: {
        isValid,
        qualityMetrics: response.qualityMetrics
      },
      workflowStats: stats,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 工作流执行失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '工作流执行失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
