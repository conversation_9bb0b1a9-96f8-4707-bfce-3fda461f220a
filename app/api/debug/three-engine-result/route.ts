// 三引擎调试结果API端点
// 接收和存储三引擎工作流的调试信息

import { NextRequest, NextResponse } from 'next/server';

// 存储最新的三引擎调试结果
let latestThreeEngineResult: any = null;
let debugHistory: any[] = [];
const MAX_HISTORY_SIZE = 50;

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    console.log('🔍 收到三引擎调试数据:', {
      workflowId: data.workflowResult?.metadata?.workflowId,
      totalTime: data.workflowResult?.performance?.totalTime,
      quality: data.workflowResult?.quality?.overall,
      timestamp: data.timestamp
    });

    // 存储最新结果
    latestThreeEngineResult = {
      ...data,
      receivedAt: new Date().toISOString()
    };

    // 添加到历史记录
    debugHistory.unshift(latestThreeEngineResult);
    
    // 限制历史记录大小
    if (debugHistory.length > MAX_HISTORY_SIZE) {
      debugHistory = debugHistory.slice(0, MAX_HISTORY_SIZE);
    }

    return NextResponse.json({ 
      success: true, 
      message: '三引擎调试数据已保存',
      historySize: debugHistory.length
    });
  } catch (error) {
    console.error('❌ 保存三引擎调试数据失败:', error);
    return NextResponse.json(
      { error: '保存调试数据失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'latest';
    const limit = parseInt(searchParams.get('limit') || '10');

    switch (type) {
      case 'latest':
        return NextResponse.json({
          success: true,
          data: latestThreeEngineResult,
          timestamp: new Date().toISOString()
        });

      case 'history':
        return NextResponse.json({
          success: true,
          data: debugHistory.slice(0, limit),
          total: debugHistory.length,
          timestamp: new Date().toISOString()
        });

      case 'stats':
        const stats = calculateThreeEngineStats();
        return NextResponse.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: '不支持的查询类型' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('❌ 获取三引擎调试数据失败:', error);
    return NextResponse.json(
      { error: '获取调试数据失败' },
      { status: 500 }
    );
  }
}

/**
 * 计算三引擎统计信息
 */
function calculateThreeEngineStats() {
  if (debugHistory.length === 0) {
    return {
      totalWorkflows: 0,
      averageTime: 0,
      averageQuality: 0,
      cacheHitRate: 0,
      enginePerformance: {}
    };
  }

  const validResults = debugHistory.filter(item => 
    item.workflowResult && item.workflowResult.performance
  );

  if (validResults.length === 0) {
    return {
      totalWorkflows: debugHistory.length,
      averageTime: 0,
      averageQuality: 0,
      cacheHitRate: 0,
      enginePerformance: {}
    };
  }

  // 计算平均处理时间
  const totalTime = validResults.reduce((sum, item) => 
    sum + (item.workflowResult.performance.totalTime || 0), 0
  );
  const averageTime = totalTime / validResults.length;

  // 计算平均质量分数
  const totalQuality = validResults.reduce((sum, item) => 
    sum + (item.workflowResult.quality?.overall || 0), 0
  );
  const averageQuality = totalQuality / validResults.length;

  // 计算缓存命中率
  const cacheHits = validResults.filter(item => 
    item.workflowResult.performance?.cacheUtilization?.used
  ).length;
  const cacheHitRate = cacheHits / validResults.length;

  // 计算各引擎性能
  const engineTimes = {
    navigator: 0,
    contextRetriever: 0,
    integrationGenerator: 0,
    coldStoreRetrieval: 0
  };

  validResults.forEach(item => {
    const times = item.workflowResult.performance.engineTimes;
    if (times) {
      engineTimes.navigator += times.navigator || 0;
      engineTimes.contextRetriever += times.contextRetriever || 0;
      engineTimes.integrationGenerator += times.integrationGenerator || 0;
      engineTimes.coldStoreRetrieval += times.coldStoreRetrieval || 0;
    }
  });

  // 计算平均引擎时间
  Object.keys(engineTimes).forEach(key => {
    engineTimes[key as keyof typeof engineTimes] /= validResults.length;
  });

  return {
    totalWorkflows: debugHistory.length,
    validWorkflows: validResults.length,
    averageTime: Math.round(averageTime),
    averageQuality: Math.round(averageQuality * 100) / 100,
    cacheHitRate: Math.round(cacheHitRate * 100) / 100,
    enginePerformance: {
      averageTimes: engineTimes,
      parallelEfficiency: validResults.reduce((sum, item) => 
        sum + (item.workflowResult.performance.parallelEfficiency || 0), 0
      ) / validResults.length
    },
    qualityBreakdown: {
      coherence: validResults.reduce((sum, item) => 
        sum + (item.workflowResult.quality?.coherence || 0), 0
      ) / validResults.length,
      relevance: validResults.reduce((sum, item) => 
        sum + (item.workflowResult.quality?.relevance || 0), 0
      ) / validResults.length,
      empathy: validResults.reduce((sum, item) => 
        sum + (item.workflowResult.quality?.empathy || 0), 0
      ) / validResults.length,
      insight: validResults.reduce((sum, item) => 
        sum + (item.workflowResult.quality?.insight || 0), 0
      ) / validResults.length
    }
  };
}

export async function DELETE() {
  try {
    latestThreeEngineResult = null;
    debugHistory = [];
    
    return NextResponse.json({ 
      success: true, 
      message: '三引擎调试数据已清空' 
    });
  } catch (error) {
    console.error('❌ 清空三引擎调试数据失败:', error);
    return NextResponse.json(
      { error: '清空调试数据失败' },
      { status: 500 }
    );
  }
}
