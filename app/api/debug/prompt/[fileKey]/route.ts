import { NextRequest } from 'next/server';
import { readMemoryFile, writeMemoryFile, readPromptFile, writePromptFile } from '@/lib/storage/memory-manager';
import { MEMORY_FILES, PROMPT_FILES } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";

// 文件键映射
const FILE_KEY_MAP: Record<string, string> = {
  'USER_PROFILE': MEMORY_FILES.USER_PROFILE,
  'MENTAL_ELEMENTS': MEMORY_FILES.MENTAL_ELEMENTS,
  'XIAO_JING_PERSONA': MEMORY_FILES.XIAO_JING_PERSONA,
  'DAILY_CHAT_PROMPT': PROMPT_FILES.DAILY_CHAT_PROMPT,
  'DAILY_INSIGHT_PROMPT': PROMPT_FILES.DAILY_INSIGHT_PROMPT,
  'SYSTEM_PROMPT': PROMPT_FILES.SYSTEM_PROMPT,
  'DEEP_REFINEMENT_PROMPT': PROMPT_FILES.DEEP_REFINEMENT_PROMPT
};

// 判断是否为提示词文件
const isPromptFile = (fileKey: string): boolean => {
  return ['DAILY_CHAT_PROMPT', 'DAILY_INSIGHT_PROMPT', 'SYSTEM_PROMPT', 'DEEP_REFINEMENT_PROMPT'].includes(fileKey);
};

// GET - 读取提示词文件
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileKey: string }> }
) {
  try {
    const { fileKey } = await params;
    const filename = FILE_KEY_MAP[fileKey];
    
    if (!filename) {
      return new Response('文件不存在', { status: 404 });
    }

    let content: string;
    if (isPromptFile(fileKey)) {
      content = await readPromptFile(filename);
    } else {
      content = await readMemoryFile(filename);
    }

    return new Response(content, {
      headers: { 'Content-Type': 'text/plain; charset=utf-8' }
    });
  } catch (error) {
    console.error('读取提示词文件失败:', error);
    return new Response('读取文件失败', { status: 500 });
  }
}

// POST - 保存提示词文件
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ fileKey: string }> }
) {
  try {
    const { fileKey } = await params;
    const { content } = await request.json();
    const filename = FILE_KEY_MAP[fileKey];
    
    if (!filename) {
      return new Response('文件不存在', { status: 404 });
    }

    if (typeof content !== 'string') {
      return new Response('内容格式错误', { status: 400 });
    }

    if (isPromptFile(fileKey)) {
      await writePromptFile(filename, content);
    } else {
      await writeMemoryFile(filename, content);
    }

    console.log(`✅ 提示词文件已保存: ${filename}`);
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: '文件保存成功',
      filename 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('保存提示词文件失败:', error);
    return new Response('保存文件失败', { status: 500 });
  }
}
