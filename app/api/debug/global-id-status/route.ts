/**
 * 全局ID系统状态调试API
 * 用于查看ID注册表和统计信息
 */

import { globalIdManager } from "@/lib/services/vector-database/global-id-system";

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🔍 获取全局ID系统状态...');
    
    // 初始化ID管理器
    await globalIdManager.initialize();
    
    // 获取今日统计
    const todayStats = globalIdManager.getTodayStats();
    
    // 获取历史统计（最近7天）
    const historyStats = globalIdManager.getHistoryStats(7);
    
    // 获取所有ID的元数据（仅用于调试，生产环境应该分页）
    const allIds: any[] = [];
    
    // 由于无法直接访问私有属性，我们通过公共方法获取信息
    // 这里我们模拟一些测试数据来展示结构
    
    const response = {
      success: true,
      data: {
        todayStats,
        historyStats,
        systemInfo: {
          initialized: true,
          totalRegisteredIds: todayStats?.totalInputs || 0 + todayStats?.totalDerivations || 0,
          currentDate: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
          lastUpdated: new Date().toISOString()
        },
        recentIds: allIds.slice(-10) // 最近10个ID
      },
      timestamp: new Date().toISOString()
    };
    
    console.log('✅ 全局ID系统状态获取成功');
    
    return new Response(JSON.stringify(response, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 获取全局ID系统状态失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '获取状态失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { action } = await req.json();
    
    await globalIdManager.initialize();
    
    let result: any = {};
    
    switch (action) {
      case 'generateTestId':
        const testId = await globalIdManager.generateUserInputId();
        await globalIdManager.updateContentMetadata(testId, {
          contentLength: 20,
          contentHash: 'test-hash-' + Date.now()
        });
        result = { testId, message: '测试ID生成成功' };
        break;
        
      case 'validateId':
        const { id } = await req.json();
        const isValid = globalIdManager.validateId(id);
        const metadata = globalIdManager.getIdMetadata(id);
        result = { id, isValid, metadata };
        break;
        
      default:
        throw new Error(`未知操作: ${action}`);
    }
    
    return new Response(JSON.stringify({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 全局ID系统操作失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '操作失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
