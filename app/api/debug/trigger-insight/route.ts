import { readMemoryFile, MEMORY_FILES } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";

// POST - 手动触发每日洞察（用于调试）
export async function POST() {
  try {
    console.log('🧠 手动触发每日洞察分析...');
    
    // 获取对话状态
    const stateResponse = await fetch('http://localhost:3000/api/debug/conversation-state');
    const stateData = await stateResponse.json();
    
    console.log('📊 当前对话状态:', stateData);
    
    // 检查是否有足够的对话
    if (stateData.dialogueBlocksCount < 6) {
      return new Response(JSON.stringify({
        success: false,
        message: `对话块数量不足，当前: ${stateData.dialogueBlocksCount}，需要: 6`,
        currentState: stateData
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 构建对话块数据（模拟最近6轮对话）
    const dialogueHistory = await readMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY);
    const lines = dialogueHistory.split('\n');
    const userMessages = lines.filter(line => line.includes('**用户**:')).slice(-6);
    const assistantMessages = lines.filter(line => line.includes('**小镜**:')).slice(-6);
    
    const dialogueBlocks = userMessages.map((userLine, index) => ({
      userMessage: {
        id: `manual-${index}-user`,
        role: 'user',
        content: userLine.replace('**用户**: ', ''),
        timestamp: new Date().toISOString()
      },
      aiResponse: {
        id: `manual-${index}-ai`,
        role: 'assistant',
        content: assistantMessages[index]?.replace('**小镜**: ', '') || '',
        timestamp: new Date().toISOString()
      }
    }));
    
    console.log(`📝 构建了 ${dialogueBlocks.length} 个对话块用于分析`);
    
    // 调用每日洞察API
    const insightResponse = await fetch('http://localhost:3000/api/daily-insight', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ dialogueBlocks })
    });
    
    if (insightResponse.ok) {
      const insightResult = await insightResponse.json();
      console.log('✅ 每日洞察手动触发成功');
      
      return new Response(JSON.stringify({
        success: true,
        message: '每日洞察手动触发成功',
        insight: insightResult.insight,
        stats: insightResult.stats,
        dialogueBlocksAnalyzed: dialogueBlocks.length,
        currentState: stateData
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      const errorText = await insightResponse.text();
      throw new Error(`每日洞察API调用失败: ${errorText}`);
    }
    
  } catch (error) {
    console.error('❌ 手动触发每日洞察失败:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: '手动触发每日洞察失败',
      details: error instanceof Error ? error.message : String(error)
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
