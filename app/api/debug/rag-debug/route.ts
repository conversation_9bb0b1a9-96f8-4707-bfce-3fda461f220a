import { NextRequest, NextResponse } from 'next/server'
import { ragRetriever } from '@/lib/services/rag-retriever'
import { RAGDebugParams } from '@/types/rag'
import fs from 'fs/promises'
import path from 'path'

export const runtime = "nodejs"
export const maxDuration = 30

// 读取内存文件的辅助函数
async function readMemoryFile(fileName: string): Promise<string> {
  try {
    const filePath = path.join(process.cwd(), 'memory', fileName)
    const content = await fs.readFile(filePath, 'utf-8')
    return content
  } catch (error) {
    console.warn(`⚠️ 无法读取文件 ${fileName}:`, error)
    return ''
  }
}

export async function POST(req: NextRequest) {
  console.log('🔧 收到RAG调试请求')

  try {
    const { debugParams, testQuery } = await req.json()
    
    if (!debugParams) {
      return NextResponse.json(
        { error: '缺少调试参数' },
        { status: 400 }
      )
    }

    console.log('🔧 调试参数:', debugParams)

    // 读取必要的内存文件
    const userProfile = await readMemoryFile('用户画像.md')
    const dailyInsight = await readMemoryFile('每日洞察今天.md')
    
    // 使用测试查询或默认查询
    const query = testQuery || '最近的心情状态如何？'

    console.log('🔍 开始执行RAG调试检索...')

    // 为了测试目的，先返回模拟数据
    const debugResult = {
      memories: [
        "这是第一个检索到的记忆片段，包含了用户的一些重要信息和情感状态。",
        "第二个记忆片段描述了用户的日常活动和思考过程。",
        "第三个记忆片段记录了用户的目标和计划。"
      ],
      metadata: {
        totalCandidates: debugParams.topK,
        filteredCandidates: Math.floor(debugParams.topK * 0.8),
        finalResults: debugParams.finalCount,
        processingTime: Math.floor(Math.random() * 1000) + 100,
        averageRelevanceScore: 0.75 + Math.random() * 0.2
      },
      debugParams,
      modeSpecificInfo: {
        hybridWeightBreakdown: [
          {
            chunkId: "chunk_1",
            semanticScore: 0.85,
            tagScore: 0.72,
            memoryImportanceScore: 0.68,
            finalScore: debugParams.w1 * 0.85 + debugParams.w2 * 0.72 + debugParams.w3 * 0.68
          },
          {
            chunkId: "chunk_2",
            semanticScore: 0.78,
            tagScore: 0.65,
            memoryImportanceScore: 0.71,
            finalScore: debugParams.w1 * 0.78 + debugParams.w2 * 0.65 + debugParams.w3 * 0.71
          }
        ]
      },
      debugInfo: {
        topCandidates: [],
        weightingBreakdown: []
      }
    }

    // 真实的调试检索函数（暂时注释掉）
    // const debugResult = await ragRetriever.retrieveWithDebugInfo(
    //   query,
    //   userProfile,
    //   dailyInsight,
    //   debugParams as RAGDebugParams
    // )

    console.log('✅ RAG调试检索完成')

    return NextResponse.json(debugResult)

  } catch (error) {
    console.error('💥 RAG调试失败:', error)
    return NextResponse.json(
      { 
        error: 'RAG调试失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  // 返回当前RAG系统状态
  try {
    const debugInfo = ragRetriever.getDebugInfo()
    return NextResponse.json({
      status: 'ok',
      debugInfo
    })
  } catch (error) {
    console.error('💥 获取RAG状态失败:', error)
    return NextResponse.json(
      { error: '获取RAG状态失败' },
      { status: 500 }
    )
  }
}
