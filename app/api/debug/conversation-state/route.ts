import { readMemoryFile, MEMORY_FILES } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";

// GET - 获取对话状态信息用于恢复
export async function GET() {
  try {
    console.log('🔍 分析对话历史以恢复状态...');
    
    // 读取对话历史文件
    const dialogueHistory = await readMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY);
    
    if (!dialogueHistory.trim()) {
      return new Response(JSON.stringify({
        dialogueBlocksCount: 0,
        lastConversationTime: null,
        totalUserMessages: 0,
        totalAssistantMessages: 0
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 分析对话历史结构
    const lines = dialogueHistory.split('\n');
    const userMessages = lines.filter(line => line.includes('**用户**:'));
    const assistantMessages = lines.filter(line => line.includes('**小镜**:'));
    
    // 计算对话块数量（取较小值确保配对）
    const dialogueBlocksCount = Math.min(userMessages.length, assistantMessages.length);
    
    // 提取最后一次对话时间
    let lastConversationTime = null;
    const timeStampPattern = /### (\d{4}\/\d{1,2}\/\d{1,2} \d{1,2}:\d{1,2}:\d{1,2})/g;
    const timeStamps = [...dialogueHistory.matchAll(timeStampPattern)];
    if (timeStamps.length > 0) {
      lastConversationTime = timeStamps[timeStamps.length - 1][1];
    }
    
    console.log(`📊 分析结果: ${dialogueBlocksCount} 个对话块, 最后对话时间: ${lastConversationTime}`);
    
    return new Response(JSON.stringify({
      dialogueBlocksCount,
      lastConversationTime,
      totalUserMessages: userMessages.length,
      totalAssistantMessages: assistantMessages.length,
      shouldTriggerInsight: dialogueBlocksCount >= 6 && dialogueBlocksCount % 6 === 0,
      shouldTriggerRefinement: dialogueBlocksCount >= 30 && dialogueBlocksCount % 30 === 0,
      nextInsightAt: Math.ceil(dialogueBlocksCount / 6) * 6,
      nextRefinementAt: Math.ceil(dialogueBlocksCount / 30) * 30
    }), {
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
    
  } catch (error) {
    console.error('获取对话状态失败:', error);
    return new Response(JSON.stringify({ 
      error: '获取对话状态失败',
      details: error instanceof Error ? error.message : String(error)
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
