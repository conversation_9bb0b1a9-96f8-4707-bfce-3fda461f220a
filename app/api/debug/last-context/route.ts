export const runtime = "nodejs";

// 存储最后一次的上下文数据（简单的内存存储）
let lastContextData: any = null;

// GET - 获取最后一次的上下文数据
export async function GET() {
  try {
    return new Response(JSON.stringify(lastContextData || {}), {
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('获取上下文数据失败:', error);
    return new Response('获取失败', { status: 500 });
  }
}

// POST - 更新上下文数据（由chat API调用）
export async function POST(request: Request) {
  try {
    const contextData = await request.json();
    lastContextData = {
      ...contextData,
      timestamp: new Date().toISOString()
    };
    
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('更新上下文数据失败:', error);
    return new Response('更新失败', { status: 500 });
  }
}
