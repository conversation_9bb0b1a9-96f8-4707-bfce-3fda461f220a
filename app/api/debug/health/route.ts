/**
 * System Health Management API - 系统健康管理接口
 * 
 * 提供系统健康检查、监控和恢复功能
 */

import { systemHealthChecker, ComponentType, HealthStatus } from '@/lib/health/system-health-checker';

export const runtime = "nodejs";

/**
 * GET /api/debug/health
 * 获取系统健康状态和报告
 */
export async function GET(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const component = url.searchParams.get('component') as ComponentType;

    let data: any = {};

    switch (action) {
      case 'status':
        // 获取健康检查状态
        data = systemHealthChecker.getHealthCheckingStatus();
        break;
        
      case 'report':
        // 生成健康报告
        data = await systemHealthChecker.generateHealthReport();
        break;
        
      case 'component':
        // 获取特定组件健康状态
        if (!component) {
          throw new Error('获取组件健康状态需要提供component参数');
        }
        data = systemHealthChecker.getComponentHealth(component);
        break;
        
      case 'checks':
        // 获取所有健康检查
        data = await getAllHealthChecks();
        break;
        
      case 'actions':
        // 获取可用的恢复操作
        data = {
          recoveryActions: systemHealthChecker.getAvailableRecoveryActions(component),
          totalActions: systemHealthChecker.getAvailableRecoveryActions().length
        };
        break;
        
      case 'overview':
        // 获取系统健康概览
        data = await getSystemHealthOverview();
        break;
        
      default:
        // 获取完整的健康信息
        data = {
          status: systemHealthChecker.getHealthCheckingStatus(),
          report: await systemHealthChecker.generateHealthReport(),
          overview: await getSystemHealthOverview()
        };
    }

    const response = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestParams: { action, component }
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 获取系统健康信息失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'SYSTEM_HEALTH_ERROR',
        message: error instanceof Error ? error.message : '获取系统健康信息失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * POST /api/debug/health
 * 系统健康管理操作
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const { action, checkId, actionId, component, enabled } = await request.json();
    
    let result: any = {};
    
    switch (action) {
      case 'start':
        // 启动健康检查
        systemHealthChecker.startHealthChecking();
        result = {
          action: 'start',
          message: '系统健康检查已启动',
          status: systemHealthChecker.getHealthCheckingStatus()
        };
        break;
        
      case 'stop':
        // 停止健康检查
        systemHealthChecker.stopHealthChecking();
        result = {
          action: 'stop',
          message: '系统健康检查已停止',
          status: systemHealthChecker.getHealthCheckingStatus()
        };
        break;
        
      case 'runChecks':
        // 手动运行健康检查
        const checkResults = await systemHealthChecker.runAllHealthChecks();
        result = {
          action: 'runChecks',
          message: '健康检查已完成',
          results: Array.from(checkResults.entries()).map(([id, result]) => ({
            id,
            ...result
          })),
          summary: {
            total: checkResults.size,
            healthy: Array.from(checkResults.values()).filter(r => r.status === HealthStatus.HEALTHY).length,
            warning: Array.from(checkResults.values()).filter(r => r.status === HealthStatus.WARNING).length,
            critical: Array.from(checkResults.values()).filter(r => r.status === HealthStatus.CRITICAL).length
          }
        };
        break;
        
      case 'toggleCheck':
        // 启用/禁用健康检查
        if (!checkId || enabled === undefined) {
          throw new Error('切换健康检查需要提供checkId和enabled');
        }
        
        const toggleResult = systemHealthChecker.toggleHealthCheck(checkId, enabled);
        result = {
          action: 'toggleCheck',
          checkId,
          enabled,
          success: toggleResult,
          message: toggleResult ? 
            `健康检查已${enabled ? '启用' : '禁用'}` : 
            '健康检查不存在'
        };
        break;
        
      case 'executeRecovery':
        // 执行恢复操作
        if (!actionId) {
          throw new Error('执行恢复操作需要提供actionId');
        }
        
        const recoveryResult = await systemHealthChecker.executeRecoveryAction(actionId);
        result = {
          action: 'executeRecovery',
          actionId,
          success: recoveryResult,
          message: recoveryResult ? '恢复操作执行成功' : '恢复操作执行失败'
        };
        break;
        
      case 'generateReport':
        // 生成详细健康报告
        const detailedReport = await systemHealthChecker.generateHealthReport();
        result = {
          action: 'generateReport',
          report: detailedReport,
          message: '健康报告生成完成'
        };
        break;
        
      case 'systemDiagnostic':
        // 执行系统诊断
        result = await performSystemDiagnostic();
        break;
        
      case 'emergencyRecovery':
        // 紧急恢复
        result = await performEmergencyRecovery();
        break;
        
      default:
        throw new Error(`不支持的操作: ${action}`);
    }
    
    const response = {
      success: true,
      data: result,
      metadata: {
        timestamp: new Date().toISOString(),
        requestedAction: action
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 系统健康管理操作失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'SYSTEM_HEALTH_MANAGEMENT_ERROR',
        message: error instanceof Error ? error.message : '系统健康管理操作失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 获取所有健康检查
 */
async function getAllHealthChecks(): Promise<any> {
  const status = systemHealthChecker.getHealthCheckingStatus();
  const components = Object.values(ComponentType);
  
  const checks = components.map(component => {
    const componentHealth = systemHealthChecker.getComponentHealth(component);
    return {
      component,
      status: componentHealth.status,
      score: componentHealth.score,
      checks: componentHealth.checks
    };
  });
  
  return {
    status,
    components: checks,
    summary: {
      totalComponents: components.length,
      healthyComponents: checks.filter(c => c.status === HealthStatus.HEALTHY).length,
      warningComponents: checks.filter(c => c.status === HealthStatus.WARNING).length,
      criticalComponents: checks.filter(c => c.status === HealthStatus.CRITICAL).length
    }
  };
}

/**
 * 获取系统健康概览
 */
async function getSystemHealthOverview(): Promise<any> {
  const report = await systemHealthChecker.generateHealthReport();
  const status = systemHealthChecker.getHealthCheckingStatus();
  
  return {
    overallHealth: {
      status: report.overallStatus,
      score: report.overallScore,
      uptime: report.uptime
    },
    systemMetrics: {
      memoryUsage: getMemoryUsage(),
      processInfo: {
        pid: process.pid,
        uptime: Math.round(process.uptime()),
        nodeVersion: process.version
      }
    },
    monitoring: {
      isRunning: status.isRunning,
      totalChecks: status.totalChecks,
      enabledChecks: status.enabledChecks,
      lastCheck: status.lastCheckTime
    },
    alerts: {
      total: report.alerts.length,
      critical: report.alerts.filter(a => a.level === 'critical').length,
      warning: report.alerts.filter(a => a.level === 'warning').length,
      info: report.alerts.filter(a => a.level === 'info').length
    },
    recommendations: report.recommendations.slice(0, 5), // 只显示前5个建议
    quickActions: getQuickActions()
  };
}

/**
 * 获取内存使用情况
 */
function getMemoryUsage(): any {
  const memoryUsage = process.memoryUsage();
  return {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024),
    usagePercent: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100 * 100) / 100
  };
}

/**
 * 获取快速操作
 */
function getQuickActions(): any[] {
  return [
    {
      id: 'memory-cleanup',
      name: '内存清理',
      description: '执行垃圾回收释放内存',
      severity: 'medium'
    },
    {
      id: 'cache-refresh',
      name: '缓存刷新',
      description: '刷新缓存提高命中率',
      severity: 'low'
    },
    {
      id: 'performance-monitor-restart',
      name: '性能监控重启',
      description: '重启性能监控器',
      severity: 'high'
    }
  ];
}

/**
 * 执行系统诊断
 */
async function performSystemDiagnostic(): Promise<any> {
  console.log('🔍 开始系统诊断...');
  
  try {
    // 运行所有健康检查
    const checkResults = await systemHealthChecker.runAllHealthChecks();
    
    // 生成健康报告
    const healthReport = await systemHealthChecker.generateHealthReport();
    
    // 收集系统信息
    const systemInfo = {
      memory: getMemoryUsage(),
      uptime: Math.round(process.uptime()),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };
    
    // 分析问题
    const issues = analyzeSystemIssues(healthReport, checkResults);
    
    return {
      action: 'systemDiagnostic',
      timestamp: new Date().toISOString(),
      healthReport,
      systemInfo,
      checkResults: Array.from(checkResults.entries()).map(([id, result]) => ({
        id,
        ...result
      })),
      issues,
      recommendations: generateDiagnosticRecommendations(issues),
      message: '系统诊断完成'
    };
  } catch (error) {
    return {
      action: 'systemDiagnostic',
      error: error instanceof Error ? error.message : '系统诊断失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 执行紧急恢复
 */
async function performEmergencyRecovery(): Promise<any> {
  console.log('🚨 开始紧急恢复...');
  
  try {
    const recoveryActions = systemHealthChecker.getAvailableRecoveryActions();
    const criticalActions = recoveryActions.filter(action => 
      action.severity === 'critical' || action.severity === 'high'
    );
    
    const results: any[] = [];
    
    for (const action of criticalActions) {
      if (action.autoExecute) {
        try {
          const success = await systemHealthChecker.executeRecoveryAction(action.id);
          results.push({
            actionId: action.id,
            name: action.name,
            success,
            component: action.component
          });
        } catch (error) {
          results.push({
            actionId: action.id,
            name: action.name,
            success: false,
            error: error instanceof Error ? error.message : String(error),
            component: action.component
          });
        }
      }
    }
    
    // 等待一段时间后重新检查
    await new Promise(resolve => setTimeout(resolve, 2000));
    const postRecoveryCheck = await systemHealthChecker.runAllHealthChecks();
    
    return {
      action: 'emergencyRecovery',
      timestamp: new Date().toISOString(),
      recoveryResults: results,
      postRecoveryStatus: Array.from(postRecoveryCheck.entries()).map(([id, result]) => ({
        id,
        status: result.status,
        message: result.message
      })),
      summary: {
        actionsExecuted: results.length,
        successfulActions: results.filter(r => r.success).length,
        failedActions: results.filter(r => !r.success).length
      },
      message: '紧急恢复完成'
    };
  } catch (error) {
    return {
      action: 'emergencyRecovery',
      error: error instanceof Error ? error.message : '紧急恢复失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 分析系统问题
 */
function analyzeSystemIssues(healthReport: any, checkResults: Map<string, any>): any[] {
  const issues: any[] = [];
  
  // 分析整体健康状态
  if (healthReport.overallScore < 50) {
    issues.push({
      type: 'critical',
      category: 'overall',
      message: '系统整体健康状态严重',
      score: healthReport.overallScore
    });
  }
  
  // 分析组件状态
  for (const [componentType, componentData] of Object.entries(healthReport.components)) {
    if ((componentData as any).status === HealthStatus.CRITICAL) {
      issues.push({
        type: 'critical',
        category: 'component',
        component: componentType,
        message: `${componentType} 组件状态严重`,
        score: (componentData as any).score
      });
    }
  }
  
  // 分析内存使用
  const memoryUsage = getMemoryUsage();
  if (memoryUsage.usagePercent > 90) {
    issues.push({
      type: 'critical',
      category: 'memory',
      message: '内存使用率过高',
      usagePercent: memoryUsage.usagePercent
    });
  } else if (memoryUsage.usagePercent > 80) {
    issues.push({
      type: 'warning',
      category: 'memory',
      message: '内存使用率较高',
      usagePercent: memoryUsage.usagePercent
    });
  }
  
  return issues;
}

/**
 * 生成诊断建议
 */
function generateDiagnosticRecommendations(issues: any[]): string[] {
  const recommendations: string[] = [];
  
  const criticalIssues = issues.filter(issue => issue.type === 'critical');
  const warningIssues = issues.filter(issue => issue.type === 'warning');
  
  if (criticalIssues.length > 0) {
    recommendations.push(`发现 ${criticalIssues.length} 个严重问题，建议立即处理`);
  }
  
  if (warningIssues.length > 0) {
    recommendations.push(`发现 ${warningIssues.length} 个警告问题，建议及时处理`);
  }
  
  // 基于问题类型的具体建议
  const memoryIssues = issues.filter(issue => issue.category === 'memory');
  if (memoryIssues.length > 0) {
    recommendations.push('建议执行内存清理或优化内存使用');
  }
  
  const componentIssues = issues.filter(issue => issue.category === 'component');
  if (componentIssues.length > 0) {
    recommendations.push('建议检查和重启相关组件');
  }
  
  if (issues.length === 0) {
    recommendations.push('系统运行正常，无需特别处理');
  }
  
  return recommendations;
}
