// 双核抽象层调试API端点
// 提供配置管理、状态查询和测试操作的API接口

import { NextRequest, NextResponse } from 'next/server';
// TODO: 重构后重新启用
// import { dualCoreDebugController } from '@/lib/services/dual-core-debug-controller';

export async function GET(request: NextRequest) {
  try {
    // 返回模拟数据
    const mockData = {
      status: 'AI模块重构中',
      message: '双核心系统暂时不可用'
    };

    return NextResponse.json({
      success: true,
      data: mockData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('双核调试API查询失败:', error);
    return NextResponse.json({
      success: false,
      error: 'AI模块重构中，暂时不可用'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      message: 'AI模块重构中，暂时不可用',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('双核调试API操作失败:', error);
    return NextResponse.json({
      success: false,
      error: 'AI模块重构中，暂时不可用'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      message: 'AI模块重构中，暂时不可用',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'AI模块重构中，暂时不可用'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      message: 'AI模块重构中，暂时不可用',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'AI模块重构中，暂时不可用'
    }, { status: 500 });
  }
}
