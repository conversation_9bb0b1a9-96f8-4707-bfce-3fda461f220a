/**
 * Testing Management API - 测试管理接口
 * 
 * 提供测试执行、监控和报告功能
 */

import { unifiedTestFramework, TestType, TestPriority } from '@/lib/testing/unified-test-framework';
import { TestSuiteGenerator } from '@/lib/testing/test-generators';

export const runtime = "nodejs";

/**
 * GET /api/debug/testing
 * 获取测试状态和统计信息
 */
export async function GET(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const suiteId = url.searchParams.get('suiteId');

    let data: any = {};

    switch (action) {
      case 'status':
        // 获取测试执行状态
        data = {
          executionStatus: unifiedTestFramework.getExecutionStatus(),
          testStats: unifiedTestFramework.getTestStats()
        };
        break;
        
      case 'stats':
        // 获取测试统计信息
        data = unifiedTestFramework.getTestStats();
        break;
        
      case 'suites':
        // 获取测试套件列表
        data = {
          availableSuites: [
            {
              id: 'selfmirror-core',
              name: 'SelfMirror核心功能测试',
              description: '测试SelfMirror系统的核心功能模块',
              testCount: 5
            },
            {
              id: 'ai-functionality',
              name: 'AI功能测试套件',
              description: '测试AI相关的功能模块',
              testCount: 3
            },
            {
              id: 'performance',
              name: '性能测试套件',
              description: '测试系统各组件的性能表现',
              testCount: 4
            }
          ]
        };
        break;
        
      default:
        // 获取完整的测试信息
        data = {
          executionStatus: unifiedTestFramework.getExecutionStatus(),
          testStats: unifiedTestFramework.getTestStats(),
          systemInfo: {
            frameworkVersion: '1.0.0',
            supportedTestTypes: Object.values(TestType),
            supportedPriorities: Object.values(TestPriority),
            features: [
              '单元测试',
              '集成测试',
              '性能测试',
              '端到端测试',
              '并行执行',
              '自动重试',
              '测试报告生成'
            ]
          }
        };
    }

    const response = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestParams: { action, suiteId }
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 获取测试信息失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'TESTING_INFO_ERROR',
        message: error instanceof Error ? error.message : '获取测试信息失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * POST /api/debug/testing
 * 执行测试和管理操作
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const { action, suiteId, testType, priority, tags, options } = await request.json();
    
    let result: any = {};
    
    switch (action) {
      case 'runAll':
        // 执行所有测试
        await initializeTestSuites();
        result = await unifiedTestFramework.runAllTests({
          testTypes: testType ? [testType] : undefined,
          priority: priority || undefined,
          tags: tags || undefined
        });
        break;
        
      case 'runSuite':
        // 执行特定测试套件
        if (!suiteId) {
          throw new Error('执行测试套件需要提供suiteId');
        }
        
        await initializeTestSuites();
        result = await unifiedTestFramework.runTestSuite(suiteId);
        break;
        
      case 'runCore':
        // 执行核心功能测试
        await initializeTestSuites();
        result = await unifiedTestFramework.runAllTests({
          suiteIds: ['selfmirror-core']
        });
        break;
        
      case 'runPerformance':
        // 执行性能测试
        await initializeTestSuites();
        result = await unifiedTestFramework.runAllTests({
          testTypes: [TestType.PERFORMANCE]
        });
        break;
        
      case 'runCritical':
        // 执行关键测试
        await initializeTestSuites();
        result = await unifiedTestFramework.runAllTests({
          priority: TestPriority.CRITICAL
        });
        break;
        
      case 'initialize':
        // 初始化测试套件
        result = await initializeTestSuites();
        break;
        
      case 'clear':
        // 清理测试结果
        unifiedTestFramework.clearResults();
        result = {
          action: 'clear',
          message: '测试结果已清理',
          timestamp: new Date().toISOString()
        };
        break;
        
      case 'generateReport':
        // 生成测试报告
        result = await generateTestReport();
        break;
        
      case 'healthCheck':
        // 测试健康检查
        result = await performTestHealthCheck();
        break;
        
      default:
        throw new Error(`不支持的操作: ${action}`);
    }
    
    const response = {
      success: true,
      data: result,
      metadata: {
        timestamp: new Date().toISOString(),
        requestedAction: action
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 测试管理操作失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'TESTING_MANAGEMENT_ERROR',
        message: error instanceof Error ? error.message : '测试管理操作失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 初始化测试套件
 */
async function initializeTestSuites(): Promise<{
  initialized: string[];
  totalTests: number;
}> {
  console.log('🧪 初始化测试套件...');
  
  const initialized: string[] = [];
  let totalTests = 0;
  
  try {
    // 注册核心功能测试套件
    const coreTestSuite = TestSuiteGenerator.generateSelfMirrorCoreTestSuite();
    unifiedTestFramework.registerTestSuite(coreTestSuite);
    initialized.push(coreTestSuite.id);
    totalTests += coreTestSuite.testCases.length;
    
    // 注册AI功能测试套件
    const aiTestSuite = TestSuiteGenerator.generateAITestSuite();
    unifiedTestFramework.registerTestSuite(aiTestSuite);
    initialized.push(aiTestSuite.id);
    totalTests += aiTestSuite.testCases.length;
    
    // 注册性能测试套件
    const performanceTestSuite = TestSuiteGenerator.generatePerformanceTestSuite();
    unifiedTestFramework.registerTestSuite(performanceTestSuite);
    initialized.push(performanceTestSuite.id);
    totalTests += performanceTestSuite.testCases.length;
    
    console.log(`✅ 测试套件初始化完成: ${initialized.length} 个套件, ${totalTests} 个测试用例`);
    
    return { initialized, totalTests };
    
  } catch (error) {
    console.error('❌ 测试套件初始化失败:', error);
    throw error;
  }
}

/**
 * 生成测试报告
 */
async function generateTestReport(): Promise<any> {
  try {
    const stats = unifiedTestFramework.getTestStats();
    const executionStatus = unifiedTestFramework.getExecutionStatus();
    
    return {
      reportId: `report-${Date.now()}`,
      generatedAt: new Date().toISOString(),
      testStats: stats,
      executionStatus,
      summary: {
        totalSuites: stats.totalSuites,
        totalTests: stats.totalTests,
        lastRunResults: stats.lastRunResults,
        isCurrentlyRunning: executionStatus.isRunning
      },
      recommendations: generateTestRecommendations(stats)
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '报告生成失败',
      generatedAt: new Date().toISOString()
    };
  }
}

/**
 * 执行测试健康检查
 */
async function performTestHealthCheck(): Promise<any> {
  try {
    const stats = unifiedTestFramework.getTestStats();
    const executionStatus = unifiedTestFramework.getExecutionStatus();
    
    // 检查测试框架健康状态
    const healthChecks = {
      frameworkInitialized: stats.totalSuites > 0,
      testsAvailable: stats.totalTests > 0,
      notCurrentlyRunning: !executionStatus.isRunning,
      lastRunSuccessful: stats.lastRunResults ? stats.lastRunResults.passRate > 50 : null
    };
    
    const healthyChecks = Object.values(healthChecks).filter(check => check === true).length;
    const totalChecks = Object.values(healthChecks).filter(check => check !== null).length;
    const healthScore = totalChecks > 0 ? (healthyChecks / totalChecks) * 100 : 0;
    
    return {
      healthScore: Math.round(healthScore),
      status: healthScore >= 80 ? 'HEALTHY' : healthScore >= 60 ? 'WARNING' : 'CRITICAL',
      checks: healthChecks,
      recommendations: generateHealthRecommendations(healthChecks, stats),
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      healthScore: 0,
      status: 'ERROR',
      error: error instanceof Error ? error.message : '健康检查失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 生成测试建议
 */
function generateTestRecommendations(stats: any): string[] {
  const recommendations: string[] = [];
  
  if (stats.totalTests < 20) {
    recommendations.push('测试用例数量较少，建议增加更多测试覆盖');
  }
  
  if (stats.lastRunResults) {
    if (stats.lastRunResults.passRate < 80) {
      recommendations.push('测试通过率较低，建议修复失败的测试用例');
    }
    if (stats.lastRunResults.failed > 0) {
      recommendations.push(`发现 ${stats.lastRunResults.failed} 个失败的测试，建议优先处理`);
    }
  } else {
    recommendations.push('尚未执行测试，建议运行一次完整测试');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('测试状态良好，继续保持');
  }
  
  return recommendations;
}

/**
 * 生成健康建议
 */
function generateHealthRecommendations(healthChecks: any, stats: any): string[] {
  const recommendations: string[] = [];
  
  if (!healthChecks.frameworkInitialized) {
    recommendations.push('测试框架未初始化，请先初始化测试套件');
  }
  
  if (!healthChecks.testsAvailable) {
    recommendations.push('没有可用的测试用例，请注册测试套件');
  }
  
  if (healthChecks.lastRunSuccessful === false) {
    recommendations.push('上次测试运行失败率较高，建议检查测试用例');
  }
  
  if (stats.totalSuites < 3) {
    recommendations.push('测试套件数量较少，建议增加更多测试套件');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('测试框架健康状态良好');
  }
  
  return recommendations;
}
