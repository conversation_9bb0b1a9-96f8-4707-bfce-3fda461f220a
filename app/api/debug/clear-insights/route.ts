import { writeMemoryFile } from '@/lib/storage/memory-manager';
import { MEMORY_FILES } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";

// POST - 清空每日洞察
export async function POST() {
  try {
    // 清空每日洞察热文件和冷文件
    await Promise.all([
      writeMemoryFile(MEMORY_FILES.DAILY_INSIGHT_HOT, ''),
      writeMemoryFile(MEMORY_FILES.DAILY_INSIGHT_COLD, '')
    ]);
    
    console.log('✅ 每日洞察已清空');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: '每日洞察已清空' 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('清空每日洞察失败:', error);
    return new Response('清空失败', { status: 500 });
  }
}
