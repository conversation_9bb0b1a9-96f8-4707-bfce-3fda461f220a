/**
 * Performance Monitoring API - 性能监控管理接口
 * 
 * 提供性能监控、分析和优化功能
 */

import { performanceMonitor, MetricType } from '@/lib/monitoring/performance-monitor';
import { PerformanceUtils } from '@/lib/monitoring/performance-decorators';

export const runtime = "nodejs";

/**
 * GET /api/debug/performance
 * 获取性能监控数据和分析
 */
export async function GET(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const component = url.searchParams.get('component');
    const metricType = url.searchParams.get('metricType') as MetricType;

    let data: any = {};

    switch (action) {
      case 'status':
        // 获取监控状态
        data = performanceMonitor.getMonitoringStatus();
        break;
        
      case 'stats':
        // 获取性能统计
        const stats = performanceMonitor.getPerformanceStats();
        data = {
          totalStats: stats.size,
          stats: Array.from(stats.entries()).map(([key, stat]) => ({
            key,
            ...stat
          }))
        };
        break;
        
      case 'alerts':
        // 获取活跃警报
        data = {
          activeAlerts: performanceMonitor.getActiveAlerts(),
          alertsCount: performanceMonitor.getActiveAlerts().length
        };
        break;
        
      case 'report':
        // 生成性能报告
        data = await generatePerformanceReport();
        break;
        
      case 'metrics':
        // 获取特定指标
        data = await getMetricsData(component, metricType);
        break;
        
      case 'health':
        // 获取系统健康状态
        data = await getSystemHealth();
        break;
        
      default:
        // 获取完整的性能监控信息
        data = {
          status: performanceMonitor.getMonitoringStatus(),
          stats: Array.from(performanceMonitor.getPerformanceStats().entries()).map(([key, stat]) => ({
            key,
            ...stat
          })),
          activeAlerts: performanceMonitor.getActiveAlerts(),
          systemHealth: await getSystemHealth()
        };
    }

    const response = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestParams: { action, component, metricType }
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 获取性能监控信息失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'PERFORMANCE_MONITORING_ERROR',
        message: error instanceof Error ? error.message : '获取性能监控信息失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * POST /api/debug/performance
 * 性能监控管理操作
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const { action, component, metricType, value, alertId, benchmarkConfig } = await request.json();
    
    let result: any = {};
    
    switch (action) {
      case 'start':
        // 启动性能监控
        performanceMonitor.startMonitoring();
        result = {
          action: 'start',
          message: '性能监控已启动',
          status: performanceMonitor.getMonitoringStatus()
        };
        break;
        
      case 'stop':
        // 停止性能监控
        performanceMonitor.stopMonitoring();
        result = {
          action: 'stop',
          message: '性能监控已停止',
          status: performanceMonitor.getMonitoringStatus()
        };
        break;
        
      case 'recordMetric':
        // 手动记录指标
        if (!component || !metricType || value === undefined) {
          throw new Error('记录指标需要提供component、metricType和value');
        }
        
        performanceMonitor.recordMetric(
          metricType as MetricType,
          value,
          component,
          'manual_record'
        );
        
        result = {
          action: 'recordMetric',
          message: '指标已记录',
          metric: { component, metricType, value }
        };
        break;
        
      case 'resolveAlert':
        // 解决警报
        if (!alertId) {
          throw new Error('解决警报需要提供alertId');
        }
        
        const resolved = performanceMonitor.resolveAlert(alertId);
        result = {
          action: 'resolveAlert',
          alertId,
          resolved,
          message: resolved ? '警报已解决' : '警报不存在或已解决'
        };
        break;
        
      case 'benchmark':
        // 执行性能基准测试
        result = await runPerformanceBenchmark(benchmarkConfig);
        break;
        
      case 'loadTest':
        // 执行负载测试
        result = await runLoadTest();
        break;
        
      case 'memoryTest':
        // 执行内存测试
        result = await runMemoryTest();
        break;
        
      case 'optimize':
        // 触发性能优化
        result = await triggerPerformanceOptimization(component);
        break;
        
      case 'generateReport':
        // 生成详细报告
        result = await generateDetailedReport();
        break;
        
      default:
        throw new Error(`不支持的操作: ${action}`);
    }
    
    const response = {
      success: true,
      data: result,
      metadata: {
        timestamp: new Date().toISOString(),
        requestedAction: action
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 性能监控管理操作失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'PERFORMANCE_MANAGEMENT_ERROR',
        message: error instanceof Error ? error.message : '性能监控管理操作失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 生成性能报告
 */
async function generatePerformanceReport(): Promise<any> {
  try {
    const status = performanceMonitor.getMonitoringStatus();
    const stats = performanceMonitor.getPerformanceStats();
    const alerts = performanceMonitor.getActiveAlerts();
    
    return {
      reportId: `perf-report-${Date.now()}`,
      generatedAt: new Date().toISOString(),
      monitoringStatus: status,
      performanceStats: {
        totalComponents: new Set(Array.from(stats.values()).map(s => s.component)).size,
        totalMetrics: stats.size,
        averageResponseTime: calculateAverageResponseTime(stats),
        memoryUsage: getCurrentMemoryUsage(),
        cacheHitRate: calculateAverageCacheHitRate(stats)
      },
      alerts: {
        total: alerts.length,
        critical: alerts.filter(a => a.level === 'critical').length,
        warning: alerts.filter(a => a.level === 'warning').length,
        details: alerts
      },
      recommendations: generatePerformanceRecommendations(stats, alerts)
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '报告生成失败',
      generatedAt: new Date().toISOString()
    };
  }
}

/**
 * 获取指标数据
 */
async function getMetricsData(component?: string, metricType?: MetricType): Promise<any> {
  const stats = performanceMonitor.getPerformanceStats();
  let filteredStats = Array.from(stats.entries());
  
  if (component) {
    filteredStats = filteredStats.filter(([key, stat]) => stat.component === component);
  }
  
  if (metricType) {
    filteredStats = filteredStats.filter(([key, stat]) => stat.metricType === metricType);
  }
  
  return {
    totalMetrics: filteredStats.length,
    metrics: filteredStats.map(([key, stat]) => ({
      key,
      ...stat
    })),
    summary: {
      components: [...new Set(filteredStats.map(([key, stat]) => stat.component))],
      metricTypes: [...new Set(filteredStats.map(([key, stat]) => stat.metricType))]
    }
  };
}

/**
 * 获取系统健康状态
 */
async function getSystemHealth(): Promise<any> {
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  const uptime = process.uptime();
  const alerts = performanceMonitor.getActiveAlerts();
  
  // 计算健康分数
  let healthScore = 100;
  
  // 基于内存使用扣分
  const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
  if (memoryUsagePercent > 90) healthScore -= 30;
  else if (memoryUsagePercent > 80) healthScore -= 15;
  else if (memoryUsagePercent > 70) healthScore -= 5;
  
  // 基于警报扣分
  const criticalAlerts = alerts.filter(a => a.level === 'critical').length;
  const warningAlerts = alerts.filter(a => a.level === 'warning').length;
  healthScore -= criticalAlerts * 20 + warningAlerts * 5;
  
  healthScore = Math.max(0, Math.min(100, healthScore));
  
  return {
    healthScore,
    status: healthScore >= 90 ? 'EXCELLENT' : 
            healthScore >= 70 ? 'GOOD' : 
            healthScore >= 50 ? 'FAIR' : 'POOR',
    systemMetrics: {
      memoryUsage: {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        usagePercent: Math.round(memoryUsagePercent * 100) / 100
      },
      uptime: Math.round(uptime),
      processId: process.pid
    },
    alerts: {
      critical: criticalAlerts,
      warning: warningAlerts,
      total: alerts.length
    },
    recommendations: generateHealthRecommendations(healthScore, memoryUsagePercent, alerts)
  };
}

/**
 * 运行性能基准测试
 */
async function runPerformanceBenchmark(config: any = {}): Promise<any> {
  try {
    const {
      testName = 'API响应测试',
      iterations = 50,
      concurrency = 1
    } = config;
    
    // 模拟API调用测试
    const testFunction = async () => {
      const delay = Math.random() * 100 + 50; // 50-150ms随机延迟
      await new Promise(resolve => setTimeout(resolve, delay));
      return { success: true, delay };
    };
    
    const benchmark = await PerformanceUtils.createBenchmark(
      testName,
      testFunction,
      { iterations, concurrency, component: 'Benchmark' }
    );
    
    return {
      benchmark,
      message: '基准测试完成',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '基准测试失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 运行负载测试
 */
async function runLoadTest(): Promise<any> {
  try {
    console.log('🚀 开始负载测试...');
    
    const testFunction = async () => {
      // 模拟负载测试
      const delay = Math.random() * 200 + 100;
      await new Promise(resolve => setTimeout(resolve, delay));
      return { processed: true };
    };
    
    const results = await PerformanceUtils.concurrentPerformanceTest(
      testFunction,
      20, // 20个并发请求
      'LoadTest',
      'concurrent_load'
    );
    
    return {
      loadTest: {
        concurrency: 20,
        totalRequests: 20,
        successfulRequests: results.successCount,
        failedRequests: results.errorCount,
        averageResponseTime: results.averageTime,
        totalTime: results.totalTime,
        throughput: (results.successCount / results.totalTime) * 1000
      },
      message: '负载测试完成',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '负载测试失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 运行内存测试
 */
async function runMemoryTest(): Promise<any> {
  try {
    console.log('🧠 开始内存测试...');
    
    const initialMemory = process.memoryUsage();
    
    // 模拟内存密集操作
    const testFunction = async () => {
      const largeArray = new Array(100000).fill(0).map((_, i) => ({ id: i, data: Math.random() }));
      return largeArray.length;
    };
    
    const { result, memoryDelta } = await PerformanceUtils.measureMemory(
      testFunction,
      'MemoryTest',
      'memory_intensive'
    );
    
    const finalMemory = process.memoryUsage();
    
    return {
      memoryTest: {
        initialMemory: {
          heapUsed: Math.round(initialMemory.heapUsed / 1024 / 1024),
          heapTotal: Math.round(initialMemory.heapTotal / 1024 / 1024)
        },
        finalMemory: {
          heapUsed: Math.round(finalMemory.heapUsed / 1024 / 1024),
          heapTotal: Math.round(finalMemory.heapTotal / 1024 / 1024)
        },
        memoryDelta: Math.round(memoryDelta / 1024 / 1024),
        processedItems: result
      },
      message: '内存测试完成',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '内存测试失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 触发性能优化
 */
async function triggerPerformanceOptimization(component?: string): Promise<any> {
  try {
    console.log(`🔧 触发性能优化${component ? ` (${component})` : ''}...`);
    
    const optimizations: string[] = [];
    
    // 触发垃圾回收
    if (global.gc) {
      global.gc();
      optimizations.push('垃圾回收');
    }
    
    // 记录优化操作
    performanceMonitor.recordMetric(
      MetricType.RESPONSE_TIME,
      0,
      component || 'System',
      'optimization_trigger',
      { optimizations }
    );
    
    return {
      optimization: {
        component: component || 'System',
        optimizations,
        timestamp: new Date().toISOString()
      },
      message: '性能优化已触发',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '性能优化失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 生成详细报告
 */
async function generateDetailedReport(): Promise<any> {
  try {
    const basicReport = await generatePerformanceReport();
    const systemHealth = await getSystemHealth();
    const benchmarkResult = await runPerformanceBenchmark({ testName: '系统基准测试', iterations: 10 });
    
    return {
      detailedReport: {
        ...basicReport,
        systemHealth,
        benchmark: benchmarkResult.benchmark,
        analysis: {
          performanceTrend: 'stable', // 这里可以实现真实的趋势分析
          bottlenecks: identifyBottlenecks(),
          optimizationOpportunities: identifyOptimizationOpportunities()
        }
      },
      message: '详细报告生成完成',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '详细报告生成失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 辅助函数
 */
function calculateAverageResponseTime(stats: Map<string, any>): number {
  const responseTimeStats = Array.from(stats.values()).filter(s => s.metricType === MetricType.RESPONSE_TIME);
  if (responseTimeStats.length === 0) return 0;
  return responseTimeStats.reduce((sum, s) => sum + s.average, 0) / responseTimeStats.length;
}

function getCurrentMemoryUsage(): number {
  const memoryUsage = process.memoryUsage();
  return Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100 * 100) / 100;
}

function calculateAverageCacheHitRate(stats: Map<string, any>): number {
  const cacheStats = Array.from(stats.values()).filter(s => s.metricType === MetricType.CACHE_HIT_RATE);
  if (cacheStats.length === 0) return 0;
  return cacheStats.reduce((sum, s) => sum + s.average, 0) / cacheStats.length;
}

function generatePerformanceRecommendations(stats: Map<string, any>, alerts: any[]): string[] {
  const recommendations: string[] = [];
  
  const avgResponseTime = calculateAverageResponseTime(stats);
  if (avgResponseTime > 2000) {
    recommendations.push('API响应时间较长，建议优化数据库查询和缓存策略');
  }
  
  const memoryUsage = getCurrentMemoryUsage();
  if (memoryUsage > 80) {
    recommendations.push('内存使用率较高，建议检查内存泄漏和优化数据结构');
  }
  
  const criticalAlerts = alerts.filter(a => a.level === 'critical').length;
  if (criticalAlerts > 0) {
    recommendations.push(`发现 ${criticalAlerts} 个严重警报，建议立即处理`);
  }
  
  if (recommendations.length === 0) {
    recommendations.push('系统性能表现良好，继续保持');
  }
  
  return recommendations;
}

function generateHealthRecommendations(healthScore: number, memoryUsage: number, alerts: any[]): string[] {
  const recommendations: string[] = [];
  
  if (healthScore < 50) {
    recommendations.push('系统健康状态较差，建议立即检查和优化');
  } else if (healthScore < 70) {
    recommendations.push('系统健康状态一般，建议进行性能优化');
  }
  
  if (memoryUsage > 80) {
    recommendations.push('内存使用率过高，建议释放内存或增加内存容量');
  }
  
  if (alerts.length > 5) {
    recommendations.push('活跃警报较多，建议优先处理关键问题');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('系统健康状态良好');
  }
  
  return recommendations;
}

function identifyBottlenecks(): string[] {
  return [
    '数据库查询优化机会',
    '缓存策略改进空间',
    'API响应时间优化潜力'
  ];
}

function identifyOptimizationOpportunities(): string[] {
  return [
    '启用更多缓存层',
    '优化数据库索引',
    '实施连接池管理',
    '启用压缩传输'
  ];
}
