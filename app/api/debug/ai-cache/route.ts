/**
 * AI Cache Management API - AI缓存管理接口
 * 
 * 提供AI提供商缓存的管理和监控功能
 */

import { cachedAIFactory } from '@/lib/ai/cached-ai-factory';
import { aiProviderCacheLayer } from '@/lib/optimization/ai-provider-cache-layer';

export const runtime = "nodejs";

/**
 * GET /api/debug/ai-cache
 * 获取AI缓存统计信息
 */
export async function GET(): Promise<Response> {
  try {
    const stats = cachedAIFactory.getCacheStats();
    const factoryStatus = cachedAIFactory.getStatus();
    
    const response = {
      success: true,
      data: {
        cacheStats: stats,
        factoryStatus,
        cacheLayer: {
          enabled: factoryStatus.cacheEnabled,
          memoryUsage: `${stats.memoryUsageMB} MB`,
          efficiency: {
            hitRate: `${(stats.hitRate * 100).toFixed(1)}%`,
            totalRequests: stats.hitCount + stats.missCount,
            cacheHits: stats.hitCount,
            cacheMisses: stats.missCount
          },
          performance: {
            averageResponseSize: `${stats.averageResponseSize} bytes`,
            totalCachedItems: stats.totalItems,
            topModels: stats.topModels
          },
          maintenance: {
            lastCleanupTime: stats.lastCleanupTime,
            autoCleanupEnabled: true
          }
        },
        recommendations: generateRecommendations(stats)
      },
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 获取AI缓存统计失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'AI_CACHE_STATS_ERROR',
        message: error instanceof Error ? error.message : '获取缓存统计失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * POST /api/debug/ai-cache
 * 管理AI缓存（清空、配置等）
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const { action, config } = await request.json();
    
    let result: any = {};
    
    switch (action) {
      case 'clear':
        cachedAIFactory.clearCache();
        result = {
          action: 'clear',
          message: 'AI缓存已清空',
          timestamp: new Date().toISOString()
        };
        break;
        
      case 'enable':
        cachedAIFactory.setCacheEnabled(true);
        result = {
          action: 'enable',
          message: 'AI缓存已启用',
          enabled: true
        };
        break;
        
      case 'disable':
        cachedAIFactory.setCacheEnabled(false);
        result = {
          action: 'disable',
          message: 'AI缓存已禁用',
          enabled: false
        };
        break;
        
      case 'test':
        // 测试缓存功能
        const testResult = await testCachePerformance();
        result = {
          action: 'test',
          message: '缓存性能测试完成',
          testResult
        };
        break;
        
      default:
        return new Response(JSON.stringify({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: `不支持的操作: ${action}`,
            supportedActions: ['clear', 'enable', 'disable', 'test']
          }
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
    }
    
    const response = {
      success: true,
      data: result,
      metadata: {
        timestamp: new Date().toISOString(),
        requestedAction: action
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ AI缓存管理操作失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'AI_CACHE_MANAGEMENT_ERROR',
        message: error instanceof Error ? error.message : '缓存管理操作失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 生成缓存优化建议
 */
function generateRecommendations(stats: any): string[] {
  const recommendations: string[] = [];
  
  // 命中率建议
  if (stats.hitRate < 0.3) {
    recommendations.push('缓存命中率较低，建议检查缓存键生成策略');
  } else if (stats.hitRate > 0.8) {
    recommendations.push('缓存命中率很高，系统运行良好');
  }
  
  // 内存使用建议
  if (stats.memoryUsageMB > 80) {
    recommendations.push('内存使用较高，建议调整缓存大小或TTL设置');
  } else if (stats.memoryUsageMB < 10) {
    recommendations.push('内存使用较低，可以考虑增加缓存大小以提高命中率');
  }
  
  // 响应大小建议
  if (stats.averageResponseSize > 10000) {
    recommendations.push('平均响应较大，建议启用响应压缩');
  }
  
  // 缓存项数量建议
  if (stats.totalItems < 10) {
    recommendations.push('缓存项较少，系统可能刚启动或使用率较低');
  } else if (stats.totalItems > 800) {
    recommendations.push('缓存项较多，建议监控内存使用情况');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('缓存系统运行正常，无需特别优化');
  }
  
  return recommendations;
}

/**
 * 测试缓存性能
 */
async function testCachePerformance(): Promise<any> {
  try {
    const testPrompt = "这是一个缓存性能测试提示";
    const provider = await cachedAIFactory.getDefaultProvider();
    
    // 第一次调用（应该是缓存未命中）
    const startTime1 = Date.now();
    const result1 = await provider.generateText(testPrompt, { temperature: 0.7 });
    const time1 = Date.now() - startTime1;
    
    // 第二次调用（应该是缓存命中）
    const startTime2 = Date.now();
    const result2 = await provider.generateText(testPrompt, { temperature: 0.7 });
    const time2 = Date.now() - startTime2;
    
    return {
      firstCall: {
        time: time1,
        cached: result1.usage?.cached || false,
        responseLength: result1.text.length
      },
      secondCall: {
        time: time2,
        cached: result2.usage?.cached || false,
        responseLength: result2.text.length
      },
      speedImprovement: time1 > 0 ? `${((time1 - time2) / time1 * 100).toFixed(1)}%` : '0%',
      cacheWorking: (result1.usage?.cached === false) && (result2.usage?.cached === true)
    };
    
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '测试失败',
      success: false
    };
  }
}
