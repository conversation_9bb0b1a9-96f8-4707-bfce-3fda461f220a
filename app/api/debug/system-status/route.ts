import { conversationManager } from '@/lib/services/conversation';
import { readMemoryFile, MEMORY_FILES } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";

// GET - 获取系统状态诊断信息
export async function GET() {
  try {
    console.log('🔍 开始系统状态诊断...');
    
    // 1. 检查对话管理器状态
    const conversationState = conversationManager.getState();
    const messages = conversationManager.getMessages();
    
    // 2. 检查每日洞察文件状态
    const dailyInsightHot = await readMemoryFile(MEMORY_FILES.DAILY_INSIGHT_HOT);
    const dailyInsightCold = await readMemoryFile(MEMORY_FILES.DAILY_INSIGHT_COLD);
    const dialogueHistory = await readMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY);
    
    // 3. 分析对话轮数
    const dialogueBlocks = (conversationManager as any).dialogueBlocks || [];
    const contextWindow = conversationState.contextWindow;
    
    // 4. 分析对话历史结构
    const dialogueLines = dialogueHistory.split('\n').filter(line => line.trim());
    const userMessages = dialogueLines.filter(line => line.includes('**用户**:'));
    const assistantMessages = dialogueLines.filter(line => line.includes('**小镜**:'));
    
    // 5. 检查触发条件
    const shouldTriggerInsight = dialogueBlocks.length >= 6 && dialogueBlocks.length % 6 === 0;
    const shouldTriggerRefinement = dialogueBlocks.length >= 30 && dialogueBlocks.length % 30 === 0;
    
    const diagnosticInfo = {
      timestamp: new Date().toISOString(),
      
      // 对话管理器状态
      conversationManager: {
        totalMessages: messages.length,
        contextWindowSize: contextWindow.length,
        currentMode: conversationState.currentMode,
        isProcessing: conversationState.isProcessing,
        dialogueBlocksCount: dialogueBlocks.length,
        isRefining: (conversationManager as any).isRefining || false
      },
      
      // 触发条件检查
      triggers: {
        shouldTriggerInsight,
        shouldTriggerRefinement,
        nextInsightAt: Math.ceil(dialogueBlocks.length / 6) * 6,
        nextRefinementAt: Math.ceil(dialogueBlocks.length / 30) * 30
      },
      
      // 记忆文件状态
      memoryFiles: {
        dailyInsightHot: {
          exists: !!dailyInsightHot,
          length: dailyInsightHot.length,
          lastModified: dailyInsightHot ? 'Unknown' : 'N/A',
          preview: dailyInsightHot.slice(-200) || '空文件'
        },
        dailyInsightCold: {
          exists: !!dailyInsightCold,
          length: dailyInsightCold.length,
          preview: dailyInsightCold.slice(-200) || '空文件'
        },
        dialogueHistory: {
          exists: !!dialogueHistory,
          length: dialogueHistory.length,
          totalLines: dialogueLines.length,
          userMessageCount: userMessages.length,
          assistantMessageCount: assistantMessages.length,
          preview: dialogueHistory.slice(-300) || '空文件'
        }
      },
      
      // 上下文管理分析
      contextAnalysis: {
        messagesInMemory: messages.length,
        contextWindowMessages: contextWindow.length,
        expectedWindowSize: (conversationManager as any).isRefining ? 8 : 6,
        oldestMessageInContext: contextWindow.length > 0 ? contextWindow[0].timestamp : null,
        newestMessageInContext: contextWindow.length > 0 ? contextWindow[contextWindow.length - 1].timestamp : null
      },
      
      // 问题诊断
      issues: [] as Array<{
        type: string;
        severity: string;
        message: string;
        suggestion: string;
      }>
    };
    
    // 诊断问题
    const issues = [];
    
    // 检查每日洞察触发问题
    if (dialogueBlocks.length >= 6 && !shouldTriggerInsight) {
      issues.push({
        type: 'trigger_logic',
        severity: 'high',
        message: `对话块数量为 ${dialogueBlocks.length}，但每日洞察未触发`,
        suggestion: '检查 shouldTriggerDailyInsight() 方法的逻辑'
      });
    }
    
    // 检查热日志更新问题
    if (dialogueBlocks.length >= 6 && dailyInsightHot.length === 0) {
      issues.push({
        type: 'hot_log',
        severity: 'high',
        message: '每日洞察热日志为空，但已有足够对话触发洞察',
        suggestion: '检查 processDailyInsightOutput() 方法和文件写入权限'
      });
    }
    
    // 检查上下文滚动问题
    if (messages.length > 6 && contextWindow.length !== Math.min(messages.length, 6)) {
      issues.push({
        type: 'context_window',
        severity: 'medium',
        message: `消息总数 ${messages.length}，但上下文窗口大小为 ${contextWindow.length}`,
        suggestion: '检查 addMessage() 和 buildContextWindow() 方法'
      });
    }
    
    // 检查对话历史记录问题
    if (messages.length > 0 && userMessages.length === 0) {
      issues.push({
        type: 'dialogue_history',
        severity: 'medium',
        message: '内存中有消息但对话历史文件中没有记录',
        suggestion: '检查 saveConversationToHistory() 方法'
      });
    }
    
    diagnosticInfo.issues = issues;
    
    return new Response(JSON.stringify(diagnosticInfo, null, 2), {
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
    
  } catch (error) {
    console.error('系统状态诊断失败:', error);
    return new Response(JSON.stringify({ 
      error: '系统状态诊断失败',
      details: error instanceof Error ? error.message : String(error)
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
