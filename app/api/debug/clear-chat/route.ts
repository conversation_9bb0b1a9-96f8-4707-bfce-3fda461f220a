import { writeMemoryFile } from '@/lib/storage/memory-manager';
import { MEMORY_FILES } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";

// POST - 清空对话历史
export async function POST() {
  try {
    // 清空对话历史文件
    await writeMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY, '');
    
    console.log('✅ 对话历史已清空');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: '对话历史已清空' 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('清空对话历史失败:', error);
    return new Response('清空失败', { status: 500 });
  }
}
