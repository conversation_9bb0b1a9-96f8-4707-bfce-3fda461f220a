/**
 * Error Management API - 错误管理接口
 * 
 * 提供错误统计、监控、恢复等管理功能
 */

import { unifiedErrorHandler } from '@/lib/error/unified-error-handler';
import { ErrorFactory } from '@/lib/error/error-types';

export const runtime = "nodejs";

/**
 * GET /api/debug/errors
 * 获取错误统计和监控信息
 */
export async function GET(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const stats = url.searchParams.get('stats') === 'true';
    const circuitBreakers = url.searchParams.get('circuitBreakers') === 'true';

    let data: any = {};

    if (stats) {
      // 返回错误统计信息
      data.errorStats = unifiedErrorHandler.getStats();
    }

    if (circuitBreakers) {
      // 返回熔断器状态
      data.circuitBreakers = unifiedErrorHandler.getCircuitBreakers();
    }

    if (!stats && !circuitBreakers) {
      // 返回完整的错误监控信息
      data = {
        errorStats: unifiedErrorHandler.getStats(),
        circuitBreakers: unifiedErrorHandler.getCircuitBreakers(),
        systemHealth: {
          totalErrors: unifiedErrorHandler.getStats().totalErrors,
          criticalErrors: unifiedErrorHandler.getStats().errorsBySeverity.CRITICAL || 0,
          recoveryRate: calculateRecoveryRate(unifiedErrorHandler.getStats()),
          systemStatus: determineSystemStatus(unifiedErrorHandler.getStats())
        },
        recommendations: generateErrorRecommendations(unifiedErrorHandler.getStats())
      };
    }

    const response = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestParams: {
          type,
          stats,
          circuitBreakers
        }
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 获取错误信息失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'ERROR_STATS_ERROR',
        message: error instanceof Error ? error.message : '获取错误统计失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * POST /api/debug/errors
 * 错误管理操作
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const { action, errorType, component, operation, message, testData } = await request.json();
    
    let result: any = {};
    
    switch (action) {
      case 'reset':
        // 重置错误统计
        unifiedErrorHandler.resetStats();
        result = {
          action: 'reset',
          message: '错误统计已重置',
          timestamp: new Date().toISOString()
        };
        break;
        
      case 'resetCircuitBreakers':
        // 重置熔断器
        unifiedErrorHandler.resetCircuitBreakers();
        result = {
          action: 'resetCircuitBreakers',
          message: '所有熔断器已重置',
          timestamp: new Date().toISOString()
        };
        break;
        
      case 'testError':
        // 测试错误处理
        if (!errorType || !component || !operation) {
          throw new Error('测试错误需要提供errorType、component和operation');
        }
        
        const testError = await testErrorHandling(errorType, component, operation, message, testData);
        result = {
          action: 'testError',
          errorType,
          component,
          operation,
          testResult: testError,
          message: '错误处理测试完成'
        };
        break;
        
      case 'simulateRecovery':
        // 模拟错误恢复
        if (!component || !operation) {
          throw new Error('模拟恢复需要提供component和operation');
        }
        
        const recoveryResult = await simulateErrorRecovery(component, operation);
        result = {
          action: 'simulateRecovery',
          component,
          operation,
          recoveryResult,
          message: '错误恢复模拟完成'
        };
        break;
        
      case 'checkCircuitBreaker':
        // 检查熔断器状态
        if (!component || !operation) {
          throw new Error('检查熔断器需要提供component和operation');
        }
        
        const isOpen = unifiedErrorHandler.isCircuitBreakerOpen(component, operation);
        result = {
          action: 'checkCircuitBreaker',
          component,
          operation,
          isOpen,
          message: `熔断器状态: ${isOpen ? '开启' : '关闭'}`
        };
        break;
        
      default:
        throw new Error(`不支持的操作: ${action}`);
    }
    
    const response = {
      success: true,
      data: result,
      metadata: {
        timestamp: new Date().toISOString(),
        requestedAction: action
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 错误管理操作失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'ERROR_MANAGEMENT_ERROR',
        message: error instanceof Error ? error.message : '错误管理操作失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 计算恢复率
 */
function calculateRecoveryRate(stats: any): number {
  const totalRecoveryAttempts = stats.recoveryAttempts;
  if (totalRecoveryAttempts === 0) return 0;
  
  return Math.round((stats.successfulRecoveries / totalRecoveryAttempts) * 100);
}

/**
 * 确定系统状态
 */
function determineSystemStatus(stats: any): string {
  const criticalErrors = stats.errorsBySeverity.CRITICAL || 0;
  const highErrors = stats.errorsBySeverity.HIGH || 0;
  const totalErrors = stats.totalErrors;
  const recoveryRate = calculateRecoveryRate(stats);

  if (criticalErrors > 0) {
    return 'CRITICAL';
  } else if (highErrors > 5) {
    return 'WARNING';
  } else if (totalErrors > 50 && recoveryRate < 50) {
    return 'DEGRADED';
  } else if (totalErrors === 0) {
    return 'HEALTHY';
  } else {
    return 'STABLE';
  }
}

/**
 * 生成错误处理建议
 */
function generateErrorRecommendations(stats: any): string[] {
  const recommendations: string[] = [];
  const recoveryRate = calculateRecoveryRate(stats);
  const criticalErrors = stats.errorsBySeverity.CRITICAL || 0;
  const highErrors = stats.errorsBySeverity.HIGH || 0;

  // 恢复率建议
  if (recoveryRate < 30) {
    recommendations.push('错误恢复率较低，建议检查恢复策略配置');
  } else if (recoveryRate > 80) {
    recommendations.push('错误恢复率良好，系统自愈能力强');
  }

  // 严重错误建议
  if (criticalErrors > 0) {
    recommendations.push('存在严重错误，需要立即处理');
  }

  if (highErrors > 10) {
    recommendations.push('高级别错误较多，建议检查核心组件');
  }

  // AI提供商错误建议
  const aiErrors = stats.errorsByType.AI_PROVIDER_ERROR || 0;
  if (aiErrors > 5) {
    recommendations.push('AI提供商错误较多，建议检查API配置和网络连接');
  }

  // 缓存错误建议
  const cacheErrors = stats.errorsByType.CACHE_ERROR || 0;
  if (cacheErrors > 10) {
    recommendations.push('缓存错误较多，建议检查缓存配置和内存使用');
  }

  // 网络错误建议
  const networkErrors = stats.errorsByType.NETWORK_ERROR || 0;
  if (networkErrors > 5) {
    recommendations.push('网络错误较多，建议检查网络连接和超时配置');
  }

  if (recommendations.length === 0) {
    recommendations.push('错误处理系统运行正常，无需特别关注');
  }

  return recommendations;
}

/**
 * 测试错误处理
 */
async function testErrorHandling(
  errorType: string,
  component: string,
  operation: string,
  message?: string,
  testData?: any
): Promise<any> {
  try {
    let testError: any;
    const testMessage = message || `测试${errorType}错误`;

    // 根据错误类型创建测试错误
    switch (errorType) {
      case 'API_ERROR':
        testError = ErrorFactory.createAPIError(testMessage, {
          component,
          operation,
          statusCode: 500,
          metadata: testData
        });
        break;
        
      case 'AI_PROVIDER_ERROR':
        testError = ErrorFactory.createAIProviderError(testMessage, 'test-provider', {
          component,
          operation,
          model: 'test-model',
          prompt: 'test prompt'
        });
        break;
        
      case 'CACHE_ERROR':
        testError = ErrorFactory.createCacheError(testMessage, 'test-cache', {
          component,
          operation,
          cacheKey: 'test-key'
        });
        break;
        
      case 'NETWORK_ERROR':
        testError = ErrorFactory.createNetworkError(testMessage, {
          component,
          operation,
          url: 'https://test.example.com',
          timeout: 5000
        });
        break;
        
      default:
        throw new Error(`不支持的错误类型: ${errorType}`);
    }

    // 处理测试错误
    const handledError = await unifiedErrorHandler.handleError(testError, {
      component,
      operation,
      metadata: { test: true, ...testData }
    });

    return {
      errorId: handledError.id,
      errorType: handledError.type,
      severity: handledError.severity,
      recoveryStrategy: handledError.recoveryStrategy,
      processed: true
    };

  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '测试失败',
      processed: false
    };
  }
}

/**
 * 模拟错误恢复
 */
async function simulateErrorRecovery(component: string, operation: string): Promise<any> {
  try {
    // 创建一个需要恢复的测试错误
    const testError = ErrorFactory.createAIProviderError(
      '模拟AI提供商错误用于恢复测试',
      'test-provider',
      { component, operation }
    );

    // 处理错误并尝试恢复
    const handledError = await unifiedErrorHandler.handleError(testError, {
      component,
      operation,
      metadata: { simulation: true }
    });

    return {
      errorId: handledError.id,
      recoveryStrategy: handledError.recoveryStrategy,
      recoveryAttempted: true,
      success: true
    };

  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '恢复模拟失败',
      success: false
    };
  }
}
