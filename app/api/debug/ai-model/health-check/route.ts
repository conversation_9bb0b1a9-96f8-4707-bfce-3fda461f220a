/**
 * AI 模型健康检查 API
 * 检查指定 AI 模型的健康状态和性能指标
 */

import { NextRequest, NextResponse } from 'next/server'
import { AIFactory } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const { provider } = await request.json()

    // 验证提供商
    if (!provider || !['gemini', 'doubao'].includes(provider)) {
      return NextResponse.json(
        { error: '无效的 AI 提供商' },
        { status: 400 }
      )
    }

    console.log(`🔍 执行 ${provider} 健康检查...`)

    const startTime = Date.now()

    try {
      // 使用AI工厂进行健康检查
      const factory = AIFactory.getInstance()
      const result = await factory.checkProviderHealth(provider)

      const endTime = Date.now()
      const latency = endTime - startTime

      console.log(`✅ ${provider} 健康检查完成:`, {
        isHealthy: result.isHealthy,
        latency: `${latency}ms`
      })

      return NextResponse.json({
        provider: result.provider,
        isHealthy: result.isHealthy,
        latency: result.latency || latency,
        timestamp: new Date().toISOString(),
        error: result.error,
        metadata: {
          factoryVersion: '2.0',
          timestamp: new Date().toISOString()
        }
      })

    } catch (error) {
      const endTime = Date.now()
      const latency = endTime - startTime

      console.error(`❌ ${provider} 健康检查失败:`, error)

      return NextResponse.json({
        provider,
        isHealthy: false,
        latency,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '健康检查失败'
      })
    }

  } catch (error) {
    console.error('❌ 健康检查 API 错误:', error)
    return NextResponse.json(
      { error: '健康检查失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
