/**
 * AI 模型切换 API
 * 支持在运行时动态切换 AI 模型提供商
 */

import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { provider } = await request.json()

    // 验证提供商
    if (!provider || !['gemini', 'doubao'].includes(provider)) {
      return NextResponse.json(
        { error: '无效的 AI 提供商' },
        { status: 400 }
      )
    }

    console.log(`🔄 切换 AI 模型到: ${provider}`)

    // 模拟切换逻辑（实际项目中应该更新配置）
    console.log(`✅ AI 模型已切换到: ${provider}`)

    return NextResponse.json({
      success: true,
      provider,
      message: `AI 模型已切换到 ${provider}`
    })

  } catch (error) {
    console.error('❌ AI 模型切换失败:', error)
    return NextResponse.json(
      { error: '模型切换失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
