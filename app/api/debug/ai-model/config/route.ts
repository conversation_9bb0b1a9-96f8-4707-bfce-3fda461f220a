/**
 * AI 模型配置管理 API
 * 支持获取和更新 AI 模型配置
 */

import { NextRequest, NextResponse } from 'next/server'
import { getGlobalConfigManager } from '../../../../../lib/config/ConfigManager'

export async function GET() {
  try {
    console.log('📋 获取 AI 模型配置...')

    // 获取配置管理器
    const configManager = getGlobalConfigManager()
    const config = configManager.getConfig()

    // 提取 AI 相关配置
    const aiConfig = config.ai

    // 构建返回的配置格式
    const configs = {
      gemini: {
        provider: 'gemini',
        apiKey: aiConfig.models.gemini?.apiKey || '',
        model: aiConfig.models.gemini?.model || 'gemini-2.5-flash-preview-05-20',
        proxyUrl: aiConfig.models.gemini?.proxyUrl || '',
        thinkingBudget: aiConfig.models.gemini?.thinkingBudget || 0,
        temperature: 0.7,
        maxTokens: 500,
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0,
        timeout: aiConfig.retry.retryDelay * aiConfig.retry.maxRetries || 30000,
        retries: aiConfig.retry.maxRetries || 3,
        cacheEnabled: aiConfig.cache.enabled,
        healthCheckEnabled: aiConfig.healthCheck.enabled,
        healthCheckInterval: aiConfig.healthCheck.interval
      },
      doubao: {
        provider: 'doubao',
        apiKey: aiConfig.models.doubao?.apiKey || '',
        model: aiConfig.models.doubao?.model || 'doubao-pro-4k',
        baseUrl: aiConfig.models.doubao?.baseUrl || 'https://ark.cn-beijing.volces.com/api/v3',
        temperature: 0.7,
        maxTokens: 500,
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0,
        timeout: aiConfig.retry.retryDelay * aiConfig.retry.maxRetries || 30000,
        retries: aiConfig.retry.maxRetries || 3,
        cacheEnabled: aiConfig.cache.enabled,
        healthCheckEnabled: aiConfig.healthCheck.enabled,
        healthCheckInterval: aiConfig.healthCheck.interval
      }
    }

    console.log('✅ AI 模型配置获取成功')

    return NextResponse.json({
      configs,
      currentProvider: aiConfig.defaultProvider,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ 获取 AI 模型配置失败:', error)
    return NextResponse.json(
      { error: '获取配置失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { configs } = await request.json()

    if (!configs) {
      return NextResponse.json(
        { error: '缺少配置数据' },
        { status: 400 }
      )
    }

    console.log('💾 保存 AI 模型配置...')

    // 获取配置管理器
    const configManager = getGlobalConfigManager()
    const currentConfig = configManager.getConfig()

    // 构建新的 AI 配置
    const newAIConfig = {
      ...currentConfig.ai,
      models: {
        ...currentConfig.ai.models,
        gemini: {
          ...currentConfig.ai.models.gemini,
          apiKey: configs.gemini?.apiKey || '',
          model: configs.gemini?.model || 'gemini-2.5-flash-preview-05-20',
          proxyUrl: configs.gemini?.proxyUrl || undefined,
          thinkingBudget: configs.gemini?.thinkingBudget || 0
        },
        doubao: {
          ...currentConfig.ai.models.doubao,
          apiKey: configs.doubao?.apiKey || '',
          model: configs.doubao?.model || 'doubao-pro-4k',
          baseUrl: configs.doubao?.baseUrl || 'https://ark.cn-beijing.volces.com/api/v3'
        }
      },
      cache: {
        ...currentConfig.ai.cache,
        enabled: configs.gemini?.cacheEnabled ?? configs.doubao?.cacheEnabled ?? true
      },
      healthCheck: {
        ...currentConfig.ai.healthCheck,
        enabled: configs.gemini?.healthCheckEnabled ?? configs.doubao?.healthCheckEnabled ?? true,
        interval: configs.gemini?.healthCheckInterval || configs.doubao?.healthCheckInterval || 60000
      },
      retry: {
        ...currentConfig.ai.retry,
        maxRetries: configs.gemini?.retries || configs.doubao?.retries || 3
      }
    }

    // 更新配置
    await configManager.updateConfig({
      ai: newAIConfig
    })

    console.log('✅ AI 模型配置保存成功')

    return NextResponse.json({
      success: true,
      message: 'AI 模型配置已保存',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ 保存 AI 模型配置失败:', error)
    return NextResponse.json(
      { error: '保存配置失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { provider, config } = await request.json()

    if (!provider || !config) {
      return NextResponse.json(
        { error: '缺少提供商或配置数据' },
        { status: 400 }
      )
    }

    console.log(`🔧 更新 ${provider} 配置...`)

    // 获取配置管理器
    const configManager = getGlobalConfigManager()
    const currentConfig = configManager.getConfig()

    // 更新指定提供商的配置
    const newAIConfig = {
      ...currentConfig.ai,
      models: {
        ...currentConfig.ai.models,
        [provider]: {
          ...(currentConfig.ai.models[provider as keyof typeof currentConfig.ai.models] || {}),
          ...config
        }
      }
    }

    // 更新配置
    await configManager.updateConfig({
      ai: newAIConfig
    })

    console.log(`✅ ${provider} 配置更新成功`)

    return NextResponse.json({
      success: true,
      message: `${provider} 配置已更新`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error(`❌ 更新配置失败:`, error)
    return NextResponse.json(
      { error: '更新配置失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
