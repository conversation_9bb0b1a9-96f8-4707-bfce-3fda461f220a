/**
 * AI 模型连接测试 API
 * 测试指定 AI 模型的连接状态和基本功能
 */

import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { provider } = await request.json()

    if (!provider || !['gemini', 'doubao'].includes(provider)) {
      return NextResponse.json(
        { error: '无效的提供商' },
        { status: 400 }
      )
    }

    console.log(`🔍 测试 ${provider} 连接...`)

    // 简化的连接测试 - 只检查基本可达性
    try {
      const isConnected = await simpleConnectionTest(provider)

      console.log(`${isConnected ? '✅' : '❌'} ${provider} 连接测试${isConnected ? '成功' : '失败'}`)

      return NextResponse.json({
        success: isConnected,
        provider,
        message: isConnected ? '连接正常' : '连接失败',
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      console.error(`❌ ${provider} 连接测试失败:`, error)

      return NextResponse.json({
        success: false,
        provider,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      })
    }

  } catch (error) {
    console.error('❌ 连接测试 API 错误:', error)
    return NextResponse.json(
      { error: '连接测试失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

/**
 * 简化的连接测试
 */
async function simpleConnectionTest(provider: string): Promise<boolean> {
  try {
    // 模拟简单的连接测试
    // 实际项目中可以发送一个最小的测试请求

    if (provider === 'gemini') {
      // 简单检查 Gemini API 可达性
      const response = await fetch('https://generativelanguage.googleapis.com', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000) // 5秒超时
      })
      return response.status < 500
    } else if (provider === 'doubao') {
      // 简单检查豆包 API 可达性
      const response = await fetch('https://ark.cn-beijing.volces.com', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000) // 5秒超时
      })
      return response.status < 500
    }

    return false
  } catch (error) {
    console.error(`连接测试失败:`, error)
    return false
  }
}
