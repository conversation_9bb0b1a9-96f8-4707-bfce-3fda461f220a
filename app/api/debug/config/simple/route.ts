/**
 * 简化的配置管理 API
 * 只处理核心配置项，移除复杂的验证和历史功能
 */

import { NextRequest, NextResponse } from 'next/server'

export async function GET() {
  try {
    console.log('📋 获取简化配置...')

    // 返回默认的扩展配置
    const simpleConfig = {
      app: {
        name: 'SelfMirror',
        environment: 'development',
        debug: true,
        locale: 'zh-CN'
      },
      ai: {
        defaultProvider: 'gemini',
        providers: {
          gemini: {
            apiKey: '',
            proxyUrl: '',
            models: {
              default: 'gemini-2.5-flash-preview-05-20',
              chat: 'gemini-2.5-flash-preview-05-20',
              meaning: 'gemini-2.5-flash-preview-05-20',
              prompt: 'gemini-1.5-pro',
              insight: 'gemini-2.5-flash-preview-05-20'
            }
          },
          doubao: {
            apiKey: '',
            baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
            models: {
              default: 'doubao-pro-4k',
              chat: 'doubao-pro-4k',
              meaning: 'doubao-pro-4k',
              prompt: 'doubao-pro-32k',
              insight: 'doubao-pro-4k'
            }
          }
        }
      },
      storage: {
        type: 'filesystem',
        path: './data',
        maxSize: 1024 * 1024 * 100, // 100MB
        autoBackup: true
      }
    }

    console.log('✅ 简化配置获取成功')

    return NextResponse.json({
      config: simpleConfig,
      lastUpdated: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ 获取简化配置失败:', error)
    return NextResponse.json(
      { error: '获取配置失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { config } = await request.json()

    if (!config) {
      return NextResponse.json(
        { error: '缺少配置数据' },
        { status: 400 }
      )
    }

    console.log('💾 保存扩展配置...', JSON.stringify(config, null, 2))

    // 验证配置结构
    if (config.ai?.providers) {
      console.log('📋 AI 提供商配置:')
      Object.entries(config.ai.providers).forEach(([provider, providerConfig]: [string, any]) => {
        console.log(`  ${provider}:`)
        console.log(`    API密钥: ${providerConfig.apiKey ? '已配置' : '未配置'}`)
        if (providerConfig.models) {
          console.log(`    场景模型: ${Object.keys(providerConfig.models).length} 个`)
          Object.entries(providerConfig.models).forEach(([scenario, model]) => {
            console.log(`      ${scenario}: ${model}`)
          })
        }
      })
    }

    // 模拟保存配置（实际项目中应该保存到文件或数据库）
    console.log('✅ 扩展配置保存成功')

    return NextResponse.json({
      success: true,
      message: '配置已保存',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ 保存简化配置失败:', error)
    return NextResponse.json(
      { error: '保存配置失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
