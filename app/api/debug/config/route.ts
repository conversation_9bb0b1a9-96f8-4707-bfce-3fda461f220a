/**
 * Configuration Management API - 配置管理接口
 * 
 * 提供配置的查看、修改、重载等管理功能
 */

import { layeredConfigManager, ConfigLayer } from '@/lib/config/layered-config-manager';
import { getSelfMirrorConfig, updateSelfMirrorConfig } from '@/lib/config/config-layers/selfmirror-config';
import { configHotReloadService } from '@/lib/config/config-hot-reload';

export const runtime = "nodejs";

/**
 * GET /api/debug/config
 * 获取配置信息
 */
export async function GET(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const layer = url.searchParams.get('layer') as ConfigLayer;
    const key = url.searchParams.get('key');
    const stats = url.searchParams.get('stats') === 'true';

    let data: any = {};

    if (stats) {
      // 返回配置统计信息
      data = {
        configStats: layeredConfigManager.getStats(),
        hotReloadStats: configHotReloadService.getStats(),
        handlers: configHotReloadService.getHandlers(),
        history: layeredConfigManager.getHistory(20)
      };
    } else if (key) {
      // 返回特定配置项
      if (layer) {
        data = {
          key,
          layer,
          value: layeredConfigManager.getFromLayer(key, layer),
          allLayers: Object.values(ConfigLayer).reduce((acc, l) => {
            acc[l] = layeredConfigManager.getFromLayer(key, l);
            return acc;
          }, {} as Record<string, any>)
        };
      } else {
        data = {
          key,
          value: layeredConfigManager.get(key),
          effectiveLayer: findEffectiveLayer(key)
        };
      }
    } else if (layer) {
      // 返回特定层的所有配置
      data = {
        layer,
        config: layeredConfigManager.getLayerData(layer)
      };
    } else {
      // 返回合并后的完整配置
      data = {
        mergedConfig: getSelfMirrorConfig(),
        layers: Object.values(ConfigLayer).reduce((acc, l) => {
          acc[l] = layeredConfigManager.getLayerData(l);
          return acc;
        }, {} as Record<string, any>)
      };
    }

    const response = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestParams: {
          layer,
          key,
          stats
        }
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 获取配置信息失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'CONFIG_GET_ERROR',
        message: error instanceof Error ? error.message : '获取配置失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * POST /api/debug/config
 * 修改配置
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const { action, key, value, layer, config } = await request.json();
    
    let result: any = {};
    
    switch (action) {
      case 'set':
        if (!key || value === undefined) {
          throw new Error('设置配置需要提供key和value');
        }
        
        const targetLayer = layer || ConfigLayer.USER;
        layeredConfigManager.set(key, value, targetLayer);
        
        result = {
          action: 'set',
          key,
          value,
          layer: targetLayer,
          message: `配置项 ${key} 已设置为 ${JSON.stringify(value)}`
        };
        break;
        
      case 'delete':
        if (!key) {
          throw new Error('删除配置需要提供key');
        }
        
        const deleteLayer = layer || ConfigLayer.USER;
        const deleted = layeredConfigManager.delete(key, deleteLayer);
        
        result = {
          action: 'delete',
          key,
          layer: deleteLayer,
          deleted,
          message: deleted ? `配置项 ${key} 已删除` : `配置项 ${key} 不存在`
        };
        break;
        
      case 'update':
        if (!config || typeof config !== 'object') {
          throw new Error('批量更新需要提供config对象');
        }
        
        const updateLayer = layer || ConfigLayer.USER;
        updateSelfMirrorConfig(config, updateLayer);
        
        result = {
          action: 'update',
          layer: updateLayer,
          updatedKeys: Object.keys(config),
          message: `已批量更新 ${Object.keys(config).length} 个配置项`
        };
        break;
        
      case 'reload':
        await configHotReloadService.triggerReload();
        
        result = {
          action: 'reload',
          message: '配置已重新加载',
          timestamp: new Date().toISOString()
        };
        break;
        
      case 'reset':
        const resetLayer = layer || ConfigLayer.USER;
        const layerData = layeredConfigManager.getLayerData(resetLayer);
        const keys = Object.keys(layerData);
        
        keys.forEach(k => layeredConfigManager.delete(k, resetLayer));
        
        result = {
          action: 'reset',
          layer: resetLayer,
          clearedKeys: keys,
          message: `已清空 ${resetLayer} 层的 ${keys.length} 个配置项`
        };
        break;
        
      case 'test':
        if (!key || value === undefined) {
          throw new Error('测试配置变更需要提供key和value');
        }
        
        await configHotReloadService.testConfigChange(key, value);
        
        result = {
          action: 'test',
          key,
          value,
          message: '配置变更测试完成'
        };
        break;
        
      default:
        throw new Error(`不支持的操作: ${action}`);
    }
    
    const response = {
      success: true,
      data: result,
      metadata: {
        timestamp: new Date().toISOString(),
        requestedAction: action
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 配置管理操作失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'CONFIG_MANAGEMENT_ERROR',
        message: error instanceof Error ? error.message : '配置管理操作失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * PUT /api/debug/config
 * 批量更新配置
 */
export async function PUT(request: Request): Promise<Response> {
  try {
    const { config, layer } = await request.json();
    
    if (!config || typeof config !== 'object') {
      throw new Error('批量更新需要提供config对象');
    }
    
    const targetLayer = layer || ConfigLayer.USER;
    const updatedKeys: string[] = [];
    
    // 扁平化配置并逐个设置
    const flatConfig = flattenObject(config);
    Object.entries(flatConfig).forEach(([key, value]) => {
      layeredConfigManager.set(key, value, targetLayer);
      updatedKeys.push(key);
    });
    
    const response = {
      success: true,
      data: {
        action: 'batch_update',
        layer: targetLayer,
        updatedKeys,
        totalUpdated: updatedKeys.length,
        message: `已批量更新 ${updatedKeys.length} 个配置项`
      },
      metadata: {
        timestamp: new Date().toISOString()
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 批量配置更新失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'CONFIG_BATCH_UPDATE_ERROR',
        message: error instanceof Error ? error.message : '批量配置更新失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 查找配置项的有效层级
 */
function findEffectiveLayer(key: string): ConfigLayer | null {
  const layers = [ConfigLayer.RUNTIME, ConfigLayer.USER, ConfigLayer.DEFAULT, ConfigLayer.ENVIRONMENT];
  
  for (const layer of layers) {
    const value = layeredConfigManager.getFromLayer(key, layer);
    if (value !== undefined) {
      return layer;
    }
  }
  
  return null;
}

/**
 * 扁平化对象
 */
function flattenObject(obj: any, prefix = ''): Record<string, any> {
  const flattened: Record<string, any> = {};
  
  Object.keys(obj).forEach(key => {
    const value = obj[key];
    const newKey = prefix ? `${prefix}.${key}` : key;
    
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      Object.assign(flattened, flattenObject(value, newKey));
    } else {
      flattened[newKey] = value;
    }
  });
  
  return flattened;
}
