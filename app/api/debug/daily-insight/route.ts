import { readMemoryFile } from '@/lib/storage/memory-manager';
import { MEMORY_FILES } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";

// GET - 读取每日洞察内容
export async function GET() {
  try {
    const content = await readMemoryFile(MEMORY_FILES.DAILY_INSIGHT_HOT);
    
    return new Response(content, {
      headers: { 
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('读取每日洞察失败:', error);
    return new Response('读取失败', { status: 500 });
  }
}
