/**
 * Cache Coordination Management API - 缓存协调管理接口
 * 
 * 提供缓存协调的监控、管理和测试功能
 */

import { cacheCoordinator, CacheLayerType } from '@/lib/cache/cache-coordinator';
import { 
  getCacheCoordinatorStatus, 
  triggerCacheCoordinatorOperations, 
  testCacheCoordination 
} from '@/lib/cache/cache-coordinator-init';
import { CacheAdapterFactory } from '@/lib/cache/adapters';

export const runtime = "nodejs";

/**
 * GET /api/debug/cache-coordination
 * 获取缓存协调状态和监控信息
 */
export async function GET(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const layer = url.searchParams.get('layer') as CacheLayerType;

    let data: any = {};

    switch (action) {
      case 'status':
        // 获取协调器状态
        data = getCacheCoordinatorStatus();
        break;
        
      case 'performance':
        // 获取性能指标
        data = {
          performanceMetrics: cacheCoordinator.getPerformanceMetrics(),
          registeredLayers: cacheCoordinator.getRegisteredLayers()
        };
        break;
        
      case 'layers':
        // 获取缓存层信息
        data = {
          registeredLayers: cacheCoordinator.getRegisteredLayers(),
          availableAdapters: CacheAdapterFactory.getAdapterInfo()
        };
        break;
        
      case 'consistency':
        // 触发一致性检查
        data = {
          consistencyCheck: await cacheCoordinator.triggerConsistencyCheck()
        };
        break;
        
      default:
        // 获取完整的协调状态
        data = {
          status: getCacheCoordinatorStatus(),
          performanceMetrics: cacheCoordinator.getPerformanceMetrics(),
          registeredLayers: cacheCoordinator.getRegisteredLayers(),
          adapterInfo: CacheAdapterFactory.getAdapterInfo(),
          systemHealth: await generateSystemHealthReport()
        };
    }

    const response = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestParams: { action, layer }
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 获取缓存协调信息失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'CACHE_COORDINATION_ERROR',
        message: error instanceof Error ? error.message : '获取缓存协调信息失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * POST /api/debug/cache-coordination
 * 缓存协调管理操作
 */
export async function POST(request: Request): Promise<Response> {
  try {
    const { action, key, value, ttl, targetLayers, syncStrategy, testData } = await request.json();
    
    let result: any = {};
    
    switch (action) {
      case 'set':
        // 协调设置缓存
        if (!key || value === undefined) {
          throw new Error('协调设置需要提供key和value');
        }
        
        result = await cacheCoordinator.coordinatedSet(key, value, {
          ttl,
          targetLayers,
          syncStrategy
        });
        break;
        
      case 'get':
        // 协调获取缓存
        if (!key) {
          throw new Error('协调获取需要提供key');
        }
        
        result = await cacheCoordinator.coordinatedGet(key, targetLayers);
        break;
        
      case 'delete':
        // 协调删除缓存
        if (!key) {
          throw new Error('协调删除需要提供key');
        }
        
        result = await cacheCoordinator.coordinatedDelete(key, targetLayers);
        break;
        
      case 'triggerOperations':
        // 触发协调器操作
        result = await triggerCacheCoordinatorOperations();
        break;
        
      case 'testCoordination':
        // 测试缓存协调功能
        result = await testCacheCoordination();
        break;
        
      case 'consistencyCheck':
        // 手动触发一致性检查
        result = {
          consistencyCheck: await cacheCoordinator.triggerConsistencyCheck()
        };
        break;
        
      case 'performanceUpdate':
        // 手动触发性能更新
        cacheCoordinator.triggerPerformanceUpdate();
        result = {
          performanceMetrics: cacheCoordinator.getPerformanceMetrics(),
          message: '性能指标已更新'
        };
        break;
        
      case 'layerHealthCheck':
        // 检查特定层健康状态
        result = await performLayerHealthCheck(targetLayers);
        break;
        
      case 'simulateFailure':
        // 模拟缓存层故障
        result = await simulateCacheLayerFailure(targetLayers);
        break;
        
      default:
        throw new Error(`不支持的操作: ${action}`);
    }
    
    const response = {
      success: true,
      data: result,
      metadata: {
        timestamp: new Date().toISOString(),
        requestedAction: action
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('❌ 缓存协调管理操作失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: {
        code: 'CACHE_COORDINATION_MANAGEMENT_ERROR',
        message: error instanceof Error ? error.message : '缓存协调管理操作失败'
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 生成系统健康报告
 */
async function generateSystemHealthReport(): Promise<any> {
  try {
    const performanceMetrics = cacheCoordinator.getPerformanceMetrics();
    const registeredLayers = cacheCoordinator.getRegisteredLayers();
    const consistencyCheck = await cacheCoordinator.triggerConsistencyCheck();
    
    // 计算健康分数
    const healthScore = calculateHealthScore(performanceMetrics, consistencyCheck);
    
    return {
      healthScore,
      overallStatus: determineOverallStatus(healthScore, consistencyCheck),
      performanceSummary: {
        overallHitRate: `${(performanceMetrics.overallHitRate * 100).toFixed(1)}%`,
        totalRequests: performanceMetrics.totalRequests,
        totalHits: performanceMetrics.totalHits,
        totalMisses: performanceMetrics.totalMisses
      },
      layerSummary: {
        totalLayers: registeredLayers.length,
        enabledLayers: registeredLayers.filter(l => l.enabled).length,
        healthyLayers: consistencyCheck.healthyLayers,
        inconsistentLayers: consistencyCheck.inconsistentLayers.length
      },
      recommendations: generateHealthRecommendations(performanceMetrics, consistencyCheck),
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : '健康报告生成失败',
      lastUpdated: new Date().toISOString()
    };
  }
}

/**
 * 计算健康分数
 */
function calculateHealthScore(performanceMetrics: any, consistencyCheck: any): number {
  let score = 100;
  
  // 基于命中率扣分
  const hitRate = performanceMetrics.overallHitRate;
  if (hitRate < 0.3) score -= 30;
  else if (hitRate < 0.5) score -= 15;
  else if (hitRate < 0.7) score -= 5;
  
  // 基于一致性扣分
  const inconsistentRatio = consistencyCheck.inconsistentLayers.length / consistencyCheck.totalLayers;
  if (inconsistentRatio > 0.5) score -= 40;
  else if (inconsistentRatio > 0.3) score -= 20;
  else if (inconsistentRatio > 0) score -= 10;
  
  // 基于性能问题扣分
  score -= consistencyCheck.performanceIssues.length * 5;
  
  return Math.max(0, Math.min(100, score));
}

/**
 * 确定整体状态
 */
function determineOverallStatus(healthScore: number, consistencyCheck: any): string {
  if (healthScore >= 90 && consistencyCheck.overallHealth === 'healthy') {
    return 'EXCELLENT';
  } else if (healthScore >= 70 && consistencyCheck.overallHealth !== 'critical') {
    return 'GOOD';
  } else if (healthScore >= 50) {
    return 'FAIR';
  } else if (consistencyCheck.overallHealth === 'critical') {
    return 'CRITICAL';
  } else {
    return 'POOR';
  }
}

/**
 * 生成健康建议
 */
function generateHealthRecommendations(performanceMetrics: any, consistencyCheck: any): string[] {
  const recommendations: string[] = [];
  
  // 性能建议
  if (performanceMetrics.overallHitRate < 0.5) {
    recommendations.push('整体缓存命中率较低，建议检查缓存策略和TTL设置');
  }
  
  // 一致性建议
  if (consistencyCheck.inconsistentLayers.length > 0) {
    recommendations.push(`发现 ${consistencyCheck.inconsistentLayers.length} 个不一致的缓存层，建议进行健康检查`);
  }
  
  // 性能问题建议
  if (consistencyCheck.performanceIssues.length > 0) {
    recommendations.push('发现性能问题，建议优化缓存配置');
  }
  
  // 层级建议
  const enabledLayers = cacheCoordinator.getRegisteredLayers().filter(l => l.enabled).length;
  if (enabledLayers < 3) {
    recommendations.push('启用的缓存层较少，建议启用更多缓存层以提高性能');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('缓存协调系统运行良好，无需特别关注');
  }
  
  return recommendations;
}

/**
 * 执行层健康检查
 */
async function performLayerHealthCheck(targetLayers?: CacheLayerType[]): Promise<any> {
  const layers = cacheCoordinator.getRegisteredLayers();
  const results: any = {};
  
  for (const layer of layers) {
    if (targetLayers && !targetLayers.includes(layer.type)) continue;
    
    try {
      // 这里应该调用实际的健康检查方法
      // 目前返回模拟结果
      results[layer.type] = {
        name: layer.name,
        enabled: layer.enabled,
        healthy: true,
        lastCheck: new Date().toISOString()
      };
    } catch (error) {
      results[layer.type] = {
        name: layer.name,
        enabled: layer.enabled,
        healthy: false,
        error: error instanceof Error ? error.message : '健康检查失败',
        lastCheck: new Date().toISOString()
      };
    }
  }
  
  return results;
}

/**
 * 模拟缓存层故障
 */
async function simulateCacheLayerFailure(targetLayers?: CacheLayerType[]): Promise<any> {
  console.log('🧪 模拟缓存层故障...');
  
  // 这里可以实现故障模拟逻辑
  // 目前返回模拟结果
  return {
    message: '故障模拟功能待实现',
    targetLayers: targetLayers || [],
    timestamp: new Date().toISOString()
  };
}
