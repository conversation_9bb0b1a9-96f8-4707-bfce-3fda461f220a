/**
 * 测试上下文包装工厂的API端点
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试上下文包装工厂...');
    
    const results: any = {
      success: true,
      tests: [],
      packagingStats: null
    };
    
    // 1. 初始化上下文包装工厂
    console.log('📦 初始化上下文包装工厂...');
    const { ContextPackagingFactory, SortingStrategy, PriorityStrategy } = await import("@/lib/services/dual-core/context-packaging-factory");
    
    const factory = new ContextPackagingFactory({
      maxContextLength: 2000,
      maxContextItems: 8,
      sortingStrategy: SortingStrategy.BALANCED,
      priorityStrategy: PriorityStrategy.ADAPTIVE,
      sortingWeights: {
        relevance: 0.3,
        semantic: 0.25,
        temporal: 0.2,
        importance: 0.25
      },
      optimizationSettings: {
        enableDuplicateRemoval: true,
        enableContentCompression: true,
        enableSmartTruncation: true,
        enableContextualGrouping: true
      },
      qualityThresholds: {
        minRelevanceScore: 0.2,
        minSemanticScore: 0.2,
        minImportanceScore: 0.1
      },
      enableRealTimeAdjustment: true
    });
    
    await factory.initialize();
    
    results.tests.push({
      name: '上下文包装工厂初始化',
      success: true,
      message: '上下文包装工厂初始化成功'
    });
    
    // 2. 准备测试数据
    console.log('📦 准备测试数据...');
    
    const testRawItems = [
      {
        chunkId: 'chunk-001',
        content: '今天天气很好，适合出门散步和运动',
        metadata: {
          sourceType: 'conversation',
          sourceDocumentId: 'conv-001',
          createdAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1小时前
          relevanceScore: 0.8,
          semanticScore: 0.7,
          temporalScore: 0.9,
          importanceScore: 0.6,
          priorityLevel: 3
        }
      },
      {
        chunkId: 'chunk-002',
        content: 'React性能优化的最佳实践包括使用memo、useMemo和useCallback',
        metadata: {
          sourceType: 'technical',
          sourceDocumentId: 'tech-001',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1天前
          relevanceScore: 0.9,
          semanticScore: 0.8,
          temporalScore: 0.6,
          importanceScore: 0.8,
          priorityLevel: 4
        }
      },
      {
        chunkId: 'chunk-003',
        content: '压力管理的方法包括深呼吸、冥想、运动和良好的睡眠习惯',
        metadata: {
          sourceType: 'wellness',
          sourceDocumentId: 'wellness-001',
          createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30分钟前
          relevanceScore: 0.7,
          semanticScore: 0.8,
          temporalScore: 0.8,
          importanceScore: 0.7,
          priorityLevel: 4
        }
      },
      {
        chunkId: 'chunk-004',
        content: '学习新技能需要持续的练习、反思和实际应用',
        metadata: {
          sourceType: 'education',
          sourceDocumentId: 'edu-001',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2小时前
          relevanceScore: 0.6,
          semanticScore: 0.7,
          temporalScore: 0.5,
          importanceScore: 0.6,
          priorityLevel: 3
        }
      },
      {
        chunkId: 'chunk-005',
        content: '工作生活平衡对心理健康和整体幸福感非常重要',
        metadata: {
          sourceType: 'lifestyle',
          sourceDocumentId: 'life-001',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6小时前
          relevanceScore: 0.8,
          semanticScore: 0.6,
          temporalScore: 0.7,
          importanceScore: 0.8,
          priorityLevel: 5
        }
      },
      {
        chunkId: 'chunk-006',
        content: '今天天气很好，适合出门散步和运动', // 重复内容用于测试去重
        metadata: {
          sourceType: 'conversation',
          sourceDocumentId: 'conv-002',
          createdAt: new Date().toISOString(),
          relevanceScore: 0.7,
          semanticScore: 0.6,
          temporalScore: 0.9,
          importanceScore: 0.5,
          priorityLevel: 2
        }
      }
    ];
    
    results.tests.push({
      name: '测试数据准备',
      success: true,
      testItemCount: testRawItems.length,
      message: `准备了 ${testRawItems.length} 个测试项目`
    });
    
    // 3. 测试基础上下文包装
    console.log('📦 测试基础上下文包装...');
    
    const userMessage = "我想了解如何管理压力和保持工作生活平衡";
    const retrievalContext = {
      primaryIntent: "问答",
      emotionalTone: "中性",
      searchStrategy: "comprehensive"
    };
    
    const basicPackage = await factory.createContextPackage(
      testRawItems,
      userMessage,
      retrievalContext
    );
    
    results.tests.push({
      name: '基础上下文包装测试',
      success: basicPackage.contextItems.length > 0,
      package: {
        packageId: basicPackage.packageId,
        finalContextLength: basicPackage.finalContext.length,
        itemCount: basicPackage.contextItems.length,
        averageRelevance: basicPackage.packageMetadata.averageRelevance,
        averageSemantic: basicPackage.packageMetadata.averageSemantic,
        averageImportance: basicPackage.packageMetadata.averageImportance,
        qualityScore: basicPackage.packageMetadata.qualityScore,
        processingTime: basicPackage.packagingStats.processingTime,
        optimizationsApplied: basicPackage.packageMetadata.optimizationsApplied
      },
      message: '基础上下文包装成功'
    });
    
    // 4. 测试不同排序策略
    console.log('📦 测试不同排序策略...');
    
    const sortingStrategies = [
      SortingStrategy.RELEVANCE_FIRST,
      SortingStrategy.TEMPORAL_FIRST,
      SortingStrategy.IMPORTANCE_FIRST,
      SortingStrategy.SEMANTIC_FIRST
    ];
    
    const sortingResults = [];
    for (const strategy of sortingStrategies) {
      const strategyFactory = new ContextPackagingFactory({
        sortingStrategy: strategy,
        maxContextItems: 5
      });
      await strategyFactory.initialize();
      
      const strategyPackage = await strategyFactory.createContextPackage(
        testRawItems.slice(0, 4), // 使用前4个项目
        userMessage,
        retrievalContext
      );
      
      sortingResults.push({
        strategy,
        itemCount: strategyPackage.contextItems.length,
        qualityScore: strategyPackage.packageMetadata.qualityScore,
        processingTime: strategyPackage.packagingStats.processingTime,
        topItemContent: strategyPackage.contextItems[0]?.content.slice(0, 30) + '...'
      });
    }
    
    results.tests.push({
      name: '排序策略测试',
      success: sortingResults.every(r => r.itemCount > 0),
      sortingResults,
      message: '所有排序策略测试成功'
    });
    
    // 5. 测试不同优先级策略
    console.log('📦 测试不同优先级策略...');
    
    const priorityStrategies = [
      PriorityStrategy.HIGH_FIRST,
      PriorityStrategy.LOW_FIRST,
      PriorityStrategy.BALANCED,
      PriorityStrategy.ADAPTIVE
    ];
    
    const priorityResults = [];
    for (const strategy of priorityStrategies) {
      const priorityFactory = new ContextPackagingFactory({
        priorityStrategy: strategy,
        maxContextItems: 4
      });
      await priorityFactory.initialize();
      
      const priorityPackage = await priorityFactory.createContextPackage(
        testRawItems.slice(0, 4),
        userMessage,
        retrievalContext
      );
      
      priorityResults.push({
        strategy,
        itemCount: priorityPackage.contextItems.length,
        qualityScore: priorityPackage.packageMetadata.qualityScore,
        averagePriorityWeight: priorityPackage.contextItems.reduce(
          (sum, item) => sum + item.packagingMetrics.priorityWeight, 0
        ) / priorityPackage.contextItems.length
      });
    }
    
    results.tests.push({
      name: '优先级策略测试',
      success: priorityResults.every(r => r.itemCount > 0),
      priorityResults,
      message: '所有优先级策略测试成功'
    });
    
    // 6. 测试优化功能
    console.log('📦 测试优化功能...');
    
    // 测试去重优化
    const duplicateFactory = new ContextPackagingFactory({
      optimizationSettings: {
        enableDuplicateRemoval: true,
        enableContentCompression: false,
        enableSmartTruncation: false,
        enableContextualGrouping: false
      }
    });
    await duplicateFactory.initialize();
    
    const duplicatePackage = await duplicateFactory.createContextPackage(
      testRawItems, // 包含重复项目
      userMessage,
      retrievalContext
    );
    
    // 测试压缩优化
    const compressionFactory = new ContextPackagingFactory({
      optimizationSettings: {
        enableDuplicateRemoval: false,
        enableContentCompression: true,
        enableSmartTruncation: false,
        enableContextualGrouping: false
      }
    });
    await compressionFactory.initialize();
    
    const compressionPackage = await compressionFactory.createContextPackage(
      testRawItems.slice(0, 3),
      userMessage,
      retrievalContext
    );
    
    results.tests.push({
      name: '优化功能测试',
      success: true,
      optimizations: {
        duplicateRemoval: {
          originalItems: testRawItems.length,
          finalItems: duplicatePackage.contextItems.length,
          duplicatesRemoved: duplicatePackage.packagingStats.duplicatesRemoved
        },
        contentCompression: {
          compressionRatio: compressionPackage.packagingStats.compressionRatio,
          optimizationsApplied: compressionPackage.packageMetadata.optimizationsApplied
        }
      },
      message: '优化功能测试成功'
    });
    
    // 7. 测试质量过滤
    console.log('📦 测试质量过滤...');
    
    const lowQualityItems = [
      {
        chunkId: 'low-001',
        content: '低质量内容',
        metadata: {
          sourceType: 'low_quality',
          relevanceScore: 0.1,
          semanticScore: 0.1,
          temporalScore: 0.1,
          importanceScore: 0.05,
          priorityLevel: 1
        }
      },
      ...testRawItems.slice(0, 2) // 添加一些高质量项目
    ];
    
    const qualityFactory = new ContextPackagingFactory({
      qualityThresholds: {
        minRelevanceScore: 0.3,
        minSemanticScore: 0.3,
        minImportanceScore: 0.2
      }
    });
    await qualityFactory.initialize();
    
    const qualityPackage = await qualityFactory.createContextPackage(
      lowQualityItems,
      userMessage,
      retrievalContext
    );
    
    results.tests.push({
      name: '质量过滤测试',
      success: qualityPackage.contextItems.length < lowQualityItems.length,
      filtering: {
        originalItems: lowQualityItems.length,
        filteredItems: qualityPackage.contextItems.length,
        itemsFiltered: qualityPackage.packagingStats.itemsFiltered,
        qualityScore: qualityPackage.packageMetadata.qualityScore
      },
      message: '质量过滤功能正常'
    });
    
    // 8. 测试实时参数调整
    console.log('📦 测试实时参数调整...');
    
    const adjustableFactory = new ContextPackagingFactory({
      enableRealTimeAdjustment: true
    });
    await adjustableFactory.initialize();
    
    // 更新配置
    adjustableFactory.updateConfig({
      maxContextItems: 3,
      sortingWeights: {
        relevance: 0.4,
        semantic: 0.3,
        temporal: 0.2,
        importance: 0.1
      }
    });
    
    const adjustedPackage = await adjustableFactory.createContextPackage(
      testRawItems.slice(0, 5),
      userMessage,
      retrievalContext
    );
    
    results.tests.push({
      name: '实时参数调整测试',
      success: adjustedPackage.contextItems.length <= 3,
      adjustment: {
        maxItemsSet: 3,
        actualItems: adjustedPackage.contextItems.length,
        newWeights: {
          relevance: 0.4,
          semantic: 0.3,
          temporal: 0.2,
          importance: 0.1
        }
      },
      message: '实时参数调整功能正常'
    });
    
    // 9. 测试包装统计
    console.log('📦 测试包装统计...');
    
    const packagingStats = factory.getPackagingStats();
    results.packagingStats = {
      totalPackages: packagingStats.totalPackages,
      averageProcessingTime: Math.round(packagingStats.averageProcessingTime),
      averageContextLength: Math.round(packagingStats.averageContextLength),
      averageQualityScore: Math.round(packagingStats.averageQualityScore * 1000) / 1000,
      sortingStrategyUsage: packagingStats.sortingStrategyUsage,
      priorityStrategyUsage: packagingStats.priorityStrategyUsage,
      optimizationEffectiveness: packagingStats.optimizationEffectiveness
    };
    
    results.tests.push({
      name: '包装统计测试',
      success: packagingStats.totalPackages > 0,
      stats: results.packagingStats,
      message: '包装统计功能正常'
    });
    
    // 10. 综合性能测试
    console.log('📦 执行综合性能测试...');
    
    const performanceTests = [];
    const testMessages = [
      "如何提高工作效率？",
      "压力管理的最佳方法",
      "学习新技能的建议",
      "保持健康的生活方式"
    ];
    
    for (const message of testMessages) {
      const start = Date.now();
      
      const perfPackage = await factory.createContextPackage(
        testRawItems,
        message,
        { primaryIntent: "问答", emotionalTone: "中性" }
      );
      
      const time = Date.now() - start;
      
      performanceTests.push({
        message: message.slice(0, 15) + '...',
        processingTime: time,
        contextLength: perfPackage.finalContext.length,
        itemCount: perfPackage.contextItems.length,
        qualityScore: perfPackage.packageMetadata.qualityScore
      });
    }
    
    const averageProcessingTime = performanceTests.reduce((sum, t) => sum + t.processingTime, 0) / performanceTests.length;
    
    results.tests.push({
      name: '综合性能测试',
      success: averageProcessingTime < 100, // 期望平均处理时间小于100ms
      averageProcessingTime: Math.round(averageProcessingTime),
      performanceTests,
      message: `平均处理时间: ${Math.round(averageProcessingTime)}ms`
    });
    
    // 11. 综合功能评估
    console.log('📦 综合功能评估...');
    
    const successfulTests = results.tests.filter(test => test.success).length;
    const totalTests = results.tests.length;
    const successRate = Math.round((successfulTests / totalTests) * 100);
    
    results.tests.push({
      name: '综合功能评估',
      success: successRate >= 90,
      evaluation: {
        successfulTests,
        totalTests,
        successRate: `${successRate}%`,
        factoryReady: successRate >= 90
      },
      message: `上下文包装工厂功能完整性: ${successRate}%`
    });
    
    console.log('✅ 上下文包装工厂测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
