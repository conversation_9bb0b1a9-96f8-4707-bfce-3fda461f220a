// RAG系统初始化API

import { NextRequest, NextResponse } from 'next/server';
import { ragSystem } from '@/lib/services/rag-system';
import { loadMemoryContext } from '@/lib/storage/memory-manager';

export const runtime = "nodejs";
export const maxDuration = 300; // 5分钟超时

export async function POST(req: NextRequest) {
  console.log('🚀 收到RAG系统初始化请求');
  
  try {
    const { forceRebuild = false } = await req.json();
    
    if (forceRebuild) {
      console.log('🔄 强制重建RAG系统...');
      await ragSystem.reset();
    } else {
      console.log('📦 初始化RAG系统...');
      await ragSystem.initialize();
    }
    
    // 加载并索引所有记忆文件
    console.log('📚 开始索引记忆文件...');
    const memoryContext = await loadMemoryContext();
    
    const memoryContents = {
      USER_PROFILE: memoryContext.userProfile,
      MENTAL_ELEMENTS: memoryContext.mentalElements,
      KEY_EVENTS: memoryContext.keyEvents,
      DAILY_INSIGHT_COLD: memoryContext.dailyInsightCold,
      DAILY_INSIGHT_HOT: memoryContext.dailyInsightHot,
      DIALOGUE_HISTORY: memoryContext.dialogueHistory
    };
    
    await ragSystem.indexAllMemoryFiles(memoryContents);
    
    // 获取系统状态
    const status = await ragSystem.getSystemStatus();
    
    return NextResponse.json({
      success: true,
      message: 'RAG系统初始化完成',
      status,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('💥 RAG系统初始化失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  console.log('📊 获取RAG系统状态');
  
  try {
    const status = await ragSystem.getSystemStatus();
    const healthCheck = await ragSystem.healthCheck();
    
    return NextResponse.json({
      success: true,
      status,
      healthCheck,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ 获取RAG状态失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
