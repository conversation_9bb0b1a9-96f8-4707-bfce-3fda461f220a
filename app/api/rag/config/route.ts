import { NextRequest, NextResponse } from 'next/server'
import {
  RAGConfig,
  EnhancedRetrievalConfig,
  APIResponse
} from '@/types/simplified-rag'

// 简化的类型定义
interface ConfigRequest {
  presetName?: string;
  config?: Partial<RAGConfig>;
}

interface ConfigPreset {
  name: string;
  description: string;
  type?: string;
  isDefault?: boolean;
  indexingConfig: RAGConfig;
  retrievalConfig: EnhancedRetrievalConfig;
}

/**
 * 配置预设存储（在实际应用中应该使用数据库）
 */
const configPresets: Map<string, ConfigPreset> = new Map()

// 初始化默认预设
initializeDefaultPresets()

/**
 * GET /api/rag/config
 * 获取配置预设
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log('📋 获取配置预设请求')
    
    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const configType = searchParams.get('type') as 'indexing' | 'retrieval' | null
    const presetId = searchParams.get('id')
    
    if (presetId) {
      // 获取特定预设
      const preset = configPresets.get(presetId)
      if (!preset) {
        return NextResponse.json({
          success: false,
          error: '配置预设不存在',
          timestamp: new Date().toISOString(),
          processingTime: Date.now() - startTime
        } as APIResponse, { status: 404 })
      }
      
      console.log(`📋 返回配置预设: ${preset.name}`)
      
      return NextResponse.json({
        success: true,
        data: preset,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse<ConfigPreset>)
    } else {
      // 获取预设列表
      const presets = Array.from(configPresets.values())
        .filter(preset => !configType || preset.type === configType)
        .sort((a, b) => {
          // 默认预设排在前面
          if (a.isDefault && !b.isDefault) return -1
          if (!a.isDefault && b.isDefault) return 1
          return a.name.localeCompare(b.name)
        })
      
      console.log(`📋 返回 ${presets.length} 个配置预设`)
      
      return NextResponse.json({
        success: true,
        data: presets,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse<ConfigPreset[]>)
    }
    
  } catch (error) {
    console.error('💥 获取配置预设失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取配置预设失败',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 500 })
  }
}

/**
 * POST /api/rag/config
 * 保存配置预设
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log('💾 保存配置预设请求')
    
    // 解析请求体
    const body: ConfigRequest = await request.json()
    const { action, configType, config, preset } = body
    
    if (action !== 'save') {
      return NextResponse.json({
        success: false,
        error: '无效的操作类型',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 400 })
    }
    
    if (!preset || !config) {
      return NextResponse.json({
        success: false,
        error: '预设信息和配置不能为空',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 400 })
    }
    
    // 验证配置类型
    if (configType !== 'indexing' && configType !== 'retrieval') {
      return NextResponse.json({
        success: false,
        error: '无效的配置类型',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 400 })
    }
    
    // 创建新的配置预设
    const newPreset: ConfigPreset = {
      id: generatePresetId(),
      name: preset.name,
      description: preset.description,
      type: configType,
      config,
      isDefault: preset.isDefault || false,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    // 如果设置为默认预设，取消其他同类型的默认预设
    if (newPreset.isDefault) {
      for (const [id, existingPreset] of configPresets.entries()) {
        if (existingPreset.type === configType && existingPreset.isDefault) {
          configPresets.set(id, { ...existingPreset, isDefault: false })
        }
      }
    }
    
    // 保存预设
    configPresets.set(newPreset.id, newPreset)
    
    console.log(`💾 配置预设已保存: ${newPreset.name} (${newPreset.id})`)
    
    return NextResponse.json({
      success: true,
      data: newPreset,
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse<ConfigPreset>)
    
  } catch (error) {
    console.error('💥 保存配置预设失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '保存配置预设失败',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 500 })
  }
}

/**
 * PUT /api/rag/config
 * 更新配置预设
 */
export async function PUT(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log('🔄 更新配置预设请求')
    
    // 解析请求体
    const body: ConfigRequest & { presetId: string } = await request.json()
    const { presetId, config, preset } = body
    
    if (!presetId) {
      return NextResponse.json({
        success: false,
        error: '预设ID不能为空',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 400 })
    }
    
    // 检查预设是否存在
    const existingPreset = configPresets.get(presetId)
    if (!existingPreset) {
      return NextResponse.json({
        success: false,
        error: '配置预设不存在',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 404 })
    }
    
    // 更新预设
    const updatedPreset: ConfigPreset = {
      ...existingPreset,
      name: preset?.name || existingPreset.name,
      description: preset?.description || existingPreset.description,
      config: config || existingPreset.config,
      isDefault: preset?.isDefault !== undefined ? preset.isDefault : existingPreset.isDefault,
      updatedAt: new Date()
    }
    
    // 如果设置为默认预设，取消其他同类型的默认预设
    if (updatedPreset.isDefault && !existingPreset.isDefault) {
      for (const [id, preset] of configPresets.entries()) {
        if (preset.type === updatedPreset.type && preset.isDefault && id !== presetId) {
          configPresets.set(id, { ...preset, isDefault: false })
        }
      }
    }
    
    configPresets.set(presetId, updatedPreset)
    
    console.log(`🔄 配置预设已更新: ${updatedPreset.name} (${presetId})`)
    
    return NextResponse.json({
      success: true,
      data: updatedPreset,
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse<ConfigPreset>)
    
  } catch (error) {
    console.error('💥 更新配置预设失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '更新配置预设失败',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 500 })
  }
}

/**
 * DELETE /api/rag/config
 * 删除配置预设
 */
export async function DELETE(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log('🗑️ 删除配置预设请求')
    
    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const presetId = searchParams.get('id')
    
    if (!presetId) {
      return NextResponse.json({
        success: false,
        error: '预设ID不能为空',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 400 })
    }
    
    // 检查预设是否存在
    const preset = configPresets.get(presetId)
    if (!preset) {
      return NextResponse.json({
        success: false,
        error: '配置预设不存在',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 404 })
    }
    
    // 不允许删除默认预设
    if (preset.isDefault) {
      return NextResponse.json({
        success: false,
        error: '不能删除默认预设',
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse, { status: 400 })
    }
    
    // 删除预设
    configPresets.delete(presetId)
    
    console.log(`🗑️ 配置预设已删除: ${preset.name} (${presetId})`)
    
    return NextResponse.json({
      success: true,
      data: { message: '配置预设已删除', deletedPreset: preset },
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse)
    
  } catch (error) {
    console.error('💥 删除配置预设失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除配置预设失败',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 500 })
  }
}

// ==================== 辅助函数 ====================

/**
 * 生成预设ID
 */
function generatePresetId(): string {
  return `preset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 初始化默认预设
 */
function initializeDefaultPresets() {
  // 默认索引配置
  const defaultIndexingConfig: IndexingConfig = {
    chunkingRules: {
      method: 'recursive',
      chunkSize: 1000,
      overlap: 200,
      preserveStructure: true
    },
    childChunkPrompt: `请为以下文本生成一个简洁但信息丰富的意义摘要，保留核心概念和关键信息：

{content}

摘要应该：
1. 保持原文的主要意思
2. 提取关键概念和情感
3. 长度控制在100字以内
4. 使用清晰简洁的语言`,
    meaningAnnotationRules: {
      extractEmotions: true,
      extractThemes: true,
      extractImportance: true,
      extractKeywords: true,
      generateSummary: true
    },
    vectorModel: {
      modelName: 'Qwen/Qwen2.5-0.5B-Instruct',
      dimensions: 384
    },
    batchSize: 10
  }
  
  // 默认检索配置
  const defaultRetrievalConfig: EnhancedRetrievalConfig = {
    mode: 'hybrid',
    vectorSearch: {
      topK: 100,
      similarityThreshold: 0.7
    },
    keywordSearch: {
      enabled: true,
      boost: 1.2
    },
    weightingParams: {
      semanticRelevance: 0.3,
      emotionalMatch: 0.2,
      themeRelevance: 0.2,
      importanceScore: 0.15,
      temporalDecay: 0.1,
      profileMatch: 0.05
    },
    filterRules: {
      minWeightThreshold: 0.1,
      maxResults: 50,
      deduplication: {
        enabled: true,
        similarityThreshold: 0.85
      },
      temporalFilter: {
        enabled: false,
        maxAge: 30
      },
      importanceFilter: {
        enabled: false,
        minImportance: 0.5
      }
    },
    finalCount: 5,
    debug: {
      enabled: true,
      includeScores: true,
      includeWeightBreakdown: true
    }
  }
  
  // 创建默认预设
  const indexingPreset: ConfigPreset = {
    id: 'default_indexing',
    name: '默认索引配置',
    description: '标准的文档索引配置，适用于大多数场景',
    type: 'indexing',
    config: defaultIndexingConfig,
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  const retrievalPreset: ConfigPreset = {
    id: 'default_retrieval',
    name: '默认检索配置',
    description: '均衡的检索配置，兼顾语义相关性和情感匹配',
    type: 'retrieval',
    config: defaultRetrievalConfig,
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  configPresets.set(indexingPreset.id, indexingPreset)
  configPresets.set(retrievalPreset.id, retrievalPreset)
  
  console.log('📋 默认配置预设已初始化')
}
