// RAG检索API - 支持传统检索和增强意义检索

import { NextRequest, NextResponse } from 'next/server';
import { ragSystem } from '@/lib/services/rag-system';
import { loadMemoryContext } from '@/lib/storage/memory-manager';
import {
  SearchRequest,
  DetailedRetrievalResult,
  APIResponse,
  RetrievalError as MeaningRetrievalError
} from '@/types/simplified-rag';
import { ragRetriever } from '@/lib/services/rag-retriever';

export const runtime = "nodejs";
export const maxDuration = 30; // 30秒超时

export async function POST(req: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('🔍 收到RAG检索请求');

    const body = await req.json();

    // 检查是否是增强检索请求（包含config字段）
    if (body.config && body.context) {
      return await handleEnhancedSearch(body as SearchRequest, startTime);
    } else {
      return await handleLegacySearch(body, startTime);
    }

  } catch (error) {
    console.error('❌ RAG检索失败:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 500 });
  }
}

/**
 * 处理增强检索请求
 */
async function handleEnhancedSearch(body: SearchRequest, startTime: number) {
  const { query, config, context, options } = body;

  // 验证请求参数
  if (!query || !query.trim()) {
    return NextResponse.json({
      success: false,
      error: '查询内容不能为空',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 400 });
  }

  if (!config) {
    return NextResponse.json({
      success: false,
      error: '检索配置不能为空',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 400 });
  }

  if (!context || !context.userProfile || !context.dailyInsight) {
    return NextResponse.json({
      success: false,
      error: '用户上下文信息不完整',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 400 });
  }

  console.log('🔧 增强检索配置:', {
    mode: config.mode,
    topK: config.vectorSearch.topK,
    finalCount: config.finalCount,
    queryLength: query.length
  });

  // 执行增强检索
  console.log('🚀 开始执行增强意义检索...');

  const result: DetailedRetrievalResult = await ragRetriever.retrieveMeaningfulMemoriesWithFullDebug(
    query,
    context.userProfile,
    context.dailyInsight,
    config
  );

  console.log('✅ 增强检索完成', {
    finalMemoriesCount: result.finalMemories.length,
    totalCandidates: result.debugInfo.totalCandidates,
    processingTime: result.performance.totalTime
  });

  // 根据选项过滤调试信息
  if (!options?.includeDebugInfo) {
    result.debugInfo = {
      ...result.debugInfo,
      candidateBreakdown: [],
      weightBreakdown: []
    };
  }

  return NextResponse.json({
    success: true,
    data: result,
    timestamp: new Date().toISOString(),
    processingTime: Date.now() - startTime
  } as APIResponse<DetailedRetrievalResult>);
}

/**
 * 处理传统检索请求（保持向后兼容）
 */
async function handleLegacySearch(body: any, startTime: number) {
  const { query, context } = body;

  if (!query || typeof query !== 'string') {
    return NextResponse.json({
      success: false,
      error: '查询参数无效',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 400 });
  }

  // 加载记忆上下文
  const memoryContext = await loadMemoryContext();

  // 使用提供的上下文或默认上下文
  const userProfile = context?.userProfile || memoryContext.userProfile;
  const dailyInsight = context?.dailyInsight || memoryContext.dailyInsightHot;

  console.log(`🔍 执行传统检索，查询: "${query.substring(0, 50)}..."`);

  // 执行检索
  const memories = await ragSystem.retrieveMemories(query, userProfile, dailyInsight);

  return NextResponse.json({
    success: true,
    query,
    memories,
    count: memories.length,
    timestamp: new Date().toISOString(),
    processingTime: Date.now() - startTime
  } as APIResponse);
}

export async function GET(req: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('📊 获取RAG检索统计');

    // 解析查询参数
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const includeDebugInfo = searchParams.get('debug') === 'true';
    const enhanced = searchParams.get('enhanced') === 'true';

    if (enhanced) {
      // 获取增强检索统计
      const debugInfo = ragRetriever.getDebugInfo();

      const enhancedStats = {
        cacheSize: debugInfo.cacheSize,
        recentQueries: debugInfo.debugLogs.slice(-limit),
        systemStats: debugInfo.systemStats,
        performance: {
          averageResponseTime: debugInfo.debugLogs.length > 0 ?
            debugInfo.debugLogs.reduce((sum, log) => sum + log.processingTime, 0) / debugInfo.debugLogs.length : 0,
          totalQueries: debugInfo.debugLogs.length,
          averageResultCount: debugInfo.debugLogs.length > 0 ?
            debugInfo.debugLogs.reduce((sum, log) => sum + log.resultCount, 0) / debugInfo.debugLogs.length : 0
        }
      };

      console.log('📈 增强检索统计:', {
        cacheSize: enhancedStats.cacheSize,
        totalQueries: enhancedStats.performance.totalQueries,
        averageResponseTime: enhancedStats.performance.averageResponseTime
      });

      return NextResponse.json({
        success: true,
        data: includeDebugInfo ? { ...enhancedStats, debugInfo } : enhancedStats,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse);

    } else {
      // 获取传统检索统计
      const status = await ragSystem.getSystemStatus();

      return NextResponse.json({
        success: true,
        retrieverStats: status.retriever,
        embeddingStats: status.embeddingModel,
        vectorStats: status.vectorDatabase,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime
      } as APIResponse);
    }

  } catch (error) {
    console.error('❌ 获取检索统计失败:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 500 });
  }
}

/**
 * DELETE /api/rag/search
 * 清空检索缓存
 */
export async function DELETE(req: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('🗑️ 收到清空检索缓存请求');

    // 清空检索缓存
    ragRetriever.clearCache();

    console.log('✅ 检索缓存已清空');

    return NextResponse.json({
      success: true,
      data: { message: '检索缓存已清空' },
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse);

  } catch (error) {
    console.error('💥 清空检索缓存失败:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '清空检索缓存失败',
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime
    } as APIResponse, { status: 500 });
  }
}
