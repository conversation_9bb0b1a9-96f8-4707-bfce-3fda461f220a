/**
 * 测试智能缓存层的API端点
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试智能缓存层...');
    
    const results: any = {
      success: true,
      tests: [],
      cacheStats: null
    };
    
    // 1. 初始化智能缓存层
    console.log('🧠 初始化智能缓存层...');
    const { IntelligentCacheLayer } = await import("@/lib/services/dual-core/intelligent-cache-layer");
    
    const cache = new IntelligentCacheLayer({
      maxCacheSize: 100,
      decayCoefficients: {
        primary: 1.0,
        secondary: 0.8,
        tertiary: 0.6
      },
      tailEliminationThreshold: 0.1,
      noiseSuppressionThreshold: 0.05,
      zeroResetThreshold: 0.02,
      noveltyThreshold: 0.7,
      decayInterval: 10000, // 10秒用于测试
      enableRealTimeAdjustment: true
    });
    
    await cache.initialize();
    
    results.tests.push({
      name: '智能缓存层初始化',
      success: true,
      message: '智能缓存层初始化成功'
    });
    
    // 2. 测试添加缓存项
    console.log('🧠 测试添加缓存项...');
    
    const testItems = [
      {
        parentChunkId: 'chunk-001',
        content: '今天天气很好，适合出门散步',
        metadata: {
          sourceType: 'conversation',
          sourceDocumentId: 'conv-001',
          relevanceScore: 0.8,
          semanticScore: 0.7,
          temporalScore: 0.9
        }
      },
      {
        parentChunkId: 'chunk-002',
        content: 'React性能优化的最佳实践包括使用memo和useMemo',
        metadata: {
          sourceType: 'technical',
          sourceDocumentId: 'tech-001',
          relevanceScore: 0.9,
          semanticScore: 0.8,
          temporalScore: 0.6
        }
      },
      {
        parentChunkId: 'chunk-003',
        content: '压力管理的方法包括深呼吸、冥想和运动',
        metadata: {
          sourceType: 'wellness',
          sourceDocumentId: 'wellness-001',
          relevanceScore: 0.7,
          semanticScore: 0.8,
          temporalScore: 0.8
        }
      },
      {
        parentChunkId: 'chunk-004',
        content: '学习新技能需要持续的练习和反思',
        metadata: {
          sourceType: 'education',
          sourceDocumentId: 'edu-001',
          relevanceScore: 0.6,
          semanticScore: 0.7,
          temporalScore: 0.5
        }
      },
      {
        parentChunkId: 'chunk-005',
        content: '工作生活平衡对心理健康很重要',
        metadata: {
          sourceType: 'lifestyle',
          sourceDocumentId: 'life-001',
          relevanceScore: 0.8,
          semanticScore: 0.6,
          temporalScore: 0.7
        }
      }
    ];
    
    const addedItems = [];
    for (const item of testItems) {
      const cacheId = await cache.addCacheItem(
        item.parentChunkId,
        item.content,
        item.metadata
      );
      addedItems.push(cacheId);
    }
    
    results.tests.push({
      name: '缓存项添加测试',
      success: addedItems.length === testItems.length,
      addedItems: addedItems.length,
      message: `成功添加 ${addedItems.length} 个缓存项`
    });
    
    // 3. 测试缓存项获取
    console.log('🧠 测试缓存项获取...');
    
    const retrievalTests = [];
    for (const cacheId of addedItems.slice(0, 3)) {
      const item = await cache.getCacheItem(cacheId);
      retrievalTests.push({
        cacheId,
        found: item !== null,
        weightedScore: item?.cacheMetrics.weightedScore || 0,
        accessCount: item?.metadata.accessCount || 0
      });
    }
    
    // 测试不存在的缓存项
    const nonExistentItem = await cache.getCacheItem('non-existent-id');
    
    results.tests.push({
      name: '缓存项获取测试',
      success: retrievalTests.every(t => t.found) && nonExistentItem === null,
      retrievalTests,
      nonExistentHandled: nonExistentItem === null,
      message: '缓存项获取功能正常'
    });
    
    // 4. 测试缓存搜索
    console.log('🧠 测试缓存搜索...');
    
    const searchQueries = [
      '天气',
      'React性能',
      '压力管理',
      '学习技能',
      '工作平衡'
    ];
    
    const searchResults = [];
    for (const query of searchQueries) {
      const results_search = await cache.searchCache(query, undefined, 3);
      searchResults.push({
        query,
        resultCount: results_search.length,
        topResult: results_search[0] ? {
          content: results_search[0].content.slice(0, 30) + '...',
          weightedScore: results_search[0].cacheMetrics.weightedScore
        } : null
      });
    }
    
    results.tests.push({
      name: '缓存搜索测试',
      success: searchResults.every(r => r.resultCount > 0),
      searchResults,
      message: '缓存搜索功能正常'
    });
    
    // 5. 测试权重衰减
    console.log('🧠 测试权重衰减...');
    
    // 获取衰减前的权重
    const beforeDecay = [];
    for (const cacheId of addedItems.slice(0, 3)) {
      const item = await cache.getCacheItem(cacheId);
      if (item) {
        beforeDecay.push({
          cacheId,
          weightedScore: item.cacheMetrics.weightedScore,
          decayFactor: item.cacheMetrics.decayFactor
        });
      }
    }
    
    // 执行权重衰减
    await cache.performWeightedDecay();
    
    // 获取衰减后的权重
    const afterDecay = [];
    for (const cacheId of addedItems.slice(0, 3)) {
      const item = await cache.getCacheItem(cacheId);
      if (item) {
        afterDecay.push({
          cacheId,
          weightedScore: item.cacheMetrics.weightedScore,
          decayFactor: item.cacheMetrics.decayFactor
        });
      }
    }
    
    results.tests.push({
      name: '权重衰减测试',
      success: true,
      beforeDecay,
      afterDecay,
      message: '权重衰减功能正常'
    });
    
    // 6. 测试噪声抑制
    console.log('🧠 测试噪声抑制...');
    
    // 获取抑制前的状态
    const beforeSuppression = [];
    for (const cacheId of addedItems.slice(0, 2)) {
      const item = await cache.getCacheItem(cacheId);
      if (item) {
        beforeSuppression.push({
          cacheId,
          weightedScore: item.cacheMetrics.weightedScore,
          noiseLevel: item.cacheMetrics.noiseLevel
        });
      }
    }
    
    // 执行噪声抑制
    await cache.performNoiseSuppressionn();
    
    // 获取抑制后的状态
    const afterSuppression = [];
    for (const cacheId of addedItems.slice(0, 2)) {
      const item = await cache.getCacheItem(cacheId);
      if (item) {
        afterSuppression.push({
          cacheId,
          weightedScore: item.cacheMetrics.weightedScore,
          noiseLevel: item.cacheMetrics.noiseLevel
        });
      }
    }
    
    results.tests.push({
      name: '噪声抑制测试',
      success: true,
      beforeSuppression,
      afterSuppression,
      message: '噪声抑制功能正常'
    });
    
    // 7. 测试尾部淘汰
    console.log('🧠 测试尾部淘汰...');
    
    // 添加更多低质量项目来触发淘汰
    const lowQualityItems = [];
    for (let i = 0; i < 10; i++) {
      const cacheId = await cache.addCacheItem(
        `low-chunk-${i}`,
        `低质量内容 ${i}`,
        {
          sourceType: 'low_quality',
          sourceDocumentId: `low-${i}`,
          relevanceScore: 0.1,
          semanticScore: 0.1,
          temporalScore: 0.1
        }
      );
      lowQualityItems.push(cacheId);
    }
    
    const beforeElimination = cache.getCacheStats().totalItems;
    
    // 执行尾部淘汰
    await cache.performTailElimination();
    
    const afterElimination = cache.getCacheStats().totalItems;
    
    results.tests.push({
      name: '尾部淘汰测试',
      success: afterElimination < beforeElimination,
      beforeElimination,
      afterElimination,
      eliminated: beforeElimination - afterElimination,
      message: '尾部淘汰功能正常'
    });
    
    // 8. 测试归零重置
    console.log('🧠 测试归零重置...');
    
    // 获取重置前的状态
    const beforeReset = cache.getCacheStats().totalItems;
    
    // 执行归零重置
    await cache.performZeroReset();
    
    // 获取重置后的状态
    const afterReset = cache.getCacheStats().totalItems;
    
    results.tests.push({
      name: '归零重置测试',
      success: true,
      beforeReset,
      afterReset,
      message: '归零重置功能正常'
    });
    
    // 9. 测试实时参数调整
    console.log('🧠 测试实时参数调整...');
    
    const originalConfig = {
      tailEliminationThreshold: 0.1,
      noiseSuppressionThreshold: 0.05,
      decayCoefficients: { primary: 1.0, secondary: 0.8, tertiary: 0.6 }
    };
    
    // 更新配置
    cache.updateConfig({
      tailEliminationThreshold: 0.2,
      noiseSuppressionThreshold: 0.1,
      decayCoefficients: { primary: 0.9, secondary: 0.7, tertiary: 0.5 }
    });
    
    results.tests.push({
      name: '实时参数调整测试',
      success: true,
      originalConfig,
      newConfig: {
        tailEliminationThreshold: 0.2,
        noiseSuppressionThreshold: 0.1,
        decayCoefficients: { primary: 0.9, secondary: 0.7, tertiary: 0.5 }
      },
      message: '实时参数调整功能正常'
    });
    
    // 10. 测试缓存统计
    console.log('🧠 测试缓存统计...');
    
    const cacheStats = cache.getCacheStats();
    results.cacheStats = {
      totalItems: cacheStats.totalItems,
      hitRate: Math.round(cacheStats.hitRate * 100) / 100,
      missRate: Math.round(cacheStats.missRate * 100) / 100,
      averageWeightedScore: Math.round(cacheStats.averageWeightedScore * 1000) / 1000,
      decayOperations: cacheStats.decayOperations,
      eliminationOperations: cacheStats.eliminationOperations,
      resetOperations: cacheStats.resetOperations,
      noiseSuppressionOperations: cacheStats.noiseSuppressionOperations,
      lastOptimizationTime: cacheStats.lastOptimizationTime
    };
    
    results.tests.push({
      name: '缓存统计测试',
      success: cacheStats.totalItems > 0,
      stats: results.cacheStats,
      message: '缓存统计功能正常'
    });
    
    // 11. 综合性能测试
    console.log('🧠 执行综合性能测试...');
    
    const performanceTests = [];
    const testQueries = ['性能', '优化', '学习', '管理', '平衡'];
    
    for (const query of testQueries) {
      const start = Date.now();
      
      // 执行搜索
      const searchResults = await cache.searchCache(query, undefined, 5);
      
      // 访问前几个结果
      for (const result of searchResults.slice(0, 2)) {
        await cache.getCacheItem(result.id);
      }
      
      const time = Date.now() - start;
      
      performanceTests.push({
        query,
        searchTime: time,
        resultCount: searchResults.length,
        averageWeightedScore: searchResults.length > 0 
          ? searchResults.reduce((sum, r) => sum + r.cacheMetrics.weightedScore, 0) / searchResults.length 
          : 0
      });
    }
    
    const averageSearchTime = performanceTests.reduce((sum, t) => sum + t.searchTime, 0) / performanceTests.length;
    
    results.tests.push({
      name: '综合性能测试',
      success: averageSearchTime < 100, // 期望平均搜索时间小于100ms
      averageSearchTime: Math.round(averageSearchTime),
      performanceTests,
      message: `平均搜索时间: ${Math.round(averageSearchTime)}ms`
    });
    
    // 12. 综合功能评估
    console.log('🧠 综合功能评估...');
    
    const successfulTests = results.tests.filter(test => test.success).length;
    const totalTests = results.tests.length;
    const successRate = Math.round((successfulTests / totalTests) * 100);
    
    results.tests.push({
      name: '综合功能评估',
      success: successRate >= 90,
      evaluation: {
        successfulTests,
        totalTests,
        successRate: `${successRate}%`,
        cacheReady: successRate >= 90
      },
      message: `智能缓存层功能完整性: ${successRate}%`
    });
    
    // 清理资源
    cache.destroy();
    
    console.log('✅ 智能缓存层测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { config, operation, params } = await req.json();
    
    const { IntelligentCacheLayer } = await import("@/lib/services/dual-core/intelligent-cache-layer");
    
    const cache = new IntelligentCacheLayer(config);
    await cache.initialize();
    
    let result: any = {};
    
    switch (operation) {
      case 'addItem':
        result.cacheId = await cache.addCacheItem(
          params.parentChunkId,
          params.content,
          params.metadata,
          params.vector
        );
        break;
        
      case 'getItem':
        result.item = await cache.getCacheItem(params.cacheId);
        break;
        
      case 'search':
        result.items = await cache.searchCache(
          params.query,
          params.queryVector,
          params.maxResults
        );
        break;
        
      case 'performDecay':
        await cache.performWeightedDecay();
        result.message = '权重衰减完成';
        break;
        
      case 'performElimination':
        await cache.performTailElimination();
        result.message = '尾部淘汰完成';
        break;
        
      case 'performSuppression':
        await cache.performNoiseSuppressionn();
        result.message = '噪声抑制完成';
        break;
        
      case 'performReset':
        await cache.performZeroReset();
        result.message = '归零重置完成';
        break;
        
      case 'updateConfig':
        cache.updateConfig(params.newConfig);
        result.message = '配置更新完成';
        break;
        
      case 'getStats':
        result.stats = cache.getCacheStats();
        break;
        
      default:
        throw new Error(`未知操作: ${operation}`);
    }
    
    const stats = cache.getCacheStats();
    cache.destroy();
    
    return new Response(JSON.stringify({
      success: true,
      result,
      stats,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 智能缓存操作失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '操作失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
