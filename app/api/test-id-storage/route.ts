/**
 * 测试ID持久化存储功能的API端点
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试ID持久化存储功能...');
    
    // 动态导入以避免初始化问题
    const { globalIdManager } = await import("@/lib/services/vector-database/global-id-system");
    const { idStorageManager } = await import("@/lib/storage/id-storage");
    
    const results: any = {
      success: true,
      tests: [],
      storageStats: null,
      persistenceTest: null
    };
    
    // 1. 初始化系统
    console.log('📋 初始化ID管理器和存储系统...');
    await globalIdManager.initialize();
    
    results.tests.push({
      name: '系统初始化',
      success: true,
      message: 'ID管理器和存储系统初始化成功'
    });
    
    // 2. 生成一些测试ID
    console.log('🆔 生成测试ID...');
    
    const testIds = [];
    for (let i = 0; i < 5; i++) {
      const userInputId = await globalIdManager.generateUserInputId();
      const derivedId = await globalIdManager.generateDerivedId(
        userInputId,
        'derived_chunk',
        'vector'
      );
      
      // 更新内容元数据
      await globalIdManager.updateContentMetadata(userInputId, {
        contentLength: 100 + i * 10,
        contentHash: `test-hash-${i}`
      });
      
      await globalIdManager.updateContentMetadata(derivedId, {
        contentLength: 1536,
        contentHash: `vector-hash-${i}`
      });
      
      testIds.push({ userInputId, derivedId });
    }
    
    results.tests.push({
      name: 'ID生成测试',
      success: true,
      count: testIds.length * 2,
      testIds: testIds
    });
    
    // 3. 获取初始统计
    const initialStats = globalIdManager.getTodayStats();
    const initialStorageStats = await globalIdManager.getStorageStats();
    
    results.tests.push({
      name: '初始统计获取',
      success: true,
      todayStats: {
        date: initialStats?.date,
        currentTurn: initialStats?.currentTurn,
        totalInputs: initialStats?.totalInputs,
        totalDerivations: initialStats?.totalDerivations
      },
      storageStats: {
        fileSize: initialStorageStats.fileSize,
        backupCount: initialStorageStats.backupCount,
        autoSaveEnabled: initialStorageStats.autoSaveEnabled,
        lastSaveTime: initialStorageStats.lastSaveTime.toISOString()
      }
    });
    
    // 4. 强制保存数据
    console.log('💾 强制保存数据到存储...');
    await globalIdManager.forceSave();
    
    results.tests.push({
      name: '强制保存测试',
      success: true,
      message: '数据已强制保存到存储'
    });
    
    // 5. 验证ID血缘关系
    console.log('🔗 验证ID血缘关系...');
    
    const bloodlineTests = [];
    for (const { userInputId, derivedId } of testIds.slice(0, 2)) {
      const userMetadata = globalIdManager.getIdMetadata(userInputId);
      const derivedMetadata = globalIdManager.getIdMetadata(derivedId);
      const sourceChain = globalIdManager.getSourceChain(derivedId);
      const childIds = globalIdManager.getChildIds(userInputId);
      
      bloodlineTests.push({
        userInputId,
        derivedId,
        userMetadata: {
          type: userMetadata?.type,
          contentLength: userMetadata?.contentLength,
          contentHash: userMetadata?.contentHash,
          derivationLevel: userMetadata?.derivationLevel
        },
        derivedMetadata: {
          type: derivedMetadata?.type,
          parentId: derivedMetadata?.parentId,
          contentLength: derivedMetadata?.contentLength,
          contentHash: derivedMetadata?.contentHash,
          derivationLevel: derivedMetadata?.derivationLevel
        },
        sourceChain,
        childIds
      });
    }
    
    results.tests.push({
      name: 'ID血缘关系验证',
      success: true,
      bloodlineTests
    });
    
    // 6. 测试存储统计
    console.log('📊 获取存储统计信息...');
    
    const finalStorageStats = await globalIdManager.getStorageStats();
    results.storageStats = {
      fileSize: finalStorageStats.fileSize,
      lastModified: finalStorageStats.lastModified.toISOString(),
      backupCount: finalStorageStats.backupCount,
      autoSaveEnabled: finalStorageStats.autoSaveEnabled,
      lastSaveTime: finalStorageStats.lastSaveTime.toISOString()
    };
    
    // 7. 持久化测试：模拟重启
    console.log('🔄 测试持久化：模拟系统重启...');
    
    // 记录当前状态
    const beforeRestart = {
      totalIds: testIds.length * 2,
      todayStats: globalIdManager.getTodayStats()
    };
    
    // 销毁当前实例（模拟关闭）
    await globalIdManager.destroy();
    
    // 重新初始化（模拟重启）
    const { globalIdManager: newManager } = await import("@/lib/services/vector-database/global-id-system");
    await newManager.initialize();
    
    // 验证数据是否正确恢复
    const afterRestart = {
      todayStats: newManager.getTodayStats(),
      firstTestId: newManager.getIdMetadata(testIds[0].userInputId),
      lastTestId: newManager.getIdMetadata(testIds[testIds.length - 1].derivedId)
    };
    
    const persistenceSuccess = 
      afterRestart.todayStats?.totalInputs === beforeRestart.todayStats?.totalInputs &&
      afterRestart.todayStats?.totalDerivations === beforeRestart.todayStats?.totalDerivations &&
      afterRestart.firstTestId !== null &&
      afterRestart.lastTestId !== null;
    
    results.persistenceTest = {
      success: persistenceSuccess,
      beforeRestart,
      afterRestart,
      message: persistenceSuccess ? '持久化测试通过' : '持久化测试失败'
    };
    
    results.tests.push({
      name: '持久化重启测试',
      success: persistenceSuccess,
      message: persistenceSuccess ? '数据在重启后正确恢复' : '数据恢复失败'
    });
    
    console.log('✅ ID持久化存储测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { action } = await req.json();
    
    const { globalIdManager } = await import("@/lib/services/vector-database/global-id-system");
    await globalIdManager.initialize();
    
    let result: any = {};
    
    switch (action) {
      case 'forceSave':
        await globalIdManager.forceSave();
        result = { message: '数据已强制保存' };
        break;
        
      case 'getStats':
        const todayStats = globalIdManager.getTodayStats();
        const storageStats = await globalIdManager.getStorageStats();
        result = { todayStats, storageStats };
        break;
        
      case 'cleanup':
        // 这里可以添加手动清理功能
        result = { message: '清理功能暂未实现' };
        break;
        
      default:
        throw new Error(`未知操作: ${action}`);
    }
    
    return new Response(JSON.stringify({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 操作失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '操作失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
