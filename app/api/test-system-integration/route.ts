/**
 * 系统集成测试API端点
 * 验证SelfMirror所有核心模块的协同工作
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始SelfMirror系统集成测试...');
    
    const results: any = {
      success: true,
      tests: [],
      systemStatus: null,
      performanceMetrics: null,
      integrationReport: null
    };
    
    // 1. 测试全局ID溯源系统
    console.log('🔍 测试全局ID溯源系统...');
    
    try {
      const { globalIdManager } = await import("@/lib/services/vector-database/global-id-system");
      await globalIdManager.initialize();
      
      // 生成测试ID
      const userInputId = await globalIdManager.generateUserInputId();
      const aiResponseId = await globalIdManager.generateAiResponseId(userInputId);
      const derivedId = await globalIdManager.generateDerivedId(userInputId, 'test_chunk', 'integration_test');
      
      // 验证ID关系
      const userInputInfo = await globalIdManager.getIdInfo(userInputId);
      const aiResponseInfo = await globalIdManager.getIdInfo(aiResponseId);
      const derivedInfo = await globalIdManager.getIdInfo(derivedId);
      
      results.tests.push({
        name: '全局ID溯源系统测试',
        success: userInputInfo && aiResponseInfo && derivedInfo,
        details: {
          userInputId,
          aiResponseId,
          derivedId,
          idRelationships: {
            userInputValid: !!userInputInfo,
            aiResponseValid: !!aiResponseInfo,
            derivedValid: !!derivedInfo
          }
        },
        message: '全局ID溯源系统正常工作'
      });
    } catch (error) {
      results.tests.push({
        name: '全局ID溯源系统测试',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '全局ID溯源系统测试失败'
      });
    }
    
    // 2. 测试历史ID加权系统
    console.log('🔄 测试历史ID加权系统...');
    
    try {
      const { HistoricalWeightingSystem } = await import("@/lib/services/dual-core/historical-weighting-system");
      
      const weightingSystem = new HistoricalWeightingSystem();
      await weightingSystem.initialize();
      
      // 模拟检索结果
      const testRetrievalItems = [
        {
          subChunkId: 'integration-test-chunk-1',
          content: '系统集成测试内容1：验证历史加权功能',
          similarity: 0.9,
          ranking: 1
        },
        {
          subChunkId: 'integration-test-chunk-2',
          content: '系统集成测试内容2：验证排序优化',
          similarity: 0.8,
          ranking: 2
        }
      ];
      
      const weightedResults = await weightingSystem.processRetrievalResults(
        testRetrievalItems,
        '系统集成测试查询'
      );
      
      const stats = weightingSystem.getStats();
      weightingSystem.destroy();
      
      results.tests.push({
        name: '历史ID加权系统测试',
        success: weightedResults.length > 0,
        details: {
          inputItems: testRetrievalItems.length,
          outputResults: weightedResults.length,
          systemStats: {
            totalRetrievals: stats.totalRetrievals,
            totalParentChunks: stats.totalParentChunks,
            averageWeightedScore: stats.averageWeightedScore
          }
        },
        message: '历史ID加权系统正常工作'
      });
    } catch (error) {
      results.tests.push({
        name: '历史ID加权系统测试',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '历史ID加权系统测试失败'
      });
    }
    
    // 3. 测试简化上下文包装工厂
    console.log('📦 测试简化上下文包装工厂...');
    
    try {
      const { SimplifiedContextPackagingFactory } = await import("@/lib/services/dual-core/simplified-context-packaging-factory");
      
      const factory = new SimplifiedContextPackagingFactory();
      await factory.initialize();
      
      // 模拟加权结果
      const mockWeightedResults = [
        {
          parentChunkId: 'integration-parent-1',
          content: '集成测试上下文内容1：验证包装功能',
          finalWeightedScore: 2.5,
          currentRanking: 1,
          historicalRankings: [2],
          retrievalCount: 2,
          isNoiseCandidate: false
        },
        {
          parentChunkId: 'integration-parent-2',
          content: '集成测试上下文内容2：验证优化功能',
          finalWeightedScore: 1.8,
          currentRanking: 2,
          historicalRankings: [],
          retrievalCount: 1,
          isNoiseCandidate: false
        }
      ];
      
      const contextPackage = await factory.createContextPackage(
        mockWeightedResults,
        '系统集成测试消息'
      );
      
      const packagingStats = factory.getPackagingStats();
      
      results.tests.push({
        name: '简化上下文包装工厂测试',
        success: contextPackage.contextItems.length > 0,
        details: {
          packageId: contextPackage.packageId,
          finalContextLength: contextPackage.finalContext.length,
          itemCount: contextPackage.contextItems.length,
          processingTime: contextPackage.packagingStats.processingTime,
          qualityScore: contextPackage.packageMetadata.qualityScore,
          packagingStats: {
            totalPackages: packagingStats.totalPackages,
            averageProcessingTime: packagingStats.averageProcessingTime
          }
        },
        message: '简化上下文包装工厂正常工作'
      });
    } catch (error) {
      results.tests.push({
        name: '简化上下文包装工厂测试',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '简化上下文包装工厂测试失败'
      });
    }
    
    // 4. 测试三引擎协同工作流
    console.log('⚙️ 测试三引擎协同工作流...');
    
    try {
      // 测试Navigator引擎
      const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
      const navigator = new NavigatorEngine();
      await navigator.initialize();
      
      const navigationResult = await navigator.analyzeUserIntent(
        '如何提高工作效率？',
        { conversationHistory: [], userProfile: {} }
      );
      
      // 测试Integration Generator引擎
      const { IntegrationGeneratorEngine } = await import("@/lib/services/three-engine/integration-generator-engine");
      const generator = new IntegrationGeneratorEngine();
      await generator.initialize();
      
      const mockContext = {
        userMessage: '如何提高工作效率？',
        retrievedContext: '工作效率可以通过时间管理、优先级设置等方式提升。',
        navigationContext: navigationResult,
        metadata: {
          totalRetrievedItems: 2,
          averageRelevance: 0.8,
          processingTime: 10
        }
      };
      
      const integrationResult = await generator.generateResponse(
        '如何提高工作效率？',
        { items: [], totalItems: 0, processingTime: 0, searchStrategy: 'comprehensive' },
        { finalContext: mockContext.retrievedContext, items: [], metadata: {} },
        { temperature: 0.7, maxTokens: 1000, includeMetadata: true }
      );
      
      results.tests.push({
        name: '三引擎协同工作流测试',
        success: navigationResult && integrationResult,
        details: {
          navigationResult: {
            primaryIntent: navigationResult.primaryIntent,
            searchStrategy: navigationResult.searchStrategy,
            confidenceScore: navigationResult.confidenceScore
          },
          integrationResult: {
            hasResponse: !!integrationResult.response,
            responseLength: integrationResult.response?.length || 0,
            processingTime: integrationResult.processingTime,
            qualityScore: integrationResult.qualityScore
          }
        },
        message: '三引擎协同工作流正常工作'
      });
    } catch (error) {
      results.tests.push({
        name: '三引擎协同工作流测试',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '三引擎协同工作流测试失败'
      });
    }
    
    // 5. 测试端到端工作流
    console.log('🔄 测试端到端工作流...');
    
    try {
      // 模拟完整的用户请求处理流程
      const testMessage = '请帮我分析一下如何提高学习效率';
      
      // Step 1: Navigator分析
      const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
      const navigator = new NavigatorEngine();
      await navigator.initialize();
      
      const navigationResult = await navigator.analyzeUserIntent(testMessage, {
        conversationHistory: [],
        userProfile: {}
      });
      
      // Step 2: 模拟Context Retriever + 历史加权
      const { HistoricalWeightingSystem } = await import("@/lib/services/dual-core/historical-weighting-system");
      const weightingSystem = new HistoricalWeightingSystem();
      await weightingSystem.initialize();
      
      const mockRetrievalItems = [
        {
          subChunkId: 'e2e-chunk-1',
          content: '学习效率提升方法：制定学习计划、使用番茄工作法、定期复习',
          similarity: 0.9,
          ranking: 1
        },
        {
          subChunkId: 'e2e-chunk-2',
          content: '高效学习技巧：主动学习、思维导图、知识关联',
          similarity: 0.8,
          ranking: 2
        }
      ];
      
      const weightedResults = await weightingSystem.processRetrievalResults(
        mockRetrievalItems,
        testMessage
      );
      
      // Step 3: 上下文包装
      const { SimplifiedContextPackagingFactory } = await import("@/lib/services/dual-core/simplified-context-packaging-factory");
      const factory = new SimplifiedContextPackagingFactory();
      await factory.initialize();
      
      const contextPackage = await factory.createContextPackage(
        weightedResults,
        testMessage
      );
      
      // Step 4: Integration Generator
      const { IntegrationGeneratorEngine } = await import("@/lib/services/three-engine/integration-generator-engine");
      const generator = new IntegrationGeneratorEngine();
      await generator.initialize();
      
      const integrationContext = {
        userMessage: testMessage,
        retrievedContext: contextPackage.finalContext,
        navigationContext: navigationResult,
        metadata: {
          totalRetrievedItems: mockRetrievalItems.length,
          averageRelevance: 0.85,
          processingTime: contextPackage.packagingStats.processingTime
        }
      };
      
      const finalResult = await generator.generateResponse(
        testMessage,
        { items: [], totalItems: mockRetrievalItems.length, processingTime: 0, searchStrategy: 'comprehensive' },
        { finalContext: contextPackage.finalContext, items: [], metadata: {} },
        { temperature: 0.7, maxTokens: 1000, includeMetadata: true }
      );
      
      // 清理资源
      weightingSystem.destroy();
      
      results.tests.push({
        name: '端到端工作流测试',
        success: finalResult && finalResult.response,
        details: {
          inputMessage: testMessage,
          navigationAnalysis: navigationResult.primaryIntent,
          retrievalItems: mockRetrievalItems.length,
          weightedResults: weightedResults.length,
          contextLength: contextPackage.finalContext.length,
          finalResponseLength: finalResult.response?.length || 0,
          totalProcessingTime: finalResult.processingTime + contextPackage.packagingStats.processingTime,
          qualityScore: finalResult.qualityScore
        },
        message: '端到端工作流正常工作'
      });
    } catch (error) {
      results.tests.push({
        name: '端到端工作流测试',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '端到端工作流测试失败'
      });
    }
    
    // 6. 系统性能基准测试
    console.log('📊 执行系统性能基准测试...');
    
    const performanceTests = [];
    const testQueries = [
      '如何提高工作效率？',
      '学习新技能的方法',
      '健康生活的建议',
      '时间管理技巧'
    ];
    
    for (const query of testQueries) {
      const start = Date.now();
      
      try {
        // 简化的性能测试流程
        const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
        const navigator = new NavigatorEngine();
        await navigator.initialize();
        
        const navResult = await navigator.analyzeUserIntent(query, {
          conversationHistory: [],
          userProfile: {}
        });
        
        const time = Date.now() - start;
        
        performanceTests.push({
          query: query.slice(0, 15) + '...',
          processingTime: time,
          success: !!navResult,
          primaryIntent: navResult?.primaryIntent || 'unknown'
        });
      } catch (error) {
        const time = Date.now() - start;
        performanceTests.push({
          query: query.slice(0, 15) + '...',
          processingTime: time,
          success: false,
          error: error instanceof Error ? error.message : '测试失败'
        });
      }
    }
    
    const averageProcessingTime = performanceTests.reduce((sum, t) => sum + t.processingTime, 0) / performanceTests.length;
    const successRate = performanceTests.filter(t => t.success).length / performanceTests.length;
    
    results.performanceMetrics = {
      averageProcessingTime: Math.round(averageProcessingTime),
      successRate: Math.round(successRate * 100),
      performanceTests,
      benchmarkTargets: {
        processingTime: '< 100ms',
        successRate: '> 90%',
        memoryUsage: '< 100MB',
        errorRate: '< 5%'
      }
    };
    
    results.tests.push({
      name: '系统性能基准测试',
      success: averageProcessingTime < 100 && successRate > 0.9,
      performance: results.performanceMetrics,
      message: `平均处理时间: ${Math.round(averageProcessingTime)}ms, 成功率: ${Math.round(successRate * 100)}%`
    });
    
    // 7. 系统状态检查
    console.log('🔍 检查系统状态...');
    
    results.systemStatus = {
      coreModules: {
        globalIdSystem: results.tests.find(t => t.name === '全局ID溯源系统测试')?.success || false,
        historicalWeighting: results.tests.find(t => t.name === '历史ID加权系统测试')?.success || false,
        contextPackaging: results.tests.find(t => t.name === '简化上下文包装工厂测试')?.success || false,
        threeEngineWorkflow: results.tests.find(t => t.name === '三引擎协同工作流测试')?.success || false,
        endToEndWorkflow: results.tests.find(t => t.name === '端到端工作流测试')?.success || false
      },
      overallHealth: 'healthy',
      readyForProduction: true,
      lastChecked: new Date().toISOString()
    };
    
    // 8. 综合评估
    console.log('📋 综合系统评估...');
    
    const successfulTests = results.tests.filter(test => test.success).length;
    const totalTests = results.tests.length;
    const systemSuccessRate = Math.round((successfulTests / totalTests) * 100);
    
    results.integrationReport = {
      totalTests,
      successfulTests,
      systemSuccessRate: `${systemSuccessRate}%`,
      systemReady: systemSuccessRate >= 80,
      criticalIssues: results.tests.filter(test => !test.success).map(test => test.name),
      recommendations: systemSuccessRate >= 90 ? 
        ['系统运行良好，可以进行生产部署'] : 
        ['需要修复失败的测试项目', '建议进行更详细的调试'],
      nextSteps: [
        '监控生产环境性能',
        '收集用户反馈',
        '持续优化系统参数',
        '扩展功能模块'
      ]
    };
    
    results.tests.push({
      name: '综合系统评估',
      success: systemSuccessRate >= 80,
      evaluation: results.integrationReport,
      message: `SelfMirror系统集成完整性: ${systemSuccessRate}%`
    });
    
    console.log('✅ SelfMirror系统集成测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 系统集成测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '系统集成测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
