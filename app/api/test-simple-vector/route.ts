/**
 * 简单的向量数据库测试API
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始简单的向量数据库测试...');
    
    // 动态导入以避免初始化问题
    const { globalIdManager } = await import("@/lib/services/vector-database/global-id-system");
    const { DualVectorManager } = await import("@/lib/services/vector-database/dual-vector-manager");
    
    // 初始化全局ID管理器
    await globalIdManager.initialize();
    console.log('✅ 全局ID管理器初始化成功');
    
    // 创建双向量管理器实例
    const hotConfig = { name: 'hot_store', type: 'hot', dimensions: 1536 };
    const coldConfig = { name: 'cold_store', type: 'cold', dimensions: 1536 };
    const dualManager = new DualVectorManager(hotConfig, coldConfig);
    
    await dualManager.initialize();
    console.log('✅ 双向量管理器初始化成功');
    
    // 测试全局ID生成
    const userInputId = await globalIdManager.generateUserInputId();
    console.log(`✅ 生成用户输入ID: ${userInputId}`);
    
    const derivedId = await globalIdManager.generateDerivedId(
      userInputId,
      'derived_chunk',
      'vector'
    );
    console.log(`✅ 生成衍生ID: ${derivedId}`);
    
    // 测试向量添加
    const testVector = Array.from({length: 1536}, () => Math.random());
    const metadata = {
      chunkId: 'test-chunk-' + Date.now(),
      globalId: derivedId,
      sourceDocumentType: 'daily_insight_hot' as const,
      sourceDocumentId: userInputId,
      parentChunk: '测试父块内容',
      childChunk: '测试意义子块内容',
      createdAt: new Date().toISOString()
    };
    
    const chunkId = await dualManager.addVector(testVector, metadata);
    console.log(`✅ 向量添加成功: ${chunkId}`);
    
    // 获取今日统计
    const todayStats = globalIdManager.getTodayStats();
    
    // 获取系统统计
    const systemStats = await dualManager.getSystemStats();
    
    const result = {
      success: true,
      userInputId,
      derivedId,
      chunkId,
      todayStats: {
        date: todayStats?.date,
        currentTurn: todayStats?.currentTurn,
        totalInputs: todayStats?.totalInputs,
        totalDerivations: todayStats?.totalDerivations
      },
      systemStats: {
        hotStore: {
          totalVectors: systemStats.hotStore.totalVectors,
          averageTemperature: systemStats.hotStore.averageTemperature
        },
        coldStore: {
          totalVectors: systemStats.coldStore.totalVectors,
          indexNodes: systemStats.coldStore.indexNodes
        }
      },
      timestamp: new Date().toISOString()
    };
    
    console.log('✅ 简单向量数据库测试完成');
    
    return new Response(JSON.stringify(result, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
