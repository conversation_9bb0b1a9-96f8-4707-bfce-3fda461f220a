import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateText } from "ai";
import { buildDeepRefinementContext, validateContext, getContextStats } from "@/lib/services/context-manager";
import { processDeepRefinementOutput, validateOutput, getOutputStats } from "@/lib/services/output-processor";

export const runtime = "nodejs";
export const maxDuration = 120; // 深度精炼需要更长时间

// 硬编码的配置（仅用于本地开发）
const API_KEY = "AIzaSyDHdlBWyQXDBbQVBcjr6zvNayUgkZd6N1w";
const MODEL_NAME = "gemini-2.5-flash-preview-05-20";
const PROXY_URL = "http://127.0.0.1:7897";

// 创建自定义的fetch函数，支持代理
const createCustomFetch = () => {
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { ProxyAgent, fetch: undiciFetch } = require('undici');
    const proxyAgent = new ProxyAgent(PROXY_URL);
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return async (url: string, options: any) => {
      console.log('🌐 使用代理:', PROXY_URL);
      
      return undiciFetch(url, {
        ...options,
        dispatcher: proxyAgent,
      });
    };
  } else {
    return fetch;
  }
};

// 配置Google AI客户端
const google = createGoogleGenerativeAI({
  apiKey: API_KEY,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fetch: createCustomFetch() as any,
});

export async function POST(req: Request) {
  console.log('🔬 收到深度精炼请求');

  try {
    const { triggerReason } = await req.json();
    console.log('🎯 触发原因:', triggerReason);

    // 构建深度精炼上下文
    const context = await buildDeepRefinementContext();

    // 验证上下文质量
    if (!validateContext(context)) {
      throw new Error('上下文构建失败');
    }

    // 输出上下文统计信息
    const stats = getContextStats(context);
    console.log('📊 上下文统计:', stats);
    
    console.log('🤖 开始调用 Gemini API (深度精炼模式)...');

    const result = await generateText({
      model: google(MODEL_NAME),
      prompt: `${context.systemPrompt}\n\n${context.analysisContent}`,
      temperature: 0.9,
      maxTokens: 3000,
    });

    console.log('✨ 深度精炼分析完成');

    // 处理AI输出
    if (result.text) {
      const processingTime = Date.now() - Date.now(); // 这里应该记录实际的处理时间
      const output = await processDeepRefinementOutput(result.text, processingTime);

      // 验证输出质量
      if (validateOutput(output)) {
        const stats = getOutputStats(output);
        console.log('📊 输出统计:', stats);

        return new Response(
          JSON.stringify({
            success: true,
            analysis: output.analysisResult,
            updatedFiles: output.updatedFiles,
            stats: stats,
            message: '深度精炼完成，记忆系统已更新'
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      } else {
        throw new Error('输出质量验证失败');
      }
    } else {
      throw new Error('AI未生成有效内容');
    }

  } catch (error) {
    console.error('💥 深度精炼失败:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: '深度精炼失败，请稍后重试',
        details: error instanceof Error ? error.message : '未知错误'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
