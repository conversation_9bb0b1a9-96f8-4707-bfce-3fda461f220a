/**
 * 测试批量向量添加和搜索的全局ID集成
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试批量向量添加和搜索...');
    
    // 动态导入以避免初始化问题
    const { globalIdManager } = await import("@/lib/services/vector-database/global-id-system");
    const { DualVectorManager } = await import("@/lib/services/vector-database/dual-vector-manager");
    
    // 初始化系统
    await globalIdManager.initialize();
    
    const hotConfig = { name: 'hot_store', type: 'hot', dimensions: 1536 };
    const coldConfig = { name: 'cold_store', type: 'cold', dimensions: 1536 };
    const dualManager = new DualVectorManager(hotConfig, coldConfig);
    await dualManager.initialize();
    
    console.log('✅ 系统初始化完成');
    
    const results: any = {
      success: true,
      batchTests: [],
      searchTests: [],
      globalIdStats: null,
      systemStats: null
    };
    
    // 1. 测试批量添加（Hot Store类型）
    console.log('🔥 测试Hot Store批量添加...');
    
    const hotBatch = [];
    for (let i = 0; i < 3; i++) {
      const baseId = await globalIdManager.generateUserInputId();
      const derivedId = await globalIdManager.generateDerivedId(
        baseId,
        'derived_chunk',
        'vector'
      );
      
      hotBatch.push({
        vector: Array.from({length: 1536}, () => Math.random()),
        metadata: {
          chunkId: `hot-batch-chunk-${Date.now()}-${i}`,
          globalId: derivedId,
          sourceDocumentType: 'daily_insight_hot' as const,
          sourceDocumentId: baseId,
          parentChunk: `热存储批量测试父块 ${i + 1}`,
          childChunk: `热存储批量测试意义子块 ${i + 1}`,
          createdAt: new Date().toISOString()
        }
      });
    }
    
    const hotChunkIds = await dualManager.addBatch(hotBatch);
    results.batchTests.push({
      name: 'Hot Store批量添加',
      success: true,
      count: hotChunkIds.length,
      chunkIds: hotChunkIds,
      globalIds: hotBatch.map(item => item.metadata.globalId)
    });
    
    // 2. 测试批量添加（Cold Store类型）
    console.log('❄️ 测试Cold Store批量添加...');
    
    const coldBatch = [];
    for (let i = 0; i < 2; i++) {
      const baseId = await globalIdManager.generateUserInputId();
      const derivedId = await globalIdManager.generateDerivedId(
        baseId,
        'derived_chunk',
        'vector'
      );
      
      coldBatch.push({
        vector: Array.from({length: 1536}, () => Math.random()),
        metadata: {
          chunkId: `cold-batch-chunk-${Date.now()}-${i}`,
          globalId: derivedId,
          sourceDocumentType: 'user_profile' as const,
          sourceDocumentId: baseId,
          parentChunk: `冷存储批量测试父块 ${i + 1}`,
          childChunk: `冷存储批量测试意义子块 ${i + 1}`,
          createdAt: new Date().toISOString()
        }
      });
    }
    
    const coldChunkIds = await dualManager.addBatch(coldBatch);
    results.batchTests.push({
      name: 'Cold Store批量添加',
      success: true,
      count: coldChunkIds.length,
      chunkIds: coldChunkIds,
      globalIds: coldBatch.map(item => item.metadata.globalId)
    });
    
    // 3. 测试向量搜索
    console.log('🔍 测试向量搜索...');
    
    const queryVector = Array.from({length: 1536}, () => Math.random());
    const searchResults = await dualManager.searchVector(queryVector, {
      searchStrategy: 'parallel',
      maxResults: 10,
      hotStoreWeight: 0.6,
      coldStoreWeight: 0.4,
      mergeStrategy: 'score_based'
    });
    
    results.searchTests.push({
      name: '并行搜索测试',
      success: true,
      totalResults: searchResults.mergedResults.length,
      hotResults: searchResults.hotResults.length,
      coldResults: searchResults.coldResults.length,
      searchTime: searchResults.searchStats.totalTime,
      results: searchResults.mergedResults.slice(0, 5).map(r => ({
        chunkId: r.metadata.chunkId,
        globalId: r.metadata.globalId,
        similarity: r.similarity,
        sourceType: r.metadata.sourceDocumentType,
        store: r.metadata.sourceDocumentType?.includes('hot') ? 'hot' : 'cold'
      }))
    });
    
    // 4. 测试热优先搜索
    console.log('🔥 测试热优先搜索...');
    
    const hotFirstResults = await dualManager.searchVector(queryVector, {
      searchStrategy: 'hot_first',
      maxResults: 5
    });
    
    results.searchTests.push({
      name: '热优先搜索测试',
      success: true,
      totalResults: hotFirstResults.mergedResults.length,
      searchTime: hotFirstResults.searchStats.totalTime,
      results: hotFirstResults.mergedResults.map(r => ({
        chunkId: r.metadata.chunkId,
        globalId: r.metadata.globalId,
        similarity: r.similarity,
        sourceType: r.metadata.sourceDocumentType
      }))
    });
    
    // 5. 验证全局ID统计
    const todayStats = globalIdManager.getTodayStats();
    results.globalIdStats = {
      date: todayStats?.date,
      currentTurn: todayStats?.currentTurn,
      totalInputs: todayStats?.totalInputs,
      totalDerivations: todayStats?.totalDerivations
    };
    
    // 6. 获取系统统计
    const systemStats = await dualManager.getSystemStats();
    results.systemStats = {
      hotStore: {
        totalVectors: systemStats.hotStore.totalVectors,
        averageTemperature: systemStats.hotStore.averageTemperature
      },
      coldStore: {
        totalVectors: systemStats.coldStore.totalVectors,
        indexNodes: systemStats.coldStore.indexNodes
      },
      searchStats: {
        totalSearches: systemStats.searchStats.totalSearches,
        averageSearchTime: systemStats.searchStats.averageSearchTime
      },
      dataDistribution: {
        hotPercentage: systemStats.dataDistribution.hotPercentage,
        coldPercentage: systemStats.dataDistribution.coldPercentage
      }
    };
    
    // 7. 验证ID血缘关系
    if (searchResults.mergedResults.length > 0) {
      const firstResult = searchResults.mergedResults[0];
      const globalId = firstResult.metadata.globalId;
      
      if (globalId) {
        const metadata = globalIdManager.getIdMetadata(globalId);
        const sourceChain = globalIdManager.getSourceChain(globalId);
        
        results.bloodlineTest = {
          globalId,
          metadata: {
            type: metadata?.type,
            parentId: metadata?.parentId,
            contentLength: metadata?.contentLength,
            createdAt: metadata?.createdAt
          },
          sourceChain
        };
      }
    }
    
    console.log('✅ 批量向量测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
