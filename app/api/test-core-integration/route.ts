/**
 * 核心系统集成测试API端点
 * 专注于验证SelfMirror核心模块的基本功能
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始SelfMirror核心系统集成测试...');
    
    const results: any = {
      success: true,
      tests: [],
      coreSystemStatus: null,
      integrationSummary: null
    };
    
    // 1. 测试全局ID溯源系统基础功能
    console.log('🔍 测试全局ID溯源系统基础功能...');
    
    try {
      const { globalIdManager } = await import("@/lib/services/vector-database/global-id-system");
      await globalIdManager.initialize();
      
      // 生成测试ID
      const userInputId = await globalIdManager.generateUserInputId();
      const aiResponseId = await globalIdManager.generateAiResponseId(userInputId);
      const derivedId = await globalIdManager.generateDerivedId(userInputId, 'derived_chunk', 'text');
      
      // 验证ID格式
      const userInputValid = globalIdManager.validateId(userInputId);
      const aiResponseValid = globalIdManager.validateId(aiResponseId);
      const derivedValid = globalIdManager.validateId(derivedId);
      
      // 验证ID信息获取
      const userInputInfo = globalIdManager.getIdInfo(userInputId);
      const aiResponseInfo = globalIdManager.getIdInfo(aiResponseId);
      
      results.tests.push({
        name: '全局ID溯源系统基础功能',
        success: userInputValid && aiResponseValid && derivedValid && userInputInfo && aiResponseInfo,
        details: {
          generatedIds: {
            userInputId,
            aiResponseId,
            derivedId
          },
          validationResults: {
            userInputValid,
            aiResponseValid,
            derivedValid
          },
          infoRetrieval: {
            userInputInfoExists: !!userInputInfo,
            aiResponseInfoExists: !!aiResponseInfo
          }
        },
        message: '全局ID溯源系统基础功能正常'
      });
    } catch (error) {
      results.tests.push({
        name: '全局ID溯源系统基础功能',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '全局ID溯源系统基础功能测试失败'
      });
    }
    
    // 2. 测试历史ID加权系统核心功能
    console.log('🔄 测试历史ID加权系统核心功能...');
    
    try {
      const { HistoricalWeightingSystem } = await import("@/lib/services/dual-core/historical-weighting-system");
      
      const weightingSystem = new HistoricalWeightingSystem({
        maxHistoryDepth: 3,
        decayCoefficients: [1.0, 0.8, 0.6],
        topicChangeThreshold: 0.5,
        noiseEliminationThreshold: 0.1
      });
      
      await weightingSystem.initialize();
      
      // 第一次检索
      const firstRetrievalItems = [
        {
          subChunkId: 'core-test-chunk-1',
          content: '核心测试内容1：系统集成验证',
          similarity: 0.9,
          ranking: 1
        },
        {
          subChunkId: 'core-test-chunk-2',
          content: '核心测试内容2：功能验证',
          similarity: 0.8,
          ranking: 2
        }
      ];
      
      const firstResults = await weightingSystem.processRetrievalResults(
        firstRetrievalItems,
        '核心系统测试查询'
      );
      
      // 第二次检索（相同内容，验证历史加权）
      const secondResults = await weightingSystem.processRetrievalResults(
        firstRetrievalItems,
        '核心系统测试查询重复'
      );
      
      const stats = weightingSystem.getStats();
      const cacheStatus = weightingSystem.getCacheStatus();
      
      weightingSystem.destroy();
      
      results.tests.push({
        name: '历史ID加权系统核心功能',
        success: firstResults.length > 0 && secondResults.length > 0,
        details: {
          firstRetrievalResults: firstResults.length,
          secondRetrievalResults: secondResults.length,
          weightingImprovement: secondResults.length > 0 && firstResults.length > 0 ? 
            secondResults[0].finalWeightedScore >= firstResults[0].finalWeightedScore : false,
          systemStats: {
            totalRetrievals: stats.totalRetrievals,
            totalParentChunks: stats.totalParentChunks,
            cacheSize: cacheStatus.cacheSize
          }
        },
        message: '历史ID加权系统核心功能正常'
      });
    } catch (error) {
      results.tests.push({
        name: '历史ID加权系统核心功能',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '历史ID加权系统核心功能测试失败'
      });
    }
    
    // 3. 测试简化上下文包装工厂核心功能
    console.log('📦 测试简化上下文包装工厂核心功能...');
    
    try {
      const { SimplifiedContextPackagingFactory } = await import("@/lib/services/dual-core/simplified-context-packaging-factory");
      
      const factory = new SimplifiedContextPackagingFactory({
        maxContextLength: 1000,
        maxContextItems: 5,
        qualityThresholds: {
          minWeightedScore: 0.1,
          minContentLength: 10
        }
      });
      
      await factory.initialize();
      
      // 创建测试加权结果
      const testWeightedResults = [
        {
          parentChunkId: 'core-parent-1',
          content: '核心测试上下文内容1：这是一个有效的测试内容，用于验证包装功能',
          finalWeightedScore: 2.5,
          currentRanking: 1,
          historicalRankings: [2],
          retrievalCount: 2,
          isNoiseCandidate: false
        },
        {
          parentChunkId: 'core-parent-2',
          content: '核心测试上下文内容2：这是另一个有效的测试内容，用于验证优化功能',
          finalWeightedScore: 1.8,
          currentRanking: 2,
          historicalRankings: [],
          retrievalCount: 1,
          isNoiseCandidate: false
        },
        {
          parentChunkId: 'core-parent-1', // 重复项，测试去重
          content: '核心测试上下文内容1：这是一个有效的测试内容，用于验证包装功能',
          finalWeightedScore: 1.2,
          currentRanking: 3,
          historicalRankings: [],
          retrievalCount: 1,
          isNoiseCandidate: false
        }
      ];
      
      const contextPackage = await factory.createContextPackage(
        testWeightedResults,
        '核心系统测试消息'
      );
      
      const packagingStats = factory.getPackagingStats();
      
      results.tests.push({
        name: '简化上下文包装工厂核心功能',
        success: contextPackage.contextItems.length > 0 && contextPackage.finalContext.length > 0,
        details: {
          packageId: contextPackage.packageId,
          inputItems: testWeightedResults.length,
          outputItems: contextPackage.contextItems.length,
          finalContextLength: contextPackage.finalContext.length,
          duplicatesRemoved: contextPackage.packagingStats.duplicatesRemoved,
          processingTime: contextPackage.packagingStats.processingTime,
          qualityScore: contextPackage.packageMetadata.qualityScore,
          optimizationsApplied: contextPackage.packageMetadata.optimizationsApplied
        },
        message: '简化上下文包装工厂核心功能正常'
      });
    } catch (error) {
      results.tests.push({
        name: '简化上下文包装工厂核心功能',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '简化上下文包装工厂核心功能测试失败'
      });
    }
    
    // 4. 测试Navigator引擎基础功能
    console.log('🧭 测试Navigator引擎基础功能...');
    
    try {
      const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
      
      const navigator = new NavigatorEngine();
      await navigator.initialize();
      
      const testMessages = [
        '如何提高工作效率？',
        '请帮我分析一下学习方法',
        '我想了解健康生活的建议'
      ];
      
      const navigationResults = [];
      for (const message of testMessages) {
        try {
          const result = await navigator.analyzeUserIntent(
            message,
            [], // recentContext
            undefined // userProfile
          );
          navigationResults.push({
            message: message.slice(0, 15) + '...',
            primaryIntent: result.intentAnalysis.primaryIntent,
            searchStrategy: result.searchStrategy,
            confidenceScore: result.qualityMetrics.confidence,
            success: !!result.intentAnalysis.primaryIntent
          });
        } catch (error) {
          console.error(`❌ Navigator测试失败 (${message}):`, error);
          navigationResults.push({
            message: message.slice(0, 15) + '...',
            primaryIntent: undefined,
            searchStrategy: { type: 'unknown' },
            confidenceScore: 0,
            success: false,
            error: error instanceof Error ? error.message : '测试失败'
          });
        }
      }
      
      const successfulNavigations = navigationResults.filter(r => r.success).length;
      
      results.tests.push({
        name: 'Navigator引擎基础功能',
        success: successfulNavigations > 0,
        details: {
          totalTests: testMessages.length,
          successfulNavigations,
          successRate: Math.round((successfulNavigations / testMessages.length) * 100),
          navigationResults
        },
        message: 'Navigator引擎基础功能正常'
      });
    } catch (error) {
      results.tests.push({
        name: 'Navigator引擎基础功能',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: 'Navigator引擎基础功能测试失败'
      });
    }
    
    // 5. 测试双核心系统协同工作
    console.log('🔄 测试双核心系统协同工作...');
    
    try {
      // 模拟完整的双核心工作流
      const testQuery = '请帮我分析如何提高学习效率';
      
      // Step 1: 历史ID加权系统处理
      const { HistoricalWeightingSystem } = await import("@/lib/services/dual-core/historical-weighting-system");
      const weightingSystem = new HistoricalWeightingSystem();
      await weightingSystem.initialize();
      
      const mockRetrievalItems = [
        {
          subChunkId: 'dual-core-chunk-1',
          content: '学习效率提升方法：制定计划、专注练习、定期复习',
          similarity: 0.9,
          ranking: 1
        },
        {
          subChunkId: 'dual-core-chunk-2',
          content: '高效学习技巧：主动学习、思维导图、知识关联',
          similarity: 0.8,
          ranking: 2
        }
      ];
      
      const weightedResults = await weightingSystem.processRetrievalResults(
        mockRetrievalItems,
        testQuery
      );
      
      // Step 2: 简化上下文包装工厂处理
      const { SimplifiedContextPackagingFactory } = await import("@/lib/services/dual-core/simplified-context-packaging-factory");
      const factory = new SimplifiedContextPackagingFactory();
      await factory.initialize();
      
      const contextPackage = await factory.createContextPackage(
        weightedResults,
        testQuery
      );
      
      // 清理资源
      weightingSystem.destroy();
      
      results.tests.push({
        name: '双核心系统协同工作',
        success: weightedResults.length > 0 && contextPackage.contextItems.length > 0,
        details: {
          inputQuery: testQuery,
          weightingResults: weightedResults.length,
          contextPackageItems: contextPackage.contextItems.length,
          finalContextLength: contextPackage.finalContext.length,
          totalProcessingTime: contextPackage.packagingStats.processingTime,
          workflowSuccess: true
        },
        message: '双核心系统协同工作正常'
      });
    } catch (error) {
      results.tests.push({
        name: '双核心系统协同工作',
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        message: '双核心系统协同工作测试失败'
      });
    }
    
    // 6. 核心系统状态评估
    console.log('📊 核心系统状态评估...');
    
    const coreModuleResults = {
      globalIdSystem: results.tests.find(t => t.name === '全局ID溯源系统基础功能')?.success || false,
      historicalWeighting: results.tests.find(t => t.name === '历史ID加权系统核心功能')?.success || false,
      contextPackaging: results.tests.find(t => t.name === '简化上下文包装工厂核心功能')?.success || false,
      navigatorEngine: results.tests.find(t => t.name === 'Navigator引擎基础功能')?.success || false,
      dualCoreWorkflow: results.tests.find(t => t.name === '双核心系统协同工作')?.success || false
    };
    
    const successfulModules = Object.values(coreModuleResults).filter(Boolean).length;
    const totalModules = Object.keys(coreModuleResults).length;
    const systemHealthScore = Math.round((successfulModules / totalModules) * 100);
    
    results.coreSystemStatus = {
      coreModules: coreModuleResults,
      systemHealthScore: `${systemHealthScore}%`,
      readyForProduction: systemHealthScore >= 80,
      criticalModulesWorking: coreModuleResults.globalIdSystem && coreModuleResults.historicalWeighting,
      lastChecked: new Date().toISOString()
    };
    
    // 7. 综合评估
    console.log('📋 综合系统评估...');
    
    const successfulTests = results.tests.filter(test => test.success).length;
    const totalTests = results.tests.length;
    const overallSuccessRate = Math.round((successfulTests / totalTests) * 100);
    
    results.integrationSummary = {
      totalTests,
      successfulTests,
      overallSuccessRate: `${overallSuccessRate}%`,
      systemReady: overallSuccessRate >= 70,
      coreSystemsWorking: successfulModules >= 3,
      recommendations: overallSuccessRate >= 80 ? 
        ['核心系统运行良好', '可以进行进一步集成测试'] : 
        ['需要修复失败的核心模块', '建议逐个调试问题模块'],
      nextSteps: [
        '完善Integration Generator集成',
        '优化系统性能',
        '扩展端到端测试',
        '准备生产环境部署'
      ]
    };
    
    results.tests.push({
      name: '核心系统综合评估',
      success: overallSuccessRate >= 70,
      evaluation: results.integrationSummary,
      message: `SelfMirror核心系统完整性: ${overallSuccessRate}%`
    });
    
    console.log('✅ SelfMirror核心系统集成测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 核心系统集成测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '核心系统集成测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
