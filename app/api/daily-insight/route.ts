import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateText } from "ai";
import { buildDailyInsightContext, validateContext, getContextStats } from "@/lib/services/context-manager";
import { processDailyInsightOutput, validateOutput, getOutputStats } from "@/lib/services/output-processor";

export const runtime = "nodejs";
export const maxDuration = 60; // 每日洞察可能需要更长时间

// 硬编码的配置（仅用于本地开发）
const API_KEY = "AIzaSyDHdlBWyQXDBbQVBcjr6zvNayUgkZd6N1w";
const MODEL_NAME = "gemini-2.5-flash-preview-05-20";
const PROXY_URL = "http://127.0.0.1:7897";

// 创建自定义的fetch函数，支持代理
const createCustomFetch = () => {
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { ProxyAgent, fetch: undiciFetch } = require('undici');
    const proxyAgent = new ProxyAgent(PROXY_URL);
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return async (url: string, options: any) => {
      console.log('🌐 使用代理:', PROXY_URL);
      
      return undiciFetch(url, {
        ...options,
        dispatcher: proxyAgent,
      });
    };
  } else {
    return fetch;
  }
};

// 配置Google AI客户端
const google = createGoogleGenerativeAI({
  apiKey: API_KEY,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fetch: createCustomFetch() as any,
});

export async function POST(req: Request) {
  const startTime = Date.now();
  console.log('🧠 收到每日洞察请求');

  try {
    const { dialogueBlocks } = await req.json();
    console.log('📊 分析对话块数量:', dialogueBlocks?.length);

    // 构建每日洞察上下文
    const context = await buildDailyInsightContext();

    // 验证上下文质量
    if (!validateContext(context)) {
      throw new Error('上下文构建失败');
    }

    // 输出上下文统计信息
    const stats = getContextStats(context);
    console.log('📊 上下文统计:', stats);

    console.log('🤖 开始调用 Gemini API (每日洞察模式)...');

    const result = await generateText({
      model: google(MODEL_NAME),
      prompt: `${context.systemPrompt}\n\n${context.analysisContent}`,
      temperature: 0.8,
      maxTokens: 2000, // 增加token限制
    });

    console.log('✨ 每日洞察生成完成');
    console.log('🔍 AI响应调试:', {
      hasText: !!result.text,
      textLength: result.text?.length || 0,
      textPreview: result.text?.substring(0, 100) || 'null',
      fullResult: result
    });

    // 处理AI输出
    if (result.text) {
      const analysisTime = Date.now() - startTime; // 修复分析时间计算
      const output = await processDailyInsightOutput(result.text, analysisTime);

      // 验证输出质量
      if (validateOutput(output)) {
        const stats = getOutputStats(output);
        console.log('📊 输出统计:', stats);

        return new Response(
          JSON.stringify({
            success: true,
            insight: result.text,
            stats: stats,
            message: '每日洞察生成完成'
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      } else {
        throw new Error('输出质量验证失败');
      }
    } else {
      throw new Error('AI未生成有效内容');
    }

  } catch (error) {
    console.error('💥 每日洞察生成失败:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: '每日洞察生成失败，请稍后重试' 
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
