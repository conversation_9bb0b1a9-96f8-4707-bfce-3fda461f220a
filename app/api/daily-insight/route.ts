import { getDefaultAIProvider, AIError } from "@/lib/ai";
import { buildDailyInsightContext, validateContext, getContextStats } from "@/lib/services/context-manager";
import { processDailyInsightOutput, validateOutput, getOutputStats } from "@/lib/services/output-processor";

export const runtime = "nodejs";
export const maxDuration = 60; // 每日洞察可能需要更长时间

// 使用统一AI工厂，无需硬编码配置

export async function POST(req: Request) {
  const startTime = Date.now();
  console.log('🧠 收到每日洞察请求');

  try {
    const { dialogueBlocks } = await req.json();
    console.log('📊 分析对话块数量:', dialogueBlocks?.length);

    // 构建每日洞察上下文
    const context = await buildDailyInsightContext();

    // 验证上下文质量
    if (!validateContext(context)) {
      throw new Error('上下文构建失败');
    }

    // 输出上下文统计信息
    const stats = getContextStats(context);
    console.log('📊 上下文统计:', stats);

    console.log('🤖 开始调用AI (每日洞察模式)...');

    // 使用统一AI工厂
    const aiProvider = await getDefaultAIProvider();
    console.log(`🤖 使用AI提供商: ${aiProvider.name}`);

    const result = await aiProvider.generateText(
      `${context.systemPrompt}\n\n${context.analysisContent}`,
      {
        temperature: 0.8,
        maxTokens: 2000
      }
    );

    console.log('✨ 每日洞察生成完成');
    console.log('🔍 AI响应调试:', {
      hasText: !!result,
      textLength: result?.length || 0,
      textPreview: result?.substring(0, 100) || 'null'
    });

    // 处理AI输出
    if (result) {
      const analysisTime = Date.now() - startTime; // 修复分析时间计算
      const output = await processDailyInsightOutput(result, analysisTime);

      // 验证输出质量
      if (validateOutput(output)) {
        const stats = getOutputStats(output);
        console.log('📊 输出统计:', stats);

        return new Response(
          JSON.stringify({
            success: true,
            insight: result,
            stats: stats,
            message: '每日洞察生成完成'
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      } else {
        throw new Error('输出质量验证失败');
      }
    } else {
      throw new Error('AI未生成有效内容');
    }

  } catch (error) {
    console.error('💥 每日洞察生成失败:', error);

    if (error instanceof AIError) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          code: error.code,
          provider: error.provider
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: '每日洞察生成失败，请稍后重试',
        details: error instanceof Error ? error.message : String(error)
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
