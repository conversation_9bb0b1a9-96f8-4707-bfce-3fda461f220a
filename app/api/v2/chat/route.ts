/**
 * SelfMirror v2 聊天API路由
 * 
 * 使用统一API处理器架构：
 * - 标准化的请求/响应处理
 * - 集成三引擎工作流
 * - 统一上下文管理
 * - 性能监控和优化
 */

import { chatAPIHandler } from '@/lib/api/handlers/chat-api-handler';

export const runtime = "nodejs";
export const maxDuration = 60;

/**
 * POST /api/v2/chat
 * 
 * 处理聊天请求，支持流式响应
 * 
 * 请求格式：
 * {
 *   "message": "用户消息",
 *   "sessionId": "会话ID（可选）",
 *   "userId": "用户ID（可选）",
 *   "options": {
 *     "model": "AI模型名称（可选）",
 *     "temperature": 0.7,
 *     "maxTokens": 2048,
 *     "stream": true
 *   },
 *   "context": {
 *     "includeHistory": true,
 *     "historyLength": 6,
 *     "includeProfile": true
 *   }
 * }
 * 
 * 响应格式（流式）：
 * data: {"type": "init", "sessionId": "...", "messageId": "...", "timestamp": "..."}
 * data: {"type": "text", "content": "...", "timestamp": "..."}
 * data: {"type": "end", "messageId": "...", "usage": {...}, "context": {...}}
 */
export async function POST(request: Request): Promise<Response> {
  console.log('📨 收到v2聊天请求');
  
  try {
    // 使用统一API处理器处理请求
    const response = await chatAPIHandler.handle(request);
    
    console.log('✅ v2聊天请求处理完成');
    return response;
    
  } catch (error) {
    console.error('❌ v2聊天API处理失败:', error);
    
    // 创建错误响应
    const errorResponse = {
      success: false,
      error: {
        code: 'CHAT_API_ERROR',
        message: error instanceof Error ? error.message : '聊天服务暂时不可用',
        timestamp: new Date().toISOString(),
        requestId: `error-${Date.now()}`
      }
    };

    return new Response(JSON.stringify(errorResponse, null, 2), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'X-Error-Code': 'CHAT_API_ERROR'
      }
    });
  }
}

/**
 * GET /api/v2/chat
 * 
 * 获取聊天API信息和健康状态
 */
export async function GET(): Promise<Response> {
  const info = {
    success: true,
    data: {
      endpoint: '/api/v2/chat',
      version: '2.0.0',
      method: 'POST',
      description: 'SelfMirror统一聊天API，支持流式响应和上下文管理',
      features: [
        '统一API处理架构',
        '三引擎工作流集成',
        '智能上下文管理',
        '多层级缓存优化',
        '实时性能监控',
        '流式响应支持'
      ],
      requestFormat: {
        message: 'string (required) - 用户消息',
        sessionId: 'string (optional) - 会话ID',
        userId: 'string (optional) - 用户ID',
        options: {
          model: 'string (optional) - AI模型名称',
          temperature: 'number (optional, 0-2) - 生成温度',
          maxTokens: 'number (optional, 1-8192) - 最大token数',
          stream: 'boolean (optional, default: true) - 是否流式响应'
        },
        context: {
          includeHistory: 'boolean (optional, default: true) - 是否包含历史',
          historyLength: 'number (optional, 0-20, default: 6) - 历史长度',
          includeProfile: 'boolean (optional, default: true) - 是否包含用户画像'
        }
      },
      responseFormat: {
        stream: {
          init: '初始化消息，包含sessionId和messageId',
          text: '流式文本内容',
          end: '结束消息，包含使用统计和上下文信息'
        }
      },
      optimizations: [
        'AI提供商响应缓存',
        '上下文检索缓存',
        '向量检索优化',
        '智能上下文包装',
        '历史对话管理'
      ]
    },
    metadata: {
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      status: 'healthy'
    }
  };

  return new Response(JSON.stringify(info, null, 2), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=300' // 5分钟缓存
    }
  });
}
