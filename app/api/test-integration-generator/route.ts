/**
 * 测试Integration Generator引擎的API端点
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试Integration Generator引擎...');
    
    const results: any = {
      success: true,
      tests: [],
      engineStats: null
    };
    
    // 1. 初始化Integration Generator引擎
    console.log('🔗 初始化Integration Generator引擎...');
    const { IntegrationGeneratorEngine } = await import("@/lib/services/three-engine/integration-generator-engine");
    
    const generator = new IntegrationGeneratorEngine();
    await generator.initialize();
    
    results.tests.push({
      name: 'Integration Generator引擎初始化',
      success: true,
      message: 'Integration Generator引擎初始化成功'
    });
    
    // 2. 准备测试数据 - Navigator指令
    console.log('🧭 准备Navigator指令...');
    const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
    
    const navigator = new NavigatorEngine();
    await navigator.initialize();
    
    const testInstruction = await navigator.analyzeUserIntent("我最近感觉压力很大，该怎么办？");
    
    results.tests.push({
      name: 'Navigator指令准备',
      success: true,
      instruction: {
        instructionId: testInstruction.instructionId,
        primaryIntent: testInstruction.intentAnalysis.primaryIntent,
        emotionalTone: testInstruction.intentAnalysis.emotionalTone,
        confidence: testInstruction.qualityMetrics.confidence
      }
    });
    
    // 3. 准备测试数据 - Context Retriever结果
    console.log('🔍 准备Context Retriever结果...');
    const { ContextRetrieverEngine } = await import("@/lib/services/three-engine/context-retriever-engine");
    
    const retriever = new ContextRetrieverEngine();
    await retriever.initialize();
    
    const retrievalResult = await retriever.executeRetrieval(testInstruction);
    
    results.tests.push({
      name: 'Context Retriever结果准备',
      success: true,
      retrievalResult: {
        retrievalId: retrievalResult.retrievalId,
        totalResults: retrievalResult.mergedResults.length,
        searchTime: retrievalResult.stats.totalSearchTime,
        qualityMetrics: retrievalResult.qualityMetrics
      }
    });
    
    // 4. 准备上下文包装
    console.log('📦 准备上下文包装...');
    
    const contextPackage = {
      userProfile: "一个关注个人成长的用户，经常思考工作和生活的平衡",
      recentHistory: [
        "用户: 最近工作很忙，感觉有点累",
        "AI: 理解你的感受，工作压力确实会让人疲惫。你有尝试过什么放松的方法吗？",
        "用户: 我试过听音乐，但效果不太明显"
      ],
      currentMessage: "我最近感觉压力很大，该怎么办？",
      retrievedContent: retrievalResult.mergedResults,
      systemPrompts: ["你是一个温暖、理解的AI助手"],
      contextualHints: [
        "用户正在寻求压力管理的建议",
        "用户之前提到过工作忙碌的问题",
        "需要提供实用的建议"
      ],
      packageMetadata: {
        totalLength: 0,
        contentSources: ['dialogue_history', 'user_profile'],
        qualityScore: 0.8,
        timestamp: new Date().toISOString()
      }
    };
    
    // 计算包装长度
    contextPackage.packageMetadata.totalLength = 
      JSON.stringify(contextPackage).length;
    
    results.tests.push({
      name: '上下文包装准备',
      success: true,
      contextPackage: {
        totalLength: contextPackage.packageMetadata.totalLength,
        historyCount: contextPackage.recentHistory.length,
        hintsCount: contextPackage.contextualHints.length,
        qualityScore: contextPackage.packageMetadata.qualityScore
      }
    });
    
    // 5. 测试基础响应生成
    console.log('🔗 测试基础响应生成...');
    
    const basicResponse = await generator.generateResponse(
      contextPackage.currentMessage,
      retrievalResult,
      contextPackage,
      {
        streamResponse: false,
        temperature: 0.7,
        maxTokens: 500
      }
    );
    
    const isValidBasic = generator.validateResponse(basicResponse);
    
    results.tests.push({
      name: '基础响应生成测试',
      success: isValidBasic,
      response: {
        responseId: basicResponse.responseId,
        contentLength: basicResponse.content.length,
        generationTime: basicResponse.generationStats.totalTime,
        tokensGenerated: basicResponse.generationStats.tokensGenerated,
        qualityMetrics: basicResponse.qualityMetrics,
        sourcesUsed: basicResponse.responseMetadata.sourcesUsed
      },
      validation: isValidBasic
    });
    
    // 6. 测试流式响应生成
    console.log('🌊 测试流式响应生成...');
    
    const streamResponse = await generator.generateResponse(
      "能给我一些具体的压力管理建议吗？",
      retrievalResult,
      contextPackage,
      {
        streamResponse: true,
        temperature: 0.6,
        maxTokens: 300
      }
    );
    
    // 收集流式响应内容
    let streamContent = '';
    if (streamResponse.responseStream) {
      try {
        for await (const chunk of streamResponse.responseStream) {
          streamContent += chunk;
          if (streamContent.length > 500) break; // 限制测试长度
        }
      } catch (error) {
        console.warn('流式响应收集中断:', error);
      }
    }
    
    const isValidStream = streamContent.length > 0;
    
    results.tests.push({
      name: '流式响应生成测试',
      success: isValidStream,
      streamResponse: {
        responseId: streamResponse.responseId,
        streamContentLength: streamContent.length,
        streamContentSample: streamContent.slice(0, 100) + (streamContent.length > 100 ? '...' : ''),
        generationTime: streamResponse.generationStats.totalTime,
        qualityMetrics: streamResponse.qualityMetrics
      },
      validation: isValidStream
    });
    
    // 7. 测试不同配置的响应生成
    console.log('⚙️ 测试不同配置的响应生成...');
    
    const configTests = [
      { name: '高创造性', temperature: 0.9, maxTokens: 200 },
      { name: '低创造性', temperature: 0.3, maxTokens: 150 },
      { name: '结构化格式', responseFormat: 'structured' as const, temperature: 0.5 }
    ];
    
    const configResults = [];
    for (const config of configTests) {
      try {
        const configResponse = await generator.generateResponse(
          "请简单总结一下压力管理的要点",
          retrievalResult,
          contextPackage,
          { ...config, streamResponse: false }
        );
        
        configResults.push({
          configName: config.name,
          success: true,
          contentLength: configResponse.content.length,
          generationTime: configResponse.generationStats.totalTime,
          qualityMetrics: configResponse.qualityMetrics
        });
      } catch (error) {
        configResults.push({
          configName: config.name,
          success: false,
          error: error instanceof Error ? error.message : '生成失败'
        });
      }
    }
    
    results.tests.push({
      name: '不同配置测试',
      success: configResults.every(r => r.success),
      configResults,
      message: `${configResults.filter(r => r.success).length}/${configResults.length} 配置测试成功`
    });
    
    // 8. 测试质量验证功能
    console.log('✅ 测试质量验证功能...');
    
    // 创建一个低质量的响应进行测试
    const lowQualityResponse = {
      ...basicResponse,
      content: "好的",
      qualityMetrics: {
        coherence: 0.3,
        relevance: 0.2,
        completeness: 0.1,
        engagement: 0.2
      }
    };
    
    const validationResults = {
      highQualityValid: generator.validateResponse(basicResponse),
      lowQualityValid: generator.validateResponse(lowQualityResponse),
      emptyContentValid: generator.validateResponse({
        ...basicResponse,
        content: ""
      })
    };
    
    results.tests.push({
      name: '质量验证测试',
      success: validationResults.highQualityValid && !validationResults.lowQualityValid && !validationResults.emptyContentValid,
      validationResults,
      message: '质量验证功能正常工作'
    });
    
    // 9. 测试上下文优化功能
    console.log('🎯 测试上下文优化功能...');
    
    // 创建一个优化配置的生成器
    const optimizedGenerator = new IntegrationGeneratorEngine({
      contextOptimization: true,
      qualityThreshold: 0.8
    });
    await optimizedGenerator.initialize();
    
    const optimizedResponse = await optimizedGenerator.generateResponse(
      contextPackage.currentMessage,
      retrievalResult,
      contextPackage,
      { streamResponse: false }
    );
    
    results.tests.push({
      name: '上下文优化测试',
      success: true,
      optimization: {
        contextLength: optimizedResponse.responseMetadata.contextLength,
        qualityMetrics: optimizedResponse.qualityMetrics,
        generationTime: optimizedResponse.generationStats.totalTime
      },
      message: '上下文优化功能正常'
    });
    
    // 10. 获取引擎统计信息
    console.log('📊 获取引擎统计信息...');
    
    const engineStats = generator.getEngineStats();
    results.engineStats = {
      totalGenerations: engineStats.totalGenerations,
      averageGenerationTime: Math.round(engineStats.averageGenerationTime),
      averageTokensGenerated: Math.round(engineStats.averageTokensGenerated),
      averageContextLength: Math.round(engineStats.averageContextLength),
      successRate: Math.round(engineStats.successRate * 100) / 100,
      errorCount: engineStats.errorCount
    };
    
    // 11. 综合性能基准测试
    console.log('🏃 执行综合性能基准测试...');
    
    const benchmarkMessages = [
      "今天心情不错",
      "工作遇到了困难",
      "想要学习新技能",
      "感觉有点迷茫",
      "需要一些建议"
    ];
    
    const benchmarkResults = [];
    const benchmarkStart = Date.now();
    
    for (const message of benchmarkMessages) {
      const start = Date.now();
      try {
        const response = await generator.generateResponse(
          message,
          retrievalResult,
          { ...contextPackage, currentMessage: message },
          { streamResponse: false, maxTokens: 100 }
        );
        
        const time = Date.now() - start;
        benchmarkResults.push({
          message: message.slice(0, 10) + '...',
          success: true,
          generationTime: time,
          contentLength: response.content.length,
          tokensGenerated: response.generationStats.tokensGenerated,
          qualityScore: Object.values(response.qualityMetrics).reduce((a, b) => a + b, 0) / 4
        });
      } catch (error) {
        benchmarkResults.push({
          message: message.slice(0, 10) + '...',
          success: false,
          error: error instanceof Error ? error.message : '生成失败'
        });
      }
    }
    
    const totalBenchmarkTime = Date.now() - benchmarkStart;
    
    results.tests.push({
      name: '综合性能基准测试',
      success: benchmarkResults.every(r => r.success),
      totalTime: totalBenchmarkTime,
      averageTime: Math.round(totalBenchmarkTime / benchmarkMessages.length),
      results: benchmarkResults,
      message: `完成${benchmarkMessages.length}个生成，平均耗时${Math.round(totalBenchmarkTime / benchmarkMessages.length)}ms`
    });
    
    console.log('✅ Integration Generator引擎测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { userMessage, retrievalResult, contextPackage, config } = await req.json();
    
    const { IntegrationGeneratorEngine } = await import("@/lib/services/three-engine/integration-generator-engine");
    
    const generator = new IntegrationGeneratorEngine(config);
    await generator.initialize();
    
    const response = await generator.generateResponse(
      userMessage,
      retrievalResult,
      contextPackage,
      config
    );
    
    const isValid = generator.validateResponse(response);
    const stats = generator.getEngineStats();
    
    return new Response(JSON.stringify({
      success: true,
      response,
      validation: {
        isValid,
        qualityMetrics: response.qualityMetrics
      },
      engineStats: stats,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ Integration Generator生成失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '生成失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
