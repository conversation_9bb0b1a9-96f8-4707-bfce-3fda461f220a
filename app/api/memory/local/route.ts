import { NextRequest } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filePath = searchParams.get('path')
    
    if (!filePath) {
      return new Response(JSON.stringify({ error: '缺少文件路径参数' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // 构建完整的文件路径
    const fullPath = path.join(process.cwd(), 'memory', filePath)
    
    try {
      const content = await fs.readFile(fullPath, 'utf-8')
      return new Response(JSON.stringify({ content }), {
        headers: { 'Content-Type': 'application/json' }
      })
    } catch (fileError) {
      console.log(`文件不存在: ${fullPath}`)
      return new Response(JSON.stringify({ content: '' }), {
        headers: { 'Content-Type': 'application/json' }
      })
    }
  } catch (error) {
    console.error('读取文件失败:', error)
    return new Response(JSON.stringify({ error: '读取文件失败' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { path: filePath, content } = await request.json()
    
    if (!filePath) {
      return new Response(JSON.stringify({ error: '缺少文件路径参数' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // 构建完整的文件路径
    const fullPath = path.join(process.cwd(), 'memory', filePath)
    
    // 确保目录存在
    const dir = path.dirname(fullPath)
    await fs.mkdir(dir, { recursive: true })
    
    // 写入文件
    await fs.writeFile(fullPath, content || '', 'utf-8')
    
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('保存文件失败:', error)
    return new Response(JSON.stringify({ error: '保存文件失败' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
