/**
 * SelfMirror 聊天API路由
 * 使用新的AI工厂架构
 */

import { getDefaultAIProvider, AIError } from "@/lib/ai";

export const runtime = "nodejs";
export const maxDuration = 30;

export async function POST(req: Request) {
  console.log('📨 收到聊天请求');

  try {
    const { messages } = await req.json();
    console.log('💬 消息数量:', messages?.length);
    
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return new Response(
        JSON.stringify({ error: '无效的消息格式' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // 获取AI提供商
    const aiProvider = await getDefaultAIProvider();
    console.log(`🤖 使用AI提供商: ${aiProvider.name}`);

    // 构建提示词
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.role !== 'user') {
      return new Response(
        JSON.stringify({ error: '最后一条消息必须是用户消息' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const prompt = lastMessage.content;
    const systemPrompt = "你是一个智能助手，请用中文回答用户的问题。";

    // 生成流式响应
    console.log('🌊 开始生成流式响应...');
    
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        
        try {
          // 使用AI提供商生成流式文本
          const textStream = aiProvider.generateStream(prompt, {
            systemPrompt,
            maxTokens: 500,
            temperature: 0.7
          });

          for await (const chunk of textStream) {
            // 格式化为SSE格式
            const data = `data: ${JSON.stringify({ 
              type: 'text', 
              content: chunk 
            })}\n\n`;
            
            controller.enqueue(encoder.encode(data));
          }

          // 发送结束标记
          const endData = `data: ${JSON.stringify({ 
            type: 'end' 
          })}\n\n`;
          controller.enqueue(encoder.encode(endData));
          
          controller.close();
          console.log('✅ 流式响应完成');

        } catch (error) {
          console.error('❌ 流式响应失败:', error);
          
          // 发送错误信息
          const errorData = `data: ${JSON.stringify({ 
            type: 'error', 
            error: error instanceof AIError ? error.message : '生成响应时发生错误'
          })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    });

  } catch (error) {
    console.error('💥 API请求失败:', error);
    
    if (error instanceof AIError) {
      return new Response(
        JSON.stringify({ 
          error: error.message,
          code: error.code,
          provider: error.provider
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({ error: '服务暂时不可用，请稍后重试' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
