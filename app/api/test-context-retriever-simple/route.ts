/**
 * 简化的Context Retriever引擎测试API
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始简化的Context Retriever引擎测试...');
    
    const results: any = {
      success: true,
      tests: [],
      message: "Context Retriever引擎基础功能测试"
    };
    
    // 1. 测试Navigator引擎生成指令
    console.log('🧭 测试Navigator引擎生成指令...');
    
    const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
    const navigator = new NavigatorEngine();
    await navigator.initialize();
    
    const testInstruction = await navigator.analyzeUserIntent("今天天气怎么样？");
    
    results.tests.push({
      name: 'Navigator指令生成',
      success: true,
      instruction: {
        instructionId: testInstruction.instructionId,
        primaryIntent: testInstruction.intentAnalysis.primaryIntent,
        searchStrategy: testInstruction.searchStrategy,
        hotStoreQueries: testInstruction.targets.hotStoreQueries,
        coldStoreQueries: testInstruction.targets.coldStoreQueries,
        confidence: testInstruction.qualityMetrics.confidence
      }
    });
    
    // 2. 测试向量数据库基础功能
    console.log('🔍 测试向量数据库基础功能...');
    
    // 动态导入向量数据库
    const vectorDbModule = await import("@/lib/services/vector-database");
    console.log('📦 向量数据库模块导出:', Object.keys(vectorDbModule));
    
    // 检查可用的导出
    const availableExports = Object.keys(vectorDbModule);
    results.tests.push({
      name: '向量数据库模块检查',
      success: true,
      availableExports,
      message: `找到 ${availableExports.length} 个导出`
    });
    
    // 3. 尝试使用不同的导出名称
    let vectorManager = null;
    let vectorManagerName = '';
    
    if ('dualVectorSystem' in vectorDbModule) {
      vectorManager = vectorDbModule.dualVectorSystem;
      vectorManagerName = 'dualVectorSystem';
    } else if ('dualVectorManager' in vectorDbModule) {
      vectorManager = vectorDbModule.dualVectorManager;
      vectorManagerName = 'dualVectorManager';
    } else if ('DualVectorManager' in vectorDbModule) {
      vectorManager = new vectorDbModule.DualVectorManager();
      vectorManagerName = 'DualVectorManager (实例化)';
    }
    
    if (vectorManager) {
      console.log(`✅ 找到向量管理器: ${vectorManagerName}`);
      
      // 初始化向量管理器
      if (typeof vectorManager.initialize === 'function') {
        await vectorManager.initialize();
        console.log('✅ 向量管理器初始化成功');
        
        results.tests.push({
          name: '向量管理器初始化',
          success: true,
          managerType: vectorManagerName,
          message: '向量管理器初始化成功'
        });
        
        // 4. 测试向量搜索功能
        console.log('🔍 测试向量搜索功能...');
        
        // 生成测试向量
        const testVector = Array.from({ length: 1536 }, () => Math.random());
        
        // 检查搜索方法
        const searchMethods = [];
        if (typeof vectorManager.searchVector === 'function') {
          searchMethods.push('searchVector');
        }
        if (typeof vectorManager.searchSimilar === 'function') {
          searchMethods.push('searchSimilar');
        }
        if (typeof vectorManager.search === 'function') {
          searchMethods.push('search');
        }
        
        results.tests.push({
          name: '搜索方法检查',
          success: searchMethods.length > 0,
          availableMethods: searchMethods,
          message: `找到 ${searchMethods.length} 个搜索方法`
        });
        
        // 5. 尝试执行搜索
        if (searchMethods.includes('searchVector')) {
          try {
            const searchResult = await vectorManager.searchVector(testVector, {
              searchStrategy: 'comprehensive',
              maxResults: 5
            });
            
            results.tests.push({
              name: '向量搜索测试',
              success: true,
              searchResult: {
                hotResults: searchResult.hotResults?.length || 0,
                coldResults: searchResult.coldResults?.length || 0,
                totalResults: (searchResult.hotResults?.length || 0) + (searchResult.coldResults?.length || 0)
              },
              message: '向量搜索执行成功'
            });
          } catch (error) {
            results.tests.push({
              name: '向量搜索测试',
              success: false,
              error: error instanceof Error ? error.message : '搜索失败',
              message: '向量搜索执行失败'
            });
          }
        }
        
      } else {
        results.tests.push({
          name: '向量管理器初始化',
          success: false,
          managerType: vectorManagerName,
          message: '向量管理器没有initialize方法'
        });
      }
    } else {
      results.tests.push({
        name: '向量管理器查找',
        success: false,
        message: '未找到可用的向量管理器'
      });
    }
    
    // 6. 测试Context Retriever引擎的基础结构
    console.log('🔍 测试Context Retriever引擎结构...');
    
    try {
      const { ContextRetrieverEngine } = await import("@/lib/services/three-engine/context-retriever-engine");
      
      // 创建引擎实例但不初始化
      const retriever = new ContextRetrieverEngine();
      
      // 检查引擎方法
      const engineMethods = [];
      if (typeof retriever.executeRetrieval === 'function') {
        engineMethods.push('executeRetrieval');
      }
      if (typeof retriever.optimizeResults === 'function') {
        engineMethods.push('optimizeResults');
      }
      if (typeof retriever.getEngineStats === 'function') {
        engineMethods.push('getEngineStats');
      }
      
      results.tests.push({
        name: 'Context Retriever引擎结构',
        success: engineMethods.length >= 3,
        availableMethods: engineMethods,
        message: `Context Retriever引擎包含 ${engineMethods.length} 个方法`
      });
      
    } catch (error) {
      results.tests.push({
        name: 'Context Retriever引擎导入',
        success: false,
        error: error instanceof Error ? error.message : '导入失败',
        message: 'Context Retriever引擎导入失败'
      });
    }
    
    // 7. 模拟检索流程
    console.log('🔄 模拟检索流程...');
    
    const mockRetrievalResult = {
      retrievalId: 'mock-retrieval-001',
      instructionId: testInstruction.instructionId,
      timestamp: new Date().toISOString(),
      hotStoreResults: [],
      coldStoreResults: [],
      mergedResults: [],
      stats: {
        totalSearchTime: 100,
        hotStoreTime: 50,
        coldStoreTime: 50,
        hotStoreHits: 0,
        coldStoreHits: 0,
        totalProcessed: 0,
        cacheHits: 0,
        cacheMisses: 1
      },
      qualityMetrics: {
        relevanceScore: 0.8,
        diversityScore: 0.6,
        completenessScore: 0.5,
        freshness: 0.9
      },
      retrievalMetadata: {
        strategy: testInstruction.searchStrategy,
        actualScope: testInstruction.searchStrategy.scope,
        fallbackUsed: false,
        optimizations: []
      }
    };
    
    results.tests.push({
      name: '模拟检索流程',
      success: true,
      mockResult: {
        retrievalId: mockRetrievalResult.retrievalId,
        totalResults: mockRetrievalResult.mergedResults.length,
        searchTime: mockRetrievalResult.stats.totalSearchTime,
        qualityMetrics: mockRetrievalResult.qualityMetrics
      },
      message: '检索流程模拟成功'
    });
    
    console.log('✅ 简化的Context Retriever引擎测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
