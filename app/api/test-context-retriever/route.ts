/**
 * 测试Context Retriever引擎的API端点
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试Context Retriever引擎...');
    
    // 动态导入以避免初始化问题
    const { ContextRetrieverEngine } = await import("@/lib/services/three-engine/context-retriever-engine");
    const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
    const { vectorDatabase } = await import("@/lib/services/vector-database");
    
    const results: any = {
      success: true,
      tests: [],
      engineStats: null,
      sampleData: null
    };
    
    // 1. 初始化引擎
    console.log('🔍 初始化Context Retriever引擎...');
    const retriever = new ContextRetrieverEngine();
    await retriever.initialize();
    
    const navigator = new NavigatorEngine();
    await navigator.initialize();
    
    results.tests.push({
      name: 'Context Retriever引擎初始化',
      success: true,
      message: 'Context Retriever引擎初始化成功'
    });
    
    // 2. 准备测试数据
    console.log('📝 准备测试向量数据...');
    
    const testVectors = [
      {
        content: "今天天气很好，阳光明媚，适合出门散步。",
        metadata: {
          sourceDocumentType: "daily_insight_hot",
          sourceDocumentId: "weather-001",
          createdAt: new Date().toISOString()
        }
      },
      {
        content: "React组件性能优化的关键在于减少不必要的重新渲染。",
        metadata: {
          sourceDocumentType: "knowledge_base",
          sourceDocumentId: "react-perf-001",
          createdAt: new Date(Date.now() - 86400000).toISOString() // 1天前
        }
      },
      {
        content: "我最近在学习机器学习，发现深度学习很有趣。",
        metadata: {
          sourceDocumentType: "dialogue_history",
          sourceDocumentId: "chat-001",
          createdAt: new Date(Date.now() - 3600000).toISOString() // 1小时前
        }
      },
      {
        content: "JavaScript的异步编程模式包括回调、Promise和async/await。",
        metadata: {
          sourceDocumentType: "knowledge_base",
          sourceDocumentId: "js-async-001",
          createdAt: new Date(Date.now() - 172800000).toISOString() // 2天前
        }
      },
      {
        content: "今天心情不错，工作进展顺利，完成了几个重要任务。",
        metadata: {
          sourceDocumentType: "daily_insight_hot",
          sourceDocumentId: "mood-001",
          createdAt: new Date(Date.now() - 7200000).toISOString() // 2小时前
        }
      }
    ];
    
    // 添加测试向量到数据库
    for (const testVector of testVectors) {
      // 生成简单的向量（实际应用中会使用embedding模型）
      const vector = Array.from({ length: 1536 }, () => Math.random());
      await vectorDatabase.add(vector, testVector.metadata);
    }
    
    results.sampleData = {
      vectorCount: testVectors.length,
      sampleContents: testVectors.map(v => v.content.slice(0, 30) + '...')
    };
    
    results.tests.push({
      name: '测试数据准备',
      success: true,
      message: `成功添加${testVectors.length}个测试向量`
    });
    
    // 3. 测试简单检索
    console.log('🔍 测试简单检索...');
    
    const simpleInstruction = await navigator.analyzeUserIntent("今天天气怎么样？");
    const simpleResult = await retriever.executeRetrieval(simpleInstruction);
    
    results.tests.push({
      name: '简单检索测试',
      success: true,
      input: "今天天气怎么样？",
      output: {
        retrievalId: simpleResult.retrievalId,
        totalResults: simpleResult.mergedResults.length,
        hotStoreHits: simpleResult.stats.hotStoreHits,
        coldStoreHits: simpleResult.stats.coldStoreHits,
        searchTime: simpleResult.stats.totalSearchTime,
        topResult: simpleResult.mergedResults[0] ? {
          content: simpleResult.mergedResults[0].content.slice(0, 50) + '...',
          similarity: simpleResult.mergedResults[0].similarity,
          rank: simpleResult.mergedResults[0].rank
        } : null,
        qualityMetrics: simpleResult.qualityMetrics
      }
    });
    
    // 4. 测试技术问题检索
    console.log('🔍 测试技术问题检索...');
    
    const techInstruction = await navigator.analyzeUserIntent("如何优化React组件的性能？");
    const techResult = await retriever.executeRetrieval(techInstruction);
    
    results.tests.push({
      name: '技术问题检索测试',
      success: true,
      input: "如何优化React组件的性能？",
      output: {
        retrievalId: techResult.retrievalId,
        totalResults: techResult.mergedResults.length,
        searchTime: techResult.stats.totalSearchTime,
        relevanceScore: techResult.qualityMetrics.relevanceScore,
        diversityScore: techResult.qualityMetrics.diversityScore,
        topResults: techResult.mergedResults.slice(0, 3).map(r => ({
          content: r.content.slice(0, 40) + '...',
          similarity: r.similarity,
          sourceType: r.metadata.sourceType,
          rank: r.rank
        }))
      }
    });
    
    // 5. 测试情感表达检索
    console.log('🔍 测试情感表达检索...');
    
    const emotionalInstruction = await navigator.analyzeUserIntent("我今天心情很好");
    const emotionalResult = await retriever.executeRetrieval(emotionalInstruction);
    
    results.tests.push({
      name: '情感表达检索测试',
      success: true,
      input: "我今天心情很好",
      output: {
        retrievalId: emotionalResult.retrievalId,
        totalResults: emotionalResult.mergedResults.length,
        searchTime: emotionalResult.stats.totalSearchTime,
        freshness: emotionalResult.qualityMetrics.freshness,
        completenessScore: emotionalResult.qualityMetrics.completenessScore,
        topResults: emotionalResult.mergedResults.slice(0, 2).map(r => ({
          content: r.content.slice(0, 40) + '...',
          temporalScore: r.metadata.temporalScore,
          createdAt: r.metadata.createdAt,
          rank: r.rank
        }))
      }
    });
    
    // 6. 测试结果优化功能
    console.log('🎯 测试结果优化功能...');
    
    const optimizedResult = await retriever.optimizeResults(techResult);
    const optimizationImprovement = {
      originalRelevance: techResult.qualityMetrics.relevanceScore,
      optimizedRelevance: optimizedResult.qualityMetrics.relevanceScore,
      originalDiversity: techResult.qualityMetrics.diversityScore,
      optimizedDiversity: optimizedResult.qualityMetrics.diversityScore,
      optimizations: optimizedResult.retrievalMetadata.optimizations
    };
    
    results.tests.push({
      name: '结果优化测试',
      success: true,
      improvement: optimizationImprovement,
      message: `优化应用: ${optimizedResult.retrievalMetadata.optimizations.join(', ')}`
    });
    
    // 7. 测试缓存机制
    console.log('🎯 测试缓存机制...');
    
    // 第一次检索（应该缓存）
    const cacheTest1Start = Date.now();
    const cacheResult1 = await retriever.executeRetrieval(simpleInstruction);
    const cacheTest1Time = Date.now() - cacheTest1Start;
    
    // 第二次检索（应该使用缓存）
    const cacheTest2Start = Date.now();
    const cacheResult2 = await retriever.executeRetrieval(simpleInstruction);
    const cacheTest2Time = Date.now() - cacheTest2Start;
    
    const cacheSpeedup = cacheTest1Time / Math.max(cacheTest2Time, 1);
    
    results.tests.push({
      name: '缓存机制测试',
      success: cacheSpeedup > 2, // 缓存应该至少快2倍
      firstRetrievalTime: cacheTest1Time,
      secondRetrievalTime: cacheTest2Time,
      speedupRatio: Math.round(cacheSpeedup * 100) / 100,
      message: `缓存加速比: ${Math.round(cacheSpeedup * 100) / 100}x`
    });
    
    // 8. 测试并行vs串行搜索
    console.log('⚡ 测试并行vs串行搜索性能...');
    
    // 并行搜索
    const parallelRetriever = new ContextRetrieverEngine({ enableParallelSearch: true });
    await parallelRetriever.initialize();
    
    const parallelStart = Date.now();
    const parallelResult = await parallelRetriever.executeRetrieval(techInstruction);
    const parallelTime = Date.now() - parallelStart;
    
    // 串行搜索
    const serialRetriever = new ContextRetrieverEngine({ enableParallelSearch: false });
    await serialRetriever.initialize();
    
    const serialStart = Date.now();
    const serialResult = await serialRetriever.executeRetrieval(techInstruction);
    const serialTime = Date.now() - serialStart;
    
    const performanceGain = serialTime / Math.max(parallelTime, 1);
    
    results.tests.push({
      name: '并行vs串行性能测试',
      success: true,
      parallelTime,
      serialTime,
      performanceGain: Math.round(performanceGain * 100) / 100,
      parallelResults: parallelResult.mergedResults.length,
      serialResults: serialResult.mergedResults.length,
      message: `并行搜索性能提升: ${Math.round(performanceGain * 100) / 100}x`
    });
    
    // 9. 获取引擎统计信息
    console.log('📊 获取引擎统计信息...');
    
    const engineStats = retriever.getEngineStats();
    results.engineStats = {
      totalRetrievals: engineStats.totalRetrievals,
      averageRetrievalTime: Math.round(engineStats.averageRetrievalTime),
      averageResultCount: Math.round(engineStats.averageResultCount * 100) / 100,
      cacheHitRate: Math.round(engineStats.cacheHitRate * 100) / 100,
      successRate: Math.round(engineStats.successRate * 100) / 100,
      errorCount: engineStats.errorCount
    };
    
    // 10. 综合性能基准测试
    console.log('🏃 执行综合性能基准测试...');
    
    const benchmarkQueries = [
      "天气情况",
      "React性能优化",
      "JavaScript异步编程",
      "机器学习",
      "心情状态"
    ];
    
    const benchmarkResults = [];
    const benchmarkStart = Date.now();
    
    for (const query of benchmarkQueries) {
      const instruction = await navigator.analyzeUserIntent(query);
      const start = Date.now();
      const result = await retriever.executeRetrieval(instruction);
      const time = Date.now() - start;
      
      benchmarkResults.push({
        query,
        retrievalTime: time,
        resultCount: result.mergedResults.length,
        relevanceScore: result.qualityMetrics.relevanceScore,
        hotStoreHits: result.stats.hotStoreHits,
        coldStoreHits: result.stats.coldStoreHits
      });
    }
    
    const totalBenchmarkTime = Date.now() - benchmarkStart;
    
    results.tests.push({
      name: '综合性能基准测试',
      success: true,
      totalTime: totalBenchmarkTime,
      averageTime: Math.round(totalBenchmarkTime / benchmarkQueries.length),
      results: benchmarkResults,
      message: `完成${benchmarkQueries.length}个检索，平均耗时${Math.round(totalBenchmarkTime / benchmarkQueries.length)}ms`
    });
    
    console.log('✅ Context Retriever引擎测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { instruction, config } = await req.json();
    
    const { ContextRetrieverEngine } = await import("@/lib/services/three-engine/context-retriever-engine");
    
    const retriever = new ContextRetrieverEngine(config);
    await retriever.initialize();
    
    const result = await retriever.executeRetrieval(instruction);
    const optimizedResult = await retriever.optimizeResults(result);
    const stats = retriever.getEngineStats();
    
    return new Response(JSON.stringify({
      success: true,
      result: optimizedResult,
      engineStats: stats,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ Context Retriever执行失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '检索失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
