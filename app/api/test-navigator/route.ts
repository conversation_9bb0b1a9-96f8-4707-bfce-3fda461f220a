/**
 * 测试Navigator引擎的API端点
 */

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试Navigator引擎...');
    
    // 动态导入以避免初始化问题
    const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
    
    const results: any = {
      success: true,
      tests: [],
      engineStats: null
    };
    
    // 1. 初始化Navigator引擎
    console.log('🧭 初始化Navigator引擎...');
    const navigator = new NavigatorEngine();
    await navigator.initialize();
    
    results.tests.push({
      name: 'Navigator引擎初始化',
      success: true,
      message: 'Navigator引擎初始化成功'
    });
    
    // 2. 测试简单问答意图分析
    console.log('🧭 测试简单问答意图分析...');
    
    const simpleQuestion = "今天天气怎么样？";
    const simpleInstruction = await navigator.analyzeUserIntent(simpleQuestion);
    
    results.tests.push({
      name: '简单问答意图分析',
      success: true,
      input: simpleQuestion,
      output: {
        instructionId: simpleInstruction.instructionId,
        primaryIntent: simpleInstruction.intentAnalysis.primaryIntent,
        searchStrategy: simpleInstruction.searchStrategy,
        primaryKeywords: simpleInstruction.keywords.primary,
        hotStoreQueries: simpleInstruction.targets.hotStoreQueries,
        confidence: simpleInstruction.qualityMetrics.confidence
      }
    });
    
    // 3. 测试复杂情感表达分析
    console.log('🧭 测试复杂情感表达分析...');
    
    const emotionalMessage = "我最近感觉很焦虑，工作压力很大，不知道该怎么办";
    const emotionalInstruction = await navigator.analyzeUserIntent(
      emotionalMessage,
      ["昨天我们聊过关于工作的事情", "你提到过要学会放松"]
    );
    
    results.tests.push({
      name: '复杂情感表达分析',
      success: true,
      input: emotionalMessage,
      output: {
        instructionId: emotionalInstruction.instructionId,
        primaryIntent: emotionalInstruction.intentAnalysis.primaryIntent,
        emotionalTone: emotionalInstruction.intentAnalysis.emotionalTone,
        urgencyLevel: emotionalInstruction.intentAnalysis.urgencyLevel,
        emotionalKeywords: emotionalInstruction.keywords.emotional,
        contextualKeywords: emotionalInstruction.keywords.contextual,
        searchScope: emotionalInstruction.searchStrategy.scope,
        confidence: emotionalInstruction.qualityMetrics.confidence
      }
    });
    
    // 4. 测试技术问题分析
    console.log('🧭 测试技术问题分析...');
    
    const technicalQuestion = "如何优化React组件的性能？特别是在处理大量数据时";
    const technicalInstruction = await navigator.analyzeUserIntent(technicalQuestion);
    
    results.tests.push({
      name: '技术问题分析',
      success: true,
      input: technicalQuestion,
      output: {
        instructionId: technicalInstruction.instructionId,
        primaryIntent: technicalInstruction.intentAnalysis.primaryIntent,
        complexityLevel: technicalInstruction.intentAnalysis.complexityLevel,
        primaryKeywords: technicalInstruction.keywords.primary,
        secondaryKeywords: technicalInstruction.keywords.secondary,
        semanticQueries: technicalInstruction.targets.semanticQueries,
        maxResults: technicalInstruction.executionParams.maxResults,
        confidence: technicalInstruction.qualityMetrics.confidence
      }
    });
    
    // 5. 测试回忆类请求分析
    console.log('🧭 测试回忆类请求分析...');
    
    const memoryRequest = "你还记得我们上个月讨论过的那个项目吗？";
    const memoryInstruction = await navigator.analyzeUserIntent(
      memoryRequest,
      ["我们聊过很多项目", "最近在做一个新的应用"]
    );
    
    results.tests.push({
      name: '回忆类请求分析',
      success: true,
      input: memoryRequest,
      output: {
        instructionId: memoryInstruction.instructionId,
        primaryIntent: memoryInstruction.intentAnalysis.primaryIntent,
        searchPriority: memoryInstruction.searchStrategy.priority,
        temporalKeywords: memoryInstruction.keywords.temporal,
        crossReferences: memoryInstruction.targets.crossReferences,
        parallelExecution: memoryInstruction.executionParams.parallelExecution,
        confidence: memoryInstruction.qualityMetrics.confidence
      }
    });
    
    // 6. 测试指令验证功能
    console.log('🧭 测试指令验证功能...');
    
    const validationResults = [
      navigator.validateInstruction(simpleInstruction),
      navigator.validateInstruction(emotionalInstruction),
      navigator.validateInstruction(technicalInstruction),
      navigator.validateInstruction(memoryInstruction)
    ];
    
    results.tests.push({
      name: '指令验证功能',
      success: validationResults.every(result => result === true),
      validationResults,
      message: `${validationResults.filter(r => r).length}/${validationResults.length} 指令通过验证`
    });
    
    // 7. 测试降级机制
    console.log('🧭 测试降级机制...');
    
    // 创建一个会超时的Navigator实例
    const timeoutNavigator = new NavigatorEngine({
      maxAnalysisTime: 1 // 1ms超时，强制触发降级
    });
    await timeoutNavigator.initialize();
    
    const fallbackInstruction = await timeoutNavigator.analyzeUserIntent("测试降级机制");
    const fallbackValid = navigator.validateInstruction(fallbackInstruction);
    
    results.tests.push({
      name: '降级机制测试',
      success: fallbackValid,
      output: {
        instructionId: fallbackInstruction.instructionId,
        primaryIntent: fallbackInstruction.intentAnalysis.primaryIntent,
        confidence: fallbackInstruction.qualityMetrics.confidence,
        isFallback: fallbackInstruction.qualityMetrics.confidence <= 0.5
      },
      message: fallbackValid ? '降级指令生成成功且有效' : '降级指令无效'
    });
    
    // 8. 获取引擎统计信息
    console.log('🧭 获取引擎统计信息...');
    
    const engineStats = navigator.getEngineStats();
    results.engineStats = {
      totalAnalyses: engineStats.totalAnalyses,
      averageAnalysisTime: Math.round(engineStats.averageAnalysisTime),
      successRate: Math.round(engineStats.successRate * 100) / 100,
      errorCount: engineStats.errorCount
    };
    
    // 9. 性能基准测试
    console.log('🧭 执行性能基准测试...');
    
    const benchmarkMessages = [
      "你好",
      "帮我分析一下这个问题",
      "我想了解更多关于AI的知识",
      "最近心情不太好，能聊聊吗？",
      "你记得我们之前讨论的那个话题吗？"
    ];
    
    const benchmarkStart = Date.now();
    const benchmarkResults = [];
    
    for (const message of benchmarkMessages) {
      const start = Date.now();
      const instruction = await navigator.analyzeUserIntent(message);
      const time = Date.now() - start;
      
      benchmarkResults.push({
        message: message.slice(0, 20) + (message.length > 20 ? '...' : ''),
        analysisTime: time,
        confidence: instruction.qualityMetrics.confidence,
        keywordCount: instruction.keywords.primary.length + instruction.keywords.secondary.length
      });
    }
    
    const totalBenchmarkTime = Date.now() - benchmarkStart;
    
    results.tests.push({
      name: '性能基准测试',
      success: true,
      totalTime: totalBenchmarkTime,
      averageTime: Math.round(totalBenchmarkTime / benchmarkMessages.length),
      results: benchmarkResults,
      message: `完成${benchmarkMessages.length}个分析，平均耗时${Math.round(totalBenchmarkTime / benchmarkMessages.length)}ms`
    });
    
    console.log('✅ Navigator引擎测试完成');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { message, context, config } = await req.json();
    
    const { NavigatorEngine } = await import("@/lib/services/three-engine/navigator-engine");
    
    const navigator = new NavigatorEngine(config);
    await navigator.initialize();
    
    const instruction = await navigator.analyzeUserIntent(
      message,
      context || [],
      undefined
    );
    
    const isValid = navigator.validateInstruction(instruction);
    const stats = navigator.getEngineStats();
    
    return new Response(JSON.stringify({
      success: true,
      instruction,
      validation: {
        isValid,
        confidence: instruction.qualityMetrics.confidence,
        completeness: instruction.qualityMetrics.completeness,
        specificity: instruction.qualityMetrics.specificity
      },
      engineStats: stats,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ Navigator分析失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '分析失败',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
