/**
 * 测试向量数据库全局ID集成的API端点
 */

import { dualVectorSystem, globalIdManager } from "@/lib/services/vector-database";
import { VectorStoreMetadata } from "@/lib/services/vector-database/interfaces";

export const runtime = "nodejs";

export async function GET() {
  try {
    console.log('🧪 开始测试向量数据库的全局ID集成...');
    
    // 1. 初始化系统
    console.log('📋 初始化向量数据库和全局ID管理器...');
    await globalIdManager.initialize();
    await dualVectorSystem.initialize();
    
    const results: any = {
      success: true,
      tests: [],
      globalIdStats: null,
      systemStats: null
    };
    
    // 2. 测试单个向量添加（Hot Store）
    console.log('🔥 测试Hot Store向量添加...');
    
    const testVector1 = Array.from({length: 1536}, () => Math.random());
    const hotMetadata: VectorStoreMetadata = {
      chunkId: 'test-hot-chunk-' + Date.now(),
      sourceDocumentType: 'daily_insight_hot',
      sourceDocumentId: '20250703-T001',
      parentChunk: '这是一个测试的父块内容',
      childChunk: '这是对应的意义子块内容',
      createdAt: new Date().toISOString()
    };
    
    const hotChunkId = await dualVectorSystem.addVector(testVector1, hotMetadata);
    results.tests.push({
      name: 'Hot Store添加',
      success: true,
      chunkId: hotChunkId,
      globalId: hotMetadata.globalId
    });
    
    // 3. 测试单个向量添加（Cold Store）
    console.log('❄️ 测试Cold Store向量添加...');
    
    const testVector2 = Array.from({length: 1536}, () => Math.random());
    const coldMetadata: VectorStoreMetadata = {
      chunkId: 'test-cold-chunk-' + Date.now(),
      sourceDocumentType: 'user_profile',
      sourceDocumentId: '20250703-T002',
      parentChunk: '这是用户画像的父块内容',
      childChunk: '这是用户画像的意义子块内容',
      createdAt: new Date().toISOString()
    };
    
    const coldChunkId = await dualVectorSystem.addVector(testVector2, coldMetadata);
    results.tests.push({
      name: 'Cold Store添加',
      success: true,
      chunkId: coldChunkId,
      globalId: coldMetadata.globalId
    });
    
    // 4. 测试批量向量添加
    console.log('📦 测试批量向量添加...');
    
    const batchVectors = [];
    for (let i = 0; i < 2; i++) {
      batchVectors.push({
        vector: Array.from({length: 1536}, () => Math.random()),
        metadata: {
          chunkId: `test-batch-chunk-${Date.now()}-${i}`,
          sourceDocumentType: 'dialogue_history' as const,
          sourceDocumentId: `20250703-T00${i + 3}`,
          parentChunk: `批量测试父块 ${i + 1}`,
          childChunk: `批量测试意义子块 ${i + 1}`,
          createdAt: new Date().toISOString()
        }
      });
    }
    
    const batchChunkIds = await dualVectorSystem.addBatch(batchVectors);
    results.tests.push({
      name: '批量添加',
      success: true,
      chunkIds: batchChunkIds,
      count: batchChunkIds.length
    });
    
    // 5. 验证全局ID生成
    console.log('🔍 验证全局ID生成...');
    
    const todayStats = globalIdManager.getTodayStats();
    results.globalIdStats = {
      date: todayStats?.date,
      currentTurn: todayStats?.currentTurn,
      totalInputs: todayStats?.totalInputs,
      totalDerivations: todayStats?.totalDerivations
    };
    
    // 6. 测试向量搜索
    console.log('🔍 测试向量搜索...');
    
    const queryVector = Array.from({length: 1536}, () => Math.random());
    const searchResults = await dualVectorSystem.searchVector(queryVector, {
      maxResults: 3,
      searchStrategy: 'parallel'
    });
    
    results.tests.push({
      name: '向量搜索',
      success: true,
      resultsCount: searchResults.mergedResults.length,
      searchTime: searchResults.searchStats.totalTime,
      results: searchResults.mergedResults.map(r => ({
        chunkId: r.metadata.chunkId,
        globalId: r.metadata.globalId,
        similarity: r.similarity,
        sourceType: r.metadata.sourceDocumentType
      }))
    });
    
    // 7. 获取系统统计
    console.log('📈 获取系统统计...');
    
    const systemStats = await dualVectorSystem.getStats();
    results.systemStats = {
      hotStore: {
        totalVectors: systemStats.hotStore.totalVectors,
        averageTemperature: systemStats.hotStore.averageTemperature
      },
      coldStore: {
        totalVectors: systemStats.coldStore.totalVectors,
        indexNodes: systemStats.coldStore.indexNodes
      },
      searchStats: {
        totalSearches: systemStats.searchStats.totalSearches,
        averageSearchTime: systemStats.searchStats.averageSearchTime
      }
    };
    
    // 8. 验证ID血缘关系
    if (searchResults.mergedResults.length > 0) {
      const firstResult = searchResults.mergedResults[0];
      const globalId = firstResult.metadata.globalId;
      
      if (globalId) {
        const metadata = globalIdManager.getIdMetadata(globalId);
        const sourceChain = globalIdManager.getSourceChain(globalId);
        
        results.tests.push({
          name: 'ID血缘关系验证',
          success: true,
          globalId,
          metadata: {
            type: metadata?.type,
            parentId: metadata?.parentId,
            contentLength: metadata?.contentLength,
            createdAt: metadata?.createdAt
          },
          sourceChain
        });
      }
    }
    
    console.log('✅ 向量数据库全局ID集成测试完成！');
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
