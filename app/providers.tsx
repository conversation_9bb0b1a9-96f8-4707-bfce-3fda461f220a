'use client';

import { useEffect } from 'react';
import { conversationManager } from '@/lib/services/conversation';
import { RAGDebugProvider } from '@/lib/contexts/RAGDebugContext';

export function Providers({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // 初始化对话管理器
    console.log('🚀 初始化 SelfMirror...');
    conversationManager.initialize().then(() => {
      console.log('✅ SelfMirror 初始化完成');
    }).catch((error) => {
      console.error('❌ SelfMirror 初始化失败:', error);
    });
  }, []);

  return (
    <RAGDebugProvider>
      {children}
    </RAGDebugProvider>
  );
}