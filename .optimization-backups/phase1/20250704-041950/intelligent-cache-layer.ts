/**
 * Intelligent Cache Layer - 智能缓存层
 * 实现历史加权衰减排序、噪声抑制、归零重置等核心算法
 */

import { globalIdManager } from '@/lib/services/vector-database/global-id-system';

/**
 * 缓存项接口
 */
export interface CacheItem {
  id: string;
  parentChunkId: string;
  content: string;
  vector?: number[];
  metadata: {
    globalId: string;
    sourceType: string;
    sourceDocumentId: string;
    createdAt: string;
    lastAccessedAt: string;
    accessCount: number;
    relevanceScore: number;
    semanticScore: number;
    temporalScore: number;
  };
  cacheMetrics: {
    hitCount: number;
    missCount: number;
    weightedScore: number;
    decayFactor: number;
    noiseLevel: number;
    lastDecayTime: string;
  };
}

/**
 * 智能缓存配置
 */
export interface IntelligentCacheConfig {
  maxCacheSize: number;
  decayCoefficients: {
    primary: number;    // 主要衰减系数 (默认: 1.0)
    secondary: number;  // 次要衰减系数 (默认: 0.8)
    tertiary: number;   // 三级衰减系数 (默认: 0.6)
  };
  tailEliminationThreshold: number;  // 尾部淘汰阈值 (默认: 0.1)
  noiseSuppressionThreshold: number; // 噪声抑制阈值 (默认: 0.05)
  zeroResetThreshold: number;        // 归零重置阈值 (默认: 0.02)
  noveltyThreshold: number;          // 新颖性阈值 (默认: 0.7)
  decayInterval: number;             // 衰减间隔 (毫秒, 默认: 300000 = 5分钟)
  enableRealTimeAdjustment: boolean; // 启用实时参数调整
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  totalItems: number;
  hitRate: number;
  missRate: number;
  averageWeightedScore: number;
  decayOperations: number;
  eliminationOperations: number;
  resetOperations: number;
  noiseSuppressionOperations: number;
  lastOptimizationTime: string;
}

/**
 * 智能缓存层实现
 */
export class IntelligentCacheLayer {
  private cache: Map<string, CacheItem> = new Map();
  private config: IntelligentCacheConfig;
  private stats: CacheStats;
  private decayTimer: NodeJS.Timeout | null = null;
  private initialized: boolean = false;

  constructor(config?: Partial<IntelligentCacheConfig>) {
    this.config = {
      maxCacheSize: 1000,
      decayCoefficients: {
        primary: 1.0,
        secondary: 0.8,
        tertiary: 0.6
      },
      tailEliminationThreshold: 0.1,
      noiseSuppressionThreshold: 0.05,
      zeroResetThreshold: 0.02,
      noveltyThreshold: 0.7,
      decayInterval: 300000, // 5分钟
      enableRealTimeAdjustment: true,
      ...config
    };

    this.stats = {
      totalItems: 0,
      hitRate: 0,
      missRate: 0,
      averageWeightedScore: 0,
      decayOperations: 0,
      eliminationOperations: 0,
      resetOperations: 0,
      noiseSuppressionOperations: 0,
      lastOptimizationTime: new Date().toISOString()
    };
  }

  /**
   * 初始化智能缓存层
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🧠 初始化智能缓存层...');
      
      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();
      
      // 启动定期衰减
      this.startPeriodicDecay();
      
      this.initialized = true;
      console.log('🧠 智能缓存层初始化完成');
    } catch (error) {
      console.error('❌ 智能缓存层初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加缓存项
   */
  async addCacheItem(
    parentChunkId: string,
    content: string,
    metadata: any,
    vector?: number[]
  ): Promise<string> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // 生成缓存项ID - 如果父ID不存在，先创建一个用户输入ID
      let cacheId: string;
      try {
        cacheId = await globalIdManager.generateDerivedId(
          parentChunkId,
          'cache_item',
          'intelligent_cache'
        );
      } catch (error) {
        // 如果父ID不存在，生成一个新的用户输入ID作为缓存ID
        cacheId = await globalIdManager.generateUserInputId();
        console.log(`🧠 父ID不存在，生成新的缓存ID: ${cacheId}`);
      }

      // 计算初始权重分数
      const initialWeightedScore = this.calculateInitialWeightedScore(metadata);

      // 创建缓存项
      const cacheItem: CacheItem = {
        id: cacheId,
        parentChunkId,
        content,
        vector,
        metadata: {
          globalId: cacheId,
          sourceType: metadata.sourceType || 'unknown',
          sourceDocumentId: metadata.sourceDocumentId || '',
          createdAt: metadata.createdAt || new Date().toISOString(),
          lastAccessedAt: new Date().toISOString(),
          accessCount: 0,
          relevanceScore: metadata.relevanceScore || 0.5,
          semanticScore: metadata.semanticScore || 0.5,
          temporalScore: metadata.temporalScore || 0.5
        },
        cacheMetrics: {
          hitCount: 0,
          missCount: 0,
          weightedScore: initialWeightedScore,
          decayFactor: 1.0,
          noiseLevel: 0,
          lastDecayTime: new Date().toISOString()
        }
      };

      // 检查缓存容量
      if (this.cache.size >= this.config.maxCacheSize) {
        await this.performTailElimination();
      }

      // 添加到缓存
      this.cache.set(cacheId, cacheItem);
      this.updateStats();

      console.log(`🧠 缓存项已添加: ${cacheId} (权重: ${initialWeightedScore.toFixed(3)})`);
      
      return cacheId;
    } catch (error) {
      console.error('❌ 添加缓存项失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存项
   */
  async getCacheItem(cacheId: string): Promise<CacheItem | null> {
    const item = this.cache.get(cacheId);
    
    if (item) {
      // 更新访问统计
      item.metadata.lastAccessedAt = new Date().toISOString();
      item.metadata.accessCount++;
      item.cacheMetrics.hitCount++;
      
      // 重新计算权重分数
      item.cacheMetrics.weightedScore = this.calculateWeightedScore(item);
      
      this.stats.hitRate = this.calculateHitRate();
      
      console.log(`🧠 缓存命中: ${cacheId} (权重: ${item.cacheMetrics.weightedScore.toFixed(3)})`);
      
      return item;
    } else {
      this.stats.missRate = this.calculateMissRate();
      console.log(`🧠 缓存未命中: ${cacheId}`);
      return null;
    }
  }

  /**
   * 搜索缓存项
   */
  async searchCache(
    query: string,
    queryVector?: number[],
    maxResults: number = 10
  ): Promise<CacheItem[]> {
    const results: Array<{ item: CacheItem; score: number }> = [];

    for (const item of this.cache.values()) {
      let score = 0;

      // 文本相似度评分
      if (query) {
        const textSimilarity = this.calculateTextSimilarity(query, item.content);
        score += textSimilarity * 0.4;
      }

      // 向量相似度评分
      if (queryVector && item.vector) {
        const vectorSimilarity = this.calculateVectorSimilarity(queryVector, item.vector);
        score += vectorSimilarity * 0.4;
      }

      // 权重分数加成
      score += item.cacheMetrics.weightedScore * 0.2;

      if (score > 0.1) { // 最低相关性阈值
        results.push({ item, score });
      }
    }

    // 按分数排序
    results.sort((a, b) => b.score - a.score);

    // 更新访问统计
    const topResults = results.slice(0, maxResults);
    for (const { item } of topResults) {
      item.metadata.lastAccessedAt = new Date().toISOString();
      item.metadata.accessCount++;
      item.cacheMetrics.hitCount++;
    }

    console.log(`🧠 缓存搜索完成: ${topResults.length}/${results.length} 结果`);

    return topResults.map(r => r.item);
  }

  /**
   * 执行加权历史衰减
   */
  async performWeightedDecay(): Promise<void> {
    console.log('🧠 执行加权历史衰减...');
    
    let decayCount = 0;
    const now = new Date();

    for (const item of this.cache.values()) {
      const lastDecay = new Date(item.cacheMetrics.lastDecayTime);
      const timeSinceDecay = now.getTime() - lastDecay.getTime();

      // 如果距离上次衰减超过间隔时间
      if (timeSinceDecay >= this.config.decayInterval) {
        // 计算衰减系数
        const decayFactor = this.calculateDecayFactor(item);
        
        // 应用衰减
        item.cacheMetrics.weightedScore *= decayFactor;
        item.cacheMetrics.decayFactor = decayFactor;
        item.cacheMetrics.lastDecayTime = now.toISOString();
        
        decayCount++;
      }
    }

    this.stats.decayOperations += decayCount;
    console.log(`🧠 衰减完成: ${decayCount} 个项目`);
  }

  /**
   * 执行尾部淘汰
   */
  async performTailElimination(): Promise<void> {
    console.log('🧠 执行尾部淘汰...');
    
    // 按权重分数排序
    const sortedItems = Array.from(this.cache.values())
      .sort((a, b) => b.cacheMetrics.weightedScore - a.cacheMetrics.weightedScore);

    // 计算淘汰数量
    const eliminationCount = Math.floor(this.cache.size * 0.1); // 淘汰10%
    const itemsToEliminate = sortedItems.slice(-eliminationCount);

    // 执行淘汰
    for (const item of itemsToEliminate) {
      if (item.cacheMetrics.weightedScore < this.config.tailEliminationThreshold) {
        this.cache.delete(item.id);
        console.log(`🧠 淘汰低权重项: ${item.id} (权重: ${item.cacheMetrics.weightedScore.toFixed(3)})`);
      }
    }

    this.stats.eliminationOperations += itemsToEliminate.length;
    console.log(`🧠 尾部淘汰完成: ${itemsToEliminate.length} 个项目`);
  }

  /**
   * 执行噪声抑制
   */
  async performNoiseSuppressionn(): Promise<void> {
    console.log('🧠 执行噪声抑制...');
    
    let suppressionCount = 0;

    for (const item of this.cache.values()) {
      // 计算噪声水平
      const noiseLevel = this.calculateNoiseLevel(item);
      item.cacheMetrics.noiseLevel = noiseLevel;

      // 如果噪声水平过高，降低权重
      if (noiseLevel > this.config.noiseSuppressionThreshold) {
        const suppressionFactor = 1 - (noiseLevel * 0.5);
        item.cacheMetrics.weightedScore *= suppressionFactor;
        suppressionCount++;
        
        console.log(`🧠 噪声抑制: ${item.id} (噪声: ${noiseLevel.toFixed(3)}, 抑制: ${suppressionFactor.toFixed(3)})`);
      }
    }

    this.stats.noiseSuppressionOperations += suppressionCount;
    console.log(`🧠 噪声抑制完成: ${suppressionCount} 个项目`);
  }

  /**
   * 执行归零重置
   */
  async performZeroReset(): Promise<void> {
    console.log('🧠 执行归零重置...');
    
    let resetCount = 0;

    for (const item of this.cache.values()) {
      // 检查是否需要归零重置
      if (this.shouldPerformZeroReset(item)) {
        item.cacheMetrics.weightedScore = 0;
        item.cacheMetrics.decayFactor = 0;
        resetCount++;
        
        console.log(`🧠 归零重置: ${item.id} (新颖性不足)`);
      }
    }

    this.stats.resetOperations += resetCount;
    console.log(`🧠 归零重置完成: ${resetCount} 个项目`);
  }

  /**
   * 更新配置参数（实时调整）
   */
  updateConfig(newConfig: Partial<IntelligentCacheConfig>): void {
    if (!this.config.enableRealTimeAdjustment) {
      console.warn('⚠️ 实时参数调整未启用');
      return;
    }

    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };
    
    console.log('🧠 缓存配置已更新:', {
      old: oldConfig,
      new: this.config,
      changes: Object.keys(newConfig)
    });
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.updateStats();
    console.log('🧠 缓存已清空');
  }

  /**
   * 销毁缓存层
   */
  destroy(): void {
    if (this.decayTimer) {
      clearInterval(this.decayTimer);
      this.decayTimer = null;
    }
    this.cache.clear();
    this.initialized = false;
    console.log('🧠 智能缓存层已销毁');
  }

  // 私有方法

  /**
   * 启动定期衰减
   */
  private startPeriodicDecay(): void {
    this.decayTimer = setInterval(async () => {
      try {
        await this.performWeightedDecay();
        await this.performNoiseSuppressionn();
        
        // 定期执行尾部淘汰
        if (this.cache.size > this.config.maxCacheSize * 0.8) {
          await this.performTailElimination();
        }
        
        this.stats.lastOptimizationTime = new Date().toISOString();
      } catch (error) {
        console.error('❌ 定期衰减失败:', error);
      }
    }, this.config.decayInterval);
  }

  /**
   * 计算初始权重分数
   */
  private calculateInitialWeightedScore(metadata: any): number {
    const relevance = metadata.relevanceScore || 0.5;
    const semantic = metadata.semanticScore || 0.5;
    const temporal = metadata.temporalScore || 0.5;
    
    return (relevance * 0.4 + semantic * 0.4 + temporal * 0.2) * this.config.decayCoefficients.primary;
  }

  /**
   * 计算权重分数
   */
  private calculateWeightedScore(item: CacheItem): number {
    const baseScore = (
      item.metadata.relevanceScore * 0.4 +
      item.metadata.semanticScore * 0.4 +
      item.metadata.temporalScore * 0.2
    );
    
    const accessBonus = Math.min(item.metadata.accessCount * 0.1, 0.5);
    const decayPenalty = 1 - item.cacheMetrics.decayFactor;
    
    return Math.max(0, baseScore + accessBonus - decayPenalty);
  }

  /**
   * 计算衰减系数
   */
  private calculateDecayFactor(item: CacheItem): number {
    const timeSinceCreation = Date.now() - new Date(item.metadata.createdAt).getTime();
    const timeSinceAccess = Date.now() - new Date(item.metadata.lastAccessedAt).getTime();
    
    // 基于时间的衰减
    const timeDecay = Math.exp(-timeSinceCreation / (1000 * 60 * 60 * 24)); // 24小时衰减
    
    // 基于访问频率的衰减
    const accessDecay = Math.exp(-timeSinceAccess / (1000 * 60 * 60)); // 1小时衰减
    
    // 选择适当的衰减系数
    if (item.metadata.accessCount > 10) {
      return this.config.decayCoefficients.primary * timeDecay * accessDecay;
    } else if (item.metadata.accessCount > 3) {
      return this.config.decayCoefficients.secondary * timeDecay * accessDecay;
    } else {
      return this.config.decayCoefficients.tertiary * timeDecay * accessDecay;
    }
  }

  /**
   * 计算噪声水平
   */
  private calculateNoiseLevel(item: CacheItem): number {
    const hitMissRatio = item.cacheMetrics.hitCount / Math.max(1, item.cacheMetrics.hitCount + item.cacheMetrics.missCount);
    const accessVariability = 1 / Math.max(1, item.metadata.accessCount);
    const scoreVariability = Math.abs(item.cacheMetrics.weightedScore - 0.5);
    
    return (1 - hitMissRatio) * 0.4 + accessVariability * 0.3 + scoreVariability * 0.3;
  }

  /**
   * 判断是否需要归零重置
   */
  private shouldPerformZeroReset(item: CacheItem): boolean {
    // 基于新颖性阈值判断
    const noveltyScore = item.metadata.relevanceScore * item.metadata.semanticScore;
    return noveltyScore < this.config.noveltyThreshold && 
           item.cacheMetrics.weightedScore < this.config.zeroResetThreshold;
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(query: string, content: string): number {
    // 简单的文本相似度计算（实际应用中可以使用更复杂的算法）
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentWords = content.toLowerCase().split(/\s+/);
    
    const intersection = queryWords.filter(word => contentWords.includes(word));
    const union = [...new Set([...queryWords, ...contentWords])];
    
    return intersection.length / union.length;
  }

  /**
   * 计算向量相似度
   */
  private calculateVectorSimilarity(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) return 0;
    
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    for (let i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }
    
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  /**
   * 计算命中率
   */
  private calculateHitRate(): number {
    const totalHits = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.cacheMetrics.hitCount, 0);
    const totalMisses = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.cacheMetrics.missCount, 0);
    
    return totalHits / Math.max(1, totalHits + totalMisses);
  }

  /**
   * 计算未命中率
   */
  private calculateMissRate(): number {
    return 1 - this.calculateHitRate();
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalItems = this.cache.size;
    this.stats.hitRate = this.calculateHitRate();
    this.stats.missRate = this.calculateMissRate();
    
    const totalWeightedScore = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.cacheMetrics.weightedScore, 0);
    this.stats.averageWeightedScore = totalWeightedScore / Math.max(1, this.cache.size);
  }
}

// 导出默认实例
export const intelligentCacheLayer = new IntelligentCacheLayer();
