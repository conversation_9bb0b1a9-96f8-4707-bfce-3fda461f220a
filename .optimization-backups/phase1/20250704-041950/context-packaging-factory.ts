/**
 * Context Packaging Factory - 上下文包装工厂
 * 实现可配置排序逻辑、优先级管理、智能上下文组装等核心功能
 */

import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import { intelligentCacheLayer } from './intelligent-cache-layer';

/**
 * 上下文项接口
 */
export interface ContextItem {
  id: string;
  parentChunkId: string;
  content: string;
  metadata: {
    globalId: string;
    sourceType: string;
    sourceDocumentId: string;
    createdAt: string;
    relevanceScore: number;
    semanticScore: number;
    temporalScore: number;
    importanceScore: number;
    priorityLevel: number;
  };
  packagingMetrics: {
    sortingScore: number;
    priorityWeight: number;
    contextualRelevance: number;
    positionScore: number;
    lastPackaged: string;
  };
}

/**
 * 排序策略枚举
 */
export enum SortingStrategy {
  RELEVANCE_FIRST = 'relevance_first',
  TEMPORAL_FIRST = 'temporal_first',
  IMPORTANCE_FIRST = 'importance_first',
  SEMANTIC_FIRST = 'semantic_first',
  BALANCED = 'balanced',
  CUSTOM = 'custom'
}

/**
 * 优先级策略枚举
 */
export enum PriorityStrategy {
  HIGH_FIRST = 'high_first',
  LOW_FIRST = 'low_first',
  BALANCED = 'balanced',
  ADAPTIVE = 'adaptive'
}

/**
 * 上下文包装配置
 */
export interface ContextPackagingConfig {
  maxContextLength: number;
  maxContextItems: number;
  sortingStrategy: SortingStrategy;
  priorityStrategy: PriorityStrategy;
  sortingWeights: {
    relevance: number;
    semantic: number;
    temporal: number;
    importance: number;
  };
  priorityLevels: {
    critical: number;    // 5
    high: number;        // 4
    medium: number;      // 3
    low: number;         // 2
    minimal: number;     // 1
  };
  optimizationSettings: {
    enableDuplicateRemoval: boolean;
    enableContentCompression: boolean;
    enableSmartTruncation: boolean;
    enableContextualGrouping: boolean;
  };
  qualityThresholds: {
    minRelevanceScore: number;
    minSemanticScore: number;
    minImportanceScore: number;
  };
  enableRealTimeAdjustment: boolean;
}

/**
 * 上下文包装结果
 */
export interface ContextPackage {
  packageId: string;
  timestamp: string;
  finalContext: string;
  contextItems: ContextItem[];
  packageMetadata: {
    totalLength: number;
    itemCount: number;
    averageRelevance: number;
    averageSemantic: number;
    averageImportance: number;
    sortingStrategy: SortingStrategy;
    priorityStrategy: PriorityStrategy;
    optimizationsApplied: string[];
    qualityScore: number;
  };
  packagingStats: {
    processingTime: number;
    itemsProcessed: number;
    itemsIncluded: number;
    itemsFiltered: number;
    duplicatesRemoved: number;
    compressionRatio: number;
  };
}

/**
 * 上下文包装统计
 */
interface PackagingStats {
  totalPackages: number;
  averageProcessingTime: number;
  averageContextLength: number;
  averageQualityScore: number;
  sortingStrategyUsage: Record<SortingStrategy, number>;
  priorityStrategyUsage: Record<PriorityStrategy, number>;
  optimizationEffectiveness: {
    duplicateRemoval: number;
    contentCompression: number;
    smartTruncation: number;
    contextualGrouping: number;
  };
}

/**
 * 上下文包装工厂实现
 */
export class ContextPackagingFactory {
  private config: ContextPackagingConfig;
  private stats: PackagingStats;
  private initialized: boolean = false;

  constructor(config?: Partial<ContextPackagingConfig>) {
    this.config = {
      maxContextLength: 4000,
      maxContextItems: 10,
      sortingStrategy: SortingStrategy.BALANCED,
      priorityStrategy: PriorityStrategy.ADAPTIVE,
      sortingWeights: {
        relevance: 0.3,
        semantic: 0.25,
        temporal: 0.2,
        importance: 0.25
      },
      priorityLevels: {
        critical: 5,
        high: 4,
        medium: 3,
        low: 2,
        minimal: 1
      },
      optimizationSettings: {
        enableDuplicateRemoval: true,
        enableContentCompression: true,
        enableSmartTruncation: true,
        enableContextualGrouping: true
      },
      qualityThresholds: {
        minRelevanceScore: 0.3,
        minSemanticScore: 0.3,
        minImportanceScore: 0.2
      },
      enableRealTimeAdjustment: true,
      ...config
    };

    this.stats = {
      totalPackages: 0,
      averageProcessingTime: 0,
      averageContextLength: 0,
      averageQualityScore: 0,
      sortingStrategyUsage: {
        [SortingStrategy.RELEVANCE_FIRST]: 0,
        [SortingStrategy.TEMPORAL_FIRST]: 0,
        [SortingStrategy.IMPORTANCE_FIRST]: 0,
        [SortingStrategy.SEMANTIC_FIRST]: 0,
        [SortingStrategy.BALANCED]: 0,
        [SortingStrategy.CUSTOM]: 0
      },
      priorityStrategyUsage: {
        [PriorityStrategy.HIGH_FIRST]: 0,
        [PriorityStrategy.LOW_FIRST]: 0,
        [PriorityStrategy.BALANCED]: 0,
        [PriorityStrategy.ADAPTIVE]: 0
      },
      optimizationEffectiveness: {
        duplicateRemoval: 0,
        contentCompression: 0,
        smartTruncation: 0,
        contextualGrouping: 0
      }
    };
  }

  /**
   * 初始化上下文包装工厂
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('📦 初始化上下文包装工厂...');

      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();

      // 确保智能缓存层已初始化
      await intelligentCacheLayer.initialize();

      this.initialized = true;
      console.log('📦 上下文包装工厂初始化完成');
    } catch (error) {
      console.error('❌ 上下文包装工厂初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建上下文包装
   */
  async createContextPackage(
    rawContextItems: any[],
    userMessage: string,
    retrievalContext?: any
  ): Promise<ContextPackage> {
    if (!this.initialized) {
      await this.initialize();
    }

    const startTime = Date.now();

    try {
      console.log(`📦 开始创建上下文包装: ${rawContextItems.length} 个原始项目`);

      // 生成包装ID
      const packageId = await globalIdManager.generateUserInputId();

      // 转换为标准上下文项
      const contextItems = await this.convertToContextItems(rawContextItems);

      // 应用质量过滤
      const filteredItems = this.applyQualityFiltering(contextItems);

      // 计算优先级
      const prioritizedItems = this.calculatePriorities(filteredItems, userMessage, retrievalContext);

      // 应用排序策略
      const sortedItems = this.applySortingStrategy(prioritizedItems);

      // 应用优化
      const optimizedItems = await this.applyOptimizations(sortedItems);

      // 构建最终上下文
      const finalContext = this.buildFinalContext(optimizedItems);

      // 计算统计信息
      const processingTime = Date.now() - startTime;
      const packageMetadata = this.calculatePackageMetadata(optimizedItems, finalContext);
      const packagingStats = this.calculatePackagingStats(
        contextItems,
        optimizedItems,
        processingTime
      );

      // 构建上下文包装
      const contextPackage: ContextPackage = {
        packageId,
        timestamp: new Date().toISOString(),
        finalContext,
        contextItems: optimizedItems,
        packageMetadata,
        packagingStats
      };

      // 更新统计信息
      this.updateStats(contextPackage);

      console.log(`📦 上下文包装创建完成: ${processingTime}ms, ${optimizedItems.length} 项目, ${finalContext.length} 字符`);

      return contextPackage;

    } catch (error) {
      console.error('❌ 上下文包装创建失败:', error);
      throw error;
    }
  }

  /**
   * 转换为标准上下文项
   */
  private async convertToContextItems(rawItems: any[]): Promise<ContextItem[]> {
    const contextItems: ContextItem[] = [];

    for (const rawItem of rawItems) {
      try {
        // 生成上下文项ID
        const itemId = await globalIdManager.generateUserInputId();

        const contextItem: ContextItem = {
          id: itemId,
          parentChunkId: rawItem.chunkId || rawItem.id || itemId,
          content: rawItem.content || rawItem.text || '',
          metadata: {
            globalId: rawItem.globalId || itemId,
            sourceType: rawItem.metadata?.sourceType || rawItem.sourceType || 'unknown',
            sourceDocumentId: rawItem.metadata?.sourceDocumentId || rawItem.sourceDocumentId || '',
            createdAt: rawItem.metadata?.createdAt || rawItem.createdAt || new Date().toISOString(),
            relevanceScore: rawItem.similarity || rawItem.score || rawItem.relevanceScore || 0.5,
            semanticScore: rawItem.metadata?.semanticScore || rawItem.semanticScore || 0.5,
            temporalScore: rawItem.metadata?.temporalScore || rawItem.temporalScore || 0.5,
            importanceScore: rawItem.metadata?.importanceScore || rawItem.importanceScore || 0.5,
            priorityLevel: rawItem.metadata?.priorityLevel || rawItem.priorityLevel || 3
          },
          packagingMetrics: {
            sortingScore: 0,
            priorityWeight: 0,
            contextualRelevance: 0,
            positionScore: 0,
            lastPackaged: new Date().toISOString()
          }
        };

        contextItems.push(contextItem);
      } catch (error) {
        console.warn('⚠️ 转换上下文项失败:', error);
      }
    }

    return contextItems;
  }

  /**
   * 应用质量过滤
   */
  private applyQualityFiltering(items: ContextItem[]): ContextItem[] {
    return items.filter(item => {
      return (
        item.metadata.relevanceScore >= this.config.qualityThresholds.minRelevanceScore &&
        item.metadata.semanticScore >= this.config.qualityThresholds.minSemanticScore &&
        item.metadata.importanceScore >= this.config.qualityThresholds.minImportanceScore
      );
    });
  }

  /**
   * 计算优先级
   */
  private calculatePriorities(
    items: ContextItem[],
    userMessage: string,
    retrievalContext?: any
  ): ContextItem[] {
    for (const item of items) {
      // 基础优先级权重
      let priorityWeight = this.config.priorityLevels.medium;

      // 根据优先级策略调整
      switch (this.config.priorityStrategy) {
        case PriorityStrategy.HIGH_FIRST:
          priorityWeight = item.metadata.priorityLevel * 1.2;
          break;

        case PriorityStrategy.LOW_FIRST:
          priorityWeight = (6 - item.metadata.priorityLevel) * 1.2;
          break;

        case PriorityStrategy.BALANCED:
          priorityWeight = item.metadata.priorityLevel;
          break;

        case PriorityStrategy.ADAPTIVE:
          // 自适应优先级：基于用户消息和检索上下文
          priorityWeight = this.calculateAdaptivePriority(item, userMessage, retrievalContext);
          break;
      }

      item.packagingMetrics.priorityWeight = priorityWeight;
    }

    return items;
  }

  /**
   * 应用排序策略
   */
  private applySortingStrategy(items: ContextItem[]): ContextItem[] {
    // 计算排序分数
    for (const item of items) {
      item.packagingMetrics.sortingScore = this.calculateSortingScore(item);
    }

    // 根据排序策略排序
    switch (this.config.sortingStrategy) {
      case SortingStrategy.RELEVANCE_FIRST:
        return items.sort((a, b) => b.metadata.relevanceScore - a.metadata.relevanceScore);

      case SortingStrategy.TEMPORAL_FIRST:
        return items.sort((a, b) => b.metadata.temporalScore - a.metadata.temporalScore);

      case SortingStrategy.IMPORTANCE_FIRST:
        return items.sort((a, b) => b.metadata.importanceScore - a.metadata.importanceScore);

      case SortingStrategy.SEMANTIC_FIRST:
        return items.sort((a, b) => b.metadata.semanticScore - a.metadata.semanticScore);

      case SortingStrategy.BALANCED:
        return items.sort((a, b) => b.packagingMetrics.sortingScore - a.packagingMetrics.sortingScore);

      case SortingStrategy.CUSTOM:
        return this.applyCustomSorting(items);

      default:
        return items.sort((a, b) => b.packagingMetrics.sortingScore - a.packagingMetrics.sortingScore);
    }
  }

  /**
   * 计算排序分数
   */
  private calculateSortingScore(item: ContextItem): number {
    const weights = this.config.sortingWeights;

    return (
      item.metadata.relevanceScore * weights.relevance +
      item.metadata.semanticScore * weights.semantic +
      item.metadata.temporalScore * weights.temporal +
      item.metadata.importanceScore * weights.importance
    ) * item.packagingMetrics.priorityWeight;
  }

  /**
   * 应用优化
   */
  private async applyOptimizations(items: ContextItem[]): Promise<ContextItem[]> {
    let optimizedItems = [...items];
    const optimizationsApplied: string[] = [];

    // 1. 去重优化
    if (this.config.optimizationSettings.enableDuplicateRemoval) {
      const beforeCount = optimizedItems.length;
      optimizedItems = this.removeDuplicates(optimizedItems);
      const afterCount = optimizedItems.length;

      if (beforeCount > afterCount) {
        optimizationsApplied.push('duplicate_removal');
        this.stats.optimizationEffectiveness.duplicateRemoval += beforeCount - afterCount;
      }
    }

    // 2. 上下文分组优化
    if (this.config.optimizationSettings.enableContextualGrouping) {
      optimizedItems = this.applyContextualGrouping(optimizedItems);
      optimizationsApplied.push('contextual_grouping');
      this.stats.optimizationEffectiveness.contextualGrouping++;
    }

    // 3. 智能截断优化
    if (this.config.optimizationSettings.enableSmartTruncation) {
      optimizedItems = this.applySmartTruncation(optimizedItems);
      optimizationsApplied.push('smart_truncation');
      this.stats.optimizationEffectiveness.smartTruncation++;
    }

    // 4. 内容压缩优化
    if (this.config.optimizationSettings.enableContentCompression) {
      optimizedItems = this.applyContentCompression(optimizedItems);
      optimizationsApplied.push('content_compression');
      this.stats.optimizationEffectiveness.contentCompression++;
    }

    // 限制项目数量
    if (optimizedItems.length > this.config.maxContextItems) {
      optimizedItems = optimizedItems.slice(0, this.config.maxContextItems);
    }

    return optimizedItems;
  }

  /**
   * 去重优化
   */
  private removeDuplicates(items: ContextItem[]): ContextItem[] {
    const seen = new Set<string>();
    const uniqueItems: ContextItem[] = [];

    for (const item of items) {
      // 基于内容的简单去重
      const contentHash = this.generateContentHash(item.content);

      if (!seen.has(contentHash)) {
        seen.add(contentHash);
        uniqueItems.push(item);
      }
    }

    return uniqueItems;
  }

  /**
   * 上下文分组优化
   */
  private applyContextualGrouping(items: ContextItem[]): ContextItem[] {
    // 按来源类型分组
    const groups = new Map<string, ContextItem[]>();

    for (const item of items) {
      const sourceType = item.metadata.sourceType;
      if (!groups.has(sourceType)) {
        groups.set(sourceType, []);
      }
      groups.get(sourceType)!.push(item);
    }

    // 重新排列：每个组的最佳项目优先
    const groupedItems: ContextItem[] = [];
    const groupKeys = Array.from(groups.keys()).sort();

    for (const groupKey of groupKeys) {
      const groupItems = groups.get(groupKey)!;
      // 按排序分数排序组内项目
      groupItems.sort((a, b) => b.packagingMetrics.sortingScore - a.packagingMetrics.sortingScore);
      groupedItems.push(...groupItems);
    }

    return groupedItems;
  }

  /**
   * 智能截断优化
   */
  private applySmartTruncation(items: ContextItem[]): ContextItem[] {
    let totalLength = 0;
    const truncatedItems: ContextItem[] = [];

    for (const item of items) {
      const itemLength = item.content.length;

      if (totalLength + itemLength <= this.config.maxContextLength) {
        truncatedItems.push(item);
        totalLength += itemLength;
      } else {
        // 尝试截断内容
        const remainingLength = this.config.maxContextLength - totalLength;
        if (remainingLength > 100) { // 至少保留100字符
          const truncatedContent = this.smartTruncateContent(item.content, remainingLength);
          const truncatedItem = {
            ...item,
            content: truncatedContent
          };
          truncatedItems.push(truncatedItem);
          break;
        } else {
          break;
        }
      }
    }

    return truncatedItems;
  }

  /**
   * 内容压缩优化
   */
  private applyContentCompression(items: ContextItem[]): ContextItem[] {
    return items.map(item => ({
      ...item,
      content: this.compressContent(item.content)
    }));
  }

  /**
   * 构建最终上下文
   */
  private buildFinalContext(items: ContextItem[]): string {
    const contextParts: string[] = [];

    // 添加上下文头部
    contextParts.push('以下是相关的上下文信息：\n');

    // 添加每个上下文项
    items.forEach((item, index) => {
      const prefix = `${index + 1}. `;
      const content = item.content.trim();
      const sourceInfo = item.metadata.sourceType !== 'unknown'
        ? ` (来源: ${item.metadata.sourceType})`
        : '';

      contextParts.push(`${prefix}${content}${sourceInfo}\n`);
    });

    // 添加上下文尾部
    if (items.length > 0) {
      contextParts.push('\n请基于以上信息回答用户的问题。');
    }

    return contextParts.join('');
  }

  /**
   * 更新配置参数（实时调整）
   */
  updateConfig(newConfig: Partial<ContextPackagingConfig>): void {
    if (!this.config.enableRealTimeAdjustment) {
      console.warn('⚠️ 实时参数调整未启用');
      return;
    }

    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    console.log('📦 上下文包装配置已更新:', {
      old: oldConfig,
      new: this.config,
      changes: Object.keys(newConfig)
    });
  }

  /**
   * 获取包装统计信息
   */
  getPackagingStats(): PackagingStats {
    return { ...this.stats };
  }

  // 私有辅助方法

  /**
   * 计算自适应优先级
   */
  private calculateAdaptivePriority(
    item: ContextItem,
    userMessage: string,
    retrievalContext?: any
  ): number {
    let adaptivePriority = item.metadata.priorityLevel;

    // 基于用户消息的关键词匹配
    const messageKeywords = userMessage.toLowerCase().split(/\s+/);
    const contentKeywords = item.content.toLowerCase().split(/\s+/);
    const keywordMatch = messageKeywords.filter(keyword =>
      contentKeywords.some(contentKeyword => contentKeyword.includes(keyword))
    ).length;

    if (keywordMatch > 0) {
      adaptivePriority += keywordMatch * 0.5;
    }

    // 基于检索上下文的相关性
    if (retrievalContext) {
      const contextRelevance = this.calculateContextualRelevance(item, retrievalContext);
      adaptivePriority += contextRelevance;
    }

    // 基于时效性的动态调整
    const timeSinceCreation = Date.now() - new Date(item.metadata.createdAt).getTime();
    const daysSinceCreation = timeSinceCreation / (1000 * 60 * 60 * 24);

    if (daysSinceCreation < 1) {
      adaptivePriority += 1; // 新内容加分
    } else if (daysSinceCreation > 30) {
      adaptivePriority -= 0.5; // 旧内容减分
    }

    return Math.max(1, Math.min(5, adaptivePriority));
  }

  /**
   * 应用自定义排序
   */
  private applyCustomSorting(items: ContextItem[]): ContextItem[] {
    // 自定义排序逻辑：优先级 > 相关性 > 时效性 > 语义性 > 重要性
    return items.sort((a, b) => {
      // 首先按优先级排序
      if (a.packagingMetrics.priorityWeight !== b.packagingMetrics.priorityWeight) {
        return b.packagingMetrics.priorityWeight - a.packagingMetrics.priorityWeight;
      }

      // 然后按相关性排序
      if (a.metadata.relevanceScore !== b.metadata.relevanceScore) {
        return b.metadata.relevanceScore - a.metadata.relevanceScore;
      }

      // 然后按时效性排序
      if (a.metadata.temporalScore !== b.metadata.temporalScore) {
        return b.metadata.temporalScore - a.metadata.temporalScore;
      }

      // 最后按综合分数排序
      return b.packagingMetrics.sortingScore - a.packagingMetrics.sortingScore;
    });
  }

  /**
   * 计算上下文相关性
   */
  private calculateContextualRelevance(item: ContextItem, retrievalContext: any): number {
    // 简化的上下文相关性计算
    let relevance = 0;

    if (retrievalContext.primaryIntent) {
      // 基于主要意图的相关性
      const intentKeywords = retrievalContext.primaryIntent.toLowerCase().split(/\s+/);
      const contentKeywords = item.content.toLowerCase().split(/\s+/);
      const intentMatch = intentKeywords.filter(keyword =>
        contentKeywords.some(contentKeyword => contentKeyword.includes(keyword))
      ).length;

      relevance += intentMatch * 0.3;
    }

    if (retrievalContext.emotionalTone) {
      // 基于情感色调的相关性
      const emotionalBonus = retrievalContext.emotionalTone === '积极' ? 0.2 :
                           retrievalContext.emotionalTone === '消极' ? 0.1 : 0;
      relevance += emotionalBonus;
    }

    return Math.min(2, relevance);
  }

  /**
   * 生成内容哈希
   */
  private generateContentHash(content: string): string {
    // 简单的内容哈希生成
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 智能截断内容
   */
  private smartTruncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) {
      return content;
    }

    // 尝试在句子边界截断
    const sentences = content.split(/[。！？.!?]/);
    let truncated = '';

    for (const sentence of sentences) {
      if (truncated.length + sentence.length + 1 <= maxLength - 3) {
        truncated += sentence + '。';
      } else {
        break;
      }
    }

    if (truncated.length === 0) {
      // 如果没有完整句子，直接截断
      truncated = content.substring(0, maxLength - 3);
    }

    return truncated + '...';
  }

  /**
   * 压缩内容
   */
  private compressContent(content: string): string {
    // 简单的内容压缩：移除多余空白和重复标点
    return content
      .replace(/\s+/g, ' ')           // 多个空白字符替换为单个空格
      .replace(/[。]{2,}/g, '。')      // 多个句号替换为单个
      .replace(/[！]{2,}/g, '！')      // 多个感叹号替换为单个
      .replace(/[？]{2,}/g, '？')      // 多个问号替换为单个
      .trim();
  }

  /**
   * 计算包装元数据
   */
  private calculatePackageMetadata(
    items: ContextItem[],
    finalContext: string
  ): ContextPackage['packageMetadata'] {
    const totalRelevance = items.reduce((sum, item) => sum + item.metadata.relevanceScore, 0);
    const totalSemantic = items.reduce((sum, item) => sum + item.metadata.semanticScore, 0);
    const totalImportance = items.reduce((sum, item) => sum + item.metadata.importanceScore, 0);

    const averageRelevance = items.length > 0 ? totalRelevance / items.length : 0;
    const averageSemantic = items.length > 0 ? totalSemantic / items.length : 0;
    const averageImportance = items.length > 0 ? totalImportance / items.length : 0;

    const qualityScore = (averageRelevance + averageSemantic + averageImportance) / 3;

    return {
      totalLength: finalContext.length,
      itemCount: items.length,
      averageRelevance: Math.round(averageRelevance * 1000) / 1000,
      averageSemantic: Math.round(averageSemantic * 1000) / 1000,
      averageImportance: Math.round(averageImportance * 1000) / 1000,
      sortingStrategy: this.config.sortingStrategy,
      priorityStrategy: this.config.priorityStrategy,
      optimizationsApplied: this.getAppliedOptimizations(),
      qualityScore: Math.round(qualityScore * 1000) / 1000
    };
  }

  /**
   * 计算包装统计
   */
  private calculatePackagingStats(
    originalItems: ContextItem[],
    finalItems: ContextItem[],
    processingTime: number
  ): ContextPackage['packagingStats'] {
    const duplicatesRemoved = originalItems.length - finalItems.length;
    const originalLength = originalItems.reduce((sum, item) => sum + item.content.length, 0);
    const finalLength = finalItems.reduce((sum, item) => sum + item.content.length, 0);
    const compressionRatio = originalLength > 0 ? finalLength / originalLength : 1;

    return {
      processingTime,
      itemsProcessed: originalItems.length,
      itemsIncluded: finalItems.length,
      itemsFiltered: Math.max(0, originalItems.length - finalItems.length),
      duplicatesRemoved: Math.max(0, duplicatesRemoved),
      compressionRatio: Math.round(compressionRatio * 1000) / 1000
    };
  }

  /**
   * 获取应用的优化
   */
  private getAppliedOptimizations(): string[] {
    const optimizations: string[] = [];

    if (this.config.optimizationSettings.enableDuplicateRemoval) {
      optimizations.push('duplicate_removal');
    }
    if (this.config.optimizationSettings.enableContentCompression) {
      optimizations.push('content_compression');
    }
    if (this.config.optimizationSettings.enableSmartTruncation) {
      optimizations.push('smart_truncation');
    }
    if (this.config.optimizationSettings.enableContextualGrouping) {
      optimizations.push('contextual_grouping');
    }

    return optimizations;
  }

  /**
   * 更新统计信息
   */
  private updateStats(contextPackage: ContextPackage): void {
    this.stats.totalPackages++;

    // 更新平均处理时间
    this.stats.averageProcessingTime =
      (this.stats.averageProcessingTime * (this.stats.totalPackages - 1) +
       contextPackage.packagingStats.processingTime) / this.stats.totalPackages;

    // 更新平均上下文长度
    this.stats.averageContextLength =
      (this.stats.averageContextLength * (this.stats.totalPackages - 1) +
       contextPackage.packageMetadata.totalLength) / this.stats.totalPackages;

    // 更新平均质量分数
    this.stats.averageQualityScore =
      (this.stats.averageQualityScore * (this.stats.totalPackages - 1) +
       contextPackage.packageMetadata.qualityScore) / this.stats.totalPackages;

    // 更新策略使用统计
    this.stats.sortingStrategyUsage[contextPackage.packageMetadata.sortingStrategy]++;
    this.stats.priorityStrategyUsage[contextPackage.packageMetadata.priorityStrategy]++;
  }
}

// 导出默认实例
export const contextPackagingFactory = new ContextPackagingFactory();