/**
 * Retrieval Result Optimizer - 检索结果优化器
 * 原名：Intelligent Cache Layer（智能缓存层）
 * 
 * 重命名原因：
 * 1. 避免与传统"缓存"概念混淆
 * 2. 明确核心职责：检索结果的排序优化和历史权重管理
 * 3. 提升架构清晰度和可维护性
 * 
 * 核心功能：
 * - 历史加权衰减排序
 * - 噪声抑制和过滤
 * - 温度控制和归零重置
 * - 检索结果质量优化
 */

import { globalIdManager } from '@/lib/services/vector-database/global-id-system';

/**
 * 优化项接口（原CacheItem）
 */
export interface OptimizationItem {
  id: string;
  parentChunkId: string;
  content: string;
  vector?: number[];
  metadata: {
    globalId: string;
    sourceType: string;
    sourceDocumentId: string;
    createdAt: string;
    lastAccessedAt: string;
    accessCount: number;
    relevanceScore: number;
    semanticScore: number;
    temporalScore: number;
  };
  optimizationMetrics: {
    hitCount: number;
    missCount: number;
    weightedScore: number;
    decayFactor: number;
    noiseLevel: number;
    lastDecayTime: string;
  };
}

/**
 * 优化配置接口
 */
export interface OptimizationConfig {
  maxOptimizationItems: number;        // 最大优化项数量
  decayInterval: number;               // 衰减间隔（毫秒）
  decayRate: number;                   // 衰减率
  noiseThreshold: number;              // 噪声阈值
  temperatureDecayFactor: number;      // 温度衰减因子
  zeroResetThreshold: number;          // 归零重置阈值
  qualityThreshold: number;            // 质量阈值
  enableRealTimeOptimization: boolean; // 启用实时优化
}

/**
 * 优化统计接口
 */
export interface OptimizationStats {
  totalItems: number;
  activeItems: number;
  optimizedItems: number;
  noiseFilteredItems: number;
  averageWeightedScore: number;
  hitRate: number;
  missRate: number;
  optimizationEfficiency: number;
}

/**
 * 检索结果优化器
 * 负责检索结果的排序优化、历史权重管理和噪声过滤
 */
export class RetrievalResultOptimizer {
  private optimizationState = new Map<string, OptimizationItem>();
  private weightingHistory = new Map<string, number[]>();
  private config: OptimizationConfig;
  private stats: OptimizationStats;
  private decayTimer: NodeJS.Timeout | null = null;
  private initialized: boolean = false;

  constructor(config?: Partial<OptimizationConfig>) {
    this.config = {
      maxOptimizationItems: 1000,
      decayInterval: 300000, // 5分钟
      decayRate: 0.95,
      noiseThreshold: 0.1,
      temperatureDecayFactor: 0.9,
      zeroResetThreshold: 0.05,
      qualityThreshold: 0.3,
      enableRealTimeOptimization: true,
      ...config
    };

    this.stats = {
      totalItems: 0,
      activeItems: 0,
      optimizedItems: 0,
      noiseFilteredItems: 0,
      averageWeightedScore: 0,
      hitRate: 0,
      missRate: 0,
      optimizationEfficiency: 0
    };
  }

  /**
   * 初始化检索结果优化器
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🎯 初始化检索结果优化器...');
      
      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();
      
      // 启动定期衰减优化
      this.startPeriodicOptimization();
      
      this.initialized = true;
      console.log('🎯 检索结果优化器初始化完成');
    } catch (error) {
      console.error('❌ 检索结果优化器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 优化检索结果
   * 核心方法：对检索结果进行排序优化和权重调整
   */
  async optimizeRetrievalResults(
    results: any[],
    queryContext?: string
  ): Promise<any[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    console.log(`🎯 开始优化检索结果，输入项目数: ${results.length}`);

    const optimizedResults = [];
    const currentTime = new Date().toISOString();

    for (const result of results) {
      const parentChunkId = result.parentChunkId || result.id;
      
      // 获取或创建优化项
      let optimizationItem = await this.getOptimizationItem(parentChunkId);
      
      if (!optimizationItem) {
        optimizationItem = await this.createOptimizationItem(result, currentTime);
      } else {
        // 更新现有优化项
        await this.updateOptimizationItem(optimizationItem, result, currentTime);
      }

      // 计算优化后的权重分数
      const optimizedScore = this.calculateOptimizedScore(optimizationItem, result);
      
      // 应用噪声过滤
      if (optimizedScore >= this.config.noiseThreshold) {
        optimizedResults.push({
          ...result,
          optimizedScore,
          optimizationApplied: true,
          optimizationMetrics: optimizationItem.optimizationMetrics
        });
      } else {
        this.stats.noiseFilteredItems++;
        console.log(`🎯 噪声过滤: ${parentChunkId} (分数: ${optimizedScore.toFixed(3)})`);
      }
    }

    // 按优化分数排序
    optimizedResults.sort((a, b) => b.optimizedScore - a.optimizedScore);

    // 更新统计信息
    this.updateOptimizationStats(results.length, optimizedResults.length);

    console.log(`🎯 检索结果优化完成，输出项目数: ${optimizedResults.length}`);
    
    return optimizedResults;
  }

  /**
   * 获取优化项
   */
  async getOptimizationItem(itemId: string): Promise<OptimizationItem | null> {
    const item = this.optimizationState.get(itemId);
    
    if (item) {
      // 更新访问统计
      item.metadata.lastAccessedAt = new Date().toISOString();
      item.metadata.accessCount++;
      item.optimizationMetrics.hitCount++;
      
      // 重新计算权重分数
      item.optimizationMetrics.weightedScore = this.calculateWeightedScore(item);
      
      console.log(`🎯 优化项命中: ${itemId} (权重: ${item.optimizationMetrics.weightedScore.toFixed(3)})`);
      
      return item;
    } else {
      console.log(`🎯 优化项未命中: ${itemId}`);
      return null;
    }
  }

  /**
   * 创建优化项
   */
  private async createOptimizationItem(
    result: any,
    currentTime: string
  ): Promise<OptimizationItem> {
    const itemId = await globalIdManager.generateDerivedId(
      result.parentChunkId || result.id,
      'optimization_item',
      'retrieval'
    );

    const optimizationItem: OptimizationItem = {
      id: itemId,
      parentChunkId: result.parentChunkId || result.id,
      content: result.content || '',
      vector: result.vector,
      metadata: {
        globalId: itemId,
        sourceType: result.sourceType || 'retrieval',
        sourceDocumentId: result.sourceDocumentId || '',
        createdAt: currentTime,
        lastAccessedAt: currentTime,
        accessCount: 1,
        relevanceScore: result.similarity || 0.5,
        semanticScore: result.semanticScore || 0.5,
        temporalScore: result.temporalScore || 0.5
      },
      optimizationMetrics: {
        hitCount: 1,
        missCount: 0,
        weightedScore: this.calculateInitialWeightedScore(result),
        decayFactor: 1.0,
        noiseLevel: 0,
        lastDecayTime: currentTime
      }
    };

    // 应用大小限制
    if (this.optimizationState.size >= this.config.maxOptimizationItems) {
      await this.evictLeastOptimizedItems();
    }

    this.optimizationState.set(optimizationItem.parentChunkId, optimizationItem);
    this.stats.totalItems++;

    console.log(`🎯 创建优化项: ${optimizationItem.parentChunkId}`);
    
    return optimizationItem;
  }

  /**
   * 更新优化项
   */
  private async updateOptimizationItem(
    item: OptimizationItem,
    result: any,
    currentTime: string
  ): Promise<void> {
    // 更新元数据
    item.metadata.lastAccessedAt = currentTime;
    item.metadata.accessCount++;
    
    // 更新分数
    item.metadata.relevanceScore = result.similarity || item.metadata.relevanceScore;
    
    // 重新计算权重分数
    item.optimizationMetrics.weightedScore = this.calculateWeightedScore(item);
    
    // 更新历史权重记录
    this.updateWeightingHistory(item.parentChunkId, item.optimizationMetrics.weightedScore);
  }

  /**
   * 计算优化后的分数
   */
  private calculateOptimizedScore(item: OptimizationItem, result: any): number {
    const baseScore = result.similarity || 0.5;
    const weightedScore = item.optimizationMetrics.weightedScore;
    const decayFactor = item.optimizationMetrics.decayFactor;
    
    // 综合计算优化分数
    const optimizedScore = (baseScore * 0.6) + (weightedScore * 0.3) + (decayFactor * 0.1);
    
    return Math.max(0, Math.min(1, optimizedScore));
  }

  /**
   * 计算加权分数
   */
  private calculateWeightedScore(item: OptimizationItem): number {
    const accessWeight = Math.log(item.metadata.accessCount + 1) * 0.1;
    const relevanceWeight = item.metadata.relevanceScore * 0.4;
    const temporalWeight = this.calculateTemporalWeight(item.metadata.createdAt) * 0.3;
    const hitRateWeight = (item.optimizationMetrics.hitCount / 
                          (item.optimizationMetrics.hitCount + item.optimizationMetrics.missCount + 1)) * 0.2;
    
    return Math.max(0, Math.min(1, accessWeight + relevanceWeight + temporalWeight + hitRateWeight));
  }

  /**
   * 计算初始权重分数
   */
  private calculateInitialWeightedScore(result: any): number {
    return result.similarity || 0.5;
  }

  /**
   * 计算时间权重
   */
  private calculateTemporalWeight(createdAt: string): number {
    const now = Date.now();
    const created = new Date(createdAt).getTime();
    const ageInHours = (now - created) / (1000 * 60 * 60);
    
    // 时间衰减：24小时内权重为1，之后逐渐衰减
    return Math.max(0.1, Math.exp(-ageInHours / 24));
  }

  /**
   * 更新权重历史
   */
  private updateWeightingHistory(parentChunkId: string, score: number): void {
    if (!this.weightingHistory.has(parentChunkId)) {
      this.weightingHistory.set(parentChunkId, []);
    }
    
    const history = this.weightingHistory.get(parentChunkId)!;
    history.unshift(score);
    
    // 保持历史记录长度
    if (history.length > 10) {
      history.splice(10);
    }
  }

  /**
   * 启动定期优化
   */
  private startPeriodicOptimization(): void {
    if (this.decayTimer) {
      clearInterval(this.decayTimer);
    }

    this.decayTimer = setInterval(async () => {
      await this.performPeriodicOptimization();
    }, this.config.decayInterval);

    console.log(`🎯 启动定期优化，间隔: ${this.config.decayInterval}ms`);
  }

  /**
   * 执行定期优化
   */
  private async performPeriodicOptimization(): Promise<void> {
    console.log('🎯 执行定期优化...');
    
    const currentTime = new Date().toISOString();
    let optimizedCount = 0;
    let removedCount = 0;

    for (const [id, item] of this.optimizationState.entries()) {
      // 应用衰减
      item.optimizationMetrics.decayFactor *= this.config.decayRate;
      item.optimizationMetrics.lastDecayTime = currentTime;
      
      // 检查是否需要归零重置
      if (item.optimizationMetrics.decayFactor < this.config.zeroResetThreshold) {
        this.optimizationState.delete(id);
        this.weightingHistory.delete(id);
        removedCount++;
      } else {
        // 重新计算权重分数
        item.optimizationMetrics.weightedScore = this.calculateWeightedScore(item);
        optimizedCount++;
      }
    }

    this.stats.optimizedItems = optimizedCount;
    this.stats.activeItems = this.optimizationState.size;

    console.log(`🎯 定期优化完成: 优化 ${optimizedCount} 项，移除 ${removedCount} 项`);
  }

  /**
   * 淘汰最少优化的项目
   */
  private async evictLeastOptimizedItems(): Promise<void> {
    const items = Array.from(this.optimizationState.entries());
    
    // 按权重分数排序
    items.sort((a, b) => a[1].optimizationMetrics.weightedScore - b[1].optimizationMetrics.weightedScore);
    
    // 淘汰权重最低的20%
    const toEvict = Math.floor(items.length * 0.2);
    
    for (let i = 0; i < toEvict; i++) {
      const [id] = items[i];
      this.optimizationState.delete(id);
      this.weightingHistory.delete(id);
    }

    console.log(`🎯 淘汰优化项: ${toEvict} 个低权重项目`);
  }

  /**
   * 更新优化统计
   */
  private updateOptimizationStats(inputCount: number, outputCount: number): void {
    this.stats.optimizationEfficiency = outputCount / inputCount;
    this.stats.hitRate = this.calculateHitRate();
    this.stats.missRate = 1 - this.stats.hitRate;
    this.stats.averageWeightedScore = this.calculateAverageWeightedScore();
  }

  /**
   * 计算命中率
   */
  private calculateHitRate(): number {
    let totalHits = 0;
    let totalRequests = 0;
    
    for (const item of this.optimizationState.values()) {
      totalHits += item.optimizationMetrics.hitCount;
      totalRequests += item.optimizationMetrics.hitCount + item.optimizationMetrics.missCount;
    }
    
    return totalRequests > 0 ? totalHits / totalRequests : 0;
  }

  /**
   * 计算平均权重分数
   */
  private calculateAverageWeightedScore(): number {
    if (this.optimizationState.size === 0) return 0;
    
    let totalScore = 0;
    for (const item of this.optimizationState.values()) {
      totalScore += item.optimizationMetrics.weightedScore;
    }
    
    return totalScore / this.optimizationState.size;
  }

  /**
   * 获取优化统计
   */
  getOptimizationStats(): OptimizationStats {
    return { ...this.stats };
  }

  /**
   * 获取优化配置
   */
  getOptimizationConfig(): OptimizationConfig {
    return { ...this.config };
  }

  /**
   * 更新优化配置
   */
  updateOptimizationConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🎯 优化配置已更新:', newConfig);
  }

  /**
   * 销毁优化器
   */
  destroy(): void {
    if (this.decayTimer) {
      clearInterval(this.decayTimer);
      this.decayTimer = null;
    }
    
    this.optimizationState.clear();
    this.weightingHistory.clear();
    this.initialized = false;
    
    console.log('🎯 检索结果优化器已销毁');
  }
}

// 导出类型别名以保持向后兼容
export type CacheItem = OptimizationItem;
export type CacheConfig = OptimizationConfig;
export type CacheStats = OptimizationStats;

// 导出单例实例
export const retrievalResultOptimizer = new RetrievalResultOptimizer();
