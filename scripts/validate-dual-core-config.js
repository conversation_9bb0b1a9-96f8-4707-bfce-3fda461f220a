#!/usr/bin/env node

/**
 * 双核抽象层配置验证脚本
 * 用于验证生产环境配置的正确性和完整性
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 配置验证规则
const configRules = {
  // 必需的环境变量
  required: [
    'DUAL_CORE_DEBUG_MODE',
    'DUAL_CORE_CACHE_SIZE',
    'DUAL_CORE_MAX_CONTEXT_LENGTH',
    'DUAL_CORE_PERFORMANCE_MONITORING'
  ],
  
  // 数值类型验证
  numeric: {
    'DUAL_CORE_CACHE_SIZE': { min: 10, max: 200 },
    'DUAL_CORE_MAX_CONTEXT_LENGTH': { min: 1000, max: 10000 },
    'DUAL_CORE_MAX_MEMORY_USAGE_MB': { min: 50, max: 500 },
    'DUAL_CORE_MAX_PROCESSING_TIME_MS': { min: 50, max: 1000 },
    'DUAL_CORE_OPERATION_TIMEOUT_MS': { min: 5000, max: 60000 }
  },
  
  // 布尔类型验证
  boolean: [
    'DUAL_CORE_DEBUG_MODE',
    'DUAL_CORE_PERFORMANCE_MONITORING',
    'DUAL_CORE_ENABLE_QUALITY_ASSESSMENT',
    'DUAL_CORE_TEST_MODE'
  ],
  
  // 枚举值验证
  enum: {
    'DUAL_CORE_LOG_LEVEL': ['debug', 'info', 'warn', 'error'],
    'NODE_ENV': ['development', 'production', 'test'],
    'DUAL_CORE_ENVIRONMENT': ['development', 'production', 'staging', 'test']
  },
  
  // 生产环境特殊规则
  production: {
    'DUAL_CORE_DEBUG_MODE': 'false',
    'DUAL_CORE_TEST_MODE': 'false',
    'DUAL_CORE_MOCK_DATA_ENABLED': 'false',
    'NODE_ENV': 'production'
  }
};

// 安全检查规则
const securityRules = {
  // 生产环境不应启用的功能
  productionDisabled: [
    'DUAL_CORE_DEBUG_MODE',
    'DUAL_CORE_TEST_MODE',
    'DUAL_CORE_MOCK_DATA_ENABLED',
    'DUAL_CORE_SIMULATION_MODE'
  ],
  
  // 应该启用的安全功能
  securityEnabled: [
    'DUAL_CORE_DEBUG_CONSOLE_AUTH_REQUIRED',
    'DUAL_CORE_ENABLE_FALLBACK'
  ]
};

function validateConfig() {
  log('blue', '🔍 开始验证双核抽象层配置...\n');
  
  let errors = 0;
  let warnings = 0;
  
  // 1. 检查必需的环境变量
  log('blue', '📋 检查必需的环境变量:');
  configRules.required.forEach(key => {
    if (!process.env[key]) {
      log('red', `  ❌ 缺少必需的环境变量: ${key}`);
      errors++;
    } else {
      log('green', `  ✅ ${key}: ${process.env[key]}`);
    }
  });
  
  // 2. 验证数值类型
  log('blue', '\n🔢 验证数值配置:');
  Object.entries(configRules.numeric).forEach(([key, rule]) => {
    const value = process.env[key];
    if (value) {
      const numValue = parseInt(value);
      if (isNaN(numValue)) {
        log('red', `  ❌ ${key} 不是有效数字: ${value}`);
        errors++;
      } else if (numValue < rule.min || numValue > rule.max) {
        log('yellow', `  ⚠️ ${key} 超出推荐范围 [${rule.min}-${rule.max}]: ${numValue}`);
        warnings++;
      } else {
        log('green', `  ✅ ${key}: ${numValue}`);
      }
    }
  });
  
  // 3. 验证布尔类型
  log('blue', '\n🔘 验证布尔配置:');
  configRules.boolean.forEach(key => {
    const value = process.env[key];
    if (value && !['true', 'false'].includes(value)) {
      log('red', `  ❌ ${key} 必须是 true 或 false: ${value}`);
      errors++;
    } else if (value) {
      log('green', `  ✅ ${key}: ${value}`);
    }
  });
  
  // 4. 验证枚举值
  log('blue', '\n📝 验证枚举配置:');
  Object.entries(configRules.enum).forEach(([key, validValues]) => {
    const value = process.env[key];
    if (value && !validValues.includes(value)) {
      log('red', `  ❌ ${key} 值无效: ${value}，有效值: ${validValues.join(', ')}`);
      errors++;
    } else if (value) {
      log('green', `  ✅ ${key}: ${value}`);
    }
  });
  
  // 5. 生产环境特殊检查
  if (process.env.NODE_ENV === 'production') {
    log('blue', '\n🏭 生产环境安全检查:');
    
    Object.entries(configRules.production).forEach(([key, expectedValue]) => {
      const actualValue = process.env[key];
      if (actualValue !== expectedValue) {
        log('red', `  ❌ 生产环境 ${key} 应该是 ${expectedValue}，当前是: ${actualValue}`);
        errors++;
      } else {
        log('green', `  ✅ ${key}: ${actualValue}`);
      }
    });
    
    // 安全功能检查
    securityRules.securityEnabled.forEach(key => {
      const value = process.env[key];
      if (value !== 'true') {
        log('yellow', `  ⚠️ 建议在生产环境启用 ${key}`);
        warnings++;
      } else {
        log('green', `  ✅ ${key}: ${value}`);
      }
    });
  }
  
  // 6. 资源配置合理性检查
  log('blue', '\n💾 资源配置检查:');
  const cacheSize = parseInt(process.env.DUAL_CORE_CACHE_SIZE || '50');
  const maxMemory = parseInt(process.env.DUAL_CORE_MAX_MEMORY_USAGE_MB || '100');
  const maxContextLength = parseInt(process.env.DUAL_CORE_MAX_CONTEXT_LENGTH || '4000');
  
  // 检查缓存大小与内存的比例
  const estimatedMemoryPerItem = 2; // MB per cached item
  const estimatedCacheMemory = cacheSize * estimatedMemoryPerItem;
  
  if (estimatedCacheMemory > maxMemory * 0.8) {
    log('yellow', `  ⚠️ 缓存大小可能导致内存不足: 预估${estimatedCacheMemory}MB，限制${maxMemory}MB`);
    warnings++;
  } else {
    log('green', `  ✅ 内存配置合理: 缓存${cacheSize}项，内存限制${maxMemory}MB`);
  }
  
  // 检查上下文长度配置
  if (maxContextLength > 8000) {
    log('yellow', `  ⚠️ 上下文长度较大，可能影响性能: ${maxContextLength}`);
    warnings++;
  } else {
    log('green', `  ✅ 上下文长度配置合理: ${maxContextLength}`);
  }
  
  // 7. 文件系统检查
  log('blue', '\n📁 文件系统检查:');
  const requiredDirs = [
    '/data/dual-core/strategies',
    '/data/dual-core/logs',
    '/data/dual-core/performance'
  ];
  
  requiredDirs.forEach(dir => {
    try {
      if (fs.existsSync(dir)) {
        const stats = fs.statSync(dir);
        if (stats.isDirectory()) {
          log('green', `  ✅ 目录存在: ${dir}`);
        } else {
          log('red', `  ❌ 路径不是目录: ${dir}`);
          errors++;
        }
      } else {
        log('yellow', `  ⚠️ 目录不存在，将在运行时创建: ${dir}`);
        warnings++;
      }
    } catch (error) {
      log('red', `  ❌ 无法访问目录: ${dir} - ${error.message}`);
      errors++;
    }
  });
  
  // 8. 依赖检查
  log('blue', '\n📦 依赖包检查:');
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = ['next', 'react', 'typescript'];
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
        log('green', `  ✅ ${dep}: 已安装`);
      } else {
        log('red', `  ❌ 缺少依赖: ${dep}`);
        errors++;
      }
    });
    
    // 检查可选依赖
    const optionalDeps = ['recharts', 'ws'];
    optionalDeps.forEach(dep => {
      if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
        log('green', `  ✅ ${dep}: 已安装 (可选)`);
      } else {
        log('yellow', `  ⚠️ ${dep}: 未安装 (可选，用于高级功能)`);
      }
    });
    
  } catch (error) {
    log('red', `  ❌ 无法读取 package.json: ${error.message}`);
    errors++;
  }
  
  // 9. 生成配置报告
  log('blue', '\n📊 配置摘要:');
  console.log(`  环境: ${process.env.NODE_ENV || '未设置'}`);
  console.log(`  调试模式: ${process.env.DUAL_CORE_DEBUG_MODE || '未设置'}`);
  console.log(`  缓存大小: ${process.env.DUAL_CORE_CACHE_SIZE || '未设置'}`);
  console.log(`  最大内存: ${process.env.DUAL_CORE_MAX_MEMORY_USAGE_MB || '未设置'}MB`);
  console.log(`  性能监控: ${process.env.DUAL_CORE_PERFORMANCE_MONITORING || '未设置'}`);
  
  // 10. 最终结果
  log('blue', '\n📋 验证结果:');
  if (errors === 0 && warnings === 0) {
    log('green', '🎉 配置验证通过！所有配置都正确。');
    return 0;
  } else if (errors === 0) {
    log('yellow', `⚠️ 配置基本正确，但有 ${warnings} 个警告需要注意。`);
    return 0;
  } else {
    log('red', `❌ 配置验证失败！发现 ${errors} 个错误和 ${warnings} 个警告。`);
    log('red', '请修复错误后重新验证。');
    return 1;
  }
}

// 主函数
function main() {
  console.log('🔧 SelfMirror 双核抽象层配置验证工具 v2.1.0\n');
  
  // 检查是否在项目根目录
  if (!fs.existsSync('package.json')) {
    log('red', '❌ 请在项目根目录运行此脚本');
    process.exit(1);
  }
  
  // 加载环境变量
  const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.local';
  if (fs.existsSync(envFile)) {
    log('blue', `📄 加载环境配置: ${envFile}`);
    require('dotenv').config({ path: envFile });
  } else {
    log('yellow', `⚠️ 环境配置文件不存在: ${envFile}`);
  }
  
  const exitCode = validateConfig();
  process.exit(exitCode);
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { validateConfig };
