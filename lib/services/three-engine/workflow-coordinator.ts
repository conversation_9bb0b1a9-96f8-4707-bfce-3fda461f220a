/**
 * Three-Engine Workflow Coordinator - 三引擎工作流协调器
 * 统一管理Navigator、Context Retriever、Integration Generator的协同工作流
 */

import { NavigatorEngine } from './navigator-engine';
import { ContextRetrieverEngine } from './context-retriever-engine';
import { IntegrationGeneratorEngine } from './integration-generator-engine';
import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import {
  IWorkflowCoordinator,
  WorkflowRequest,
  WorkflowResponse,
  WorkflowConfig,
  WorkflowStats,
  ThreeEngineError
} from './interfaces';

/**
 * 工作流协调器配置
 */
export interface CoordinatorConfig {
  enableParallelProcessing: boolean;
  workflowTimeout: number;
  enableCaching: boolean;
  enableFallback: boolean;
  retryAttempts: number;
  qualityThreshold: number;
  enableMetrics: boolean;
}

/**
 * 工作流执行统计
 */
interface WorkflowExecutionStats {
  totalWorkflows: number;
  successfulWorkflows: number;
  failedWorkflows: number;
  averageExecutionTime: number;
  averageNavigatorTime: number;
  averageRetrieverTime: number;
  averageGeneratorTime: number;
  lastExecutionTime: number;
  errorsByStage: {
    navigator: number;
    retriever: number;
    generator: number;
    coordination: number;
  };
}

/**
 * 三引擎工作流协调器实现
 */
export class ThreeEngineWorkflowCoordinator implements IWorkflowCoordinator {
  private navigatorEngine: NavigatorEngine;
  private retrieverEngine: ContextRetrieverEngine;
  private generatorEngine: IntegrationGeneratorEngine;
  private config: CoordinatorConfig;
  private stats: WorkflowExecutionStats;
  private initialized: boolean = false;
  private workflowCache: Map<string, WorkflowResponse> = new Map();

  constructor(config?: Partial<CoordinatorConfig>) {
    this.config = {
      enableParallelProcessing: true,
      workflowTimeout: 30000, // 30秒
      enableCaching: true,
      enableFallback: true,
      retryAttempts: 2,
      qualityThreshold: 0.6,
      enableMetrics: true,
      ...config
    };

    this.stats = {
      totalWorkflows: 0,
      successfulWorkflows: 0,
      failedWorkflows: 0,
      averageExecutionTime: 0,
      averageNavigatorTime: 0,
      averageRetrieverTime: 0,
      averageGeneratorTime: 0,
      lastExecutionTime: 0,
      errorsByStage: {
        navigator: 0,
        retriever: 0,
        generator: 0,
        coordination: 0
      }
    };

    // 初始化三个引擎
    this.navigatorEngine = new NavigatorEngine();
    this.retrieverEngine = new ContextRetrieverEngine();
    this.generatorEngine = new IntegrationGeneratorEngine();
  }

  /**
   * 初始化工作流协调器
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔄 初始化三引擎工作流协调器...');
      
      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();
      
      // 并行初始化三个引擎
      if (this.config.enableParallelProcessing) {
        await Promise.all([
          this.navigatorEngine.initialize(),
          this.retrieverEngine.initialize(),
          this.generatorEngine.initialize()
        ]);
      } else {
        await this.navigatorEngine.initialize();
        await this.retrieverEngine.initialize();
        await this.generatorEngine.initialize();
      }
      
      this.initialized = true;
      console.log('🔄 三引擎工作流协调器初始化完成');
    } catch (error) {
      console.error('❌ 工作流协调器初始化失败:', error);
      throw new ThreeEngineError(
        '工作流协调器初始化失败',
        'COORDINATOR_ERROR',
        'initialization',
        error
      );
    }
  }

  /**
   * 执行完整的三引擎工作流
   */
  async executeWorkflow(request: WorkflowRequest): Promise<WorkflowResponse> {
    if (!this.initialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    let workflowId: string;

    try {
      console.log(`🔄 开始执行三引擎工作流: ${request.userMessage.slice(0, 50)}...`);
      
      // 生成工作流ID
      workflowId = await globalIdManager.generateUserInputId();
      
      // 检查缓存
      if (this.config.enableCaching) {
        const cachedResponse = this.getCachedResponse(request);
        if (cachedResponse) {
          console.log('💾 使用缓存的工作流响应');
          return cachedResponse;
        }
      }

      // 执行三引擎工作流
      const response = await this.executeThreeEngineWorkflow(
        workflowId,
        request,
        startTime
      );

      // 缓存响应
      if (this.config.enableCaching && this.isHighQualityResponse(response)) {
        this.cacheResponse(request, response);
      }

      // 更新统计信息
      this.updateStats(Date.now() - startTime, true, response);

      console.log(`🔄 三引擎工作流执行完成: ${Date.now() - startTime}ms`);
      
      return response;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateStats(executionTime, false);
      
      console.error('❌ 三引擎工作流执行失败:', error);
      
      // 尝试降级处理
      if (this.config.enableFallback) {
        return await this.handleFallback(request, error, workflowId!);
      }
      
      throw new ThreeEngineError(
        '三引擎工作流执行失败',
        'WORKFLOW_ERROR',
        'execution',
        error
      );
    }
  }

  /**
   * 验证工作流响应质量
   */
  validateWorkflow(response: WorkflowResponse): boolean {
    try {
      // 基础字段验证
      if (!response.workflowId || !response.finalResponse) {
        return false;
      }

      // 各阶段结果验证
      if (!response.navigatorResult || !response.retrievalResult || !response.generationResult) {
        return false;
      }

      // 质量指标验证
      const overallQuality = this.calculateOverallQuality(response);
      if (overallQuality < this.config.qualityThreshold) {
        return false;
      }

      // 执行时间验证
      if (response.executionStats.totalTime <= 0) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ 工作流验证失败:', error);
      return false;
    }
  }

  /**
   * 获取工作流统计信息
   */
  getWorkflowStats(): WorkflowStats {
    return {
      totalExecutions: this.stats.totalWorkflows,
      successRate: this.stats.totalWorkflows > 0 
        ? this.stats.successfulWorkflows / this.stats.totalWorkflows 
        : 0,
      averageExecutionTime: this.stats.averageExecutionTime,
      enginePerformance: {
        navigator: {
          averageTime: this.stats.averageNavigatorTime,
          errorCount: this.stats.errorsByStage.navigator
        },
        retriever: {
          averageTime: this.stats.averageRetrieverTime,
          errorCount: this.stats.errorsByStage.retriever
        },
        generator: {
          averageTime: this.stats.averageGeneratorTime,
          errorCount: this.stats.errorsByStage.generator
        }
      },
      lastExecutionTime: this.stats.lastExecutionTime,
      cacheStats: {
        size: this.workflowCache.size,
        hitRate: 0 // 简化实现，实际应该跟踪缓存命中率
      }
    };
  }

  // 私有方法

  /**
   * 执行三引擎工作流的核心逻辑
   */
  private async executeThreeEngineWorkflow(
    workflowId: string,
    request: WorkflowRequest,
    startTime: number
  ): Promise<WorkflowResponse> {
    const timings = {
      navigator: 0,
      retriever: 0,
      generator: 0
    };

    // 阶段1: Navigator引擎 - 意图分析
    console.log('🧭 阶段1: Navigator引擎分析用户意图...');
    const navigatorStart = Date.now();
    
    const navigatorResult = await this.executeWithTimeout(
      () => this.navigatorEngine.analyzeUserIntent(request.userMessage),
      this.config.workflowTimeout / 3,
      'Navigator引擎超时'
    );
    
    timings.navigator = Date.now() - navigatorStart;
    console.log(`🧭 Navigator完成: ${timings.navigator}ms, 置信度: ${navigatorResult.qualityMetrics.confidence}`);

    // 阶段2: Context Retriever引擎 - 检索执行
    console.log('🔍 阶段2: Context Retriever引擎执行检索...');
    const retrieverStart = Date.now();
    
    const retrievalResult = await this.executeWithTimeout(
      () => this.retrieverEngine.executeRetrieval(navigatorResult),
      this.config.workflowTimeout / 3,
      'Context Retriever引擎超时'
    );
    
    timings.retriever = Date.now() - retrieverStart;
    console.log(`🔍 Context Retriever完成: ${timings.retriever}ms, 结果数: ${retrievalResult.mergedResults.length}`);

    // 构建上下文包装
    const contextPackage = this.buildContextPackage(request, retrievalResult);

    // 阶段3: Integration Generator引擎 - 响应生成
    console.log('🔗 阶段3: Integration Generator引擎生成响应...');
    const generatorStart = Date.now();
    
    const generationResult = await this.executeWithTimeout(
      () => this.generatorEngine.generateResponse(
        request.userMessage,
        retrievalResult,
        contextPackage,
        request.generationConfig
      ),
      this.config.workflowTimeout / 3,
      'Integration Generator引擎超时'
    );
    
    timings.generator = Date.now() - generatorStart;
    console.log(`🔗 Integration Generator完成: ${timings.generator}ms, 内容长度: ${generationResult.content.length}`);

    // 构建工作流响应
    const totalTime = Date.now() - startTime;
    const response: WorkflowResponse = {
      workflowId,
      timestamp: new Date().toISOString(),
      userMessage: request.userMessage,
      finalResponse: generationResult.content,
      navigatorResult,
      retrievalResult,
      generationResult,
      executionStats: {
        totalTime,
        navigatorTime: timings.navigator,
        retrieverTime: timings.retriever,
        generatorTime: timings.generator,
        coordinationOverhead: totalTime - (timings.navigator + timings.retriever + timings.generator)
      },
      qualityMetrics: {
        overallQuality: this.calculateOverallQuality({
          navigatorResult,
          retrievalResult,
          generationResult
        } as WorkflowResponse),
        navigatorConfidence: navigatorResult.qualityMetrics.confidence,
        retrievalRelevance: retrievalResult.qualityMetrics.relevanceScore,
        generationCoherence: generationResult.qualityMetrics.coherence
      },
      workflowMetadata: {
        engineVersions: {
          navigator: '1.0.0',
          retriever: '1.0.0',
          generator: '1.0.0'
        },
        configUsed: this.config,
        globalIds: {
          workflowId,
          navigatorId: navigatorResult.instructionId,
          retrievalId: retrievalResult.retrievalId,
          responseId: generationResult.responseId
        }
      }
    };

    return response;
  }

  /**
   * 构建上下文包装
   */
  private buildContextPackage(request: WorkflowRequest, retrievalResult: any): any {
    return {
      userProfile: request.userProfile || "SelfMirror用户",
      recentHistory: request.conversationHistory || [],
      currentMessage: request.userMessage,
      retrievedContent: retrievalResult.mergedResults,
      systemPrompts: ["你是SelfMirror的AI助手"],
      contextualHints: [
        "基于用户的历史对话提供个性化建议",
        "保持温暖、理解的语气",
        "提供实用的建议和支持"
      ],
      packageMetadata: {
        totalLength: 0,
        contentSources: ['conversation_history', 'retrieval_results'],
        qualityScore: retrievalResult.qualityMetrics.relevanceScore,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 带超时的执行包装器
   */
  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number,
    errorMessage: string
  ): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new ThreeEngineError(
            errorMessage,
            'TIMEOUT_ERROR',
            'execution'
          ));
        }, timeoutMs);
      })
    ]);
  }

  /**
   * 计算整体质量分数
   */
  private calculateOverallQuality(response: Partial<WorkflowResponse>): number {
    const weights = {
      navigator: 0.2,
      retrieval: 0.3,
      generation: 0.5
    };

    let totalScore = 0;
    let totalWeight = 0;

    if (response.navigatorResult) {
      totalScore += response.navigatorResult.qualityMetrics.confidence * weights.navigator;
      totalWeight += weights.navigator;
    }

    if (response.retrievalResult) {
      totalScore += response.retrievalResult.qualityMetrics.relevanceScore * weights.retrieval;
      totalWeight += weights.retrieval;
    }

    if (response.generationResult) {
      const genQuality = Object.values(response.generationResult.qualityMetrics)
        .reduce((sum, score) => sum + score, 0) / 4;
      totalScore += genQuality * weights.generation;
      totalWeight += weights.generation;
    }

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * 检查是否为高质量响应
   */
  private isHighQualityResponse(response: WorkflowResponse): boolean {
    const overallQuality = this.calculateOverallQuality(response);
    return overallQuality >= this.config.qualityThreshold;
  }

  /**
   * 获取缓存的响应
   */
  private getCachedResponse(request: WorkflowRequest): WorkflowResponse | null {
    const cacheKey = this.generateCacheKey(request);
    return this.workflowCache.get(cacheKey) || null;
  }

  /**
   * 缓存响应
   */
  private cacheResponse(request: WorkflowRequest, response: WorkflowResponse): void {
    const cacheKey = this.generateCacheKey(request);
    this.workflowCache.set(cacheKey, response);
    
    // 简单的缓存清理：保持最多100个条目
    if (this.workflowCache.size > 100) {
      const firstKey = this.workflowCache.keys().next().value;
      this.workflowCache.delete(firstKey);
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(request: WorkflowRequest): string {
    return `${request.userMessage.slice(0, 50)}_${request.userProfile || 'default'}`;
  }

  /**
   * 处理降级情况
   */
  private async handleFallback(
    request: WorkflowRequest,
    error: any,
    workflowId: string
  ): Promise<WorkflowResponse> {
    console.log('🔄 执行降级处理...');
    
    // 简单的降级响应
    const fallbackResponse: WorkflowResponse = {
      workflowId,
      timestamp: new Date().toISOString(),
      userMessage: request.userMessage,
      finalResponse: "抱歉，我现在遇到了一些技术问题，无法为您提供完整的回答。请稍后再试，或者您可以换个方式提问。",
      navigatorResult: null as any,
      retrievalResult: null as any,
      generationResult: null as any,
      executionStats: {
        totalTime: 0,
        navigatorTime: 0,
        retrieverTime: 0,
        generatorTime: 0,
        coordinationOverhead: 0
      },
      qualityMetrics: {
        overallQuality: 0.3,
        navigatorConfidence: 0,
        retrievalRelevance: 0,
        generationCoherence: 0.3
      },
      workflowMetadata: {
        engineVersions: {
          navigator: '1.0.0',
          retriever: '1.0.0',
          generator: '1.0.0'
        },
        configUsed: this.config,
        globalIds: {
          workflowId,
          navigatorId: '',
          retrievalId: '',
          responseId: ''
        },
        fallbackUsed: true,
        originalError: error instanceof Error ? error.message : '未知错误'
      }
    };

    return fallbackResponse;
  }

  /**
   * 更新统计信息
   */
  private updateStats(
    executionTime: number,
    success: boolean,
    response?: WorkflowResponse
  ): void {
    this.stats.totalWorkflows++;
    this.stats.lastExecutionTime = executionTime;
    
    if (success) {
      this.stats.successfulWorkflows++;
      
      if (response) {
        // 更新平均执行时间
        this.stats.averageExecutionTime = 
          (this.stats.averageExecutionTime * (this.stats.totalWorkflows - 1) + executionTime) / 
          this.stats.totalWorkflows;
        
        // 更新各引擎平均时间
        this.stats.averageNavigatorTime = 
          (this.stats.averageNavigatorTime * (this.stats.successfulWorkflows - 1) + response.executionStats.navigatorTime) / 
          this.stats.successfulWorkflows;
        
        this.stats.averageRetrieverTime = 
          (this.stats.averageRetrieverTime * (this.stats.successfulWorkflows - 1) + response.executionStats.retrieverTime) / 
          this.stats.successfulWorkflows;
        
        this.stats.averageGeneratorTime = 
          (this.stats.averageGeneratorTime * (this.stats.successfulWorkflows - 1) + response.executionStats.generatorTime) / 
          this.stats.successfulWorkflows;
      }
    } else {
      this.stats.failedWorkflows++;
      this.stats.errorsByStage.coordination++;
    }
  }
}

// 导出默认实例
export const threeEngineWorkflowCoordinator = new ThreeEngineWorkflowCoordinator();
