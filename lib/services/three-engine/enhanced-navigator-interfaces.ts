/**
 * 增强Navigator引擎接口定义
 * 基于LLM的智能检索指令生成器
 */

/**
 * 用户画像数据结构
 */
export interface UserProfile {
  userId?: string;
  preferences: {
    topics: string[];           // 感兴趣的话题
    domains: string[];          // 专业领域
    communicationStyle: string; // 沟通风格
    detailLevel: 'brief' | 'detailed' | 'comprehensive';
  };
  history: {
    frequentQueries: string[];  // 常见查询
    recentTopics: string[];     // 最近话题
    interactionPatterns: string[]; // 交互模式
  };
  context: {
    currentSession: string;     // 当前会话上下文
    timeZone?: string;         // 时区
    language?: string;         // 语言偏好
  };
}

/**
 * 心智要素结构
 */
export interface MentalModel {
  knowledgeGraph: {
    concepts: ConceptNode[];    // 概念节点
    relationships: RelationshipEdge[]; // 关系边
    domains: DomainCluster[];   // 领域聚类
  };
  semanticNetwork: {
    embeddings: EmbeddingSpace; // 嵌入空间
    similarities: SimilarityMatrix; // 相似度矩阵
    clusters: SemanticCluster[]; // 语义聚类
  };
  temporalStructure: {
    timelineEvents: TimelineEvent[]; // 时间线事件
    periodicPatterns: PeriodicPattern[]; // 周期性模式
    contextualMemory: ContextualMemory[]; // 上下文记忆
  };
}

/**
 * 概念节点
 */
export interface ConceptNode {
  id: string;
  name: string;
  type: 'entity' | 'concept' | 'event' | 'relation';
  properties: Record<string, any>;
  importance: number;         // 重要性权重
  frequency: number;          // 出现频率
  lastAccessed: Date;         // 最后访问时间
}

/**
 * 关系边
 */
export interface RelationshipEdge {
  id: string;
  source: string;             // 源节点ID
  target: string;             // 目标节点ID
  type: string;               // 关系类型
  strength: number;           // 关系强度
  confidence: number;         // 置信度
  metadata: Record<string, any>;
}

/**
 * 领域聚类
 */
export interface DomainCluster {
  id: string;
  name: string;
  concepts: string[];         // 包含的概念ID
  centroid: number[];         // 聚类中心
  coherence: number;          // 聚类一致性
}

/**
 * 嵌入空间
 */
export interface EmbeddingSpace {
  dimensions: number;
  vectors: Map<string, number[]>;
  metadata: {
    model: string;
    version: string;
    createdAt: Date;
  };
}

/**
 * 相似度矩阵
 */
export interface SimilarityMatrix {
  size: number;
  matrix: number[][];
  indices: Map<string, number>;
}

/**
 * 语义聚类
 */
export interface SemanticCluster {
  id: string;
  centroid: number[];
  members: string[];
  coherence: number;
  label?: string;
}

/**
 * 时间线事件
 */
export interface TimelineEvent {
  id: string;
  timestamp: Date;
  type: string;
  content: string;
  importance: number;
  tags: string[];
}

/**
 * 周期性模式
 */
export interface PeriodicPattern {
  id: string;
  pattern: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  strength: number;
  examples: string[];
}

/**
 * 上下文记忆
 */
export interface ContextualMemory {
  id: string;
  context: string;
  associatedConcepts: string[];
  strength: number;
  lastReinforced: Date;
}

/**
 * 增强的检索指令
 */
export interface EnhancedRetrievalInstruction {
  instructionId: string;
  timestamp: string;
  userMessage: string;
  
  // 意图分析
  intentAnalysis: {
    primaryIntent: string;
    secondaryIntents: string[];
    intentConfidence: number;
    complexityLevel: number;
    urgencyLevel: number;
    emotionalContext: string;
  };
  
  // 检索目标
  retrievalTargets: {
    conceptTargets: ConceptTarget[];     // 概念检索目标
    relationshipTargets: RelationshipTarget[]; // 关系检索目标
    temporalTargets: TemporalTarget[];   // 时间检索目标
    semanticTargets: SemanticTarget[];   // 语义检索目标
  };
  
  // 检索策略
  retrievalStrategy: {
    primaryStrategy: 'concept-based' | 'semantic-based' | 'temporal-based' | 'hybrid';
    searchDepth: number;                 // 搜索深度
    expansionFactor: number;             // 扩展因子
    diversityWeight: number;             // 多样性权重
    relevanceThreshold: number;          // 相关性阈值
  };
  
  // 优先级和权重
  prioritization: {
    hotStoreWeight: number;              // 热存储权重
    coldStoreWeight: number;             // 冷存储权重
    recentnessWeight: number;            // 时效性权重
    importanceWeight: number;            // 重要性权重
    personalRelevanceWeight: number;     // 个人相关性权重
  };
  
  // 执行参数
  executionParams: {
    maxResults: number;
    timeoutMs: number;
    parallelExecution: boolean;
    fallbackStrategy: string;
    qualityThreshold: number;
  };
  
  // 质量指标
  qualityMetrics: {
    instructionCompleteness: number;
    targetSpecificity: number;
    strategyCoherence: number;
    expectedRelevance: number;
  };
}

/**
 * 概念检索目标
 */
export interface ConceptTarget {
  conceptId?: string;
  conceptName: string;
  conceptType: string;
  searchRadius: number;       // 搜索半径
  includeRelated: boolean;    // 是否包含相关概念
  weight: number;             // 权重
}

/**
 * 关系检索目标
 */
export interface RelationshipTarget {
  relationshipType: string;
  sourceConstraint?: string;
  targetConstraint?: string;
  strengthThreshold: number;
  weight: number;
}

/**
 * 时间检索目标
 */
export interface TemporalTarget {
  timeRange: {
    start?: Date;
    end?: Date;
  };
  temporalPattern?: string;
  recencyWeight: number;
  weight: number;
}

/**
 * 语义检索目标
 */
export interface SemanticTarget {
  query: string;
  embeddingSpace: string;
  similarityThreshold: number;
  maxExpansion: number;
  weight: number;
}

/**
 * 上下文数据结构
 */
export interface ConversationContext {
  currentMessage: string;
  recentMessages: string[];   // 最近5条消息
  sessionHistory: string[];   // 会话历史
  userProfile: UserProfile;
  mentalModel: MentalModel;
  environmentContext: {
    timestamp: Date;
    sessionId: string;
    deviceInfo?: string;
    locationContext?: string;
  };
}

/**
 * 增强Navigator引擎接口
 */
export interface IEnhancedNavigatorEngine {
  /**
   * 生成智能检索指令
   */
  generateRetrievalInstruction(
    context: ConversationContext
  ): Promise<EnhancedRetrievalInstruction>;
  
  /**
   * 验证检索指令质量
   */
  validateInstruction(
    instruction: EnhancedRetrievalInstruction
  ): boolean;
  
  /**
   * 优化检索指令
   */
  optimizeInstruction(
    instruction: EnhancedRetrievalInstruction,
    feedback?: InstructionFeedback
  ): Promise<EnhancedRetrievalInstruction>;
  
  /**
   * 获取引擎统计信息
   */
  getEngineStats(): NavigatorEngineStats;
}

/**
 * 指令反馈
 */
export interface InstructionFeedback {
  instructionId: string;
  retrievalResults: any[];
  userSatisfaction: number;
  relevanceScore: number;
  completenessScore: number;
  suggestions: string[];
}

/**
 * Navigator引擎统计
 */
export interface NavigatorEngineStats {
  totalInstructions: number;
  averageGenerationTime: number;
  averageQualityScore: number;
  successRate: number;
  optimizationCount: number;
  lastOptimization: Date;
}
