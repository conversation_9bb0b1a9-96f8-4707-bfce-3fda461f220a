/**
 * Context Retriever Engine - 基于Navigator指令的精准检索引擎
 * 负责执行向量检索、结果合并和优化
 */

import { vectorDatabase } from '@/lib/services/vector-database';
import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import {
  IContextRetrieverEngine,
  NavigatorInstruction,
  RetrievalResult,
  RetrievalResultItem,
  RetrievalStats,
  ThreeEngineError
} from './interfaces';

/**
 * Context Retriever引擎配置
 */
export interface ContextRetrieverConfig {
  maxResults: number;
  searchTimeout: number;
  enableCaching: boolean;
  enableParallelSearch: boolean;
  similarityThreshold: number;
  diversityWeight: number;
  recencyWeight: number;
  relevanceWeight: number;
}

/**
 * Context Retriever引擎统计
 */
interface ContextRetrieverStats {
  totalRetrievals: number;
  averageRetrievalTime: number;
  averageResultCount: number;
  cacheHitRate: number;
  successRate: number;
  lastRetrievalTime: number;
  errorCount: number;
}

/**
 * 检索缓存项
 */
interface CacheItem {
  key: string;
  result: RetrievalResult;
  timestamp: number;
  accessCount: number;
}

/**
 * Context Retriever引擎实现
 */
export class ContextRetrieverEngine implements IContextRetrieverEngine {
  private config: ContextRetrieverConfig;
  private stats: ContextRetrieverStats;
  private cache: Map<string, CacheItem>;
  private initialized: boolean = false;

  constructor(config?: Partial<ContextRetrieverConfig>) {
    this.config = {
      maxResults: 20,
      searchTimeout: 10000, // 10秒
      enableCaching: true,
      enableParallelSearch: true,
      similarityThreshold: 0.3,
      diversityWeight: 0.2,
      recencyWeight: 0.3,
      relevanceWeight: 0.5,
      ...config
    };

    this.stats = {
      totalRetrievals: 0,
      averageRetrievalTime: 0,
      averageResultCount: 0,
      cacheHitRate: 0,
      successRate: 1.0,
      lastRetrievalTime: 0,
      errorCount: 0
    };

    this.cache = new Map();
  }

  /**
   * 初始化Context Retriever引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔍 初始化Context Retriever引擎...');
      
      // 确保向量数据库已初始化
      await vectorDatabase.initialize();
      await globalIdManager.initialize();
      
      this.initialized = true;
      console.log('🔍 Context Retriever引擎初始化完成');
    } catch (error) {
      console.error('❌ Context Retriever引擎初始化失败:', error);
      throw new ThreeEngineError(
        'Context Retriever引擎初始化失败',
        'RETRIEVAL_ERROR',
        'initialization',
        error
      );
    }
  }

  /**
   * 执行检索操作
   */
  async executeRetrieval(instruction: NavigatorInstruction): Promise<RetrievalResult> {
    if (!this.initialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    
    try {
      console.log(`🔍 Context Retriever开始执行检索: ${instruction.instructionId}`);
      
      // 生成检索ID
      const retrievalId = await globalIdManager.generateDerivedId(
        instruction.instructionId,
        'retrieval_result',
        'search'
      );

      // 检查缓存
      if (this.config.enableCaching) {
        const cachedResult = this.getCachedResult(instruction);
        if (cachedResult) {
          console.log('🎯 使用缓存结果');
          this.updateStats(Date.now() - startTime, true, cachedResult.mergedResults.length, true);
          return cachedResult;
        }
      }

      // 执行检索
      const result = await this.performRetrieval(retrievalId, instruction, startTime);

      // 缓存结果
      if (this.config.enableCaching) {
        this.cacheResult(instruction, result);
      }

      // 更新统计信息
      this.updateStats(Date.now() - startTime, true, result.mergedResults.length, false);

      console.log(`🔍 Context Retriever检索完成: ${result.mergedResults.length} 个结果`);
      
      return result;

    } catch (error) {
      const retrievalTime = Date.now() - startTime;
      this.updateStats(retrievalTime, false, 0, false);
      
      console.error('❌ Context Retriever检索失败:', error);
      throw new ThreeEngineError(
        'Context Retriever检索失败',
        'RETRIEVAL_ERROR',
        'execution',
        error
      );
    }
  }

  /**
   * 优化检索结果
   */
  async optimizeResults(results: RetrievalResult): Promise<RetrievalResult> {
    try {
      console.log('🎯 优化检索结果...');
      
      // 重新排序结果
      const optimizedResults = this.reRankResults(results.mergedResults, results.retrievalMetadata.strategy);
      
      // 增强多样性
      const diversifiedResults = this.enhanceDiversity(optimizedResults);
      
      // 更新质量指标
      const updatedQualityMetrics = this.calculateQualityMetrics(diversifiedResults);
      
      const optimizedResult: RetrievalResult = {
        ...results,
        mergedResults: diversifiedResults,
        qualityMetrics: updatedQualityMetrics,
        retrievalMetadata: {
          ...results.retrievalMetadata,
          optimizations: ['reranking', 'diversity_enhancement', 'quality_recalculation']
        }
      };

      console.log(`🎯 结果优化完成: ${diversifiedResults.length} 个优化结果`);
      return optimizedResult;
      
    } catch (error) {
      console.error('❌ 结果优化失败:', error);
      return results; // 返回原始结果
    }
  }

  /**
   * 获取引擎统计信息
   */
  getEngineStats(): ContextRetrieverStats {
    return { ...this.stats };
  }

  // 私有方法

  /**
   * 执行实际的检索操作
   */
  private async performRetrieval(
    retrievalId: string,
    instruction: NavigatorInstruction,
    startTime: number
  ): Promise<RetrievalResult> {
    const hotStoreResults: RetrievalResultItem[] = [];
    const coldStoreResults: RetrievalResultItem[] = [];
    
    let hotStoreTime = 0;
    let coldStoreTime = 0;
    let hotStoreHits = 0;
    let coldStoreHits = 0;

    try {
      // 根据搜索策略决定执行方式
      if (this.config.enableParallelSearch && instruction.executionParams.parallelExecution) {
        // 并行搜索
        const [hotResults, coldResults] = await Promise.all([
          this.searchHotStore(instruction),
          this.searchColdStore(instruction)
        ]);
        
        hotStoreResults.push(...hotResults.results);
        coldStoreResults.push(...coldResults.results);
        hotStoreTime = hotResults.time;
        coldStoreTime = coldResults.time;
        hotStoreHits = hotResults.results.length;
        coldStoreHits = coldResults.results.length;
      } else {
        // 串行搜索
        const hotResults = await this.searchHotStore(instruction);
        hotStoreResults.push(...hotResults.results);
        hotStoreTime = hotResults.time;
        hotStoreHits = hotResults.results.length;
        
        const coldResults = await this.searchColdStore(instruction);
        coldStoreResults.push(...coldResults.results);
        coldStoreTime = coldResults.time;
        coldStoreHits = coldResults.results.length;
      }

      // 合并和排序结果
      const mergedResults = this.mergeAndRankResults(
        hotStoreResults,
        coldStoreResults,
        instruction.searchStrategy
      );

      // 限制结果数量
      const limitedResults = mergedResults.slice(0, instruction.executionParams.maxResults);

      // 计算统计信息
      const totalSearchTime = Date.now() - startTime;
      const stats: RetrievalStats = {
        totalSearchTime,
        hotStoreTime,
        coldStoreTime,
        hotStoreHits,
        coldStoreHits,
        totalProcessed: hotStoreHits + coldStoreHits,
        cacheHits: 0,
        cacheMisses: 1
      };

      // 计算质量指标
      const qualityMetrics = this.calculateQualityMetrics(limitedResults);

      const result: RetrievalResult = {
        retrievalId,
        instructionId: instruction.instructionId,
        timestamp: new Date().toISOString(),
        hotStoreResults,
        coldStoreResults,
        mergedResults: limitedResults,
        stats,
        qualityMetrics,
        retrievalMetadata: {
          strategy: instruction.searchStrategy,
          actualScope: instruction.searchStrategy.scope,
          fallbackUsed: false,
          optimizations: []
        }
      };

      return result;

    } catch (error) {
      console.error('❌ 检索执行失败:', error);
      throw error;
    }
  }

  /**
   * 搜索热存储
   */
  private async searchHotStore(instruction: NavigatorInstruction): Promise<{
    results: RetrievalResultItem[];
    time: number;
  }> {
    const startTime = Date.now();

    try {
      const results: RetrievalResultItem[] = [];

      // 执行热存储查询
      for (const query of instruction.targets.hotStoreQueries) {
        // 生成查询向量（简化实现，实际应用中会使用embedding模型）
        const queryVector = this.generateQueryVector(query);

        const searchResults = await vectorDatabase.search(
          queryVector,
          Math.ceil(instruction.executionParams.maxResults / 2)
        );

        // 转换为标准格式
        for (const result of searchResults) {
          if (result.score >= this.config.similarityThreshold) {
            results.push({
              chunkId: result.chunkId,
              globalId: result.metadata?.globalId || '',
              content: result.content,
              metadata: {
                sourceType: result.metadata?.sourceDocumentType || 'unknown',
                sourceDocumentId: result.metadata?.sourceDocumentId || '',
                createdAt: result.metadata?.createdAt || new Date().toISOString(),
                relevanceScore: result.score,
                semanticScore: result.score,
                temporalScore: this.calculateTemporalScore(result.metadata?.createdAt)
              },
              similarity: result.score,
              rank: 0 // 将在合并时重新计算
            });
          }
        }
      }

      const time = Date.now() - startTime;
      console.log(`🔥 热存储搜索完成: ${results.length} 个结果, ${time}ms`);

      return { results, time };

    } catch (error) {
      console.error('❌ 热存储搜索失败:', error);
      return { results: [], time: Date.now() - startTime };
    }
  }

  /**
   * 搜索冷存储
   */
  private async searchColdStore(instruction: NavigatorInstruction): Promise<{
    results: RetrievalResultItem[];
    time: number;
  }> {
    const startTime = Date.now();

    try {
      const results: RetrievalResultItem[] = [];

      // 执行冷存储查询
      for (const query of instruction.targets.coldStoreQueries) {
        // 生成查询向量（简化实现，实际应用中会使用embedding模型）
        const queryVector = this.generateQueryVector(query);

        const searchResults = await vectorDatabase.search(
          queryVector,
          Math.ceil(instruction.executionParams.maxResults / 2)
        );

        // 转换为标准格式
        for (const result of searchResults) {
          if (result.score >= this.config.similarityThreshold) {
            results.push({
              chunkId: result.chunkId,
              globalId: result.metadata?.globalId || '',
              content: result.content,
              metadata: {
                sourceType: result.metadata?.sourceDocumentType || 'unknown',
                sourceDocumentId: result.metadata?.sourceDocumentId || '',
                createdAt: result.metadata?.createdAt || new Date().toISOString(),
                relevanceScore: result.score,
                semanticScore: result.score,
                temporalScore: this.calculateTemporalScore(result.metadata?.createdAt)
              },
              similarity: result.score,
              rank: 0 // 将在合并时重新计算
            });
          }
        }
      }

      const time = Date.now() - startTime;
      console.log(`❄️ 冷存储搜索完成: ${results.length} 个结果, ${time}ms`);

      return { results, time };

    } catch (error) {
      console.error('❌ 冷存储搜索失败:', error);
      return { results: [], time: Date.now() - startTime };
    }
  }

  /**
   * 合并和排序结果
   */
  private mergeAndRankResults(
    hotResults: RetrievalResultItem[],
    coldResults: RetrievalResultItem[],
    strategy: any
  ): RetrievalResultItem[] {
    // 合并所有结果
    const allResults = [...hotResults, ...coldResults];
    
    // 去重（基于chunkId）
    const uniqueResults = new Map<string, RetrievalResultItem>();
    for (const result of allResults) {
      if (!uniqueResults.has(result.chunkId) || 
          uniqueResults.get(result.chunkId)!.similarity < result.similarity) {
        uniqueResults.set(result.chunkId, result);
      }
    }
    
    // 转换为数组并排序
    const mergedResults = Array.from(uniqueResults.values());
    
    // 根据策略排序
    return this.reRankResults(mergedResults, strategy);
  }

  /**
   * 重新排序结果
   */
  private reRankResults(results: RetrievalResultItem[], strategy: any): RetrievalResultItem[] {
    return results.sort((a, b) => {
      let scoreA = 0;
      let scoreB = 0;
      
      // 根据优先级计算综合分数
      switch (strategy.priority) {
        case 'relevance':
          scoreA = a.metadata.relevanceScore * this.config.relevanceWeight;
          scoreB = b.metadata.relevanceScore * this.config.relevanceWeight;
          break;
        case 'recency':
          scoreA = a.metadata.temporalScore * this.config.recencyWeight;
          scoreB = b.metadata.temporalScore * this.config.recencyWeight;
          break;
        case 'balanced':
        default:
          scoreA = (a.metadata.relevanceScore * this.config.relevanceWeight) +
                   (a.metadata.temporalScore * this.config.recencyWeight);
          scoreB = (b.metadata.relevanceScore * this.config.relevanceWeight) +
                   (b.metadata.temporalScore * this.config.recencyWeight);
          break;
      }
      
      return scoreB - scoreA;
    }).map((result, index) => ({
      ...result,
      rank: index + 1
    }));
  }

  /**
   * 增强结果多样性
   */
  private enhanceDiversity(results: RetrievalResultItem[]): RetrievalResultItem[] {
    if (results.length <= 3) return results;
    
    const diversifiedResults: RetrievalResultItem[] = [];
    const usedSources = new Set<string>();
    
    // 首先添加最高分的结果
    if (results.length > 0) {
      diversifiedResults.push(results[0]);
      usedSources.add(results[0].metadata.sourceType);
    }
    
    // 然后添加不同来源的结果
    for (const result of results.slice(1)) {
      if (!usedSources.has(result.metadata.sourceType) || diversifiedResults.length < 3) {
        diversifiedResults.push(result);
        usedSources.add(result.metadata.sourceType);
      }
      
      if (diversifiedResults.length >= results.length * 0.8) break;
    }
    
    // 填充剩余位置
    for (const result of results) {
      if (!diversifiedResults.includes(result)) {
        diversifiedResults.push(result);
      }
    }
    
    return diversifiedResults;
  }

  /**
   * 生成查询向量（简化实现）
   * 实际应用中应该使用embedding模型
   */
  private generateQueryVector(query: string): number[] {
    // 简化的向量生成：基于查询文本的哈希值生成伪向量
    const hash = this.simpleHash(query);
    const vector = Array.from({ length: 1536 }, (_, i) => {
      const seed = hash + i;
      return Math.sin(seed) * 0.5 + 0.5; // 归一化到 [0, 1]
    });

    // 归一化向量
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return vector.map(val => val / magnitude);
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 计算时间分数
   */
  private calculateTemporalScore(createdAt?: string): number {
    if (!createdAt) return 0.5;

    const now = new Date();
    const created = new Date(createdAt);
    const daysDiff = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);

    // 越新的内容分数越高
    if (daysDiff <= 1) return 1.0;
    if (daysDiff <= 7) return 0.8;
    if (daysDiff <= 30) return 0.6;
    if (daysDiff <= 90) return 0.4;
    return 0.2;
  }

  /**
   * 计算质量指标
   */
  private calculateQualityMetrics(results: RetrievalResultItem[]): any {
    if (results.length === 0) {
      return {
        relevanceScore: 0,
        diversityScore: 0,
        completenessScore: 0,
        freshness: 0
      };
    }
    
    const relevanceScore = results.reduce((sum, r) => sum + r.metadata.relevanceScore, 0) / results.length;
    const freshness = results.reduce((sum, r) => sum + r.metadata.temporalScore, 0) / results.length;
    
    // 计算多样性分数
    const sourceTypes = new Set(results.map(r => r.metadata.sourceType));
    const diversityScore = Math.min(1, sourceTypes.size / 3);
    
    // 计算完整性分数
    const completenessScore = Math.min(1, results.length / 10);
    
    return {
      relevanceScore: Math.round(relevanceScore * 100) / 100,
      diversityScore: Math.round(diversityScore * 100) / 100,
      completenessScore: Math.round(completenessScore * 100) / 100,
      freshness: Math.round(freshness * 100) / 100
    };
  }

  /**
   * 获取缓存结果
   */
  private getCachedResult(instruction: NavigatorInstruction): RetrievalResult | null {
    const cacheKey = this.generateCacheKey(instruction);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
      cached.accessCount++;
      return cached.result;
    }
    
    return null;
  }

  /**
   * 缓存结果
   */
  private cacheResult(instruction: NavigatorInstruction, result: RetrievalResult): void {
    const cacheKey = this.generateCacheKey(instruction);
    this.cache.set(cacheKey, {
      key: cacheKey,
      result,
      timestamp: Date.now(),
      accessCount: 1
    });
    
    // 清理过期缓存
    if (this.cache.size > 100) {
      this.cleanupCache();
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(instruction: NavigatorInstruction): string {
    const keyData = {
      queries: [...instruction.targets.hotStoreQueries, ...instruction.targets.coldStoreQueries].sort(),
      strategy: instruction.searchStrategy,
      maxResults: instruction.executionParams.maxResults
    };
    return JSON.stringify(keyData);
  }

  /**
   * 清理过期缓存
   */
  private cleanupCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > 300000) { // 5分钟过期
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.cache.delete(key));
  }

  /**
   * 更新统计信息
   */
  private updateStats(
    retrievalTime: number,
    success: boolean,
    resultCount: number,
    cacheHit: boolean
  ): void {
    this.stats.totalRetrievals++;
    this.stats.lastRetrievalTime = retrievalTime;
    
    // 更新平均检索时间
    this.stats.averageRetrievalTime = 
      (this.stats.averageRetrievalTime * (this.stats.totalRetrievals - 1) + retrievalTime) / 
      this.stats.totalRetrievals;
    
    // 更新平均结果数量
    this.stats.averageResultCount = 
      (this.stats.averageResultCount * (this.stats.totalRetrievals - 1) + resultCount) / 
      this.stats.totalRetrievals;
    
    // 更新缓存命中率
    if (cacheHit) {
      this.stats.cacheHitRate = 
        (this.stats.cacheHitRate * (this.stats.totalRetrievals - 1) + 1) / 
        this.stats.totalRetrievals;
    } else {
      this.stats.cacheHitRate = 
        (this.stats.cacheHitRate * (this.stats.totalRetrievals - 1)) / 
        this.stats.totalRetrievals;
    }
    
    // 更新成功率
    if (!success) {
      this.stats.errorCount++;
    }
    this.stats.successRate = 
      (this.stats.totalRetrievals - this.stats.errorCount) / this.stats.totalRetrievals;
  }
}

// 导出默认实例
export const contextRetrieverEngine = new ContextRetrieverEngine();
