/**
 * Integration Generator Engine - 整合生成引擎
 * 负责基于检索结果生成高质量的AI响应
 */

import { getDefaultAIProvider } from '@/lib/ai';
import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import {
  IIntegrationGeneratorEngine,
  RetrievalResult,
  ContextPackage,
  IntegratedResponse,
  GenerationConfig,
  ThreeEngineError
} from './interfaces';

/**
 * Integration Generator引擎配置
 */
export interface IntegrationGeneratorConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  streamResponse: boolean;
  includeMetadata: boolean;
  responseFormat: 'text' | 'structured' | 'hybrid';
  contextOptimization: boolean;
  qualityThreshold: number;
}

/**
 * Integration Generator引擎统计
 */
interface IntegrationGeneratorStats {
  totalGenerations: number;
  averageGenerationTime: number;
  averageTokensGenerated: number;
  averageContextLength: number;
  successRate: number;
  lastGenerationTime: number;
  errorCount: number;
}

/**
 * Integration Generator引擎实现
 */
export class IntegrationGeneratorEngine implements IIntegrationGeneratorEngine {
  private config: IntegrationGeneratorConfig;
  private stats: IntegrationGeneratorStats;
  private initialized: boolean = false;

  constructor(config?: Partial<IntegrationGeneratorConfig>) {
    this.config = {
      model: 'gemini',
      temperature: 0.7,
      maxTokens: 2000,
      streamResponse: true,
      includeMetadata: true,
      responseFormat: 'text',
      contextOptimization: true,
      qualityThreshold: 0.7,
      ...config
    };

    this.stats = {
      totalGenerations: 0,
      averageGenerationTime: 0,
      averageTokensGenerated: 0,
      averageContextLength: 0,
      successRate: 1.0,
      lastGenerationTime: 0,
      errorCount: 0
    };
  }

  /**
   * 初始化Integration Generator引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔗 初始化Integration Generator引擎...');
      
      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();
      
      this.initialized = true;
      console.log('🔗 Integration Generator引擎初始化完成');
    } catch (error) {
      console.error('❌ Integration Generator引擎初始化失败:', error);
      throw new ThreeEngineError(
        'Integration Generator引擎初始化失败',
        'INTEGRATION_ERROR',
        'initialization',
        error
      );
    }
  }

  /**
   * 生成整合响应
   */
  async generateResponse(
    userMessage: string,
    retrievalResult: RetrievalResult,
    contextPackage: ContextPackage,
    config?: Partial<GenerationConfig>
  ): Promise<IntegratedResponse> {
    if (!this.initialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    
    try {
      console.log(`🔗 Integration Generator开始生成响应: ${retrievalResult.retrievalId}`);
      
      // 生成响应ID
      const responseId = await globalIdManager.generateDerivedId(
        retrievalResult.retrievalId,
        'integrated_response',
        'generation'
      );

      // 合并配置
      const generationConfig: GenerationConfig = {
        model: this.config.model,
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens,
        streamResponse: this.config.streamResponse,
        includeMetadata: this.config.includeMetadata,
        responseFormat: this.config.responseFormat,
        ...config
      };

      // 构建上下文
      const optimizedContext = this.buildOptimizedContext(
        userMessage,
        retrievalResult,
        contextPackage
      );

      // 生成AI响应
      const aiProvider = await getDefaultAIProvider();
      let content = '';
      let responseStream: AsyncIterable<string> | undefined;
      let tokensGenerated = 0;

      if (generationConfig.streamResponse) {
        // 流式生成
        responseStream = this.generateStreamResponse(
          aiProvider,
          optimizedContext,
          generationConfig
        );
      } else {
        // 一次性生成
        content = await aiProvider.generateText(optimizedContext, {
          temperature: generationConfig.temperature,
          maxTokens: generationConfig.maxTokens
        });
        tokensGenerated = this.estimateTokenCount(content);
      }

      // 计算质量指标
      const qualityMetrics = this.calculateQualityMetrics(
        content,
        retrievalResult,
        contextPackage
      );

      // 验证响应质量
      if (qualityMetrics.coherence < this.config.qualityThreshold) {
        console.warn('⚠️ 响应质量低于阈值，尝试重新生成...');
        // 可以在这里实现重新生成逻辑
      }

      // 构建响应对象
      const generationTime = Date.now() - startTime;
      const response: IntegratedResponse = {
        responseId,
        retrievalId: retrievalResult.retrievalId,
        timestamp: new Date().toISOString(),
        content,
        responseStream,
        generationStats: {
          totalTime: generationTime,
          tokensGenerated,
          modelUsed: generationConfig.model,
          temperature: generationConfig.temperature
        },
        qualityMetrics,
        responseMetadata: {
          contextLength: optimizedContext.length,
          sourcesUsed: this.extractSourcesUsed(retrievalResult),
          generationConfig,
          globalIds: {
            userInputId: this.extractUserInputId(retrievalResult),
            responseId
          }
        }
      };

      // 更新统计信息
      this.updateStats(generationTime, true, tokensGenerated, optimizedContext.length);

      console.log(`🔗 Integration Generator生成完成: ${generationTime}ms, ${tokensGenerated} tokens`);
      
      return response;

    } catch (error) {
      const generationTime = Date.now() - startTime;
      this.updateStats(generationTime, false, 0, 0);
      
      console.error('❌ Integration Generator生成失败:', error);
      throw new ThreeEngineError(
        'Integration Generator生成失败',
        'INTEGRATION_ERROR',
        'generation',
        error
      );
    }
  }

  /**
   * 验证响应质量
   */
  validateResponse(response: IntegratedResponse): boolean {
    try {
      // 基础字段验证
      if (!response.responseId || !response.content) {
        return false;
      }

      // 质量指标验证
      if (response.qualityMetrics.coherence < 0.5) {
        return false;
      }

      // 内容长度验证
      if (response.content.length < 10) {
        return false;
      }

      // 生成统计验证
      if (response.generationStats.totalTime <= 0) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ 响应验证失败:', error);
      return false;
    }
  }

  /**
   * 获取引擎统计信息
   */
  getEngineStats(): IntegrationGeneratorStats {
    return { ...this.stats };
  }

  // 私有方法

  /**
   * 构建优化的上下文
   */
  private buildOptimizedContext(
    userMessage: string,
    retrievalResult: RetrievalResult,
    contextPackage: ContextPackage
  ): string {
    const contextParts: string[] = [];

    // 系统提示词
    contextParts.push(`你是SelfMirror的AI助手，一个专注于个人成长和自我反思的智能伙伴。

你的特点：
- 温暖、理解、有同理心
- 善于倾听和提供建设性建议
- 能够帮助用户进行自我反思和成长
- 基于用户的历史对话和洞察提供个性化回应

请基于以下信息回答用户的问题：`);

    // 用户历史信息
    if (contextPackage.recentHistory.length > 0) {
      contextParts.push('\n最近的对话历史：');
      contextPackage.recentHistory.slice(-3).forEach((msg, index) => {
        contextParts.push(`${index + 1}. ${msg}`);
      });
    }

    // 检索到的相关内容
    if (retrievalResult.mergedResults.length > 0) {
      contextParts.push('\n相关的历史信息：');
      retrievalResult.mergedResults.slice(0, 5).forEach((result, index) => {
        contextParts.push(`${index + 1}. ${result.content} (相关度: ${Math.round(result.similarity * 100)}%)`);
      });
    }

    // 上下文提示
    if (contextPackage.contextualHints.length > 0) {
      contextParts.push('\n上下文提示：');
      contextPackage.contextualHints.forEach(hint => {
        contextParts.push(`- ${hint}`);
      });
    }

    // 用户当前问题
    contextParts.push(`\n用户当前的问题或输入：\n"${userMessage}"\n`);

    // 回答指导
    contextParts.push(`请基于以上信息，以温暖、理解的语气回答用户。如果有相关的历史信息，请适当引用。如果没有足够的相关信息，请诚实地说明，并提供一般性的建议或引导用户提供更多信息。`);

    return contextParts.join('\n');
  }

  /**
   * 生成流式响应
   */
  private async* generateStreamResponse(
    aiProvider: any,
    context: string,
    config: GenerationConfig
  ): AsyncIterable<string> {
    try {
      const stream = await aiProvider.generateTextStream(context, {
        temperature: config.temperature,
        maxTokens: config.maxTokens
      });

      for await (const chunk of stream) {
        yield chunk;
      }
    } catch (error) {
      console.error('❌ 流式生成失败:', error);
      yield '抱歉，我在生成响应时遇到了问题。请稍后再试。';
    }
  }

  /**
   * 计算质量指标
   */
  private calculateQualityMetrics(
    content: string,
    retrievalResult: RetrievalResult,
    contextPackage: ContextPackage
  ): any {
    // 连贯性评分（基于内容长度和结构）
    const coherence = Math.min(1, content.length / 100) * 
                     (content.includes('。') || content.includes('.') ? 1 : 0.8);

    // 相关性评分（基于检索结果的质量）
    const relevance = retrievalResult.mergedResults.length > 0 
      ? retrievalResult.qualityMetrics.relevanceScore 
      : 0.5;

    // 完整性评分（基于内容长度和检索结果覆盖）
    const completeness = Math.min(1, 
      (content.length / 200) * 0.7 + 
      (retrievalResult.mergedResults.length / 5) * 0.3
    );

    // 参与度评分（基于内容的互动性）
    const engagement = content.includes('?') || content.includes('？') || 
                      content.includes('你') || content.includes('您') ? 0.9 : 0.7;

    return {
      coherence: Math.round(coherence * 100) / 100,
      relevance: Math.round(relevance * 100) / 100,
      completeness: Math.round(completeness * 100) / 100,
      engagement: Math.round(engagement * 100) / 100
    };
  }

  /**
   * 提取使用的来源
   */
  private extractSourcesUsed(retrievalResult: RetrievalResult): string[] {
    const sources = new Set<string>();
    
    retrievalResult.mergedResults.forEach(result => {
      if (result.metadata.sourceType) {
        sources.add(result.metadata.sourceType);
      }
    });
    
    return Array.from(sources);
  }

  /**
   * 提取用户输入ID
   */
  private extractUserInputId(retrievalResult: RetrievalResult): string {
    // 从检索ID中提取用户输入ID（假设格式为 YYYYMMDD-T###-D###）
    const parts = retrievalResult.retrievalId.split('-');
    if (parts.length >= 2) {
      return `${parts[0]}-${parts[1]}`;
    }
    return retrievalResult.retrievalId;
  }

  /**
   * 估算token数量
   */
  private estimateTokenCount(text: string): number {
    // 简单的token估算：中文字符按1个token，英文单词按平均4个字符1个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishChars = text.length - chineseChars;
    return chineseChars + Math.ceil(englishChars / 4);
  }

  /**
   * 更新统计信息
   */
  private updateStats(
    generationTime: number,
    success: boolean,
    tokensGenerated: number,
    contextLength: number
  ): void {
    this.stats.totalGenerations++;
    this.stats.lastGenerationTime = generationTime;
    
    // 更新平均生成时间
    this.stats.averageGenerationTime = 
      (this.stats.averageGenerationTime * (this.stats.totalGenerations - 1) + generationTime) / 
      this.stats.totalGenerations;
    
    // 更新平均token数量
    this.stats.averageTokensGenerated = 
      (this.stats.averageTokensGenerated * (this.stats.totalGenerations - 1) + tokensGenerated) / 
      this.stats.totalGenerations;
    
    // 更新平均上下文长度
    this.stats.averageContextLength = 
      (this.stats.averageContextLength * (this.stats.totalGenerations - 1) + contextLength) / 
      this.stats.totalGenerations;
    
    // 更新成功率
    if (!success) {
      this.stats.errorCount++;
    }
    this.stats.successRate = 
      (this.stats.totalGenerations - this.stats.errorCount) / this.stats.totalGenerations;
  }
}

// 导出默认实例
export const integrationGeneratorEngine = new IntegrationGeneratorEngine();
