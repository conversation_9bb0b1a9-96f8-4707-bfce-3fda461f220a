/**
 * 三引擎协同工作流接口定义
 * Navigator、Context Retriever、Integration Generator 的核心接口
 */

// ==================== Navigator Engine 接口 ====================

/**
 * 搜索策略类型
 */
export type SearchStrategyType = 'semantic' | 'keyword' | 'hybrid' | 'contextual';

/**
 * 搜索优先级
 */
export type SearchPriority = 'relevance' | 'recency' | 'importance' | 'balanced';

/**
 * 搜索范围
 */
export type SearchScope = 'hot_only' | 'cold_only' | 'comprehensive' | 'adaptive';

/**
 * Navigator 分析的搜索策略
 */
export interface SearchStrategy {
  type: SearchStrategyType;
  priority: SearchPriority;
  scope: SearchScope;
  confidence: number; // 0-1，策略置信度
}

/**
 * Navigator 生成的关键词集合
 */
export interface KeywordSet {
  primary: string[];      // 主要关键词
  secondary: string[];    // 次要关键词
  emotional: string[];    // 情感关键词
  contextual: string[];   // 上下文关键词
  temporal: string[];     // 时间相关关键词
}

/**
 * Navigator 生成的检索目标
 */
export interface RetrievalTargets {
  hotStoreQueries: string[];     // 热存储查询
  coldStoreQueries: string[];    // 冷存储查询
  crossReferences: string[];     // 交叉引用查询
  semanticQueries: string[];     // 语义查询
}

/**
 * Navigator 输出的完整指令
 */
export interface NavigatorInstruction {
  // 基础信息
  instructionId: string;
  timestamp: string;
  userMessage: string;
  
  // 意图分析
  intentAnalysis: {
    primaryIntent: string;
    secondaryIntents: string[];
    emotionalTone: string;
    urgencyLevel: number; // 0-1
    complexityLevel: number; // 0-1
  };
  
  // 搜索策略
  searchStrategy: SearchStrategy;
  
  // 关键词提取
  keywords: KeywordSet;
  
  // 检索目标
  targets: RetrievalTargets;
  
  // 执行参数
  executionParams: {
    maxResults: number;
    timeoutMs: number;
    parallelExecution: boolean;
    fallbackStrategy?: SearchStrategy;
  };
  
  // 质量评估
  qualityMetrics: {
    confidence: number;
    completeness: number;
    specificity: number;
  };
}

// ==================== Context Retriever 接口 ====================

/**
 * 检索结果项
 */
export interface RetrievalResultItem {
  chunkId: string;
  globalId: string;
  content: string;
  metadata: {
    sourceType: string;
    sourceDocumentId: string;
    createdAt: string;
    relevanceScore: number;
    semanticScore: number;
    temporalScore: number;
  };
  similarity: number;
  rank: number;
}

/**
 * 检索统计信息
 */
export interface RetrievalStats {
  totalSearchTime: number;
  hotStoreTime: number;
  coldStoreTime: number;
  hotStoreHits: number;
  coldStoreHits: number;
  totalProcessed: number;
  cacheHits: number;
  cacheMisses: number;
}

/**
 * Context Retriever 输出结果
 */
export interface RetrievalResult {
  // 基础信息
  retrievalId: string;
  instructionId: string;
  timestamp: string;
  
  // 检索结果
  hotStoreResults: RetrievalResultItem[];
  coldStoreResults: RetrievalResultItem[];
  mergedResults: RetrievalResultItem[];
  
  // 统计信息
  stats: RetrievalStats;
  
  // 质量评估
  qualityMetrics: {
    relevanceScore: number;
    diversityScore: number;
    completenessScore: number;
    freshness: number;
  };
  
  // 元数据
  retrievalMetadata: {
    strategy: SearchStrategy;
    actualScope: SearchScope;
    fallbackUsed: boolean;
    optimizations: string[];
  };
}

// ==================== Integration Generator 接口 ====================

/**
 * 上下文包装
 */
export interface ContextPackage {
  // 用户相关
  userProfile: string;
  recentHistory: string[];
  currentMessage: string;
  
  // 检索内容
  retrievedContent: RetrievalResultItem[];
  
  // 系统信息
  systemPrompts: string[];
  contextualHints: string[];
  
  // 元数据
  packageMetadata: {
    totalLength: number;
    contentSources: string[];
    qualityScore: number;
    timestamp: string;
  };
}

/**
 * 生成配置
 */
export interface GenerationConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  streamResponse: boolean;
  includeMetadata: boolean;
  responseFormat: 'text' | 'structured' | 'hybrid';
}

/**
 * Integration Generator 输出结果
 */
export interface IntegratedResponse {
  // 基础信息
  responseId: string;
  retrievalId: string;
  timestamp: string;
  
  // 响应内容
  content: string;
  responseStream?: AsyncIterable<string>;
  
  // 生成统计
  generationStats: {
    totalTime: number;
    tokensGenerated: number;
    modelUsed: string;
    temperature: number;
  };
  
  // 质量评估
  qualityMetrics: {
    coherence: number;
    relevance: number;
    completeness: number;
    engagement: number;
  };
  
  // 元数据
  responseMetadata: {
    contextLength: number;
    sourcesUsed: string[];
    generationConfig: GenerationConfig;
    globalIds: {
      userInputId: string;
      responseId: string;
    };
  };
}

// ==================== 工作流协调器接口 ====================

/**
 * 工作流执行状态
 */
export type WorkflowStatus = 'pending' | 'running' | 'completed' | 'failed' | 'timeout';

/**
 * 工作流执行结果
 */
export interface WorkflowExecutionResult {
  // 基础信息
  workflowId: string;
  status: WorkflowStatus;
  startTime: string;
  endTime: string;
  totalTime: number;
  
  // 各阶段结果
  navigatorResult?: NavigatorInstruction;
  retrievalResult?: RetrievalResult;
  integrationResult?: IntegratedResponse;
  
  // 错误信息
  errors: Array<{
    stage: string;
    error: string;
    timestamp: string;
  }>;
  
  // 性能指标
  performanceMetrics: {
    navigatorTime: number;
    retrievalTime: number;
    integrationTime: number;
    totalLatency: number;
    memoryUsage: number;
  };
  
  // 质量评估
  overallQuality: {
    accuracy: number;
    relevance: number;
    completeness: number;
    userSatisfaction: number;
  };
}

/**
 * 工作流配置
 */
export interface WorkflowConfig {
  // 执行配置
  timeoutMs: number;
  parallelExecution: boolean;
  retryAttempts: number;
  
  // 引擎配置
  navigatorConfig: {
    model: string;
    temperature: number;
    maxAnalysisTime: number;
  };
  
  retrievalConfig: {
    maxResults: number;
    searchTimeout: number;
    enableCaching: boolean;
  };
  
  integrationConfig: GenerationConfig;
  
  // 质量控制
  qualityThresholds: {
    minConfidence: number;
    minRelevance: number;
    minCompleteness: number;
  };
}

// ==================== 错误处理接口 ====================

/**
 * 三引擎错误类型
 */
export type ThreeEngineErrorType = 
  | 'NAVIGATOR_ERROR'
  | 'RETRIEVAL_ERROR' 
  | 'INTEGRATION_ERROR'
  | 'WORKFLOW_ERROR'
  | 'TIMEOUT_ERROR'
  | 'CONFIGURATION_ERROR';

/**
 * 三引擎错误类
 */
export class ThreeEngineError extends Error {
  constructor(
    message: string,
    public type: ThreeEngineErrorType,
    public stage: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ThreeEngineError';
  }
}

// ==================== 引擎接口定义 ====================

/**
 * Navigator 引擎接口
 */
export interface INavigatorEngine {
  analyzeUserIntent(
    userMessage: string,
    recentContext: string[],
    userProfile?: string
  ): Promise<NavigatorInstruction>;
  
  validateInstruction(instruction: NavigatorInstruction): boolean;
  getEngineStats(): any;
}

/**
 * Context Retriever 引擎接口
 */
export interface IContextRetrieverEngine {
  executeRetrieval(
    instruction: NavigatorInstruction
  ): Promise<RetrievalResult>;
  
  optimizeResults(results: RetrievalResult): Promise<RetrievalResult>;
  getEngineStats(): any;
}

/**
 * Integration Generator 引擎接口
 */
export interface IIntegrationGeneratorEngine {
  generateResponse(
    userMessage: string,
    retrievalResult: RetrievalResult,
    contextPackage: ContextPackage,
    config: GenerationConfig
  ): Promise<IntegratedResponse>;
  
  validateResponse(response: IntegratedResponse): boolean;
  getEngineStats(): any;
}

/**
 * 工作流协调器接口
 */
export interface IWorkflowCoordinator {
  executeWorkflow(
    userMessage: string,
    config?: Partial<WorkflowConfig>
  ): Promise<WorkflowExecutionResult>;
  
  getWorkflowStats(): any;
  healthCheck(): Promise<boolean>;
}
