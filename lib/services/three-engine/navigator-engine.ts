/**
 * Navigator Engine - 用户意图分析和检索策略生成引擎
 * 负责分析用户输入，生成精准的检索指令
 */

import { getDefaultAIProvider } from '@/lib/ai';
import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import {
  INavigatorEngine,
  NavigatorInstruction,
  SearchStrategy,
  KeywordSet,
  RetrievalTargets,
  ThreeEngineError
} from './interfaces';

/**
 * Navigator引擎配置
 */
export interface NavigatorConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  maxAnalysisTime: number;
  enableContextualAnalysis: boolean;
  enableEmotionalAnalysis: boolean;
}

/**
 * Navigator引擎统计
 */
interface NavigatorStats {
  totalAnalyses: number;
  averageAnalysisTime: number;
  successRate: number;
  lastAnalysisTime: number;
  errorCount: number;
}

/**
 * Navigator引擎实现
 */
export class NavigatorEngine implements INavigatorEngine {
  private config: NavigatorConfig;
  private stats: NavigatorStats;
  private initialized: boolean = false;

  constructor(config?: Partial<NavigatorConfig>) {
    this.config = {
      model: 'doubao/doubao-lite-4k',
      temperature: 0.3,
      maxTokens: 1000,
      maxAnalysisTime: 5000, // 5秒
      enableContextualAnalysis: true,
      enableEmotionalAnalysis: true,
      ...config
    };

    this.stats = {
      totalAnalyses: 0,
      averageAnalysisTime: 0,
      successRate: 1.0,
      lastAnalysisTime: 0,
      errorCount: 0
    };
  }

  /**
   * 初始化Navigator引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🧭 初始化Navigator引擎...');
      
      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();
      
      this.initialized = true;
      console.log('🧭 Navigator引擎初始化完成');
    } catch (error) {
      console.error('❌ Navigator引擎初始化失败:', error);
      throw new ThreeEngineError(
        'Navigator引擎初始化失败',
        'NAVIGATOR_ERROR',
        'initialization',
        error
      );
    }
  }

  /**
   * 分析用户意图并生成检索指令
   */
  async analyzeUserIntent(
    userMessage: string,
    recentContext: string[] = [],
    userProfile?: string
  ): Promise<NavigatorInstruction> {
    if (!this.initialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    
    try {
      console.log('🧭 Navigator开始分析用户意图...');
      
      // 生成指令ID
      const instructionId = await globalIdManager.generateDerivedId(
        await globalIdManager.generateUserInputId(),
        'navigator_instruction',
        'analysis'
      );

      // 构建分析提示词
      const analysisPrompt = this.buildAnalysisPrompt(userMessage, recentContext, userProfile);
      
      // 调用AI进行分析
      const aiProvider = await getDefaultAIProvider();
      const analysisResult = await Promise.race([
        aiProvider.generateText(analysisPrompt, {
          temperature: this.config.temperature,
          maxTokens: this.config.maxTokens
        }),
        this.createTimeoutPromise(this.config.maxAnalysisTime)
      ]);

      // 解析AI分析结果
      const instruction = await this.parseAnalysisResult(
        instructionId,
        userMessage,
        analysisResult,
        recentContext
      );

      // 验证指令质量
      if (!this.validateInstruction(instruction)) {
        throw new Error('生成的指令质量不符合要求');
      }

      // 更新统计信息
      const analysisTime = Date.now() - startTime;
      this.updateStats(analysisTime, true);

      console.log(`🧭 Navigator分析完成: ${analysisTime}ms, 置信度: ${instruction.qualityMetrics.confidence}`);
      
      return instruction;

    } catch (error) {
      const analysisTime = Date.now() - startTime;
      this.updateStats(analysisTime, false);
      
      console.error('❌ Navigator分析失败:', error);
      
      // 生成降级指令
      return this.generateFallbackInstruction(userMessage, recentContext);
    }
  }

  /**
   * 验证指令质量
   */
  validateInstruction(instruction: NavigatorInstruction): boolean {
    try {
      // 基础字段验证
      if (!instruction.instructionId || !instruction.userMessage) {
        return false;
      }

      // 意图分析验证
      if (!instruction.intentAnalysis.primaryIntent) {
        return false;
      }

      // 关键词验证
      if (instruction.keywords.primary.length === 0) {
        return false;
      }

      // 检索目标验证
      const totalQueries = instruction.targets.hotStoreQueries.length + 
                          instruction.targets.coldStoreQueries.length;
      if (totalQueries === 0) {
        return false;
      }

      // 质量指标验证
      if (instruction.qualityMetrics.confidence < 0.3) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ 指令验证失败:', error);
      return false;
    }
  }

  /**
   * 获取引擎统计信息
   */
  getEngineStats(): NavigatorStats {
    return { ...this.stats };
  }

  // 私有方法

  /**
   * 构建分析提示词
   */
  private buildAnalysisPrompt(
    userMessage: string,
    recentContext: string[],
    userProfile?: string
  ): string {
    const contextSection = recentContext.length > 0 
      ? `\n最近对话上下文:\n${recentContext.slice(-3).join('\n')}\n`
      : '';

    const profileSection = userProfile 
      ? `\n用户画像:\n${userProfile}\n`
      : '';

    return `你是SelfMirror的Navigator引擎，负责分析用户意图并生成检索策略。

${profileSection}${contextSection}
用户当前输入: "${userMessage}"

请分析用户意图并生成JSON格式的检索指令，包含以下信息：

1. 意图分析 (intentAnalysis):
   - primaryIntent: 主要意图 (问答/聊天/回忆/分析/其他)
   - secondaryIntents: 次要意图列表
   - emotionalTone: 情感色调 (积极/消极/中性/复杂)
   - urgencyLevel: 紧急程度 (0-1)
   - complexityLevel: 复杂程度 (0-1)

2. 搜索策略 (searchStrategy):
   - type: 搜索类型 (semantic/keyword/hybrid/contextual)
   - priority: 优先级 (relevance/recency/importance/balanced)
   - scope: 搜索范围 (hot_only/cold_only/comprehensive/adaptive)
   - confidence: 策略置信度 (0-1)

3. 关键词提取 (keywords):
   - primary: 主要关键词 (3-5个)
   - secondary: 次要关键词 (2-4个)
   - emotional: 情感关键词 (1-3个)
   - contextual: 上下文关键词 (1-3个)
   - temporal: 时间相关关键词 (0-2个)

4. 检索目标 (targets):
   - hotStoreQueries: 热存储查询语句 (1-3个)
   - coldStoreQueries: 冷存储查询语句 (1-3个)
   - crossReferences: 交叉引用查询 (0-2个)
   - semanticQueries: 语义查询 (1-2个)

5. 执行参数 (executionParams):
   - maxResults: 最大结果数 (5-20)
   - timeoutMs: 超时时间 (3000-10000)
   - parallelExecution: 是否并行执行 (true/false)

请返回完整的JSON对象，确保所有字段都有合理的值。`;
  }

  /**
   * 解析AI分析结果
   */
  private async parseAnalysisResult(
    instructionId: string,
    userMessage: string,
    analysisResult: string,
    recentContext: string[]
  ): Promise<NavigatorInstruction> {
    try {
      console.log('🔍 Navigator引擎解析AI结果:', analysisResult.slice(0, 200) + '...');

      // 清理AI返回的结果，移除markdown代码块标记
      let cleanedResult = analysisResult.trim();

      // 移除开头的 ```json 或 ```
      if (cleanedResult.startsWith('```json')) {
        cleanedResult = cleanedResult.substring(7);
      } else if (cleanedResult.startsWith('```')) {
        cleanedResult = cleanedResult.substring(3);
      }

      // 移除结尾的 ```
      if (cleanedResult.endsWith('```')) {
        cleanedResult = cleanedResult.substring(0, cleanedResult.length - 3);
      }

      // 再次清理空白字符
      cleanedResult = cleanedResult.trim();

      // 尝试解析JSON
      let parsed: any;
      try {
        parsed = JSON.parse(cleanedResult);
      } catch (jsonError) {
        console.warn('⚠️ JSON解析失败，尝试提取关键信息:', jsonError);
        // 如果JSON解析失败，使用fallback逻辑
        parsed = this.extractFallbackData(cleanedResult, userMessage);
      }
      
      // 构建完整的指令对象
      const instruction: NavigatorInstruction = {
        instructionId,
        timestamp: new Date().toISOString(),
        userMessage,
        
        intentAnalysis: {
          primaryIntent: parsed.intentAnalysis?.primaryIntent || '聊天',
          secondaryIntents: parsed.intentAnalysis?.secondaryIntents || [],
          emotionalTone: parsed.intentAnalysis?.emotionalTone || '中性',
          urgencyLevel: parsed.intentAnalysis?.urgencyLevel || 0.5,
          complexityLevel: parsed.intentAnalysis?.complexityLevel || 0.5
        },
        
        searchStrategy: {
          type: parsed.searchStrategy?.type || 'hybrid',
          priority: parsed.searchStrategy?.priority || 'balanced',
          scope: parsed.searchStrategy?.scope || 'comprehensive',
          confidence: parsed.searchStrategy?.confidence || 0.7
        },
        
        keywords: {
          primary: parsed.keywords?.primary || [userMessage.slice(0, 10)],
          secondary: parsed.keywords?.secondary || [],
          emotional: parsed.keywords?.emotional || [],
          contextual: parsed.keywords?.contextual || [],
          temporal: parsed.keywords?.temporal || []
        },
        
        targets: {
          hotStoreQueries: parsed.targets?.hotStoreQueries || [userMessage],
          coldStoreQueries: parsed.targets?.coldStoreQueries || [userMessage],
          crossReferences: parsed.targets?.crossReferences || [],
          semanticQueries: parsed.targets?.semanticQueries || [userMessage]
        },
        
        executionParams: {
          maxResults: parsed.executionParams?.maxResults || 10,
          timeoutMs: parsed.executionParams?.timeoutMs || 5000,
          parallelExecution: parsed.executionParams?.parallelExecution !== false
        },
        
        qualityMetrics: {
          confidence: parsed.searchStrategy?.confidence || 0.7,
          completeness: this.calculateCompleteness(parsed),
          specificity: this.calculateSpecificity(parsed)
        }
      };

      console.log('✅ Navigator引擎成功解析结果:', {
        primaryIntent: instruction.intentAnalysis.primaryIntent,
        searchStrategy: instruction.searchStrategy.type,
        confidence: instruction.qualityMetrics.confidence
      });

      return instruction;
    } catch (error) {
      console.error('❌ 解析分析结果失败:', error);
      console.log('🔄 使用fallback策略生成默认指令');

      // 使用fallback策略生成默认指令
      return this.generateFallbackInstruction(instructionId, userMessage, recentContext);
    }
  }

  /**
   * 提取fallback数据
   */
  private extractFallbackData(text: string, userMessage: string): any {
    console.log('🔄 使用fallback数据提取');

    // 基于关键词的简单意图识别
    const lowerMessage = userMessage.toLowerCase();
    let primaryIntent = '聊天';

    if (lowerMessage.includes('如何') || lowerMessage.includes('怎么') || lowerMessage.includes('方法')) {
      primaryIntent = '问答';
    } else if (lowerMessage.includes('分析') || lowerMessage.includes('评估') || lowerMessage.includes('比较')) {
      primaryIntent = '分析';
    } else if (lowerMessage.includes('记得') || lowerMessage.includes('之前') || lowerMessage.includes('历史')) {
      primaryIntent = '回忆';
    }

    return {
      intentAnalysis: {
        primaryIntent,
        secondaryIntents: [],
        emotionalTone: '中性',
        urgencyLevel: 0.5,
        complexityLevel: 0.5
      },
      searchStrategy: {
        type: 'hybrid',
        priority: 'balanced',
        scope: 'comprehensive',
        confidence: 0.6
      },
      keywords: {
        primary: userMessage.split(/\s+/).slice(0, 3),
        secondary: [],
        emotional: [],
        contextual: [],
        temporal: []
      },
      targets: {
        hotStoreQueries: [userMessage],
        coldStoreQueries: [userMessage],
        crossReferences: [],
        semanticQueries: [userMessage]
      },
      executionParams: {
        maxResults: 10,
        timeoutMs: 5000,
        parallelExecution: true
      }
    };
  }

  /**
   * 生成fallback指令
   */
  private generateFallbackInstruction(
    instructionId: string,
    userMessage: string,
    recentContext: string[]
  ): NavigatorInstruction {
    console.log('🔄 生成fallback指令');

    const fallbackData = this.extractFallbackData('', userMessage);

    return {
      instructionId,
      timestamp: new Date().toISOString(),
      userMessage,

      intentAnalysis: fallbackData.intentAnalysis,
      searchStrategy: fallbackData.searchStrategy,
      keywords: fallbackData.keywords,
      targets: fallbackData.targets,
      executionParams: fallbackData.executionParams,

      qualityMetrics: {
        confidence: 0.6,
        completeness: 0.7,
        specificity: 0.5
      }
    };
  }

  /**
   * 处理解析错误的fallback逻辑
   */
  private handleParsingError(error: any): never {
    console.error('❌ Navigator引擎解析失败:', error);
    throw new ThreeEngineError(
        '解析Navigator分析结果失败',
        'NAVIGATOR_ERROR',
        'parsing',
        error
      );
  }

  /**
   * 计算完整性分数
   */
  private calculateCompleteness(parsed: any): number {
    let score = 0;
    const checks = [
      parsed.intentAnalysis?.primaryIntent,
      parsed.searchStrategy?.type,
      parsed.keywords?.primary?.length > 0,
      parsed.targets?.hotStoreQueries?.length > 0,
      parsed.targets?.coldStoreQueries?.length > 0,
      parsed.executionParams?.maxResults
    ];
    
    score = checks.filter(Boolean).length / checks.length;
    return Math.min(1, Math.max(0, score));
  }

  /**
   * 计算特异性分数
   */
  private calculateSpecificity(parsed: any): number {
    const keywordCount = (parsed.keywords?.primary?.length || 0) + 
                        (parsed.keywords?.secondary?.length || 0);
    const queryCount = (parsed.targets?.hotStoreQueries?.length || 0) + 
                      (parsed.targets?.coldStoreQueries?.length || 0);
    
    const specificity = Math.min(1, (keywordCount + queryCount) / 10);
    return Math.max(0.3, specificity);
  }

  /**
   * 创建超时Promise
   */
  private createTimeoutPromise(timeoutMs: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new ThreeEngineError(
          `Navigator分析超时 (${timeoutMs}ms)`,
          'TIMEOUT_ERROR',
          'analysis'
        ));
      }, timeoutMs);
    });
  }

  /**
   * 更新统计信息
   */
  private updateStats(analysisTime: number, success: boolean): void {
    this.stats.totalAnalyses++;
    this.stats.lastAnalysisTime = analysisTime;
    
    // 更新平均分析时间
    this.stats.averageAnalysisTime = 
      (this.stats.averageAnalysisTime * (this.stats.totalAnalyses - 1) + analysisTime) / 
      this.stats.totalAnalyses;
    
    // 更新成功率
    if (!success) {
      this.stats.errorCount++;
    }
    this.stats.successRate = 
      (this.stats.totalAnalyses - this.stats.errorCount) / this.stats.totalAnalyses;
  }
}

// 导出默认实例
export const navigatorEngine = new NavigatorEngine();
