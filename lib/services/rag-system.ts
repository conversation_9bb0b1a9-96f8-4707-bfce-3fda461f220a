// RAG系统主入口 - 统一的接口和管理

import { ragIndexer } from './rag-indexer';
import { ragRetriever } from './rag-retriever';
import { embeddingModel } from './embedding-model';
import { vectorDatabase } from './vector-database';
import { ragStorage } from '@/lib/storage/rag-storage';
import { ChunkPair, RAGError } from '@/types/rag';
import { MEMORY_FILES } from '@/lib/storage/memory-manager';

class RAGSystem {
  private initialized = false;
  private initializationPromise: Promise<void> | null = null;

  /**
   * 初始化整个RAG系统
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('✅ RAG系统已初始化');
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.doInitialize();
    return this.initializationPromise;
  }

  private async doInitialize(): Promise<void> {
    console.log('🚀 开始初始化RAG系统...');
    
    try {
      // 1. 初始化存储
      console.log('📦 初始化存储系统...');
      await ragStorage.initialize();

      // 2. 初始化向量数据库
      console.log('🔢 初始化向量数据库...');
      await vectorDatabase.initialize();

      // 3. 加载嵌入模型
      console.log('🧠 加载嵌入模型...');
      await embeddingModel.load();

      this.initialized = true;
      console.log('✅ RAG系统初始化完成');
    } catch (error) {
      console.error('💥 RAG系统初始化失败:', error);
      throw new RAGError('RAG系统初始化失败', 'INITIALIZATION_ERROR', error);
    }
  }

  /**
   * 索引记忆文件
   */
  async indexMemoryFile(
    filename: keyof typeof MEMORY_FILES,
    content: string
  ): Promise<void> {
    await this.ensureInitialized();
    
    const documentType = this.getDocumentType(filename);
    const documentId = `memory_${filename}_${Date.now()}`;
    
    console.log(`📚 开始索引记忆文件: ${filename}`);
    
    try {
      await ragIndexer.processAndIndexDocument(content, documentId, documentType);
      console.log(`✅ 记忆文件 ${filename} 索引完成`);
    } catch (error) {
      console.error(`❌ 记忆文件 ${filename} 索引失败:`, error);
      throw error;
    }
  }

  /**
   * 批量索引所有记忆文件
   */
  async indexAllMemoryFiles(memoryContents: Record<string, string>): Promise<void> {
    await this.ensureInitialized();
    
    console.log('📚 开始批量索引所有记忆文件...');
    
    const indexingPromises = Object.entries(memoryContents).map(async ([filename, content]) => {
      if (content && content.trim()) {
        try {
          await this.indexMemoryFile(filename as keyof typeof MEMORY_FILES, content);
        } catch (error) {
          console.error(`⚠️ 索引文件 ${filename} 失败:`, error);
        }
      }
    });

    await Promise.all(indexingPromises);
    console.log('✅ 批量索引完成');
  }

  /**
   * 检索相关记忆
   */
  async retrieveMemories(
    query: string,
    userProfile: string,
    dailyInsight: string
  ): Promise<string[]> {
    await this.ensureInitialized();
    
    try {
      return await ragRetriever.retrieveMeaningfulMemories(query, userProfile, dailyInsight);
    } catch (error) {
      console.error('❌ 记忆检索失败:', error);
      throw error;
    }
  }

  /**
   * 重新索引特定记忆文件
   */
  async reindexMemoryFile(
    filename: keyof typeof MEMORY_FILES,
    content: string
  ): Promise<void> {
    await this.ensureInitialized();
    
    const documentType = this.getDocumentType(filename);
    const documentId = `memory_${filename}`;
    
    console.log(`🔄 重新索引记忆文件: ${filename}`);
    
    try {
      await ragIndexer.reindexDocument(content, documentId, documentType);
      console.log(`✅ 记忆文件 ${filename} 重新索引完成`);
    } catch (error) {
      console.error(`❌ 记忆文件 ${filename} 重新索引失败:`, error);
      throw error;
    }
  }

  /**
   * 获取系统状态
   */
  async getSystemStatus() {
    const status = {
      initialized: this.initialized,
      embeddingModel: embeddingModel.getModelInfo(),
      vectorDatabase: vectorDatabase.getStats(),
      storage: await ragStorage.getStorageStats(),
      retriever: ragRetriever.getDebugInfo()
    };

    return status;
  }

  /**
   * 获取索引进度
   */
  getIndexingProgress() {
    return ragIndexer.getAllProgress();
  }

  /**
   * 清理系统
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理RAG系统...');
    
    try {
      // 清理缓存
      embeddingModel.clearCache();
      ragRetriever.clearCache();
      
      // 清理进度记录
      ragIndexer.cleanupProgress();
      
      console.log('✅ RAG系统清理完成');
    } catch (error) {
      console.error('⚠️ RAG系统清理失败:', error);
    }
  }

  /**
   * 重置整个系统
   */
  async reset(): Promise<void> {
    console.log('🔄 重置RAG系统...');
    
    try {
      // 清空所有数据
      await ragStorage.clearAll();
      await vectorDatabase.clear();
      
      // 清理缓存
      await this.cleanup();
      
      // 重新初始化
      this.initialized = false;
      this.initializationPromise = null;
      await this.initialize();
      
      console.log('✅ RAG系统重置完成');
    } catch (error) {
      console.error('💥 RAG系统重置失败:', error);
      throw error;
    }
  }

  /**
   * 导出系统数据
   */
  async exportData(): Promise<{
    chunks: ChunkPair[];
    vectors: Array<{ chunkId: string; vector: number[] }>;
    metadata: any;
  }> {
    await this.ensureInitialized();
    
    try {
      const [stats, vectors] = await Promise.all([
        ragStorage.getStorageStats(),
        ragStorage.getAllVectors()
      ]);

      // 获取所有块对数据
      const chunks: ChunkPair[] = [];
      for (const vectorData of vectors) {
        const chunkPair = await ragStorage.getChunkPair(vectorData.chunkId);
        if (chunkPair) {
          chunks.push(chunkPair);
        }
      }

      return {
        chunks,
        vectors,
        metadata: {
          exportTime: new Date().toISOString(),
          totalChunks: chunks.length,
          totalVectors: vectors.length,
          storageStats: stats
        }
      };
    } catch (error) {
      throw new RAGError('数据导出失败', 'EXPORT_ERROR', error);
    }
  }

  /**
   * 确保系统已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 根据文件名获取文档类型
   */
  private getDocumentType(filename: string): ChunkPair['sourceDocumentType'] {
    const typeMap: Record<string, ChunkPair['sourceDocumentType']> = {
      'USER_PROFILE': 'user_profile',
      'KEY_EVENTS': 'key_events',
      'DAILY_INSIGHT_COLD': 'daily_insight_cold',
      'DAILY_INSIGHT_HOT': 'daily_insight_hot',
      'DIALOGUE_HISTORY': 'dialogue_history'
    };

    return typeMap[filename] || 'dialogue_history';
  }

  /**
   * 检查系统健康状态
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: any;
  }> {
    try {
      const status = await this.getSystemStatus();
      
      let healthStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      const issues: string[] = [];

      // 检查各组件状态
      if (!status.initialized) {
        healthStatus = 'unhealthy';
        issues.push('系统未初始化');
      }

      if (!status.embeddingModel.loaded) {
        healthStatus = 'degraded';
        issues.push('嵌入模型未加载');
      }

      if (!status.vectorDatabase.initialized) {
        healthStatus = 'degraded';
        issues.push('向量数据库未初始化');
      }

      if (status.storage.totalChunks === 0) {
        healthStatus = 'degraded';
        issues.push('没有索引数据');
      }

      return {
        status: healthStatus,
        details: {
          ...status,
          issues,
          checkTime: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : '未知错误',
          checkTime: new Date().toISOString()
        }
      };
    }
  }
}

// 导出单例
export const ragSystem = new RAGSystem();

// 导出便捷函数
export const initializeRAG = () => ragSystem.initialize();
export const indexMemoryFile = (filename: keyof typeof MEMORY_FILES, content: string) => 
  ragSystem.indexMemoryFile(filename, content);
export const retrieveMemories = (query: string, userProfile: string, dailyInsight: string) =>
  ragSystem.retrieveMemories(query, userProfile, dailyInsight);
export const getRAGStatus = () => ragSystem.getSystemStatus();
