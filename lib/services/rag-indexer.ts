// RAG索引器 - 数据预处理与向量化流水线

import { v4 as uuidv4 } from 'uuid';
import {
  ChunkPair,
  ChunkMetadata,
  IndexingProgress,
  ChunkingError,
  EmbeddingError,
  IndexingConfig,
  ValidationResult,
  PerformanceMetrics
} from '@/types/rag';
import { embeddingModel } from './embedding-model';
import { vectorDatabase } from './vector-database';
import { ragStorageAdapter } from '@/lib/storage/rag-storage-adapter';
import { llmService } from './llm-service';
import { ragConfigManager } from './rag-config-manager';
import { RAG_CONFIG, EMOTIONAL_TAGS, COGNITIVE_THEMES } from '@/lib/config/rag-config';

class RAGIndexer {
  private indexingProgress: Map<string, IndexingProgress> = new Map();

  /**
   * 处理一篇长文档，将其切块、生成意义子块、向量化并存入本地数据库 - 支持动态配置
   * @param documentText - 待处理的文档原文
   * @param documentId - 文档的唯一ID
   * @param config - 索引配置
   * @param documentType - 文档类型
   */
  async processAndIndexDocumentWithConfig(
    documentText: string,
    documentId: string,
    config: IndexingConfig,
    documentType: ChunkPair['sourceDocumentType'] = 'dialogue_history'
  ): Promise<IndexingProgress> {
    console.log('🚀 开始处理和索引文档（动态配置模式）...');

    // 验证配置
    const validation = ragConfigManager.validateIndexingConfig(config);
    if (!validation.isValid) {
      throw new ChunkingError('配置验证失败: ' + validation.errors.map(e => e.message).join(', '));
    }

    const startTime = Date.now();
    const progress: IndexingProgress = {
      documentId,
      totalChunks: 0,
      processedChunks: 0,
      currentStage: 'chunking',
      startTime: new Date().toISOString(),
      errors: []
    };
    this.indexingProgress.set(documentId, progress);

    try {
      // 确保模型已加载
      if (!embeddingModel.loaded) {
        await embeddingModel.load();
      }

      // 使用配置的分块策略
      const chunks = await this.chunkDocumentWithConfig(documentText, config);
      progress.totalChunks = chunks.length;

      console.log(`📄 文档分块完成，共 ${chunks.length} 个块`);

      const chunkPairs: ChunkPair[] = [];
      const batchSize = config.batchSize || 10;

      // 批量处理
      for (let i = 0; i < chunks.length; i += batchSize) {
        const batch = chunks.slice(i, i + batchSize);
        const batchPairs = await this.processBatchWithConfig(
          batch,
          documentId,
          documentType,
          config,
          i
        );

        chunkPairs.push(...batchPairs);
        progress.processedChunks += batch.length;

        console.log(`📊 批次进度: ${progress.processedChunks}/${progress.totalChunks}`);
      }

      // 保存到存储
      await ragStorageAdapter.saveChunkPairs(chunkPairs);

      // 更新向量数据库（如果初始化）
      if (vectorDatabase.initialized) {
        const vectorData = chunkPairs.map(cp => ({
          vector: cp.vector,
          metadata: { chunkId: cp.id }
        }));
        await vectorDatabase.addBatch(vectorData);
      }

      progress.currentStage = 'complete';
      progress.estimatedCompletion = new Date().toISOString();

      const totalTime = Date.now() - startTime;
      console.log(`✅ 文档索引完成，耗时 ${totalTime}ms`);

      return progress;
    } catch (error) {
      progress.currentStage = 'storage'; // 设置为最后一个有效阶段
      progress.estimatedCompletion = new Date().toISOString();
      progress.errors?.push(error instanceof Error ? error.message : '未知错误');

      console.error('❌ 文档索引失败:', error);
      throw error;
    }
  }

  /**
   * 处理一篇长文档，将其切块、生成意义子块、向量化并存入本地数据库 - 兼容原有接口
   * @param documentText - 待处理的文档原文
   * @param documentId - 文档的唯一ID
   * @param documentType - 文档类型
   */
  async processAndIndexDocument(
    documentText: string,
    documentId: string,
    documentType: ChunkPair['sourceDocumentType']
  ): Promise<void> {
    // 使用默认配置
    const defaultConfig = ragConfigManager.getDefaultIndexingConfig();
    await this.processAndIndexDocumentWithConfig(documentText, documentId, defaultConfig, documentType);
    console.log(`🔄 开始处理文档: ${documentId} (类型: ${documentType})`);
    
    // 初始化进度跟踪
    const progress: IndexingProgress = {
      documentId,
      totalChunks: 0,
      processedChunks: 0,
      currentStage: 'chunking',
      startTime: new Date().toISOString(),
      errors: []
    };
    this.indexingProgress.set(documentId, progress);

    try {
      // 确保模型已加载
      if (!embeddingModel.loaded) {
        await embeddingModel.load();
      }

      // 确保向量数据库已初始化
      if (!vectorDatabase.initialized) {
        await vectorDatabase.initialize();
      }

      // 步骤1：对文档进行初步的"母块"切分
      progress.currentStage = 'chunking';
      console.log('📄 开始文档分块...');
      const parentChunks: string[] = this.chunkDocument(documentText);
      progress.totalChunks = parentChunks.length;
      console.log(`📊 文档分块完成，共 ${parentChunks.length} 个母块`);

      const chunkPairs: ChunkPair[] = [];

      // 步骤2：处理每个母块
      for (let i = 0; i < parentChunks.length; i++) {
        const parentChunk = parentChunks[i];
        
        try {
          // 生成意义子块
          progress.currentStage = 'meaning_generation';
          console.log(`🧠 生成第 ${i + 1}/${parentChunks.length} 个意义子块...`);
          const childChunk: string = await this.generateMeaningfulChildChunk(parentChunk);

          // 向量化子块
          progress.currentStage = 'vectorization';
          console.log(`🔢 向量化第 ${i + 1}/${parentChunks.length} 个子块...`);
          const vector: number[] = await embeddingModel.encode(childChunk);

          // 生成块元数据（可选择是否使用AI增强）
          const useAIEnhancement = false; // 可以根据配置或性能需求调整
          const metadata = await this.generateChunkMetadata(parentChunk, childChunk, i, useAIEnhancement);

          // 创建块对
          const chunkPair: ChunkPair = {
            id: uuidv4(),
            parentChunk,
            childChunk,
            vector,
            sourceDocumentId: documentId,
            sourceDocumentType: documentType,
            metadata,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          chunkPairs.push(chunkPair);
          progress.processedChunks = i + 1;
          
        } catch (error) {
          const errorMsg = `处理第 ${i + 1} 个块失败: ${error}`;
          console.error('❌', errorMsg);
          progress.errors?.push(errorMsg);
          // 继续处理其他块
        }
      }

      // 步骤3：批量存储
      if (chunkPairs.length > 0) {
        progress.currentStage = 'storage';
        console.log(`💾 批量存储 ${chunkPairs.length} 个块对...`);
        
        // 保存到本地存储（自动适配环境）
        await ragStorageAdapter.saveChunkPairs(chunkPairs);
        
        // 添加到向量数据库
        const vectorData = chunkPairs.map(cp => ({
          vector: cp.vector,
          metadata: { chunkId: cp.id }
        }));
        await vectorDatabase.addBatch(vectorData);
      }

      progress.currentStage = 'complete';
      console.log(`✅ 文档 ${documentId} 处理完成，成功处理 ${chunkPairs.length} 个块对`);

    } catch (error) {
      const errorMsg = `文档处理失败: ${error}`;
      console.error('💥', errorMsg);
      progress.errors?.push(errorMsg);
      throw error;
    } finally {
      progress.estimatedCompletion = new Date().toISOString();
      this.indexingProgress.set(documentId, progress);
    }
  }

  /**
   * 使用配置的分块策略进行文档分块
   */
  private async chunkDocumentWithConfig(text: string, config: IndexingConfig): Promise<string[]> {
    console.log(`📄 使用 ${config.chunkingStrategy} 策略进行分块...`);

    switch (config.chunkingStrategy) {
      case 'recursive':
        return this.recursiveChunking(text, config);
      case 'sentence':
        return this.sentenceChunking(text, config);
      case 'paragraph':
        return this.paragraphChunking(text, config);
      case 'semantic':
        return await this.semanticChunking(text, config);
      default:
        console.warn(`⚠️ 未知的分块策略: ${config.chunkingStrategy}，使用递归分块`);
        return this.recursiveChunking(text, config);
    }
  }

  /**
   * 递归分块策略
   */
  private recursiveChunking(text: string, config: IndexingConfig): string[] {
    const chunks: string[] = [];
    const { chunkSize, chunkOverlap } = config;

    // 首先尝试按段落分割
    const paragraphs = text.split(/\n\s*\n/);

    for (const paragraph of paragraphs) {
      if (paragraph.length <= chunkSize) {
        chunks.push(paragraph.trim());
      } else {
        // 段落太长，按句子分割
        const sentences = paragraph.split(/[。！？.!?]/);
        let currentChunk = '';

        for (const sentence of sentences) {
          if ((currentChunk + sentence).length <= chunkSize) {
            currentChunk += sentence + '。';
          } else {
            if (currentChunk) {
              chunks.push(currentChunk.trim());
            }
            currentChunk = sentence + '。';
          }
        }

        if (currentChunk) {
          chunks.push(currentChunk.trim());
        }
      }
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  /**
   * 按句子分块策略
   */
  private sentenceChunking(text: string, config: IndexingConfig): string[] {
    const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
    const chunks: string[] = [];
    const { chunkSize, chunkOverlap } = config;

    let currentChunk = '';

    for (const sentence of sentences) {
      const sentenceWithPunctuation = sentence.trim() + '。';

      if ((currentChunk + sentenceWithPunctuation).length <= chunkSize) {
        currentChunk += sentenceWithPunctuation;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
        }
        currentChunk = sentenceWithPunctuation;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  /**
   * 按段落分块策略
   */
  private paragraphChunking(text: string, config: IndexingConfig): string[] {
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const chunks: string[] = [];
    const { chunkSize } = config;

    for (const paragraph of paragraphs) {
      if (paragraph.length <= chunkSize) {
        chunks.push(paragraph.trim());
      } else {
        // 段落太长，递归分块
        const subChunks = this.recursiveChunking(paragraph, config);
        chunks.push(...subChunks);
      }
    }

    return chunks;
  }

  /**
   * 语义分块策略（使用AI辅助）
   */
  private async semanticChunking(text: string, config: IndexingConfig): Promise<string[]> {
    // 这是一个高级功能，可以使用AI来识别语义边界
    console.log('🧠 使用AI辅助语义分块...');

    try {
      // 首先进行基础分块
      const baseChunks = this.recursiveChunking(text, config);

      // TODO: 可以在这里添加AI语义分析来优化分块边界
      // 目前返回基础分块结果
      return baseChunks;
    } catch (error) {
      console.error('❌ 语义分块失败，降级到递归分块:', error);
      return this.recursiveChunking(text, config);
    }
  }

  /**
   * 批量处理块对
   */
  private async processBatchWithConfig(
    chunks: string[],
    documentId: string,
    documentType: ChunkPair['sourceDocumentType'],
    config: IndexingConfig,
    startIndex: number
  ): Promise<ChunkPair[]> {
    const chunkPairs: ChunkPair[] = [];

    // 控制并发数
    const concurrency = Math.min(config.maxConcurrency || 3, chunks.length);
    const semaphore = new Array(concurrency).fill(null);

    const processChunk = async (chunk: string, index: number): Promise<ChunkPair | null> => {
      try {
        // 生成意义子块
        const childChunk = await this.generateMeaningfulChildChunkWithConfig(chunk, config);

        // 向量化
        const vector = await embeddingModel.encode(childChunk);

        // 生成元数据
        const metadata = await this.generateChunkMetadataWithConfig(
          chunk,
          childChunk,
          startIndex + index,
          config
        );

        return {
          id: uuidv4(),
          parentChunk: chunk,
          childChunk,
          vector,
          sourceDocumentId: documentId,
          sourceDocumentType: documentType,
          metadata,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      } catch (error) {
        console.error(`❌ 处理块 ${startIndex + index} 失败:`, error);
        return null;
      }
    };

    // 并发处理
    const promises = chunks.map((chunk, index) => processChunk(chunk, index));
    const results = await Promise.all(promises);

    // 过滤掉失败的结果
    return results.filter((result): result is ChunkPair => result !== null);
  }

  /**
   * 使用配置生成意义子块
   */
  private async generateMeaningfulChildChunkWithConfig(
    parentChunk: string,
    config: IndexingConfig
  ): Promise<string> {
    if (!config.enableAIEnhancement) {
      // 不使用AI增强，返回简化摘要
      return this.generateSimpleSummary(parentChunk);
    }

    try {
      // 使用配置的提示词
      const customPrompt = config.meaningGenerationPrompt.replace('{chunk}', parentChunk);
      const childChunk = await llmService.generateMeaningfulChildChunk(parentChunk, customPrompt);
      return childChunk;
    } catch (error) {
      console.error('❌ AI意义子块生成失败，使用降级方案:', error);
      return this.generateSimpleSummary(parentChunk);
    }
  }

  /**
   * 生成简化摘要
   */
  private generateSimpleSummary(text: string): string {
    const summary = text.length > 200 ? text.substring(0, 200) + '...' : text;
    return `意义摘要: ${summary}`;
  }

  /**
   * 使用配置生成块元数据
   */
  private async generateChunkMetadataWithConfig(
    parentChunk: string,
    childChunk: string,
    chunkIndex: number,
    config: IndexingConfig
  ): Promise<ChunkMetadata> {
    const useAI = config.enableAIEnhancement;

    // 提取情感标签和认知主题
    const emotionalTags = await this.extractEmotionalTagsWithConfig(parentChunk, config);
    const cognitiveThemes = await this.extractCognitiveThemesWithConfig(parentChunk, config);

    return {
      chunkIndex,
      parentLength: parentChunk.length,
      childLength: childChunk.length,
      emotionalTags,
      cognitiveThemes,
      meaningScore: this.calculateMeaningScore(parentChunk, childChunk),
      lastAccessTime: new Date().toISOString(),
      accessCount: 0
    };
  }

  /**
   * 使用配置提取情感标签
   */
  private async extractEmotionalTagsWithConfig(
    text: string,
    config: IndexingConfig
  ): Promise<string[]> {
    if (!config.enableAIEnhancement) {
      return this.extractEmotionalTags(text, false);
    }

    try {
      // 使用配置的情感分析提示词
      const customPrompt = config.emotionalAnalysisPrompt.replace('{chunk}', text);
      const aiTags = await llmService.extractEmotionalTags(text, customPrompt);
      return aiTags.length > 0 ? aiTags : this.extractEmotionalTags(text, false);
    } catch (error) {
      console.error('❌ AI情感分析失败，使用降级方案:', error);
      return this.extractEmotionalTags(text, false);
    }
  }

  /**
   * 使用配置提取认知主题
   */
  private async extractCognitiveThemesWithConfig(
    text: string,
    config: IndexingConfig
  ): Promise<string[]> {
    if (!config.enableAIEnhancement) {
      return this.extractCognitiveThemes(text, false);
    }

    try {
      // 使用配置的认知分析提示词
      const customPrompt = config.cognitiveAnalysisPrompt.replace('{chunk}', text);
      const aiThemes = await llmService.extractCognitiveThemes(text, customPrompt);
      return aiThemes.length > 0 ? aiThemes : this.extractCognitiveThemes(text, false);
    } catch (error) {
      console.error('❌ AI认知分析失败，使用降级方案:', error);
      return this.extractCognitiveThemes(text, false);
    }
  }

  /**
   * 文档分块函数 - 兼容原有接口
   */
  private chunkDocument(text: string): string[] {
    const defaultConfig = ragConfigManager.getDefaultIndexingConfig();
    return this.recursiveChunking(text, defaultConfig);
  }

  /**
   * 生成意义子块 - 使用真实的LLM服务
   */
  private async generateMeaningfulChildChunk(parentChunk: string): Promise<string> {
    try {
      console.log('🧠 调用LLM服务生成意义子块...');
      const childChunk = await llmService.generateMeaningfulChildChunk(parentChunk);
      console.log(`✅ 意义子块生成成功: ${childChunk.substring(0, 50)}...`);
      return childChunk;
    } catch (error) {
      console.error('❌ LLM意义子块生成失败，使用降级方案:', error);

      // 降级方案：简单摘要
      const summary = parentChunk.length > 200
        ? parentChunk.substring(0, 200) + '...'
        : parentChunk;

      return `意义摘要: ${summary}`;
    }
  }

  /**
   * 生成块元数据 - 支持AI增强模式
   */
  private async generateChunkMetadata(
    parentChunk: string,
    childChunk: string,
    chunkIndex: number,
    useAI: boolean = false
  ): Promise<ChunkMetadata> {
    const emotionalTags = await this.extractEmotionalTags(parentChunk, useAI);
    const cognitiveThemes = await this.extractCognitiveThemes(parentChunk, useAI);

    return {
      chunkIndex,
      parentLength: parentChunk.length,
      childLength: childChunk.length,
      emotionalTags,
      cognitiveThemes,
      meaningScore: this.calculateMeaningScore(parentChunk, childChunk),
      lastAccessTime: new Date().toISOString(),
      accessCount: 0
    };
  }

  /**
   * 提取情感标签 - 支持AI增强模式
   */
  private async extractEmotionalTags(text: string, useAI: boolean = false): Promise<string[]> {
    if (useAI) {
      try {
        console.log('🎭 使用AI增强情感标签提取...');
        const aiTags = await llmService.extractEmotionalTags(text);
        if (aiTags.length > 0) {
          console.log(`✅ AI情感标签: ${aiTags.join(', ')}`);
          return aiTags;
        }
      } catch (error) {
        console.error('❌ AI情感标签提取失败，使用降级方案:', error);
      }
    }

    // 降级方案：关键词匹配
    const tags: string[] = [];
    const lowerText = text.toLowerCase();

    for (const tag of EMOTIONAL_TAGS) {
      if (lowerText.includes(tag)) {
        tags.push(tag);
      }
    }

    return tags.slice(0, 5); // 最多返回5个标签
  }

  /**
   * 提取认知主题 - 支持AI增强模式
   */
  private async extractCognitiveThemes(text: string, useAI: boolean = false): Promise<string[]> {
    if (useAI) {
      try {
        console.log('🧠 使用AI增强认知主题提取...');
        const aiThemes = await llmService.extractCognitiveThemes(text);
        if (aiThemes.length > 0) {
          console.log(`✅ AI认知主题: ${aiThemes.join(', ')}`);
          return aiThemes;
        }
      } catch (error) {
        console.error('❌ AI认知主题提取失败，使用降级方案:', error);
      }
    }

    // 降级方案：关键词匹配
    const themes: string[] = [];
    const lowerText = text.toLowerCase();

    for (const theme of COGNITIVE_THEMES) {
      if (lowerText.includes(theme)) {
        themes.push(theme);
      }
    }

    return themes.slice(0, 3); // 最多返回3个主题
  }

  /**
   * 计算意义分数
   * TODO: 可以由贤贤优化为更复杂的意义评估算法
   */
  private calculateMeaningScore(parentChunk: string, childChunk: string): number {
    // 简单的启发式算法
    let score = 0.5; // 基础分数
    
    // 长度因子
    if (parentChunk.length > 500) score += 0.1;
    if (childChunk.length > 100) score += 0.1;
    
    // 情感词汇因子
    const emotionalWords = ['感受', '情绪', '心情', '感觉', '体验'];
    for (const word of emotionalWords) {
      if (parentChunk.includes(word)) score += 0.05;
    }
    
    // 认知词汇因子
    const cognitiveWords = ['思考', '理解', '认识', '意识', '觉察'];
    for (const word of cognitiveWords) {
      if (parentChunk.includes(word)) score += 0.05;
    }
    
    return Math.min(1.0, Math.max(0.0, score));
  }

  /**
   * 获取索引进度
   */
  getIndexingProgress(documentId: string): IndexingProgress | null {
    return this.indexingProgress.get(documentId) || null;
  }

  /**
   * 获取所有进度
   */
  getAllProgress(): IndexingProgress[] {
    return Array.from(this.indexingProgress.values());
  }

  /**
   * 清理完成的进度记录
   */
  cleanupProgress(): void {
    for (const [documentId, progress] of this.indexingProgress) {
      if (progress.currentStage === 'complete') {
        this.indexingProgress.delete(documentId);
      }
    }
  }

  /**
   * 重新索引文档
   */
  async reindexDocument(
    documentId: string,
    documentText: string,
    documentType: ChunkPair['sourceDocumentType']
  ): Promise<void> {
    console.log(`🔄 重新索引文档: ${documentId}`);
    
    // 删除现有索引
    await ragStorageAdapter.deleteByDocumentId(documentId);
    
    // 重建向量索引（因为Voy不支持删除，需要重建）
    await vectorDatabase.rebuild();
    
    // 重新处理文档
    await this.processAndIndexDocument(documentText, documentId, documentType);
  }
}

// 导出单例
export const ragIndexer = new RAGIndexer();

// 导出类型和接口供其他模块使用
export type { IndexingProgress } from '@/types/rag';
