// LLM服务 - 用于生成意义子块和其他AI任务

import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateText } from "ai";

// 硬编码的配置（仅用于本地开发）
const API_KEY = "AIzaSyDHdlBWyQXDBbQVBcjr6zvNayUgkZd6N1w";
const MODEL_NAME = "gemini-2.5-flash-preview-05-20";
const PROXY_URL = "http://127.0.0.1:7897";

// 创建自定义的fetch函数，支持代理
const createCustomFetch = () => {
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { ProxyAgent, fetch: undiciFetch } = require('undici');
    const proxyAgent = new ProxyAgent(PROXY_URL);
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return async (url: string, options: any) => {
      console.log('🌐 使用代理:', PROXY_URL);
      
      return undiciFetch(url, {
        ...options,
        dispatcher: proxyAgent,
      });
    };
  } else {
    return fetch;
  }
};

// 配置Google AI客户端
const google = createGoogleGenerativeAI({
  apiKey: API_KEY,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fetch: createCustomFetch() as any,
});

class LLMService {
  /**
   * 生成意义子块 - 将原始记忆片段转换为意义摘要
   * @param parentChunk 原始记忆片段（母块）
   * @param customPrompt 自定义提示词（可选）
   * @returns 意义摘要（子块）
   */
  async generateMeaningfulChildChunk(parentChunk: string, customPrompt?: string): Promise<string> {
    console.log('🧠 开始生成意义子块...');

    try {
      const prompt = customPrompt || this.buildMeaningExtractionPrompt(parentChunk);

      const result = await generateText({
        model: google(MODEL_NAME),
        prompt: prompt,
        temperature: 0.3, // 较低的温度以确保一致性
        maxTokens: 200,   // 限制子块长度
      });

      if (!result.text) {
        throw new Error('LLM未生成有效的意义子块');
      }

      const childChunk = result.text.trim();
      console.log(`✅ 意义子块生成成功，长度: ${childChunk.length}`);

      return childChunk;
    } catch (error) {
      console.error('❌ 意义子块生成失败:', error);

      // 降级处理：如果AI调用失败，返回简化的摘要
      const fallbackSummary = this.generateFallbackSummary(parentChunk);
      console.log('⚠️ 使用降级摘要:', fallbackSummary);
      return fallbackSummary;
    }
  }

  /**
   * 构建意义提取提示词
   */
  private buildMeaningExtractionPrompt(parentChunk: string): string {
    return `你是一个专业的记忆意义提取专家。你的任务是将用户的原始记忆片段转换为简洁而深刻的意义摘要。

## 任务要求：
1. 提取记忆片段的核心意义和情感价值
2. 保留关键的情感状态和认知洞察
3. 使用简洁、准确的语言
4. 长度控制在50-100字之间
5. 突出记忆的深层含义，而非表面事实

## 原始记忆片段：
${parentChunk}

## 请生成意义摘要：
（直接输出摘要内容，不需要额外说明）`;
  }

  /**
   * 生成降级摘要（当AI调用失败时使用）
   */
  private generateFallbackSummary(parentChunk: string): string {
    // 简单的文本处理逻辑
    let summary = parentChunk.trim();
    
    // 移除多余的空白字符
    summary = summary.replace(/\s+/g, ' ');
    
    // 如果太长，截取前100个字符
    if (summary.length > 100) {
      summary = summary.substring(0, 100);
      
      // 尝试在句号处截断
      const lastPeriod = summary.lastIndexOf('。');
      if (lastPeriod > 50) {
        summary = summary.substring(0, lastPeriod + 1);
      } else {
        summary += '...';
      }
    }
    
    return `意义摘要: ${summary}`;
  }

  /**
   * 批量生成意义子块
   */
  async generateMeaningfulChildChunksBatch(parentChunks: string[]): Promise<string[]> {
    console.log(`🧠 开始批量生成 ${parentChunks.length} 个意义子块...`);
    
    const results: string[] = [];
    const batchSize = 3; // 限制并发数以避免API限制
    
    for (let i = 0; i < parentChunks.length; i += batchSize) {
      const batch = parentChunks.slice(i, i + batchSize);
      const batchPromises = batch.map(chunk => this.generateMeaningfulChildChunk(chunk));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        console.log(`📊 批量处理进度: ${Math.min(i + batchSize, parentChunks.length)}/${parentChunks.length}`);
        
        // 添加延迟以避免API限制
        if (i + batchSize < parentChunks.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`❌ 批次 ${i}-${i + batchSize} 处理失败:`, error);
        
        // 为失败的批次生成降级摘要
        const fallbackResults = batch.map(chunk => this.generateFallbackSummary(chunk));
        results.push(...fallbackResults);
      }
    }
    
    console.log(`✅ 批量意义子块生成完成，成功处理 ${results.length} 个`);
    return results;
  }

  /**
   * 提取情感标签（使用AI增强）
   */
  async extractEmotionalTags(text: string, customPrompt?: string): Promise<string[]> {
    try {
      const prompt = customPrompt || `分析以下文本的情感状态，返回3-5个最准确的情感标签。

文本：${text}

请从以下情感标签中选择最合适的（可以选择多个）：
快乐、悲伤、愤怒、恐惧、惊讶、厌恶、焦虑、困惑、兴奋、失望、希望、绝望、感激、内疚、羞耻、骄傲、嫉妒、同情、清晰、模糊、确定、怀疑、好奇、厌倦、专注、分散、深思、冲动、理性、感性

请只返回选中的标签，用逗号分隔：`;

      const result = await generateText({
        model: google(MODEL_NAME),
        prompt: prompt,
        temperature: 0.2,
        maxTokens: 50,
      });

      if (result.text) {
        const tags = result.text.trim().split(/[,，]/).map(tag => tag.trim()).filter(tag => tag.length > 0);
        return tags.slice(0, 5); // 最多返回5个标签
      }
    } catch (error) {
      console.error('❌ AI情感标签提取失败:', error);
    }

    // 降级到简单的关键词匹配
    return this.extractEmotionalTagsFallback(text);
  }

  /**
   * 降级的情感标签提取
   */
  private extractEmotionalTagsFallback(text: string): string[] {
    const emotionalKeywords = {
      '快乐': ['开心', '高兴', '愉快', '兴奋', '满足', '快乐'],
      '悲伤': ['难过', '伤心', '沮丧', '失落', '痛苦', '悲伤'],
      '愤怒': ['生气', '愤怒', '恼火', '烦躁', '不满'],
      '焦虑': ['担心', '紧张', '焦虑', '不安', '恐惧'],
      '平静': ['平静', '安静', '放松', '宁静', '舒适'],
      '困惑': ['困惑', '迷茫', '不解', '疑惑'],
      '希望': ['希望', '期待', '憧憬', '向往'],
      '感激': ['感谢', '感激', '感恩', '谢谢']
    };

    const tags: string[] = [];
    const lowerText = text.toLowerCase();
    
    for (const [emotion, keywords] of Object.entries(emotionalKeywords)) {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        tags.push(emotion);
      }
    }
    
    return tags.slice(0, 5);
  }

  /**
   * 提取认知主题（使用AI增强）
   */
  async extractCognitiveThemes(text: string, customPrompt?: string): Promise<string[]> {
    try {
      const prompt = customPrompt || `分析以下文本的认知主题，返回2-3个最相关的主题标签。

文本：${text}

请从以下主题中选择最合适的：
自我价值、身份认同、能力评估、性格特质、价值观念、信念系统、人生目标、意义追求、人际关系、亲密关系、家庭关系、社交模式、信任问题、依恋风格、边界意识、沟通方式、学习成长、技能发展、经验总结、反思洞察、行为改变、习惯养成、目标达成、挫折应对、情感表达、情感调节、压力管理、创伤愈合、情绪模式、触发因素、应对策略、支持系统

请只返回选中的主题，用逗号分隔：`;

      const result = await generateText({
        model: google(MODEL_NAME),
        prompt: prompt,
        temperature: 0.2,
        maxTokens: 50,
      });

      if (result.text) {
        const themes = result.text.trim().split(/[,，]/).map(theme => theme.trim()).filter(theme => theme.length > 0);
        return themes.slice(0, 3); // 最多返回3个主题
      }
    } catch (error) {
      console.error('❌ AI认知主题提取失败:', error);
    }

    // 降级到简单的关键词匹配
    return this.extractCognitiveThemesFallback(text);
  }

  /**
   * 降级的认知主题提取
   */
  private extractCognitiveThemesFallback(text: string): string[] {
    const themeKeywords = {
      '学习成长': ['学习', '成长', '进步', '提升', '发展'],
      '工作事业': ['工作', '职业', '事业', '项目', '任务'],
      '人际关系': ['朋友', '同事', '家人', '关系', '社交'],
      '情感表达': ['感受', '情感', '心情', '感觉', '体验'],
      '自我认知': ['自己', '我', '认识', '理解', '觉察'],
      '目标规划': ['目标', '计划', '未来', '希望', '梦想'],
      '压力应对': ['压力', '困难', '挑战', '问题', '解决']
    };

    const themes: string[] = [];
    const lowerText = text.toLowerCase();
    
    for (const [theme, keywords] of Object.entries(themeKeywords)) {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        themes.push(theme);
      }
    }
    
    return themes.slice(0, 3);
  }
}

// 导出单例
export const llmService = new LLMService();
