// RAG检索器 - 实时检索与上下文打包流水线

import {
  VectorSearchCandidate,
  WeightedCandidate,
  RetrievalContext,
  RetrievalResult,
  RetrievalError,
  RAGDebugParams,
  RAGDebugResult
} from '@/types/rag';

// 新增：意义RAG系统类型
import {
  EnhancedRetrievalConfig,
  DetailedRetrievalResult,
  RetrievalCandidate,
  WeightBreakdown,
  WeightingParams,
  FilterRules,
  ChunkMetadata,
  RetrievalError as MeaningRetrievalError
} from '@/types/meaning-rag';
import { embeddingModel } from './embedding-model';
import { vectorDatabase } from './vector-database';
import { ragStorageAdapter } from '@/lib/storage/rag-storage-adapter';
import { RAG_CONFIG, MEANING_WEIGHTS, DEBUG_CONFIG } from '@/lib/config/rag-config';

class RAGRetriever {
  private retrievalCache: Map<string, RetrievalResult> = new Map();
  private debugLogs: any[] = [];

  /**
   * 核心的意义检索函数
   * @param latestQuery - 用户的最新一句话
   * @param userProfile - 当前的用户画像.md内容
   * @param dailyInsight - 当前的每日洞察.md内容
   * @param debugParams - 可选的调试参数，用于调试模式
   * @returns 返回一个包含Top-K个最相关记忆原文（母块）的字符串数组
   */
  async retrieveMeaningfulMemories(
    latestQuery: string,
    userProfile: string,
    dailyInsight: string,
    debugParams?: RAGDebugParams
  ): Promise<string[]> {
    const startTime = Date.now();
    console.log('🔍 开始意义检索...');

    try {
      // 构建检索上下文
      const context: RetrievalContext = {
        latestQuery,
        userProfile,
        dailyInsight
      };

      // 检查缓存
      const cacheKey = this.generateCacheKey(context);
      if (this.retrievalCache.has(cacheKey)) {
        console.log('📦 使用缓存结果');
        return this.retrievalCache.get(cacheKey)!.memories;
      }

      // 确保模型和数据库已初始化
      await this.ensureInitialized();

      // 步骤1：Embedding模型进行快速海选
      console.log('🔢 生成查询向量...');
      const queryVector = await embeddingModel.encode(latestQuery);
      
      console.log('🔍 执行向量搜索...');
      const candidates = await this.vectorSearch(queryVector, RAG_CONFIG.retrieval.maxCandidates);
      
      if (candidates.length === 0) {
        console.log('⚠️ 未找到任何候选记忆');
        return [];
      }

      console.log(`📊 找到 ${candidates.length} 个候选记忆`);

      // 根据调试参数选择不同的检索策略
      let rankedMemories: WeightedCandidate[];
      let finalTopK = debugParams?.finalCount || RAG_CONFIG.retrieval.topK;
      let maxCandidates = debugParams?.topK || RAG_CONFIG.retrieval.maxCandidates;

      if (debugParams?.mode === 'Vector Only') {
        console.log('🔍 使用纯向量搜索模式...');
        rankedMemories = this.applyVectorOnlyRanking(candidates);
      } else if (debugParams?.mode === 'Keyword Only') {
        console.log('🏷️ 使用纯关键词搜索模式...');
        rankedMemories = this.applyKeywordOnlyRanking(candidates, latestQuery, userProfile, dailyInsight);
      } else {
        console.log('⚖️ 使用混合权重模式...');
        rankedMemories = this.applyDynamicWeighting(candidates, userProfile, dailyInsight, debugParams);
      }

      // 步骤3：过滤掉权重最低的部分
      const filterThreshold = Math.floor(rankedMemories.length * (1 - RAG_CONFIG.retrieval.filterRatio));
      const filteredMemories = rankedMemories.slice(0, filterThreshold);
      console.log(`🔽 过滤后剩余 ${filteredMemories.length} 个候选记忆`);

      // 步骤4：返回最终的Top-N个记忆片段的原文（母块）
      const topMemories = await this.getParentChunksFor(
        filteredMemories.slice(0, finalTopK)
      );

      // 构建结果
      const result: RetrievalResult = {
        memories: topMemories,
        metadata: {
          totalCandidates: candidates.length,
          filteredCandidates: filteredMemories.length,
          finalResults: topMemories.length,
          processingTime: Date.now() - startTime,
          averageRelevanceScore: this.calculateAverageScore(filteredMemories.slice(0, RAG_CONFIG.retrieval.topK))
        }
      };

      // 添加调试信息
      if (DEBUG_CONFIG.enabled) {
        result.debugInfo = {
          topCandidates: filteredMemories.slice(0, 10),
          weightingBreakdown: this.getWeightingBreakdown(filteredMemories.slice(0, 5))
        };
        this.addDebugLog(context, result);
      }

      // 缓存结果
      this.addToCache(cacheKey, result);

      console.log(`✅ 意义检索完成，返回 ${topMemories.length} 个记忆片段`);
      return topMemories;

    } catch (error) {
      console.error('💥 意义检索失败:', error);
      throw new RetrievalError('意义检索失败', error);
    }
  }

  /**
   * 向量搜索
   */
  private async vectorSearch(
    queryVector: number[],
    topK: number,
    semanticAlgorithm: string = 'cosine',
    similarityThreshold?: number
  ): Promise<VectorSearchCandidate[]> {
    try {
      const candidates = await vectorDatabase.search(queryVector, topK);

      // 重新计算相似度（如果使用不同算法）
      const recalculatedCandidates = candidates.map(candidate => ({
        ...candidate,
        similarity: this.calculateSimilarity(queryVector, candidate.chunkPair.vector, semanticAlgorithm)
      }));

      // 使用动态阈值过滤低相似度结果
      const threshold = similarityThreshold ?? RAG_CONFIG.retrieval.similarityThreshold;
      return recalculatedCandidates.filter(
        candidate => candidate.similarity >= threshold
      );
    } catch (error) {
      throw new RetrievalError('向量搜索失败', error);
    }
  }

  /**
   * 专门用于调试的检索函数，返回详细的调试信息
   */
  async retrieveWithDebugInfo(
    latestQuery: string,
    userProfile: string,
    dailyInsight: string,
    debugParams: RAGDebugParams
  ): Promise<RAGDebugResult> {
    const startTime = Date.now();
    console.log('🔍 开始调试检索...');

    try {
      // 构建检索上下文
      const context: RetrievalContext = {
        latestQuery,
        userProfile,
        dailyInsight
      };

      // 确保模型和数据库已初始化
      await this.ensureInitialized();

      // 步骤1：Embedding模型进行快速海选
      console.log('🔢 生成查询向量...');
      const queryVector = await embeddingModel.encode(latestQuery);

      console.log('🔍 执行向量搜索...');
      const candidates = await this.vectorSearch(
        queryVector,
        debugParams.topK,
        debugParams.semanticAlgorithm || 'cosine',
        debugParams.similarityThreshold
      );

      if (candidates.length === 0) {
        console.log('⚠️ 未找到任何候选记忆');
        return this.createEmptyDebugResult(debugParams, startTime);
      }

      console.log(`📊 找到 ${candidates.length} 个候选记忆`);

      // 根据调试参数选择不同的检索策略
      let rankedMemories: WeightedCandidate[];
      let modeSpecificInfo: any = {};

      if (debugParams.mode === 'Vector Only') {
        console.log('🔍 使用纯向量搜索模式...');
        rankedMemories = this.applyVectorOnlyRanking(candidates);
        modeSpecificInfo.vectorOnlyResults = candidates;
      } else if (debugParams.mode === 'Keyword Only') {
        console.log('🏷️ 使用纯关键词搜索模式...');
        rankedMemories = this.applyKeywordOnlyRanking(candidates, latestQuery, userProfile, dailyInsight);
        modeSpecificInfo.keywordOnlyResults = rankedMemories;
      } else {
        console.log('⚖️ 使用混合权重模式...');
        rankedMemories = this.applyDynamicWeighting(candidates, userProfile, dailyInsight, debugParams);
        modeSpecificInfo.hybridWeightBreakdown = this.getHybridWeightBreakdown(rankedMemories.slice(0, 5), debugParams);
      }

      // 获取最终记忆片段
      const topMemories = await this.getParentChunksFor(
        rankedMemories.slice(0, debugParams.finalCount)
      );

      // 构建调试结果
      const debugResult: RAGDebugResult = {
        memories: topMemories,
        metadata: {
          totalCandidates: candidates.length,
          filteredCandidates: rankedMemories.length,
          finalResults: topMemories.length,
          processingTime: Date.now() - startTime,
          averageRelevanceScore: this.calculateAverageScore(rankedMemories.slice(0, debugParams.finalCount))
        },
        debugParams,
        modeSpecificInfo,
        debugInfo: {
          topCandidates: rankedMemories.slice(0, 10),
          weightingBreakdown: this.getWeightingBreakdown(rankedMemories.slice(0, 5))
        }
      };

      console.log(`✅ 调试检索完成，返回 ${topMemories.length} 个记忆片段`);
      return debugResult;

    } catch (error) {
      console.error('💥 调试检索失败:', error);
      throw new RetrievalError('调试检索失败', error);
    }
  }

  /**
   * 应用动态意义权重排序
   * TODO: 贤贤将在此处实现他的加权排序算法
   */
  private applyDynamicWeighting(
    candidates: VectorSearchCandidate[],
    profile: string,
    insight: string,
    debugParams?: RAGDebugParams
  ): WeightedCandidate[] {
    // 使用调试参数中的权重，如果没有则使用默认权重
    const w1 = debugParams?.w1 || MEANING_WEIGHTS.profileRelevance;
    const w2 = debugParams?.w2 || MEANING_WEIGHTS.insightRelevance;
    const w3 = debugParams?.w3 || MEANING_WEIGHTS.temporalRelevance;

    console.log(`🔧 使用权重配置: w1=${w1}, w2=${w2}, w3=${w3}`);

    // 实现基于权重的混合排序算法
    const weighted: WeightedCandidate[] = candidates.map((candidate, index) => {
      // 计算各个维度的分数
      const semanticScore = candidate.similarity; // 语义相似度
      const tagScore = this.calculateTagRelevance(candidate, profile, insight); // 标签相关性
      const memoryImportanceScore = this.calculateMemoryImportance(candidate); // 记忆重要性

      // 计算最终的动态权重
      const dynamicWeight = w1 * semanticScore + w2 * tagScore + w3 * memoryImportanceScore;

      return {
        ...candidate,
        dynamicWeight,
        weightingFactors: {
          profileRelevance: w1 * semanticScore,
          insightRelevance: w2 * tagScore,
          temporalRelevance: w3 * memoryImportanceScore,
          emotionalRelevance: 0.1,
          accessFrequency: 0.1
        },
        finalRank: index + 1
      };
    });

    // 按动态权重排序
    return weighted.sort((a, b) => b.dynamicWeight - a.dynamicWeight);
  }

  /**
   * 纯向量搜索排序
   */
  private applyVectorOnlyRanking(candidates: VectorSearchCandidate[]): WeightedCandidate[] {
    return candidates.map((candidate, index) => ({
      ...candidate,
      dynamicWeight: candidate.similarity,
      weightingFactors: {
        profileRelevance: candidate.similarity,
        insightRelevance: 0,
        temporalRelevance: 0,
        emotionalRelevance: 0,
        accessFrequency: 0
      },
      finalRank: index + 1
    })).sort((a, b) => b.dynamicWeight - a.dynamicWeight);
  }

  /**
   * 纯关键词搜索排序
   */
  private applyKeywordOnlyRanking(
    candidates: VectorSearchCandidate[],
    query: string,
    profile: string,
    insight: string
  ): WeightedCandidate[] {
    return candidates.map((candidate, index) => {
      const keywordScore = this.calculateKeywordRelevance(candidate, query, profile, insight);
      return {
        ...candidate,
        dynamicWeight: keywordScore,
        weightingFactors: {
          profileRelevance: 0,
          insightRelevance: keywordScore,
          temporalRelevance: 0,
          emotionalRelevance: 0,
          accessFrequency: 0
        },
        finalRank: index + 1
      };
    }).sort((a, b) => b.dynamicWeight - a.dynamicWeight);
  }

  /**
   * 计算标签相关性
   */
  private calculateTagRelevance(candidate: VectorSearchCandidate, profile: string, insight: string): number {
    // 简单实现：基于情感标签和认知主题的匹配度
    const tags = candidate.chunkPair.metadata.emotionalTags || [];
    const themes = candidate.chunkPair.metadata.cognitiveThemes || [];

    // 计算与profile和insight的文本匹配度
    let relevance = 0;
    const combinedText = (profile + ' ' + insight).toLowerCase();

    tags.forEach(tag => {
      if (combinedText.includes(tag.toLowerCase())) {
        relevance += 0.1;
      }
    });

    themes.forEach(theme => {
      if (combinedText.includes(theme.toLowerCase())) {
        relevance += 0.15;
      }
    });

    return Math.min(relevance, 1.0);
  }

  /**
   * 计算记忆重要性
   */
  private calculateMemoryImportance(candidate: VectorSearchCandidate): number {
    const metadata = candidate.chunkPair.metadata;
    const accessCount = metadata.accessCount || 0;
    const meaningScore = metadata.meaningScore || 0.5;

    // 基于访问频率和意义分数计算重要性
    const accessWeight = Math.min(accessCount / 10, 1.0); // 访问次数归一化
    const importance = 0.7 * meaningScore + 0.3 * accessWeight;

    return importance;
  }

  /**
   * 计算关键词相关性
   */
  private calculateKeywordRelevance(
    candidate: VectorSearchCandidate,
    query: string,
    profile: string,
    insight: string
  ): number {
    const content = candidate.chunkPair.parentChunk.toLowerCase();
    const queryWords = query.toLowerCase().split(/\s+/);
    const profileWords = profile.toLowerCase().split(/\s+/).slice(0, 20); // 取前20个词
    const insightWords = insight.toLowerCase().split(/\s+/).slice(0, 20); // 取前20个词

    let score = 0;

    // 查询词匹配
    queryWords.forEach(word => {
      if (word.length > 2 && content.includes(word)) {
        score += 0.3;
      }
    });

    // 画像词匹配
    profileWords.forEach(word => {
      if (word.length > 2 && content.includes(word)) {
        score += 0.1;
      }
    });

    // 洞察词匹配
    insightWords.forEach(word => {
      if (word.length > 2 && content.includes(word)) {
        score += 0.15;
      }
    });

    return Math.min(score, 1.0);
  }

  /**
   * 获取混合权重分解信息
   */
  private getHybridWeightBreakdown(candidates: WeightedCandidate[], debugParams: RAGDebugParams): any[] {
    return candidates.map(c => ({
      chunkId: c.chunkPair.id,
      semanticScore: c.similarity,
      tagScore: this.calculateTagRelevance(c, '', ''),
      memoryImportanceScore: this.calculateMemoryImportance(c),
      finalScore: c.dynamicWeight
    }));
  }

  // ==================== 增强检索系统 ====================

  /**
   * 扩展的意义检索函数 - 支持完整的调试和配置
   * @param latestQuery 用户查询
   * @param userProfile 用户画像
   * @param dailyInsight 每日洞察
   * @param retrievalConfig 增强检索配置
   */
  async retrieveMeaningfulMemoriesWithFullDebug(
    latestQuery: string,
    userProfile: string,
    dailyInsight: string,
    retrievalConfig: EnhancedRetrievalConfig
  ): Promise<DetailedRetrievalResult> {
    const startTime = Date.now();
    console.log('🔍 开始增强意义检索...', {
      query: latestQuery,
      config: retrievalConfig
    });

    try {
      // 尝试确保模型和数据库已初始化，如果失败则使用模拟数据
      try {
        await this.ensureInitialized();
      } catch (initError) {
        console.log('⚠️ 初始化失败，使用模拟数据进行测试:', initError instanceof Error ? initError.message : String(initError));
        return this.getMockRetrievalResult(latestQuery, retrievalConfig);
      }

      const performanceMetrics = {
        vectorSearchTime: 0,
        weightingTime: 0,
        filteringTime: 0,
        totalTime: 0
      };

      // 步骤1：多模式向量搜索
      const vectorSearchStart = Date.now();
      const candidates = await this.hybridVectorSearch(latestQuery, retrievalConfig);
      performanceMetrics.vectorSearchTime = Date.now() - vectorSearchStart;

      console.log(`📊 向量搜索完成，找到 ${candidates.length} 个候选项`);

      // 步骤2：应用动态意义权重算法
      const weightingStart = Date.now();
      const weightedResults = await this.applyDynamicMeaningWeights(
        candidates,
        userProfile,
        dailyInsight,
        retrievalConfig.weightingParams
      );
      performanceMetrics.weightingTime = Date.now() - weightingStart;

      console.log(`⚖️ 权重计算完成，处理 ${weightedResults.length} 个结果`);

      // 步骤3：智能过滤和排序
      const filteringStart = Date.now();
      const filteredResults = await this.intelligentFilter(weightedResults, retrievalConfig.filterRules);
      performanceMetrics.filteringTime = Date.now() - filteringStart;

      console.log(`🔽 过滤完成，剩余 ${filteredResults.candidates.length} 个结果`);

      // 步骤4：获取最终记忆片段
      const finalMemories = filteredResults.candidates
        .slice(0, retrievalConfig.finalCount)
        .map(candidate => candidate.chunk);

      // 步骤5：生成详细调试信息
      const weightBreakdown = this.generateWeightBreakdown(
        weightedResults.slice(0, 10), // 只分析前10个结果
        retrievalConfig.weightingParams
      );

      performanceMetrics.totalTime = Date.now() - startTime;

      const result: DetailedRetrievalResult = {
        query: latestQuery,
        finalMemories,
        debugInfo: {
          totalCandidates: candidates.length,
          candidatesAfterFiltering: filteredResults.candidates.length,
          processingTime: performanceMetrics.totalTime,
          candidateBreakdown: weightedResults.slice(0, 20), // 返回前20个候选项的详细信息
          weightBreakdown,
          filteringStats: filteredResults.filteringStats,
          configSnapshot: retrievalConfig
        },
        performance: performanceMetrics
      };

      console.log('✅ 增强意义检索完成', {
        finalCount: finalMemories.length,
        totalTime: performanceMetrics.totalTime
      });

      return result;

    } catch (error) {
      console.error('💥 增强意义检索失败:', error);
      throw new MeaningRetrievalError('增强意义检索失败', error);
    }
  }

  /**
   * 创建空的调试结果
   */
  private createEmptyDebugResult(debugParams: RAGDebugParams, startTime: number): RAGDebugResult {
    return {
      memories: [],
      metadata: {
        totalCandidates: 0,
        filteredCandidates: 0,
        finalResults: 0,
        processingTime: Date.now() - startTime,
        averageRelevanceScore: 0
      },
      debugParams,
      modeSpecificInfo: {},
      debugInfo: {
        topCandidates: [],
        weightingBreakdown: []
      }
    };
  }

  /**
   * 多模式向量搜索
   * TODO: 实现混合搜索策略
   */
  private async hybridVectorSearch(
    query: string,
    config: EnhancedRetrievalConfig
  ): Promise<ChunkMetadata[]> {
    console.log('🔍 执行多模式向量搜索...', config.mode);

    // TODO: 实现以下搜索模式：
    // 1. vector: 纯向量搜索
    // 2. keyword: 关键词搜索
    // 3. hybrid: 混合搜索
    // 4. semantic: 语义搜索
    // 5. meaning: 意义搜索

    try {
      // 尝试使用现有的向量搜索
      const queryVector = await embeddingModel.encode(query);
      const vectorCandidates = await this.vectorSearch(
        queryVector,
        config.vectorSearch.topK,
        'cosine', // 默认使用余弦相似度
        0.3       // 默认阈值
      );

      // 转换为ChunkMetadata格式
      const candidates: ChunkMetadata[] = vectorCandidates.map((candidate, index) => ({
        id: candidate.chunkPair.id,
        documentId: `doc_${candidate.chunkPair.id.split('_')[0]}`,
        chunkIndex: index,
        content: candidate.chunkPair.parentChunk,
        childChunk: candidate.chunkPair.childChunk,
        vector: [], // 向量数据
        emotionalTags: candidate.chunkPair.metadata.emotionalTags || [],
        cognitiveThemes: candidate.chunkPair.metadata.cognitiveThemes || [],
        keywords: [],
        importanceScore: candidate.chunkPair.metadata.meaningScore || 0.5,
        createdAt: new Date(),
        processingTime: 0,
        tokenCount: candidate.chunkPair.parentChunk.length
      }));

      return candidates;
    } catch (error) {
      console.log('⚠️ 向量搜索失败，返回模拟数据:', error instanceof Error ? error.message : String(error));

      // 返回模拟的候选项用于测试
      const mockCandidates: ChunkMetadata[] = [
        {
          id: 'mock_chunk_1',
          documentId: 'mock_doc_1',
          chunkIndex: 0,
          content: '这是一个模拟的记忆片段，包含了关于学习和成长的内容。用户在这里记录了自己的学习心得和感悟。',
          childChunk: '学习心得和成长感悟的记录',
          vector: new Array(384).fill(0).map(() => Math.random() - 0.5),
          emotionalTags: ['开心', '满足'],
          cognitiveThemes: ['学习', '成长'],
          keywords: ['学习', '心得', '感悟'],
          importanceScore: 0.8,
          createdAt: new Date(),
          processingTime: 50,
          tokenCount: 45
        },
        {
          id: 'mock_chunk_2',
          documentId: 'mock_doc_1',
          chunkIndex: 1,
          content: '今天完成了一个重要的项目，感到很有成就感。这个项目让我学到了很多新的技术和方法。',
          childChunk: '项目完成带来的成就感和学习收获',
          vector: new Array(384).fill(0).map(() => Math.random() - 0.5),
          emotionalTags: ['成就感', '兴奋'],
          cognitiveThemes: ['工作', '技术'],
          keywords: ['项目', '成就感', '技术'],
          importanceScore: 0.9,
          createdAt: new Date(),
          processingTime: 45,
          tokenCount: 38
        },
        {
          id: 'mock_chunk_3',
          documentId: 'mock_doc_1',
          chunkIndex: 2,
          content: '对人工智能和深度学习的研究让我感到非常兴奋，这是一个充满可能性的领域。',
          childChunk: 'AI和深度学习研究的兴奋感',
          vector: new Array(384).fill(0).map(() => Math.random() - 0.5),
          emotionalTags: ['兴奋', '好奇'],
          cognitiveThemes: ['研究', 'AI'],
          keywords: ['人工智能', '深度学习', '研究'],
          importanceScore: 0.7,
          createdAt: new Date(),
          processingTime: 40,
          tokenCount: 32
        }
      ];

      return mockCandidates.slice(0, Math.min(config.vectorSearch.topK, mockCandidates.length));
    }
  }

  /**
   * 应用动态意义权重算法
   * TODO: 实现多维度权重计算
   */
  private async applyDynamicMeaningWeights(
    candidates: ChunkMetadata[],
    userProfile: string,
    dailyInsight: string,
    weightingParams: WeightingParams
  ): Promise<RetrievalCandidate[]> {
    console.log('⚖️ 应用动态意义权重...', weightingParams);

    // TODO: 实现以下权重计算：
    // 1. semanticRelevance: 语义相关性分析
    // 2. emotionalMatch: 情感匹配度计算
    // 3. themeRelevance: 主题相关性分析
    // 4. importanceScore: 重要性分数权重
    // 5. temporalDecay: 时间衰减计算
    // 6. profileMatch: 用户画像匹配度

    const weightedCandidates: RetrievalCandidate[] = [];

    for (const chunk of candidates) {
      // 计算各维度分数
      const scores = {
        vectorSimilarity: 0.8, // 临时值
        keywordMatch: this.calculateKeywordMatch(chunk, userProfile, dailyInsight),
        semanticRelevance: this.calculateSemanticRelevance(chunk, userProfile),
        emotionalMatch: this.calculateEmotionalMatch(chunk, dailyInsight),
        themeRelevance: this.calculateThemeRelevance(chunk, userProfile),
        importanceScore: chunk.importanceScore,
        temporalScore: this.calculateTemporalScore(chunk),
        profileMatch: this.calculateProfileMatch(chunk, userProfile)
      };

      // 应用权重计算最终分数
      const finalWeight =
        scores.semanticRelevance * weightingParams.semanticRelevance +
        scores.emotionalMatch * weightingParams.emotionalMatch +
        scores.themeRelevance * weightingParams.themeRelevance +
        scores.importanceScore * weightingParams.importanceScore +
        scores.temporalScore * weightingParams.temporalDecay +
        scores.profileMatch * weightingParams.profileMatch;

      weightedCandidates.push({
        chunk,
        scores,
        finalWeight,
        rank: 0 // 将在排序后设置
      });
    }

    // 按最终权重排序并设置排名
    weightedCandidates.sort((a, b) => b.finalWeight - a.finalWeight);
    weightedCandidates.forEach((candidate, index) => {
      candidate.rank = index + 1;
    });

    return weightedCandidates;
  }

  /**
   * 智能过滤规则
   * TODO: 实现多种过滤策略
   */
  private async intelligentFilter(
    candidates: RetrievalCandidate[],
    filterRules: FilterRules
  ): Promise<{
    candidates: RetrievalCandidate[];
    filteringStats: {
      removedByThreshold: number;
      removedByDeduplication: number;
      removedByTemporal: number;
      removedByImportance: number;
    };
  }> {
    console.log('🔽 应用智能过滤规则...', filterRules);

    let filteredCandidates = [...candidates];
    const stats = {
      removedByThreshold: 0,
      removedByDeduplication: 0,
      removedByTemporal: 0,
      removedByImportance: 0
    };

    // 1. 权重阈值过滤
    const beforeThreshold = filteredCandidates.length;
    filteredCandidates = filteredCandidates.filter(
      candidate => candidate.finalWeight >= filterRules.minWeightThreshold
    );
    stats.removedByThreshold = beforeThreshold - filteredCandidates.length;

    // 2. 重要性过滤
    if (filterRules.importanceFilter.enabled) {
      const beforeImportance = filteredCandidates.length;
      filteredCandidates = filteredCandidates.filter(
        candidate => candidate.chunk.importanceScore >= filterRules.importanceFilter.minImportance
      );
      stats.removedByImportance = beforeImportance - filteredCandidates.length;
    }

    // 3. 时间过滤
    if (filterRules.temporalFilter.enabled) {
      const beforeTemporal = filteredCandidates.length;
      const maxAge = filterRules.temporalFilter.maxAge * 24 * 60 * 60 * 1000; // 转换为毫秒
      const cutoffDate = new Date(Date.now() - maxAge);

      filteredCandidates = filteredCandidates.filter(
        candidate => candidate.chunk.createdAt >= cutoffDate
      );
      stats.removedByTemporal = beforeTemporal - filteredCandidates.length;
    }

    // 4. 去重过滤
    if (filterRules.deduplication.enabled) {
      const beforeDedup = filteredCandidates.length;
      filteredCandidates = this.removeDuplicates(
        filteredCandidates,
        filterRules.deduplication.similarityThreshold
      );
      stats.removedByDeduplication = beforeDedup - filteredCandidates.length;
    }

    // 5. 数量限制
    if (filteredCandidates.length > filterRules.maxResults) {
      filteredCandidates = filteredCandidates.slice(0, filterRules.maxResults);
    }

    return { candidates: filteredCandidates, filteringStats: stats };
  }

  /**
   * 获取母块内容
   */
  private async getParentChunksFor(candidates: WeightedCandidate[]): Promise<string[]> {
    const parentChunks: string[] = [];
    
    for (const candidate of candidates) {
      try {
        const chunkPair = await ragStorageAdapter.getChunkPair(candidate.chunkPair.id);
        if (chunkPair) {
          parentChunks.push(chunkPair.parentChunk);
          
          // 更新访问统计
          await this.updateAccessStats(chunkPair.id);
        }
      } catch (error) {
        console.error(`⚠️ 获取块对 ${candidate.chunkPair.id} 失败:`, error);
      }
    }
    
    return parentChunks;
  }

  /**
   * 更新访问统计
   */
  private async updateAccessStats(chunkId: string): Promise<void> {
    try {
      const chunkPair = await ragStorageAdapter.getChunkPair(chunkId);
      if (chunkPair) {
        chunkPair.metadata.lastAccessTime = new Date().toISOString();
        chunkPair.metadata.accessCount = (chunkPair.metadata.accessCount || 0) + 1;
        chunkPair.updatedAt = new Date().toISOString();

        await ragStorageAdapter.saveChunkPair(chunkPair);
      }
    } catch (error) {
      console.error('⚠️ 更新访问统计失败:', error);
    }
  }

  /**
   * 确保系统已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!embeddingModel.loaded) {
      await embeddingModel.load();
    }
    
    if (!vectorDatabase.initialized) {
      await vectorDatabase.initialize();
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(context: RetrievalContext): string {
    const hash = this.simpleHash(
      context.latestQuery + 
      context.userProfile.substring(0, 100) + 
      context.dailyInsight.substring(0, 100)
    );
    return `retrieval_${hash}`;
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 添加到缓存
   */
  private addToCache(key: string, result: RetrievalResult): void {
    if (this.retrievalCache.size >= 100) { // 限制缓存大小
      const firstKey = this.retrievalCache.keys().next().value;
      if (firstKey !== undefined) {
        this.retrievalCache.delete(firstKey);
      }
    }
    this.retrievalCache.set(key, result);
  }

  /**
   * 计算平均分数
   */
  private calculateAverageScore(candidates: WeightedCandidate[]): number {
    if (candidates.length === 0) return 0;
    const sum = candidates.reduce((acc, c) => acc + c.dynamicWeight, 0);
    return sum / candidates.length;
  }

  /**
   * 获取权重分解信息
   */
  private getWeightingBreakdown(candidates: WeightedCandidate[]): any {
    return candidates.map(c => ({
      chunkId: c.chunkPair.id,
      similarity: c.similarity,
      dynamicWeight: c.dynamicWeight,
      factors: c.weightingFactors
    }));
  }

  /**
   * 添加调试日志
   */
  private addDebugLog(context: RetrievalContext, result: RetrievalResult): void {
    if (this.debugLogs.length >= 100) {
      this.debugLogs.shift();
    }
    
    this.debugLogs.push({
      timestamp: new Date().toISOString(),
      query: context.latestQuery,
      resultCount: result.memories.length,
      processingTime: result.metadata.processingTime,
      averageScore: result.metadata.averageRelevanceScore
    });
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      cacheSize: this.retrievalCache.size,
      debugLogs: this.debugLogs.slice(-10), // 返回最近10条日志
      systemStats: {
        embeddingModel: embeddingModel.getModelInfo(),
        vectorDatabase: vectorDatabase.getStats()
      }
    };
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.retrievalCache.clear();
    console.log('🗑️ 检索缓存已清空');
  }

  // ==================== 增强检索辅助方法 ====================

  /**
   * 生成权重分解详情
   */
  private generateWeightBreakdown(
    candidates: RetrievalCandidate[],
    weightingParams: WeightingParams
  ): WeightBreakdown[] {
    return candidates.map(candidate => ({
      chunkId: candidate.chunk.id,
      rawScores: candidate.scores,
      weightedScores: {
        semanticRelevance: candidate.scores.semanticRelevance * weightingParams.semanticRelevance,
        emotionalMatch: candidate.scores.emotionalMatch * weightingParams.emotionalMatch,
        themeRelevance: candidate.scores.themeRelevance * weightingParams.themeRelevance,
        importanceScore: candidate.scores.importanceScore * weightingParams.importanceScore,
        temporalDecay: candidate.scores.temporalScore * weightingParams.temporalDecay,
        profileMatch: candidate.scores.profileMatch * weightingParams.profileMatch
      },
      finalWeight: candidate.finalWeight,
      explanation: this.generateWeightExplanation(candidate, weightingParams)
    }));
  }

  /**
   * 生成权重计算说明
   */
  private generateWeightExplanation(
    candidate: RetrievalCandidate,
    params: WeightingParams
  ): string[] {
    const explanations: string[] = [];
    const scores = candidate.scores;

    if (scores.semanticRelevance > 0.7) {
      explanations.push(`语义相关性较高 (${scores.semanticRelevance.toFixed(2)})`);
    }
    if (scores.emotionalMatch > 0.6) {
      explanations.push(`情感匹配度良好 (${scores.emotionalMatch.toFixed(2)})`);
    }
    if (scores.importanceScore > 0.8) {
      explanations.push(`内容重要性很高 (${scores.importanceScore.toFixed(2)})`);
    }
    if (scores.profileMatch > 0.7) {
      explanations.push(`与用户画像匹配度高 (${scores.profileMatch.toFixed(2)})`);
    }

    return explanations;
  }

  /**
   * 计算关键词匹配度
   * TODO: 实现更精确的关键词匹配算法
   */
  private calculateKeywordMatch(
    chunk: ChunkMetadata,
    userProfile: string,
    dailyInsight: string
  ): number {
    // TODO: 实现基于TF-IDF或BM25的关键词匹配
    const content = chunk.content.toLowerCase();
    const profile = userProfile.toLowerCase();
    const insight = dailyInsight.toLowerCase();

    let matchScore = 0;
    const profileWords = profile.split(/\s+/).filter(word => word.length > 2);
    const insightWords = insight.split(/\s+/).filter(word => word.length > 2);

    profileWords.forEach(word => {
      if (content.includes(word)) matchScore += 0.1;
    });

    insightWords.forEach(word => {
      if (content.includes(word)) matchScore += 0.15;
    });

    return Math.min(1, matchScore);
  }

  /**
   * 计算语义相关性
   * TODO: 实现基于向量相似度的语义分析
   */
  private calculateSemanticRelevance(chunk: ChunkMetadata, userProfile: string): number {
    // TODO: 使用向量相似度计算语义相关性
    // 临时实现：基于内容长度和关键词密度
    const contentLength = chunk.content.length;
    const keywordDensity = chunk.keywords.length / Math.max(1, contentLength / 100);

    return Math.min(1, keywordDensity * 0.3 + (contentLength / 1000) * 0.7);
  }

  /**
   * 计算情感匹配度
   * TODO: 实现情感分析匹配
   */
  private calculateEmotionalMatch(chunk: ChunkMetadata, dailyInsight: string): number {
    // TODO: 分析用户当前情感状态，与块的情感标签进行匹配
    const chunkEmotions = chunk.emotionalTags;
    if (chunkEmotions.length === 0) return 0.5;

    // 简单实现：检查洞察中的情感词
    const insightEmotions = this.extractEmotionsFromText(dailyInsight);
    const matchCount = chunkEmotions.filter(emotion =>
      insightEmotions.includes(emotion)
    ).length;

    return matchCount / Math.max(1, chunkEmotions.length);
  }

  /**
   * 计算主题相关性
   * TODO: 实现主题模型匹配
   */
  private calculateThemeRelevance(chunk: ChunkMetadata, userProfile: string): number {
    // TODO: 使用主题模型分析相关性
    const chunkThemes = chunk.cognitiveThemes;
    if (chunkThemes.length === 0) return 0.5;

    // 简单实现：检查画像中的主题词
    const profileThemes = this.extractThemesFromText(userProfile);
    const matchCount = chunkThemes.filter(theme =>
      profileThemes.includes(theme)
    ).length;

    return matchCount / Math.max(1, chunkThemes.length);
  }

  /**
   * 计算时间衰减分数
   */
  private calculateTemporalScore(chunk: ChunkMetadata): number {
    const now = Date.now();
    const chunkTime = chunk.createdAt.getTime();
    const daysSinceCreation = (now - chunkTime) / (1000 * 60 * 60 * 24);

    // 指数衰减：7天半衰期
    return Math.exp(-daysSinceCreation / 7);
  }

  /**
   * 计算用户画像匹配度
   * TODO: 实现更复杂的画像匹配算法
   */
  private calculateProfileMatch(chunk: ChunkMetadata, userProfile: string): number {
    // TODO: 实现基于用户兴趣、行为模式的匹配
    const content = chunk.content.toLowerCase();
    const profile = userProfile.toLowerCase();

    // 简单实现：计算文本重叠度
    const profileWords = new Set(profile.split(/\s+/).filter(word => word.length > 2));
    const contentWords = content.split(/\s+/).filter(word => word.length > 2);

    const matchCount = contentWords.filter(word => profileWords.has(word)).length;
    return matchCount / Math.max(1, contentWords.length);
  }

  /**
   * 去重处理
   * TODO: 实现基于语义相似度的智能去重
   */
  private removeDuplicates(
    candidates: RetrievalCandidate[],
    similarityThreshold: number
  ): RetrievalCandidate[] {
    // TODO: 使用向量相似度进行智能去重
    // 临时实现：基于内容长度和关键词的简单去重
    const uniqueCandidates: RetrievalCandidate[] = [];

    for (const candidate of candidates) {
      const isDuplicate = uniqueCandidates.some(existing => {
        const contentSimilarity = this.calculateContentSimilarity(
          candidate.chunk.content,
          existing.chunk.content
        );
        return contentSimilarity > similarityThreshold;
      });

      if (!isDuplicate) {
        uniqueCandidates.push(candidate);
      }
    }

    return uniqueCandidates;
  }

  /**
   * 计算内容相似度
   */
  private calculateContentSimilarity(content1: string, content2: string): number {
    // 简单的Jaccard相似度计算
    const words1 = new Set(content1.toLowerCase().split(/\s+/));
    const words2 = new Set(content2.toLowerCase().split(/\s+/));

    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  /**
   * 计算向量相似度 - 支持多种算法
   */
  private calculateSimilarity(vector1: number[], vector2: number[], algorithm: string = 'cosine'): number {
    if (vector1.length !== vector2.length) {
      console.warn('向量维度不匹配，使用默认相似度');
      return 0;
    }

    switch (algorithm) {
      case 'cosine':
        return this.cosineSimilarity(vector1, vector2);
      case 'euclidean':
        return this.euclideanSimilarity(vector1, vector2);
      case 'dot_product':
        return this.dotProductSimilarity(vector1, vector2);
      default:
        console.warn(`未知的相似度算法: ${algorithm}，使用余弦相似度`);
        return this.cosineSimilarity(vector1, vector2);
    }
  }

  /**
   * 余弦相似度计算
   */
  private cosineSimilarity(vector1: number[], vector2: number[]): number {
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      norm1 += vector1[i] * vector1[i];
      norm2 += vector2[i] * vector2[i];
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  /**
   * 欧氏距离相似度计算（转换为0-1范围）
   */
  private euclideanSimilarity(vector1: number[], vector2: number[]): number {
    let sumSquaredDiff = 0;
    for (let i = 0; i < vector1.length; i++) {
      const diff = vector1[i] - vector2[i];
      sumSquaredDiff += diff * diff;
    }
    const distance = Math.sqrt(sumSquaredDiff);
    // 转换为相似度（距离越小，相似度越高）
    return 1 / (1 + distance);
  }

  /**
   * 点积相似度计算（归一化）
   */
  private dotProductSimilarity(vector1: number[], vector2: number[]): number {
    let dotProduct = 0;
    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
    }
    // 简单归一化到0-1范围
    return Math.max(0, Math.min(1, (dotProduct + 1) / 2));
  }

  /**
   * 从文本中提取情感词
   */
  private extractEmotionsFromText(text: string): string[] {
    const emotionKeywords = {
      '快乐': ['开心', '高兴', '愉快', '兴奋', '满足'],
      '悲伤': ['难过', '伤心', '沮丧', '失落', '痛苦'],
      '愤怒': ['生气', '愤怒', '恼火', '烦躁', '不满'],
      '焦虑': ['担心', '紧张', '焦虑', '不安', '恐惧'],
      '平静': ['平静', '安静', '放松', '宁静', '舒适']
    };

    const emotions: string[] = [];
    for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        emotions.push(emotion);
      }
    }

    return emotions;
  }

  /**
   * 从文本中提取主题词
   */
  private extractThemesFromText(text: string): string[] {
    const themeKeywords = {
      '工作': ['工作', '职业', '事业', '项目', '任务'],
      '学习': ['学习', '知识', '技能', '课程', '研究'],
      '生活': ['生活', '日常', '家庭', '朋友', '社交'],
      '健康': ['健康', '运动', '饮食', '睡眠', '医疗'],
      '情感': ['感情', '爱情', '友情', '亲情', '关系']
    };

    const themes: string[] = [];
    for (const [theme, keywords] of Object.entries(themeKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        themes.push(theme);
      }
    }

    return themes;
  }

  /**
   * 获取模拟检索结果（用于测试和演示）
   */
  private getMockRetrievalResult(
    query: string,
    config: EnhancedRetrievalConfig
  ): DetailedRetrievalResult {
    console.log('🎭 生成模拟检索结果...');

    const mockMemories: ChunkMetadata[] = [
      {
        id: 'mock_memory_1',
        documentId: 'mock_doc_1',
        chunkIndex: 0,
        content: '今天我感到很开心，因为完成了一个重要的项目。这个项目让我学到了很多关于人工智能和自然语言处理的知识。我特别喜欢研究深度学习算法，尤其是在情感分析和语义理解方面的应用。',
        childChunk: '完成重要AI项目，学习深度学习和NLP知识，专注情感分析和语义理解',
        vector: new Array(384).fill(0).map(() => Math.random() - 0.5),
        emotionalTags: ['开心', '满足', '兴奋'],
        cognitiveThemes: ['学习', '工作', '技术'],
        keywords: ['人工智能', '深度学习', '项目', '情感分析'],
        importanceScore: 0.9,
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
        processingTime: 120,
        tokenCount: 85
      },
      {
        id: 'mock_memory_2',
        documentId: 'mock_doc_1',
        chunkIndex: 1,
        content: '最近在学习新的编程技术，感觉自己的技能在不断提升。虽然有时候会遇到困难，但是解决问题的过程让我很有成就感。我希望能够继续在这个领域深入研究。',
        childChunk: '学习编程技术提升技能，解决问题获得成就感，希望深入研究',
        vector: new Array(384).fill(0).map(() => Math.random() - 0.5),
        emotionalTags: ['成就感', '希望', '坚持'],
        cognitiveThemes: ['学习', '技术', '成长'],
        keywords: ['编程', '技术', '学习', '成就感'],
        importanceScore: 0.8,
        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12小时前
        processingTime: 95,
        tokenCount: 67
      },
      {
        id: 'mock_memory_3',
        documentId: 'mock_doc_2',
        chunkIndex: 0,
        content: '今天的心情比较平静，花了一些时间思考未来的发展方向。我觉得在AI领域还有很多值得探索的地方，特别是在人机交互和情感计算方面。',
        childChunk: '心情平静思考未来，AI领域探索人机交互和情感计算',
        vector: new Array(384).fill(0).map(() => Math.random() - 0.5),
        emotionalTags: ['平静', '思考'],
        cognitiveThemes: ['思考', '未来', '技术'],
        keywords: ['AI', '人机交互', '情感计算', '未来'],
        importanceScore: 0.7,
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6小时前
        processingTime: 80,
        tokenCount: 52
      }
    ];

    // 模拟候选项
    const mockCandidates: RetrievalCandidate[] = mockMemories.map((memory, index) => ({
      chunk: memory,
      scores: {
        vectorSimilarity: 0.8 - index * 0.1,
        keywordMatch: 0.7 - index * 0.05,
        semanticRelevance: 0.85 - index * 0.08,
        emotionalMatch: 0.75 - index * 0.1,
        themeRelevance: 0.8 - index * 0.05,
        importanceScore: memory.importanceScore,
        temporalScore: 1.0 - index * 0.1,
        profileMatch: 0.7 - index * 0.05
      },
      finalWeight: 0.8 - index * 0.1,
      rank: index + 1
    }));

    // 模拟权重分解
    const mockWeightBreakdown: WeightBreakdown[] = mockCandidates.map(candidate => ({
      chunkId: candidate.chunk.id,
      rawScores: candidate.scores,
      weightedScores: {
        semanticRelevance: candidate.scores.semanticRelevance * config.weightingParams.semanticRelevance,
        emotionalMatch: candidate.scores.emotionalMatch * config.weightingParams.emotionalMatch,
        themeRelevance: candidate.scores.themeRelevance * config.weightingParams.themeRelevance,
        importanceScore: candidate.scores.importanceScore * config.weightingParams.importanceScore,
        temporalDecay: candidate.scores.temporalScore * config.weightingParams.temporalDecay,
        profileMatch: candidate.scores.profileMatch * config.weightingParams.profileMatch
      },
      finalWeight: candidate.finalWeight,
      explanation: [
        `语义相关性较高 (${candidate.scores.semanticRelevance.toFixed(2)})`,
        `情感匹配度良好 (${candidate.scores.emotionalMatch.toFixed(2)})`,
        `内容重要性很高 (${candidate.scores.importanceScore.toFixed(2)})`
      ]
    }));

    const result: DetailedRetrievalResult = {
      query,
      finalMemories: mockMemories.slice(0, config.finalCount),
      debugInfo: {
        totalCandidates: 15,
        candidatesAfterFiltering: mockCandidates.length,
        processingTime: 250,
        candidateBreakdown: mockCandidates,
        weightBreakdown: mockWeightBreakdown,
        filteringStats: {
          removedByThreshold: 2,
          removedByDeduplication: 1,
          removedByTemporal: 0,
          removedByImportance: 0
        },
        configSnapshot: config
      },
      performance: {
        vectorSearchTime: 120,
        weightingTime: 80,
        filteringTime: 50,
        totalTime: 250
      }
    };

    console.log('✅ 模拟检索结果生成完成');
    return result;
  }
}

// 导出单例
export const ragRetriever = new RAGRetriever();
