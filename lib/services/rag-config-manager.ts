// RAG配置管理服务 - 处理配置的验证、存储和管理

import { 
  IndexingConfig, 
  RetrievalConfig, 
  ConfigPreset, 
  ValidationResult, 
  ValidationError, 
  ValidationWarning,
  DynamicWeightRule,
  FilterRule,
  ConfigHistory,
  DebugSession
} from '@/types/rag';

class RAGConfigManager {
  private presets: Map<string, ConfigPreset> = new Map();
  private configHistory: ConfigHistory[] = [];
  private currentSession: DebugSession | null = null;
  private configVersions: Map<string, any[]> = new Map();
  private syncCallbacks: Set<(config: any) => void> = new Set();
  private autoBackupEnabled: boolean = true;
  private backupInterval: number = 5 * 60 * 1000; // 5分钟
  private lastBackupTime: number = 0;

  /**
   * 获取默认索引配置
   */
  getDefaultIndexingConfig(): IndexingConfig {
    return {
      chunkingStrategy: 'recursive',
      chunkSize: 800,
      chunkOverlap: 100,
      
      meaningGenerationPrompt: `你是一个专业的记忆意义提取专家。请将以下记忆片段转换为简洁而深刻的意义摘要。

## 任务要求：
1. 提取记忆片段的核心意义和情感价值
2. 保留关键的情感状态和认知洞察
3. 使用简洁、准确的语言
4. 长度控制在50-100字之间
5. 突出记忆的深层含义，而非表面事实

## 原始记忆片段：
{chunk}

## 请生成意义摘要：`,

      connectionTaggingPrompt: `分析以下记忆片段，提取3-5个最相关的连接标签。

记忆片段：{chunk}

请从以下类别中选择标签：
情感类：快乐、悲伤、愤怒、恐惧、惊讶、厌恶、焦虑、平静
认知类：学习、反思、决策、创造、分析、直觉、困惑、清晰
关系类：亲密、疏远、冲突、和谐、支持、依赖、独立、合作
成长类：突破、挫折、坚持、放弃、改变、稳定、探索、确认

请只返回选中的标签，用逗号分隔：`,

      enableAIEnhancement: true,
      
      emotionalAnalysisPrompt: `分析以下文本的情感状态，返回情感标签和强度。

文本：{chunk}

请返回JSON格式：
{
  "primary_emotion": "主要情感",
  "intensity": 0.8,
  "secondary_emotions": ["次要情感1", "次要情感2"],
  "emotional_tone": "积极/消极/中性"
}`,

      cognitiveAnalysisPrompt: `分析以下文本的认知主题和思维模式。

文本：{chunk}

请返回JSON格式：
{
  "cognitive_themes": ["主题1", "主题2"],
  "thinking_pattern": "分析性/直觉性/创造性/批判性",
  "complexity_level": "简单/中等/复杂",
  "certainty_level": 0.7
}`,

      batchSize: 10,
      maxConcurrency: 3,
      enableCaching: true
    };
  }

  /**
   * 获取默认检索配置
   */
  getDefaultRetrievalConfig(): RetrievalConfig {
    return {
      retrievalMode: 'hybrid_weighted',
      semanticAlgorithm: 'cosine',
      similarityThreshold: 0.3,
      
      weightingRules: [
        {
          id: 'recent_mention',
          name: '最近提及加权',
          description: '对最近24小时内提及的记忆给予额外权重',
          enabled: true,
          condition: {
            recentlyMentioned: true,
            timeWindow: 24
          },
          boost: 1.5,
          boostType: 'multiply',
          priority: 80
        },
        {
          id: 'emotional_intensity',
          name: '情感强度加权',
          description: '对高情感强度的记忆给予额外权重',
          enabled: true,
          condition: {
            emotions: ['快乐', '悲伤', '愤怒', '恐惧'],
            emotionsOperator: 'OR',
            minImportanceScore: 0.7
          },
          boost: 1.3,
          boostType: 'multiply',
          priority: 70
        },
        {
          id: 'core_trauma',
          name: '核心创伤记忆',
          description: '对标记为核心创伤的记忆给予最高权重',
          enabled: true,
          condition: {
            tags: ['核心创伤', '重要转折'],
            tagsOperator: 'OR'
          },
          boost: 2.0,
          boostType: 'multiply',
          priority: 100
        }
      ],
      enableDynamicWeighting: true,
      
      filterRules: [
        {
          id: 'similarity_filter',
          name: '相似度过滤',
          description: '过滤低相似度的候选项',
          enabled: true,
          filterType: 'similarity',
          parameters: {
            threshold: 0.2,
            operator: 'gte'
          },
          order: 1
        },
        {
          id: 'diversity_filter',
          name: '多样性过滤',
          description: '确保结果的多样性',
          enabled: true,
          filterType: 'diversity',
          parameters: {
            threshold: 0.8,
            operator: 'lt'
          },
          order: 2
        }
      ],
      filterPercentage: 20,
      
      topK: 100,
      finalCount: 5,
      
      enableReranking: true,
      diversityFactor: 0.3,
      temporalDecay: 0.1
    };
  }

  /**
   * 验证索引配置
   */
  validateIndexingConfig(config: IndexingConfig): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 验证分块大小
    if (config.chunkSize < 100 || config.chunkSize > 2000) {
      errors.push({
        field: 'chunkSize',
        message: '分块大小必须在100-2000之间',
        code: 'INVALID_CHUNK_SIZE'
      });
    }

    // 验证重叠大小
    if (config.chunkOverlap < 0 || config.chunkOverlap > config.chunkSize * 0.5) {
      errors.push({
        field: 'chunkOverlap',
        message: '重叠大小不能超过分块大小的50%',
        code: 'INVALID_OVERLAP_SIZE'
      });
    }

    // 验证提示词
    if (!config.meaningGenerationPrompt || config.meaningGenerationPrompt.length < 50) {
      errors.push({
        field: 'meaningGenerationPrompt',
        message: '意义生成提示词不能为空且长度至少50字符',
        code: 'INVALID_PROMPT'
      });
    }

    // 验证批处理配置
    if (config.batchSize < 1 || config.batchSize > 100) {
      warnings.push({
        field: 'batchSize',
        message: '建议批处理大小在1-100之间',
        suggestion: '推荐值：10-20'
      });
    }

    if (config.maxConcurrency < 1 || config.maxConcurrency > 10) {
      warnings.push({
        field: 'maxConcurrency',
        message: '建议最大并发数在1-10之间',
        suggestion: '推荐值：3-5'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证检索配置
   */
  validateRetrievalConfig(config: RetrievalConfig): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 验证相似度阈值
    if (config.similarityThreshold < 0 || config.similarityThreshold > 1) {
      errors.push({
        field: 'similarityThreshold',
        message: '相似度阈值必须在0-1之间',
        code: 'INVALID_THRESHOLD'
      });
    }

    // 验证检索数量
    if (config.topK < config.finalCount) {
      errors.push({
        field: 'topK',
        message: '候选项数量不能小于最终返回数量',
        code: 'INVALID_COUNT_RATIO'
      });
    }

    // 验证权重规则
    config.weightingRules.forEach((rule, index) => {
      if (rule.boost < 0.1 || rule.boost > 10) {
        errors.push({
          field: `weightingRules[${index}].boost`,
          message: '权重提升倍数必须在0.1-10之间',
          code: 'INVALID_BOOST_VALUE'
        });
      }

      if (rule.priority < 1 || rule.priority > 100) {
        warnings.push({
          field: `weightingRules[${index}].priority`,
          message: '建议优先级在1-100之间',
          suggestion: '高优先级：80-100，中优先级：50-79，低优先级：1-49'
        });
      }
    });

    // 验证过滤百分比
    if (config.filterPercentage < 0 || config.filterPercentage > 50) {
      warnings.push({
        field: 'filterPercentage',
        message: '建议过滤百分比在0-50之间',
        suggestion: '推荐值：10-30%'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 创建配置预设
   */
  createPreset(
    name: string,
    description: string,
    category: ConfigPreset['category'],
    indexingConfig: IndexingConfig,
    retrievalConfig: RetrievalConfig
  ): ConfigPreset {
    const preset: ConfigPreset = {
      id: `preset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      category,
      indexingConfig: { ...indexingConfig },
      retrievalConfig: { ...retrievalConfig },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: 'user',
      version: '1.0.0',
      tags: [],
      usageCount: 0,
      lastUsed: new Date().toISOString()
    };

    this.presets.set(preset.id, preset);
    return preset;
  }

  /**
   * 获取所有预设
   */
  getAllPresets(): ConfigPreset[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取预设分类
   */
  getPresetsByCategory(category: ConfigPreset['category']): ConfigPreset[] {
    return Array.from(this.presets.values()).filter(preset => preset.category === category);
  }

  /**
   * 导出配置
   */
  exportConfig(indexingConfig: IndexingConfig, retrievalConfig: RetrievalConfig): string {
    const exportData = {
      version: '1.0.0',
      exportedAt: new Date().toISOString(),
      indexingConfig,
      retrievalConfig
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 导入配置
   */
  importConfig(configJson: string): { indexingConfig: IndexingConfig; retrievalConfig: RetrievalConfig } {
    try {
      const data = JSON.parse(configJson);

      if (!data.indexingConfig || !data.retrievalConfig) {
        throw new Error('配置文件格式不正确');
      }

      // 验证导入的配置
      const indexingValidation = this.validateIndexingConfig(data.indexingConfig);
      const retrievalValidation = this.validateRetrievalConfig(data.retrievalConfig);

      if (!indexingValidation.isValid || !retrievalValidation.isValid) {
        throw new Error('导入的配置验证失败');
      }

      return {
        indexingConfig: data.indexingConfig,
        retrievalConfig: data.retrievalConfig
      };
    } catch (error) {
      throw new Error(`配置导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 配置版本管理
   */
  saveConfigVersion(configId: string, config: any, changeDescription: string = ''): void {
    const version = {
      id: `v${Date.now()}`,
      config: JSON.parse(JSON.stringify(config)),
      timestamp: new Date().toISOString(),
      changeDescription,
      author: 'user'
    };

    if (!this.configVersions.has(configId)) {
      this.configVersions.set(configId, []);
    }

    const versions = this.configVersions.get(configId)!;
    versions.push(version);

    // 保留最近50个版本
    if (versions.length > 50) {
      versions.splice(0, versions.length - 50);
    }

    this.triggerAutoBackup();
  }

  getConfigVersions(configId: string): any[] {
    return this.configVersions.get(configId) || [];
  }

  restoreConfigVersion(configId: string, versionId: string): any | null {
    const versions = this.configVersions.get(configId);
    if (!versions) return null;

    const version = versions.find(v => v.id === versionId);
    return version ? version.config : null;
  }

  /**
   * 实时同步机制
   */
  onConfigChange(callback: (config: any) => void): () => void {
    this.syncCallbacks.add(callback);
    return () => this.syncCallbacks.delete(callback);
  }

  private notifyConfigChange(config: any): void {
    this.syncCallbacks.forEach(callback => {
      try {
        callback(config);
      } catch (error) {
        console.error('配置同步回调执行失败:', error);
      }
    });
  }

  updateIndexingConfig(config: IndexingConfig, changeDescription?: string): void {
    this.saveConfigVersion('indexing', config, changeDescription);
    this.notifyConfigChange({ type: 'indexing', config });
  }

  updateRetrievalConfig(config: RetrievalConfig, changeDescription?: string): void {
    this.saveConfigVersion('retrieval', config, changeDescription);
    this.notifyConfigChange({ type: 'retrieval', config });
  }

  /**
   * 自动备份功能
   */
  private triggerAutoBackup(): void {
    if (!this.autoBackupEnabled) return;

    const now = Date.now();
    if (now - this.lastBackupTime > this.backupInterval) {
      this.createBackup();
      this.lastBackupTime = now;
    }
  }

  createBackup(): string {
    const backupData = {
      timestamp: new Date().toISOString(),
      presets: Array.from(this.presets.entries()),
      configVersions: Array.from(this.configVersions.entries()),
      configHistory: this.configHistory,
      currentSession: this.currentSession
    };

    const backupJson = JSON.stringify(backupData, null, 2);

    // 在实际应用中，这里应该保存到本地存储或服务器
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(`rag_config_backup_${Date.now()}`, backupJson);
    }

    return backupJson;
  }

  restoreFromBackup(backupJson: string): void {
    try {
      const backupData = JSON.parse(backupJson);

      this.presets = new Map(backupData.presets || []);
      this.configVersions = new Map(backupData.configVersions || []);
      this.configHistory = backupData.configHistory || [];
      this.currentSession = backupData.currentSession || null;

      console.log('配置备份恢复成功');
    } catch (error) {
      throw new Error(`备份恢复失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 配置模板系统
   */
  getRecommendedPresets(context?: string): ConfigPreset[] {
    const allPresets = this.getAllPresets();

    if (!context) {
      return allPresets.slice(0, 5); // 返回前5个最常用的
    }

    // 基于上下文推荐配置
    const contextKeywords = context.toLowerCase();

    return allPresets.filter(preset => {
      const searchText = `${preset.name} ${preset.description} ${preset.tags?.join(' ')}`.toLowerCase();
      return contextKeywords.split(' ').some(keyword => searchText.includes(keyword));
    }).slice(0, 3);
  }

  createTemplatePresets(): void {
    // 情感分析优化模板
    const emotionalPreset = this.createPreset(
      '情感分析优化',
      '专门优化情感记忆检索的配置模板',
      'emotional',
      {
        ...this.getDefaultIndexingConfig(),
        meaningGenerationPrompt: `作为情感分析专家，请深度分析以下记忆片段的情感内核。

## 分析重点：
1. 识别主要情感状态和强度
2. 挖掘潜在的情感冲突或转变
3. 提取情感背后的深层需求
4. 总结情感体验的意义价值

## 记忆片段：
{chunk}

## 情感意义摘要：`,
        chunkSize: 600,
        enableAIEnhancement: true
      },
      {
        ...this.getDefaultRetrievalConfig(),
        weightingRules: [
          {
            id: 'emotional_priority',
            name: '情感优先权重',
            description: '对情感强度高的记忆给予优先权重',
            enabled: true,
            condition: {
              emotions: ['快乐', '悲伤', '愤怒', '恐惧', '感动'],
              emotionsOperator: 'OR',
              minImportanceScore: 0.6
            },
            boost: 1.8,
            boostType: 'multiply',
            priority: 90
          }
        ],
        similarityThreshold: 0.25,
        diversityFactor: 0.4
      }
    );

    // 认知分析模板
    const cognitivePreset = this.createPreset(
      '认知分析专用',
      '专门用于认知模式和思维分析的配置',
      'technical',
      {
        ...this.getDefaultIndexingConfig(),
        meaningGenerationPrompt: `作为认知心理学专家，请分析以下记忆片段的认知模式和思维特征。

## 分析维度：
1. 思维模式和认知风格
2. 决策过程和推理逻辑
3. 学习和成长的认知洞察
4. 认知偏差和思维盲点

## 记忆片段：
{chunk}

## 认知意义摘要：`,
        chunkSize: 1000,
        chunkingStrategy: 'paragraph'
      },
      {
        ...this.getDefaultRetrievalConfig(),
        retrievalMode: 'vector_only',
        semanticAlgorithm: 'cosine',
        topK: 150,
        finalCount: 8
      }
    );

    // 高性能模板
    const performancePreset = this.createPreset(
      '高性能检索',
      '优化检索速度和效率的轻量级配置',
      'technical',
      {
        ...this.getDefaultIndexingConfig(),
        chunkSize: 400,
        chunkOverlap: 50,
        batchSize: 20,
        maxConcurrency: 5,
        enableAIEnhancement: false
      },
      {
        ...this.getDefaultRetrievalConfig(),
        retrievalMode: 'vector_only',
        topK: 50,
        finalCount: 3,
        enableReranking: false,
        enableDynamicWeighting: false,
        weightingRules: []
      }
    );

    console.log('模板预设创建完成');
  }

  /**
   * 配置性能分析
   */
  analyzeConfigPerformance(indexingConfig: IndexingConfig, retrievalConfig: RetrievalConfig): any {
    const analysis = {
      estimatedIndexingTime: 0,
      estimatedRetrievalTime: 0,
      memoryUsage: 0,
      complexity: 'low',
      bottlenecks: [] as string[],
      recommendations: [] as string[],
      score: 0
    };

    // 分析索引性能
    let indexingTime = 100; // 基础时间

    if (indexingConfig.enableAIEnhancement) {
      indexingTime += indexingConfig.chunkSize * 0.5;
    }

    indexingTime += (indexingConfig.batchSize * indexingConfig.maxConcurrency) * 2;

    if (indexingConfig.chunkSize > 1000) {
      analysis.bottlenecks.push('分块大小过大可能影响处理速度');
      analysis.recommendations.push('考虑减小分块大小到800以下');
    }

    // 分析检索性能
    let retrievalTime = 50; // 基础时间

    if (retrievalConfig.retrievalMode === 'hybrid_weighted') {
      retrievalTime += 30;
    }

    retrievalTime += retrievalConfig.weightingRules.length * 5;
    retrievalTime += retrievalConfig.topK * 0.1;

    if (retrievalConfig.enableReranking) {
      retrievalTime += retrievalConfig.finalCount * 3;
    }

    // 内存使用估算
    analysis.memoryUsage = (indexingConfig.chunkSize * indexingConfig.batchSize) / 1000;
    analysis.memoryUsage += retrievalConfig.topK * 0.01;

    // 复杂度评估
    const totalTime = indexingTime + retrievalTime;
    if (totalTime > 500) {
      analysis.complexity = 'high';
    } else if (totalTime > 200) {
      analysis.complexity = 'medium';
    }

    // 性能评分 (0-100)
    analysis.score = Math.max(0, 100 - (totalTime / 10) - (analysis.memoryUsage * 2));

    analysis.estimatedIndexingTime = Math.round(indexingTime);
    analysis.estimatedRetrievalTime = Math.round(retrievalTime);

    return analysis;
  }

  /**
   * 配置优化建议
   */
  getOptimizationSuggestions(indexingConfig: IndexingConfig, retrievalConfig: RetrievalConfig): string[] {
    const suggestions: string[] = [];
    const performance = this.analyzeConfigPerformance(indexingConfig, retrievalConfig);

    if (performance.estimatedIndexingTime > 300) {
      suggestions.push('索引时间较长，建议减小分块大小或禁用AI增强');
    }

    if (performance.estimatedRetrievalTime > 200) {
      suggestions.push('检索时间较长，建议减少权重规则或降低topK值');
    }

    if (retrievalConfig.weightingRules.length > 10) {
      suggestions.push('权重规则过多，建议合并相似规则或禁用低优先级规则');
    }

    if (indexingConfig.chunkSize > 1200) {
      suggestions.push('分块大小较大，可能影响检索精度，建议调整到800-1000');
    }

    if (retrievalConfig.similarityThreshold < 0.1) {
      suggestions.push('相似度阈值过低，可能返回不相关结果，建议提高到0.2以上');
    }

    return suggestions;
  }
}

// 导出单例
export const ragConfigManager = new RAGConfigManager();
