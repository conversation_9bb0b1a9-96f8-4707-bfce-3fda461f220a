// 本地嵌入模型管理器 - 支持GGUF格式

// 条件导入以避免客户端加载问题
let getLlama: any = null;
let LlamaModel: any = null;
let LlamaEmbeddingContext: any = null;

// 暂时禁用 node-llama-cpp 以避免构建问题
async function loadLlamaCpp() {
  // 暂时返回 null，使用模拟模式
  console.warn('⚠️ node-llama-cpp 暂时禁用，使用模拟模式');
  return null;
}

import { EmbeddingModel, EmbeddingError } from '@/types/rag';
import { RAG_CONFIG, PERFORMANCE_CONFIG } from '@/lib/config/rag-config';

class LocalEmbeddingModel implements EmbeddingModel {
  private llama: any = null;
  private model: any = null;
  private context: any = null;
  private pipeline: any = null;
  private cache: Map<string, number[]> = new Map();

  public name = 'Qwen3-Embedding-4B-Q4_K_M.gguf';
  public loaded = false;
  public dimensions = 1024; // 将在模型加载后更新为实际维度

  /**
   * 加载嵌入模型
   */
  async load(): Promise<void> {
    if (this.loaded) {
      console.log('📦 嵌入模型已加载');
      return;
    }

    // 检查是否在客户端环境
    if (typeof window !== 'undefined') {
      console.warn('⚠️ GGUF模型不能在客户端加载，使用模拟模式');
      this.loaded = true;
      return;
    }

    // 动态加载依赖
    const llamaCpp = await loadLlamaCpp();
    if (!llamaCpp) {
      console.warn('⚠️ node-llama-cpp 不可用，使用模拟模式');
      this.loaded = true;
      return;
    }

    try {
      console.log(`🔄 正在加载GGUF嵌入模型: ${this.name}...`);

      // 获取llama实例
      this.llama = await (llamaCpp as any).getLlama();

      // 构建模型文件路径
      const path = await import('path');
      const modelPath = path.join(process.cwd(), 'public', 'models', this.name);
      console.log(`📁 模型路径: ${modelPath}`);

      // 加载GGUF模型
      this.model = await this.llama.loadModel({
        modelPath: modelPath,
        // 针对embedding模型的优化配置
        gpuLayers: 0, // embedding模型通常在CPU上运行更稳定
      });

      // 创建embedding上下文
      this.context = await this.model.createEmbeddingContext();

      this.loaded = true;
      console.log(`✅ GGUF嵌入模型加载成功: ${this.name}`);

      // 测试模型
      await this.testModel();
    } catch (error) {
      console.error('GGUF嵌入模型加载失败:', error);
      console.warn('⚠️ 切换到模拟模式');
      this.loaded = true; // 标记为已加载，但使用模拟模式
    }
  }

  /**
   * 测试模型是否正常工作
   */
  private async testModel(): Promise<void> {
    try {
      const testText = '这是一个测试文本';
      const embedding = await this.encode(testText);
      
      if (!Array.isArray(embedding) || embedding.length === 0) {
        throw new Error('模型输出格式不正确');
      }

      console.log(`🧪 模型测试成功，向量维度: ${embedding.length}`);
      
      // 更新实际维度
      this.dimensions = embedding.length;
    } catch (error) {
      throw new EmbeddingError('模型测试失败', error);
    }
  }

  /**
   * 确保模型已加载
   */
  async ensureLoaded(): Promise<void> {
    if (!this.loaded) {
      await this.load();
    }
  }

  /**
   * 编码单个文本
   */
  async encode(text: string): Promise<number[]> {
    if (!this.loaded) {
      throw new EmbeddingError('GGUF嵌入模型未加载');
    }

    // 检查缓存
    if (PERFORMANCE_CONFIG.enableCache && this.cache.has(text)) {
      return this.cache.get(text)!;
    }

    // 如果在模拟模式下（客户端或依赖不可用）
    if (typeof window !== 'undefined' || !this.context) {
      console.warn('⚠️ 使用模拟向量生成');
      return this.generateMockEmbedding(text);
    }

    try {
      // 文本预处理
      const processedText = this.preprocessText(text);

      // 使用node-llama-cpp生成嵌入
      const result = await Promise.race([
        this.context.getEmbeddingFor(processedText),
        this.createTimeout(PERFORMANCE_CONFIG.embeddingTimeout)
      ]);

      // 提取向量数据
      const embedding = result.vector;

      // 确保embedding是数字数组
      if (!Array.isArray(embedding) || embedding.length === 0) {
        throw new Error('GGUF模型输出格式不正确');
      }

      // 更新实际维度
      if (this.dimensions !== embedding.length) {
        this.dimensions = embedding.length;
        console.log(`📏 更新向量维度为: ${this.dimensions}`);
      }

      // 缓存结果
      if (PERFORMANCE_CONFIG.enableCache) {
        this.addToCache(text, embedding);
      }

      return embedding;
    } catch (error) {
      if (error instanceof Error && error.message === 'Timeout') {
        throw new EmbeddingError('GGUF嵌入生成超时');
      }
      console.error('GGUF嵌入生成失败:', error);
      throw new EmbeddingError('GGUF嵌入生成失败', error);
    }
  }

  /**
   * 批量编码文本
   */
  async encodeBatch(texts: string[]): Promise<number[][]> {
    if (!this.loaded || !this.pipeline) {
      throw new EmbeddingError('嵌入模型未加载');
    }

    const results: number[][] = [];
    const batchSize = PERFORMANCE_CONFIG.batchSize;

    try {
      // 分批处理以避免内存溢出
      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const batchPromises = batch.map(text => this.encode(text));
        
        // 限制并发数
        const batchResults = await this.processConcurrently(
          batchPromises, 
          PERFORMANCE_CONFIG.maxConcurrency
        );
        
        results.push(...batchResults);
        
        // 进度日志
        console.log(`📊 批量编码进度: ${Math.min(i + batchSize, texts.length)}/${texts.length}`);
      }

      console.log(`✅ 批量编码完成，处理了 ${texts.length} 个文本`);
      return results;
    } catch (error) {
      throw new EmbeddingError('批量嵌入生成失败', error);
    }
  }

  /**
   * 文本预处理
   */
  private preprocessText(text: string): string {
    // 清理文本
    let processed = text.trim();
    
    // 移除多余的空白字符
    processed = processed.replace(/\s+/g, ' ');
    
    // 限制长度
    if (processed.length > RAG_CONFIG.embedding.maxTokens * 4) { // 粗略估算
      processed = processed.substring(0, RAG_CONFIG.embedding.maxTokens * 4);
    }
    
    return processed;
  }

  /**
   * 向量归一化（可选，因为pipeline已经设置了normalize: true）
   */
  private normalizeVector(vector: number[]): number[] {
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (magnitude === 0) return vector;
    return vector.map(val => val / magnitude);
  }

  /**
   * 添加到缓存
   */
  private addToCache(text: string, embedding: number[]): void {
    if (this.cache.size >= PERFORMANCE_CONFIG.cacheSize) {
      // 删除最旧的缓存项
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(text, embedding);
  }

  /**
   * 创建超时Promise
   */
  private createTimeout(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Timeout')), ms);
    });
  }

  /**
   * 控制并发执行
   */
  private async processConcurrently<T>(
    promises: Promise<T>[], 
    maxConcurrency: number
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < promises.length; i += maxConcurrency) {
      const batch = promises.slice(i, i + maxConcurrency);
      const batchResults = await Promise.all(batch);
      results.push(...batchResults);
    }
    
    return results;
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ 嵌入缓存已清空');
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: PERFORMANCE_CONFIG.cacheSize,
      hitRate: 0 // TODO: 实现命中率统计
    };
  }

  /**
   * 获取模型信息
   */
  getModelInfo() {
    return {
      name: this.name,
      loaded: this.loaded,
      dimensions: this.dimensions,
      cacheStats: this.getCacheStats()
    };
  }

  /**
   * 生成模拟向量（用于客户端或依赖不可用时）
   */
  private generateMockEmbedding(text: string): number[] {
    // 使用简单的哈希算法生成确定性的模拟向量
    const hash = this.simpleHash(text);
    const embedding = new Array(this.dimensions);

    for (let i = 0; i < this.dimensions; i++) {
      // 使用哈希值生成伪随机数
      const seed = hash + i;
      embedding[i] = (Math.sin(seed) + 1) / 2; // 归一化到 [0, 1]
    }

    // 缓存结果
    if (PERFORMANCE_CONFIG.enableCache) {
      this.addToCache(text, embedding);
    }

    return embedding;
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 卸载模型
   */
  async unload(): Promise<void> {
    if (this.context) {
      // node-llama-cpp的context会自动清理
      this.context = null;
    }
    if (this.model) {
      // 模型也会自动清理
      this.model = null;
    }
    this.llama = null;
    this.loaded = false;
    this.clearCache();
    console.log('📦 GGUF嵌入模型已卸载');
  }
}

// 导出单例
export const embeddingModel = new LocalEmbeddingModel();
