// AI输出处理逻辑管理器
import { 
  appendToMemoryFile, 
  writeMemoryFile, 
  readMemoryFile,
  MEMORY_FILES 
} from "@/lib/storage/memory-manager";

export interface DailyChatOutput {
  type: 'stream';
  content: string;
  metadata: {
    processingTime: number;
    tokenCount?: number;
    finishReason?: string;
  };
}

export interface DailyInsightOutput {
  type: 'insight';
  content: string;
  metadata: {
    analysisTime: number;
    insightLength: number;
    savedToFile: boolean;
  };
}

export interface DeepRefinementOutput {
  type: 'refinement';
  analysisResult: any;
  updatedFiles: string[];
  metadata: {
    processingTime: number;
    filesUpdated: number;
    jsonValid: boolean;
  };
}

/**
 * A. 处理日常对话模式的输出
 * 特点：流式输出，直接返回给前端
 */
export async function processDailyChatOutput(
  aiResponse: string,
  userMessage: string,
  processingTime: number,
  metadata?: any
): Promise<DailyChatOutput> {
  console.log('📤 处理日常对话输出...');
  
  try {
    // 保存对话到历史记录
    await saveConversationToHistory(userMessage, aiResponse);
    console.log('💾 对话已保存到历史记录');
    
    return {
      type: 'stream',
      content: aiResponse,
      metadata: {
        processingTime,
        tokenCount: metadata?.usage?.totalTokens,
        finishReason: metadata?.finishReason
      }
    };
  } catch (error) {
    console.error('❌ 日常对话输出处理失败:', error);
    throw error;
  }
}

/**
 * B. 处理每日洞察模式的输出
 * 特点：追加到热洞察文件
 */
export async function processDailyInsightOutput(
  insightContent: string,
  analysisTime: number
): Promise<DailyInsightOutput> {
  console.log('📤 处理每日洞察输出...');
  
  try {
    // 将洞察追加到热洞察文件
    await appendToMemoryFile(MEMORY_FILES.DAILY_INSIGHT_HOT, insightContent);
    console.log('💾 洞察已追加到热日志');
    
    return {
      type: 'insight',
      content: insightContent,
      metadata: {
        analysisTime,
        insightLength: insightContent.length,
        savedToFile: true
      }
    };
  } catch (error) {
    console.error('❌ 每日洞察输出处理失败:', error);
    
    return {
      type: 'insight',
      content: insightContent,
      metadata: {
        analysisTime,
        insightLength: insightContent.length,
        savedToFile: false
      }
    };
  }
}

/**
 * C. 处理深度精炼模式的输出
 * 特点：结构化更新多个记忆文件
 */
export async function processDeepRefinementOutput(
  aiResponse: string,
  processingTime: number
): Promise<DeepRefinementOutput> {
  console.log('📤 处理深度精炼输出...');
  
  const updatedFiles: string[] = [];
  let analysisResult: any;
  let jsonValid = false;
  
  try {
    // 解析AI返回的JSON结果
    analysisResult = JSON.parse(aiResponse);
    jsonValid = true;
    console.log('✅ JSON解析成功');
  } catch (parseError) {
    console.error('❌ JSON解析失败:', parseError);
    throw new Error('AI返回的结果格式不正确');
  }
  
  try {
    // 更新用户画像
    if (analysisResult.user_profile_updates?.updated_content) {
      await writeMemoryFile(MEMORY_FILES.USER_PROFILE, analysisResult.user_profile_updates.updated_content);
      updatedFiles.push(MEMORY_FILES.USER_PROFILE);
      console.log('💾 用户画像已更新');
    }
    
    // 更新心智要素结构
    if (analysisResult.mental_elements_updates?.updated_content) {
      await writeMemoryFile(MEMORY_FILES.MENTAL_ELEMENTS, analysisResult.mental_elements_updates.updated_content);
      updatedFiles.push(MEMORY_FILES.MENTAL_ELEMENTS);
      console.log('💾 心智要素结构已更新');
    }
    
    // 处理关键事件批注
    if (analysisResult.key_events_annotations?.length > 0) {
      await processKeyEventsAnnotations(analysisResult.key_events_annotations);
      updatedFiles.push(MEMORY_FILES.KEY_EVENTS);
      console.log('💾 关键事件批注已更新');
    }
    
    return {
      type: 'refinement',
      analysisResult,
      updatedFiles,
      metadata: {
        processingTime,
        filesUpdated: updatedFiles.length,
        jsonValid
      }
    };
  } catch (error) {
    console.error('❌ 深度精炼输出处理失败:', error);
    throw error;
  }
}

/**
 * 处理关键事件的增量批注更新
 */
async function processKeyEventsAnnotations(annotations: any[]): Promise<void> {
  console.log('📝 处理关键事件批注...');
  
  try {
    // 读取当前的关键事件文件
    const currentContent = await readMemoryFile(MEMORY_FILES.KEY_EVENTS);
    
    // 为每个批注添加时间戳和标记
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    let updatedContent = currentContent;
    
    for (const annotation of annotations) {
      const annotationText = `
---

## 深度精炼批注 - ${timestamp}

**事件**: ${annotation.event_reference}
**新理解**: ${annotation.new_interpretation}
**深层意义**: ${annotation.deeper_meaning}

---
`;
      updatedContent += annotationText;
    }
    
    // 写回文件
    await writeMemoryFile(MEMORY_FILES.KEY_EVENTS, updatedContent);
    console.log(`✅ 已添加 ${annotations.length} 个关键事件批注`);
  } catch (error) {
    console.error('❌ 关键事件批注处理失败:', error);
    throw error;
  }
}

/**
 * 保存对话到历史记录的辅助函数
 */
async function saveConversationToHistory(userMessage: string, assistantMessage: string): Promise<void> {
  const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
  const conversationEntry = `
### ${timestamp}

**用户**: ${userMessage}

**小镜**: ${assistantMessage}

---`;
  
  await appendToMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY, conversationEntry);
}

/**
 * 输出质量验证
 */
export function validateOutput(output: DailyChatOutput | DailyInsightOutput | DeepRefinementOutput): boolean {
  switch (output.type) {
    case 'stream':
      return !!(output.content && output.content.trim().length > 0);

    case 'insight':
      return !!(output.content && output.content.trim().length > 0 && output.metadata.savedToFile);

    case 'refinement':
      return !!(output.analysisResult && output.metadata.jsonValid && output.updatedFiles.length > 0);
    
    default:
      return false;
  }
}

/**
 * 获取输出统计信息
 */
export function getOutputStats(output: DailyChatOutput | DailyInsightOutput | DeepRefinementOutput) {
  const baseStats = {
    type: output.type,
    processingTime: ('processingTime' in output.metadata) ? output.metadata.processingTime :
                   ('analysisTime' in output.metadata) ? output.metadata.analysisTime : 0
  };
  
  switch (output.type) {
    case 'stream':
      return {
        ...baseStats,
        contentLength: output.content.length,
        tokenCount: output.metadata.tokenCount,
        finishReason: output.metadata.finishReason
      };
    
    case 'insight':
      return {
        ...baseStats,
        insightLength: output.metadata.insightLength,
        savedToFile: output.metadata.savedToFile
      };
    
    case 'refinement':
      return {
        ...baseStats,
        filesUpdated: output.metadata.filesUpdated,
        jsonValid: output.metadata.jsonValid,
        updatedFiles: output.updatedFiles
      };
    
    default:
      return baseStats;
  }
}
