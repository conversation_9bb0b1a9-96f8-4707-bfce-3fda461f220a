// 双向量数据库管理器
// 协调Hot Store和Cold Store的操作，实现智能数据路由和生命周期管理

import {
  IDualVectorManager,
  IVectorStore,
  VectorStoreMetadata,
  DualSearchOptions,
  DualSearchResult,
  DualSystemStats,
  MaintenanceResult,
  MigrationResult,
  VectorSearchResult,
  DualVectorError
} from './interfaces';
import { HotVectorStore } from './hot-store';
import { ColdVectorStore } from './cold-store';
import { globalIdManager } from './global-id-system';

/**
 * 双向量数据库管理器实现
 * 统一管理Hot Store和Cold Store，提供智能路由和数据生命周期管理
 */
export class DualVectorManager implements IDualVectorManager {
  public readonly hotStore: IVectorStore;
  public readonly coldStore: IVectorStore;
  public initialized = false;

  private migrationThreshold = 7; // 7天后数据迁移到Cold Store
  private lastMaintenanceTime = new Date();
  private searchStats = {
    totalSearches: 0,
    hotSearches: 0,
    coldSearches: 0,
    parallelSearches: 0
  };

  constructor(
    hotStoreConfig: any,
    coldStoreConfig: any
  ) {
    this.hotStore = new HotVectorStore(hotStoreConfig);
    this.coldStore = new ColdVectorStore(coldStoreConfig);
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔄 初始化双向量数据库管理器...');

      // 并行初始化两个存储
      await Promise.all([
        this.hotStore.initialize(),
        this.coldStore.initialize()
      ]);

      // 启动后台维护任务
      this.startMaintenanceTasks();

      this.initialized = true;
      console.log('✅ 双向量数据库管理器初始化完成');
    } catch (error) {
      throw new DualVectorError('双向量管理器初始化失败', 'INIT_ERROR', error);
    }
  }

  async addVector(vector: number[], metadata: VectorStoreMetadata): Promise<string> {
    if (!this.initialized) {
      throw new DualVectorError('双向量管理器未初始化', 'NOT_INITIALIZED');
    }

    // 确保全局ID管理器已初始化
    await globalIdManager.initialize();

    // 智能路由：决定数据应该存储在Hot Store还是Cold Store
    const targetStore = this.determineTargetStore(metadata);

    try {
      let chunkId: string;

      if (targetStore === 'hot') {
        chunkId = await this.hotStore.add(vector, metadata);
      } else {
        chunkId = await this.coldStore.add(vector, metadata);
      }

      // 记录路由决策到全局ID系统
      if (metadata.globalId) {
        await globalIdManager.updateContentMetadata(metadata.globalId, {
          contentType: 'vector',
          contentLength: vector.length
        });
      }

      console.log(`🔀 双向量管理器路由: ${chunkId} -> ${targetStore} store (全局ID: ${metadata.globalId})`);
      return chunkId;

    } catch (error) {
      throw new DualVectorError(
        `添加向量到${targetStore} store失败`,
        'ADD_ERROR',
        { targetStore, metadata, error }
      );
    }
  }

  async addBatch(items: Array<{ vector: number[]; metadata: VectorStoreMetadata }>): Promise<string[]> {
    if (!this.initialized) {
      throw new DualVectorError('双向量管理器未初始化', 'NOT_INITIALIZED');
    }

    // 确保全局ID管理器已初始化
    await globalIdManager.initialize();

    const chunkIds: string[] = [];
    const hotItems: Array<{ vector: number[]; metadata: VectorStoreMetadata }> = [];
    const coldItems: Array<{ vector: number[]; metadata: VectorStoreMetadata }> = [];

    // 按目标存储分组
    for (const item of items) {
      const targetStore = this.determineTargetStore(item.metadata);
      if (targetStore === 'hot') {
        hotItems.push(item);
      } else {
        coldItems.push(item);
      }
    }

    try {
      // 批量添加到Hot Store
      if (hotItems.length > 0) {
        const hotChunkIds = await this.hotStore.addBatch(hotItems);
        chunkIds.push(...hotChunkIds);
        console.log(`🔥 批量添加到Hot Store: ${hotChunkIds.length} 个向量`);
      }

      // 批量添加到Cold Store
      if (coldItems.length > 0) {
        const coldChunkIds = await this.coldStore.addBatch(coldItems);
        chunkIds.push(...coldChunkIds);
        console.log(`❄️ 批量添加到Cold Store: ${coldChunkIds.length} 个向量`);
      }

      console.log(`🔀 双向量管理器批量路由完成: 总计 ${chunkIds.length} 个向量`);
      return chunkIds;

    } catch (error) {
      throw new DualVectorError(
        '批量添加向量失败',
        'BATCH_ADD_ERROR',
        { hotItems: hotItems.length, coldItems: coldItems.length, error }
      );
    }
  }

  async searchVector(queryVector: number[], options: DualSearchOptions): Promise<DualSearchResult> {
    if (!this.initialized) {
      throw new DualVectorError('双向量管理器未初始化', 'NOT_INITIALIZED');
    }

    this.searchStats.totalSearches++;
    const startTime = Date.now();

    let hotResults: VectorSearchResult[] = [];
    let coldResults: VectorSearchResult[] = [];
    let hotSearchTime = 0;
    let coldSearchTime = 0;

    try {
      switch (options.searchStrategy) {
        case 'hot_first':
          hotResults = await this.searchHotFirst(queryVector, options);
          hotSearchTime = Date.now() - startTime;
          this.searchStats.hotSearches++;
          break;

        case 'cold_first':
          coldResults = await this.searchColdFirst(queryVector, options);
          coldSearchTime = Date.now() - startTime;
          this.searchStats.coldSearches++;
          break;

        case 'parallel':
          const parallelStart = Date.now();
          [hotResults, coldResults] = await this.searchParallel(queryVector, options);
          const parallelTime = Date.now() - parallelStart;
          hotSearchTime = parallelTime / 2; // 近似分配时间
          coldSearchTime = parallelTime / 2;
          this.searchStats.parallelSearches++;
          break;

        case 'adaptive':
        default:
          [hotResults, coldResults] = await this.searchAdaptive(queryVector, options);
          const adaptiveTime = Date.now() - startTime;
          hotSearchTime = adaptiveTime * 0.3; // Hot Store通常更快
          coldSearchTime = adaptiveTime * 0.7;
          break;
      }

      // 合并结果
      const mergedResults = this.mergeResults(hotResults, coldResults, options);

      return {
        hotResults,
        coldResults,
        mergedResults,
        searchStats: {
          hotSearchTime,
          coldSearchTime,
          totalTime: Date.now() - startTime,
          hotHits: hotResults.length,
          coldHits: coldResults.length
        }
      };
    } catch (error) {
      throw new DualVectorError('搜索失败', 'SEARCH_ERROR', { options, error });
    }
  }

  async promoteToHot(chunkIds: string[]): Promise<void> {
    console.log(`🔥 提升${chunkIds.length}个向量到Hot Store...`);

    for (const chunkId of chunkIds) {
      try {
        // 从Cold Store获取数据
        const coldResults = await this.coldStore.searchByMetadata([
          { field: 'chunkId', operator: 'eq', value: chunkId }
        ]);

        if (coldResults.length > 0) {
          const result = coldResults[0];
          
          // 添加到Hot Store
          await this.hotStore.add(result.vector, result.metadata);
          
          // 从Cold Store删除
          await this.coldStore.delete(chunkId);
          
          console.log(`✅ 向量${chunkId}已提升到Hot Store`);
        }
      } catch (error) {
        console.error(`❌ 提升向量${chunkId}失败:`, error);
      }
    }
  }

  async demoteToCold(chunkIds: string[]): Promise<void> {
    console.log(`❄️ 降级${chunkIds.length}个向量到Cold Store...`);

    for (const chunkId of chunkIds) {
      try {
        // 从Hot Store获取数据
        const hotResults = await this.hotStore.searchByMetadata([
          { field: 'chunkId', operator: 'eq', value: chunkId }
        ]);

        if (hotResults.length > 0) {
          const result = hotResults[0];
          
          // 添加到Cold Store
          await this.coldStore.add(result.vector, result.metadata);
          
          // 从Hot Store删除
          await this.hotStore.delete(chunkId);
          
          console.log(`✅ 向量${chunkId}已降级到Cold Store`);
        }
      } catch (error) {
        console.error(`❌ 降级向量${chunkId}失败:`, error);
      }
    }
  }

  async archiveOldData(olderThan: Date): Promise<number> {
    console.log(`📦 归档${olderThan.toISOString()}之前的数据...`);

    let archivedCount = 0;

    try {
      // 查找Hot Store中的旧数据
      const oldDataResults = await this.hotStore.searchByMetadata([
        { field: 'createdAt', operator: 'lt', value: olderThan.toISOString() }
      ]);

      if (oldDataResults.length > 0) {
        // 批量归档到Cold Store
        if (this.coldStore && 'archive' in this.coldStore) {
          await (this.coldStore as any).archive(oldDataResults);
        }

        // 从Hot Store删除
        const chunkIds = oldDataResults.map(r => r.chunkId);
        for (const chunkId of chunkIds) {
          if (this.hotStore && 'delete' in this.hotStore) {
            await (this.hotStore as any).delete(chunkId);
          }
        }
        
        archivedCount = oldDataResults.length;
      }

      console.log(`✅ 归档完成: ${archivedCount}个向量`);
      return archivedCount;
    } catch (error) {
      console.error('❌ 归档失败:', error);
      return 0;
    }
  }

  async performMaintenance(): Promise<MaintenanceResult> {
    console.log('🔧 执行双向量系统维护...');
    const startTime = Date.now();

    const result: MaintenanceResult = {
      hotStoreMaintenance: {
        vectorsDecayed: 0,
        vectorsEvicted: 0,
        indexOptimized: false
      },
      coldStoreMaintenance: {
        vectorsArchived: 0,
        storageCompacted: false,
        indexRebuilt: false
      },
      totalTime: 0,
      errors: []
    };

    try {
      // Hot Store维护
      if (this.hotStore.performDecay) {
        result.hotStoreMaintenance.vectorsDecayed = await this.hotStore.performDecay();
      }

      await this.hotStore.optimize();
      result.hotStoreMaintenance.indexOptimized = true;

      // Cold Store维护
      await this.coldStore.optimize();
      result.coldStoreMaintenance.indexRebuilt = true;

      await this.coldStore.compact();
      result.coldStoreMaintenance.storageCompacted = true;

      // 自动归档旧数据
      const archiveDate = new Date();
      archiveDate.setDate(archiveDate.getDate() - this.migrationThreshold);
      result.coldStoreMaintenance.vectorsArchived = await this.archiveOldData(archiveDate);

      this.lastMaintenanceTime = new Date();
      result.totalTime = Date.now() - startTime;

      console.log(`✅ 维护完成 (${result.totalTime}ms)`);
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : '未知错误');
      console.error('❌ 维护过程中出现错误:', error);
    }

    return result;
  }

  async getSystemStats(): Promise<DualSystemStats> {
    const [hotStats, coldStats] = await Promise.all([
      this.hotStore.getStats(),
      this.coldStore.getStats()
    ]);

    const totalVectors = hotStats.totalVectors + coldStats.totalVectors;
    const hotPercentage = totalVectors > 0 ? (hotStats.totalVectors / totalVectors) * 100 : 0;
    const coldPercentage = 100 - hotPercentage;

    // 系统健康状态评估
    const [hotHealth, coldHealth] = await Promise.all([
      this.hotStore.getHealth(),
      this.coldStore.getHealth()
    ]);

    let systemStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (hotHealth.status === 'unhealthy' || coldHealth.status === 'unhealthy') {
      systemStatus = 'unhealthy';
    } else if (hotHealth.status === 'degraded' || coldHealth.status === 'degraded') {
      systemStatus = 'degraded';
    }

    return {
      hotStore: hotStats,
      coldStore: coldStats,
      totalVectors,
      dataDistribution: {
        hotPercentage,
        coldPercentage
      },
      systemHealth: {
        status: systemStatus,
        details: {
          vectorCount: totalVectors,
          indexHealth: 'good',
          storageHealth: 'good',
          lastMaintenance: this.lastMaintenanceTime.toISOString()
        }
      }
    };
  }

  async migrateFromLegacy(legacyData: any[]): Promise<MigrationResult> {
    console.log(`🔄 开始迁移${legacyData.length}条遗留数据...`);
    const startTime = Date.now();

    const result: MigrationResult = {
      totalRecords: legacyData.length,
      migratedToHot: 0,
      migratedToCold: 0,
      errors: [],
      migrationTime: 0
    };

    for (const data of legacyData) {
      try {
        // 转换遗留数据格式
        const metadata = this.convertLegacyMetadata(data);
        const vector = data.vector || [];

        // 根据数据类型和时间决定迁移目标
        const targetStore = this.determineTargetStore(metadata);

        if (targetStore === 'hot') {
          await this.hotStore.add(vector, metadata);
          result.migratedToHot++;
        } else {
          await this.coldStore.add(vector, metadata);
          result.migratedToCold++;
        }
      } catch (error) {
        result.errors.push(`迁移数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    result.migrationTime = Date.now() - startTime;
    console.log(`✅ 迁移完成: Hot=${result.migratedToHot}, Cold=${result.migratedToCold}, 错误=${result.errors.length}`);

    return result;
  }

  // 私有辅助方法
  private determineTargetStore(metadata: VectorStoreMetadata): 'hot' | 'cold' {
    // 根据数据类型和时间决定存储位置
    const now = new Date();
    const createdAt = new Date(metadata.createdAt);
    const daysDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);

    // 最近的数据或高频访问数据存储在Hot Store
    if (daysDiff <= this.migrationThreshold) {
      if (metadata.sourceDocumentType === 'daily_insight_hot' ||
          metadata.sourceDocumentType === 'dialogue_history') {
        return 'hot';
      }
    }

    // 其他数据存储在Cold Store
    return 'cold';
  }

  private async searchHotFirst(queryVector: number[], options: DualSearchOptions): Promise<VectorSearchResult[]> {
    // 先搜索Hot Store，如果结果不足再搜索Cold Store
    const hotResults = await this.hotStore.search(queryVector, options.maxResults || 10, options);
    
    if (hotResults.length >= (options.maxResults || 10)) {
      return hotResults;
    }

    const remainingCount = (options.maxResults || 10) - hotResults.length;
    const coldResults = await this.coldStore.search(queryVector, remainingCount, options);
    
    return [...hotResults, ...coldResults];
  }

  private async searchColdFirst(queryVector: number[], options: DualSearchOptions): Promise<VectorSearchResult[]> {
    // 先搜索Cold Store，如果结果不足再搜索Hot Store
    const coldResults = await this.coldStore.search(queryVector, options.maxResults || 10, options);
    
    if (coldResults.length >= (options.maxResults || 10)) {
      return coldResults;
    }

    const remainingCount = (options.maxResults || 10) - coldResults.length;
    const hotResults = await this.hotStore.search(queryVector, remainingCount, options);
    
    return [...coldResults, ...hotResults];
  }

  private async searchParallel(queryVector: number[], options: DualSearchOptions): Promise<[VectorSearchResult[], VectorSearchResult[]]> {
    // 并行搜索两个存储
    const [hotResults, coldResults] = await Promise.all([
      this.hotStore.search(queryVector, options.maxResults || 10, options),
      this.coldStore.search(queryVector, options.maxResults || 10, options)
    ]);

    return [hotResults, coldResults];
  }

  private async searchAdaptive(queryVector: number[], options: DualSearchOptions): Promise<[VectorSearchResult[], VectorSearchResult[]]> {
    // 自适应搜索：根据历史性能选择策略
    const hotSearchRatio = this.searchStats.hotSearches / Math.max(this.searchStats.totalSearches, 1);
    
    if (hotSearchRatio > 0.7) {
      // Hot Store搜索较多，优先搜索Hot Store
      return await this.searchParallel(queryVector, options);
    } else {
      // 平衡搜索
      return await this.searchParallel(queryVector, options);
    }
  }

  private mergeResults(
    hotResults: VectorSearchResult[],
    coldResults: VectorSearchResult[],
    options: DualSearchOptions
  ): VectorSearchResult[] {
    const hotWeight = options.hotStoreWeight || 0.6;
    const coldWeight = options.coldStoreWeight || 0.4;

    // 调整权重
    const weightedHotResults = hotResults.map(r => ({
      ...r,
      similarity: r.similarity * hotWeight
    }));

    const weightedColdResults = coldResults.map(r => ({
      ...r,
      similarity: r.similarity * coldWeight
    }));

    // 合并并排序
    const allResults = [...weightedHotResults, ...weightedColdResults];
    allResults.sort((a, b) => b.similarity - a.similarity);

    // 重新设置排名
    const maxResults = options.maxResults || 10;
    const finalResults = allResults.slice(0, maxResults);
    finalResults.forEach((result, index) => {
      result.rank = index + 1;
    });

    return finalResults;
  }

  private convertLegacyMetadata(legacyData: any): VectorStoreMetadata {
    // 转换遗留数据格式到新的元数据格式
    return {
      chunkId: legacyData.id || legacyData.chunkId || `legacy_${Date.now()}_${Math.random()}`,
      sourceDocumentId: legacyData.sourceDocumentId || 'legacy_document',
      sourceDocumentType: legacyData.sourceDocumentType || 'dialogue_history',
      createdAt: legacyData.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // 其他字段根据需要映射
      meaningScore: legacyData.meaningScore,
      emotionalTags: legacyData.emotionalTags,
      cognitiveThemes: legacyData.cognitiveThemes
    };
  }

  private startMaintenanceTasks(): void {
    // 每天执行一次维护
    setInterval(async () => {
      try {
        await this.performMaintenance();
      } catch (error) {
        console.error('定期维护失败:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24小时
  }
}

// 导出工厂函数
export function createDualVectorManager(
  hotStoreConfig: any,
  coldStoreConfig: any
): DualVectorManager {
  return new DualVectorManager(hotStoreConfig, coldStoreConfig);
}
