// 向后兼容适配器
// 确保现有的RAG系统代码可以无缝使用新的双向量数据库架构

import { DualVectorManager } from './dual-vector-manager';
import { VectorStoreConfig } from './interfaces';
import { ChunkPair, VectorSearchCandidate, StorageError } from '@/types/rag';
import { RAG_CONFIG } from '@/lib/config/rag-config';

/**
 * 遗留向量数据库适配器
 * 提供与原有vector-database.ts相同的接口，内部使用新的双向量架构
 */
export class LegacyVectorDatabaseAdapter {
  private dualManager: DualVectorManager;
  private initialized = false;
  private totalVectors = 0;
  private nextIndex = 0;
  private chunkIdMap = new Map<number, string>();

  constructor() {
    // 创建Hot Store和Cold Store配置
    const hotStoreConfig: VectorStoreConfig = {
      name: 'hot_store',
      type: 'hot',
      dimensions: RAG_CONFIG.embedding.dimensions,
      storageBackend: 'indexeddb',
      enableCache: true,
      cacheSize: 500,
      temperatureDecay: {
        enabled: true,
        decayRate: 0.1,
        minTemperature: 0.1,
        decayInterval: 6
      },
      lruEviction: {
        enabled: true,
        maxSize: 1000,
        evictionBatchSize: 50
      }
    };

    const coldStoreConfig: VectorStoreConfig = {
      name: 'cold_store',
      type: 'cold',
      dimensions: RAG_CONFIG.embedding.dimensions,
      storageBackend: 'indexeddb',
      compression: {
        enabled: true,
        algorithm: 'gzip'
      },
      indexing: {
        algorithm: 'ivf',
        parameters: {
          nlist: 100,
          nprobe: 10,
          maxLeafSize: 50
        }
      }
    };

    this.dualManager = new DualVectorManager(hotStoreConfig, coldStoreConfig);
  }

  /**
   * 初始化向量数据库（兼容原有接口）
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔄 初始化遗留适配器...');
      
      await this.dualManager.initialize();
      
      // 迁移现有数据（如果有）
      await this.migrateExistingData();
      
      this.initialized = true;
      console.log('✅ 遗留适配器初始化完成');
    } catch (error) {
      throw new StorageError('遗留适配器初始化失败', error);
    }
  }

  /**
   * 搜索向量（兼容原有接口）
   */
  async search(queryVector: number[], topK: number): Promise<VectorSearchCandidate[]> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      const searchResult = await this.dualManager.searchVector(queryVector, {
        searchStrategy: 'adaptive',
        maxResults: topK,
        hotStoreWeight: 0.6,
        coldStoreWeight: 0.4,
        mergeStrategy: 'score_based'
      });

      // 转换为遗留格式
      const candidates: VectorSearchCandidate[] = searchResult.mergedResults.map(result => ({
        chunkPair: this.convertToChunkPair(result),
        similarity: result.similarity,
        rank: result.rank,
        vector: result.vector // 添加向量字段以兼容现有代码
      }));

      console.log(`🔍 遗留适配器搜索完成: ${candidates.length} 个结果`);
      return candidates;
    } catch (error) {
      throw new StorageError('向量搜索失败', error);
    }
  }

  /**
   * 添加向量（兼容原有接口）
   */
  async add(vector: number[], metadata: any): Promise<string> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      const chunkId = metadata.chunkId;
      if (!chunkId) {
        throw new StorageError('缺少chunkId元数据');
      }

      // 转换元数据格式
      const vectorMetadata = this.convertToVectorMetadata(metadata);
      
      // 添加到双向量系统
      const resultId = await this.dualManager.addVector(vector, vectorMetadata);
      
      // 更新映射（保持兼容性）
      this.chunkIdMap.set(this.nextIndex, chunkId);
      this.nextIndex++;
      this.totalVectors++;

      console.log(`➕ 遗留适配器添加向量: ${chunkId}`);
      return resultId;
    } catch (error) {
      throw new StorageError('添加向量失败', error);
    }
  }

  /**
   * 批量添加向量（兼容原有接口）
   */
  async addBatch(vectors: Array<{ vector: number[]; metadata: any }>): Promise<string[]> {
    const chunkIds: string[] = [];
    
    for (const item of vectors) {
      const chunkId = await this.add(item.vector, item.metadata);
      chunkIds.push(chunkId);
    }
    
    console.log(`➕ 遗留适配器批量添加: ${chunkIds.length} 个向量`);
    return chunkIds;
  }

  /**
   * 更新向量（兼容原有接口）
   */
  async update(id: string, vector: number[], metadata: any): Promise<void> {
    console.warn('⚠️ 遗留适配器: 向量更新功能已重新实现');
    
    try {
      const vectorMetadata = this.convertToVectorMetadata(metadata);
      
      // 在双向量系统中，更新操作需要先删除再添加
      // 这是因为数据可能需要在Hot/Cold Store之间迁移
      await this.dualManager.addVector(vector, vectorMetadata);
      
      console.log(`🔄 遗留适配器更新向量: ${id}`);
    } catch (error) {
      throw new StorageError('向量更新失败', error);
    }
  }

  /**
   * 删除向量（兼容原有接口）
   */
  async delete(id: string): Promise<void> {
    console.warn('⚠️ 遗留适配器: 向量删除功能需要在两个存储中查找');
    
    try {
      // 尝试从Hot Store删除
      try {
        await this.dualManager.hotStore.delete(id);
        console.log(`🗑️ 从Hot Store删除向量: ${id}`);
        return;
      } catch (hotError) {
        // Hot Store中没有，尝试Cold Store
      }
      
      // 尝试从Cold Store删除
      try {
        await this.dualManager.coldStore.delete(id);
        console.log(`🗑️ 从Cold Store删除向量: ${id}`);
      } catch (coldError) {
        throw new StorageError(`向量不存在: ${id}`);
      }
    } catch (error) {
      throw new StorageError('向量删除失败', error);
    }
  }

  /**
   * 清空所有向量（兼容原有接口）
   */
  async clear(): Promise<void> {
    try {
      await Promise.all([
        this.dualManager.hotStore.clear(),
        this.dualManager.coldStore.clear()
      ]);
      
      // 重置映射
      this.chunkIdMap.clear();
      this.nextIndex = 0;
      this.totalVectors = 0;
      
      console.log('🗑️ 遗留适配器已清空所有向量');
    } catch (error) {
      throw new StorageError('清空向量数据库失败', error);
    }
  }

  /**
   * 重建索引（兼容原有接口）
   */
  async rebuild(): Promise<void> {
    console.log('🔄 遗留适配器开始重建索引...');
    
    try {
      // 优化两个存储的索引
      await Promise.all([
        this.dualManager.hotStore.optimize(),
        this.dualManager.coldStore.optimize()
      ]);
      
      console.log('✅ 遗留适配器索引重建完成');
    } catch (error) {
      throw new StorageError('重建向量索引失败', error);
    }
  }

  /**
   * 获取统计信息（兼容原有接口）
   */
  getStats() {
    return {
      initialized: this.initialized,
      totalVectors: this.totalVectors,
      dimensions: RAG_CONFIG.embedding.dimensions,
      nextIndex: this.nextIndex,
      mappingSize: this.chunkIdMap.size
    };
  }

  /**
   * 获取详细系统统计（新增功能）
   */
  async getDetailedStats() {
    if (!this.initialized) {
      return null;
    }
    
    return await this.dualManager.getSystemStats();
  }

  /**
   * 执行系统维护（新增功能）
   */
  async performMaintenance() {
    if (!this.initialized) {
      return null;
    }
    
    return await this.dualManager.performMaintenance();
  }

  // 私有辅助方法
  private convertToChunkPair(result: any): ChunkPair {
    // 从向量搜索结果转换为ChunkPair格式
    const metadata = result.metadata;
    
    return {
      id: result.chunkId,
      parentChunk: metadata.parentChunk || '',
      childChunk: metadata.childChunk || '',
      vector: result.vector,
      sourceDocumentId: metadata.sourceDocumentId,
      sourceDocumentType: metadata.sourceDocumentType,
      metadata: {
        chunkIndex: metadata.chunkIndex || 0,
        parentLength: metadata.parentLength || 0,
        childLength: metadata.childLength || 0,
        emotionalTags: metadata.emotionalTags,
        cognitiveThemes: metadata.cognitiveThemes,
        meaningScore: metadata.meaningScore,
        lastAccessTime: metadata.lastAccessTime,
        accessCount: metadata.accessCount
      },
      createdAt: metadata.createdAt,
      updatedAt: metadata.updatedAt
    };
  }

  private convertToVectorMetadata(legacyMetadata: any): any {
    // 从遗留元数据格式转换为新的向量元数据格式
    return {
      chunkId: legacyMetadata.chunkId,
      sourceDocumentId: legacyMetadata.sourceDocumentId || 'unknown',
      sourceDocumentType: legacyMetadata.sourceDocumentType || 'dialogue_history',
      createdAt: legacyMetadata.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      
      // 扩展字段
      parentChunk: legacyMetadata.parentChunk,
      childChunk: legacyMetadata.childChunk,
      meaningScore: legacyMetadata.meaningScore,
      emotionalTags: legacyMetadata.emotionalTags,
      cognitiveThemes: legacyMetadata.cognitiveThemes,
      
      // 访问统计
      accessCount: legacyMetadata.accessCount || 0,
      lastAccessTime: legacyMetadata.lastAccessTime
    };
  }

  private async migrateExistingData(): Promise<void> {
    try {
      // TODO: 实现从现有存储系统迁移数据的逻辑
      // 这里可以读取现有的IndexedDB数据并迁移到新系统
      console.log('📦 检查是否有现有数据需要迁移...');
      
      // 暂时跳过迁移，在实际部署时实现
      console.log('✅ 数据迁移检查完成');
    } catch (error) {
      console.warn('⚠️ 数据迁移过程中出现警告:', error);
      // 迁移失败不应该阻止系统启动
    }
  }
}

// 导出单例实例以保持向后兼容
export const legacyVectorDatabase = new LegacyVectorDatabaseAdapter();
