// 全局唯一ID系统 - YYYYMMDD-T### 格式
// 确保所有用户输入和衍生内容的可追溯性

import { idStorageManager } from '@/lib/storage/id-storage';

export interface GlobalIdMetadata {
  id: string; // YYYYMMDD-T### 格式
  date: string; // YYYYMMDD
  turnNumber: number; // ###
  type: 'user_input' | 'assistant_response' | 'derived_chunk' | 'insight' | 'analysis';
  parentId?: string; // 父ID，用于建立层次关系
  childIds?: string[]; // 子ID列表
  createdAt: Date;
  
  // 数据血缘
  sourceChain: string[]; // 完整的来源链
  derivationLevel: number; // 衍生层级 (0=原始输入, 1=直接衍生, 2=二级衍生...)
  
  // 内容元数据
  contentType: 'text' | 'chunk' | 'vector' | 'metadata';
  contentLength: number;
  contentHash?: string; // 内容哈希，用于去重
}

export interface DailyIdCounter {
  date: string; // YYYYMMDD
  currentTurn: number; // 当前轮次
  totalInputs: number; // 当天总输入数
  totalDerivations: number; // 当天总衍生数
  lastUpdated: Date;
}

/**
 * 全局ID管理器
 * 负责生成、验证和管理YYYYMMDD-T###格式的唯一标识符
 */
export class GlobalIdManager {
  private static instance: GlobalIdManager;
  private dailyCounters: Map<string, DailyIdCounter> = new Map();
  private idRegistry: Map<string, GlobalIdMetadata> = new Map();
  private initialized = false;

  private constructor() {}

  static getInstance(): GlobalIdManager {
    if (!GlobalIdManager.instance) {
      GlobalIdManager.instance = new GlobalIdManager();
    }
    return GlobalIdManager.instance;
  }

  /**
   * 初始化ID管理器
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🆔 初始化全局ID管理器...');

      // 初始化存储管理器
      await idStorageManager.initialize();

      // 从存储中恢复计数器和注册表
      await this.loadFromStorage();

      // 清理过期数据（保留最近30天）
      await this.cleanupOldData();

      this.initialized = true;
      console.log('🆔 全局ID管理器初始化完成');
    } catch (error) {
      console.error('❌ 全局ID管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 生成新的用户输入ID
   */
  async generateUserInputId(): Promise<string> {
    const today = this.getTodayString();
    const counter = await this.getDailyCounter(today);
    
    counter.currentTurn++;
    counter.totalInputs++;
    counter.lastUpdated = new Date();
    
    const id = this.formatId(today, counter.currentTurn);
    
    // 注册ID元数据
    const metadata: GlobalIdMetadata = {
      id,
      date: today,
      turnNumber: counter.currentTurn,
      type: 'user_input',
      createdAt: new Date(),
      sourceChain: [id],
      derivationLevel: 0,
      contentType: 'text',
      contentLength: 0 // 将在实际使用时更新
    };
    
    this.idRegistry.set(id, metadata);
    await this.saveToStorage();
    
    console.log(`🆔 生成用户输入ID: ${id}`);
    return id;
  }

  /**
   * 生成衍生内容ID
   */
  async generateDerivedId(
    parentId: string, 
    type: GlobalIdMetadata['type'],
    contentType: GlobalIdMetadata['contentType'] = 'text'
  ): Promise<string> {
    const parentMetadata = this.idRegistry.get(parentId);
    if (!parentMetadata) {
      throw new Error(`父ID不存在: ${parentId}`);
    }

    const today = this.getTodayString();
    const counter = await this.getDailyCounter(today);
    
    // 衍生内容使用相同的turn number，但添加后缀
    const derivationCount = this.getDerivedCountForTurn(today, parentMetadata.turnNumber);
    const id = this.formatDerivedId(today, parentMetadata.turnNumber, derivationCount + 1);
    
    counter.totalDerivations++;
    counter.lastUpdated = new Date();
    
    // 注册ID元数据
    const metadata: GlobalIdMetadata = {
      id,
      date: today,
      turnNumber: parentMetadata.turnNumber,
      type,
      parentId,
      createdAt: new Date(),
      sourceChain: [...parentMetadata.sourceChain, id],
      derivationLevel: parentMetadata.derivationLevel + 1,
      contentType,
      contentLength: 0
    };
    
    this.idRegistry.set(id, metadata);
    
    // 更新父ID的子ID列表
    if (!parentMetadata.childIds) {
      parentMetadata.childIds = [];
    }
    parentMetadata.childIds.push(id);
    
    await this.saveToStorage();
    
    console.log(`🆔 生成衍生ID: ${id} (父ID: ${parentId})`);
    return id;
  }

  /**
   * 验证ID格式
   */
  validateId(id: string): boolean {
    // 基础格式: YYYYMMDD-T###
    const basicPattern = /^\d{8}-T\d{3}$/;
    // 衍生格式: YYYYMMDD-T###-D###
    const derivedPattern = /^\d{8}-T\d{3}-D\d{3}$/;
    
    return basicPattern.test(id) || derivedPattern.test(id);
  }

  /**
   * 更新ID的内容元数据
   */
  async updateContentMetadata(
    id: string,
    updates: {
      contentLength?: number;
      contentHash?: string;
      contentType?: GlobalIdMetadata['contentType']
    }
  ): Promise<void> {
    const metadata = this.idRegistry.get(id);
    if (!metadata) {
      throw new Error(`ID不存在: ${id}`);
    }

    // 更新元数据
    if (updates.contentLength !== undefined) {
      metadata.contentLength = updates.contentLength;
    }
    if (updates.contentHash !== undefined) {
      metadata.contentHash = updates.contentHash;
    }
    if (updates.contentType !== undefined) {
      metadata.contentType = updates.contentType;
    }

    // 保存到存储
    await this.saveToStorage();

    console.log(`🆔 更新ID元数据: ${id}`, updates);
  }

  /**
   * 获取ID元数据
   */
  getIdMetadata(id: string): GlobalIdMetadata | null {
    return this.idRegistry.get(id) || null;
  }

  /**
   * 获取ID的完整血缘链
   */
  getSourceChain(id: string): string[] {
    const metadata = this.idRegistry.get(id);
    return metadata?.sourceChain || [];
  }

  /**
   * 获取ID的所有子ID
   */
  getChildIds(id: string, recursive = false): string[] {
    const metadata = this.idRegistry.get(id);
    if (!metadata?.childIds) return [];
    
    if (!recursive) return metadata.childIds;
    
    // 递归获取所有后代ID
    const allChildren: string[] = [];
    const queue = [...metadata.childIds];
    
    while (queue.length > 0) {
      const childId = queue.shift()!;
      allChildren.push(childId);
      
      const childMetadata = this.idRegistry.get(childId);
      if (childMetadata?.childIds) {
        queue.push(...childMetadata.childIds);
      }
    }
    
    return allChildren;
  }

  /**
   * 获取今日统计
   */
  getTodayStats(): DailyIdCounter | null {
    const today = this.getTodayString();
    return this.dailyCounters.get(today) || null;
  }

  /**
   * 获取历史统计
   */
  getHistoryStats(days = 7): DailyIdCounter[] {
    const stats: DailyIdCounter[] = [];
    const today = new Date();
    
    for (let i = 0; i < days; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateString = this.formatDate(date);
      
      const counter = this.dailyCounters.get(dateString);
      if (counter) {
        stats.push(counter);
      }
    }
    
    return stats.reverse(); // 按时间正序
  }

  /**
   * 清理过期数据
   */
  private async cleanupOldData(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 30); // 保留30天
    const cutoffString = this.formatDate(cutoffDate);

    let cleanedCounters = 0;
    let cleanedIds = 0;

    // 清理过期的日计数器
    for (const [date, counter] of this.dailyCounters.entries()) {
      if (date < cutoffString) {
        this.dailyCounters.delete(date);
        cleanedCounters++;
      }
    }

    // 清理过期的ID注册表
    for (const [id, metadata] of this.idRegistry.entries()) {
      if (metadata.date < cutoffString) {
        this.idRegistry.delete(id);
        cleanedIds++;
      }
    }

    if (cleanedCounters > 0 || cleanedIds > 0) {
      console.log(`🧹 清理了 ${cutoffString} 之前的过期数据: ${cleanedIds} 个ID, ${cleanedCounters} 个计数器`);
      // 保存清理后的数据
      await this.saveToStorage();
    }
  }

  /**
   * 强制保存数据到存储
   */
  async forceSave(): Promise<void> {
    await this.saveToStorage();
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats() {
    return await idStorageManager.getStorageStats();
  }

  /**
   * 销毁ID管理器
   */
  async destroy(): Promise<void> {
    if (this.initialized) {
      // 保存最新数据
      await this.saveToStorage();

      // 销毁存储管理器
      await idStorageManager.destroy();

      this.initialized = false;
      console.log('🗑️ 全局ID管理器已销毁');
    }
  }

  // 私有辅助方法
  private getTodayString(): string {
    return this.formatDate(new Date());
  }

  private formatDate(date: Date): string {
    return date.toISOString().slice(0, 10).replace(/-/g, '');
  }

  private formatId(date: string, turn: number): string {
    return `${date}-T${turn.toString().padStart(3, '0')}`;
  }

  private formatDerivedId(date: string, turn: number, derivation: number): string {
    return `${date}-T${turn.toString().padStart(3, '0')}-D${derivation.toString().padStart(3, '0')}`;
  }

  private async getDailyCounter(date: string): Promise<DailyIdCounter> {
    let counter = this.dailyCounters.get(date);
    if (!counter) {
      counter = {
        date,
        currentTurn: 0,
        totalInputs: 0,
        totalDerivations: 0,
        lastUpdated: new Date()
      };
      this.dailyCounters.set(date, counter);
    }
    return counter;
  }

  private getDerivedCountForTurn(date: string, turn: number): number {
    let count = 0;
    const turnPrefix = `${date}-T${turn.toString().padStart(3, '0')}-D`;
    
    for (const id of this.idRegistry.keys()) {
      if (id.startsWith(turnPrefix)) {
        count++;
      }
    }
    
    return count;
  }

  private async loadFromStorage(): Promise<void> {
    try {
      const { idRegistry, dailyCounters } = await idStorageManager.loadIdRegistry();

      // 恢复数据到内存
      this.idRegistry = idRegistry;
      this.dailyCounters = dailyCounters;

      console.log('📥 从存储加载ID数据...');
    } catch (error) {
      console.error('❌ 加载ID数据失败:', error);
      // 如果加载失败，使用空的注册表
      this.idRegistry = new Map();
      this.dailyCounters = new Map();
    }
  }

  private async saveToStorage(): Promise<void> {
    try {
      await idStorageManager.saveIdRegistry(this.idRegistry, this.dailyCounters);
      console.log('💾 保存ID数据到存储...');
    } catch (error) {
      console.error('❌ 保存ID数据失败:', error);
      // 不抛出错误，避免影响主要功能
    }
  }
}

// 导出单例实例
export const globalIdManager = GlobalIdManager.getInstance();
