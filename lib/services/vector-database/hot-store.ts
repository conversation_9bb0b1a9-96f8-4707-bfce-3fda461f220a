// Hot Store - 每日洞察数据库实现
// 高频访问的热数据存储，支持温度衰减机制和LRU淘汰策略

import {
  IVectorStore,
  VectorStoreConfig,
  VectorStoreMetadata,
  VectorSearchResult,
  VectorStoreStats,
  HealthStatus,
  SearchOptions,
  MetadataFilter,
  VectorStoreError
} from './interfaces';
import { globalIdManager } from './global-id-system';

interface HotStoreItem {
  chunkId: string;
  vector: number[];
  metadata: VectorStoreMetadata;
  temperature: number; // 温度值 (0-1)
  lastAccess: Date;
  accessCount: number;
  addedAt: Date;
}

/**
 * Hot Store 实现
 * 专门用于存储每日洞察和近期对话历史的高频访问数据
 */
export class HotVectorStore implements IVectorStore {
  private items: Map<string, HotStoreItem> = new Map();
  private accessOrder: string[] = []; // LRU访问顺序
  private lastDecayTime: Date = new Date();
  private lastMaintenanceTime: Date = new Date();
  
  public readonly config: VectorStoreConfig;
  public initialized = false;

  constructor(config: VectorStoreConfig) {
    this.config = {
      ...config,
      type: 'hot',
      // 确保Hot Store的默认配置
      temperatureDecay: config.temperatureDecay || {
        enabled: true,
        decayRate: 0.1, // 每天衰减10%
        minTemperature: 0.1, // 最低温度阈值
        decayInterval: 6 // 每6小时检查一次
      },
      lruEviction: config.lruEviction || {
        enabled: true,
        maxSize: 1000, // 最大1000个向量
        evictionBatchSize: 50 // 每次淘汰50个
      }
    };
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔥 初始化Hot Store...');
      
      // 初始化全局ID管理器
      await globalIdManager.initialize();
      
      // 从存储中恢复数据
      await this.loadFromStorage();
      
      // 启动后台维护任务
      this.startMaintenanceTasks();
      
      this.initialized = true;
      console.log(`✅ Hot Store初始化完成，当前向量数: ${this.items.size}`);
    } catch (error) {
      throw new VectorStoreError('Hot Store初始化失败', 'INIT_ERROR', 'hot', error);
    }
  }

  async add(vector: number[], metadata: VectorStoreMetadata): Promise<string> {
    if (!this.initialized) {
      throw new VectorStoreError('Hot Store未初始化', 'NOT_INITIALIZED', 'hot');
    }

    // 确保全局ID管理器已初始化
    await globalIdManager.initialize();

    // 生成全局ID（如果没有提供）
    if (!metadata.globalId) {
      if (metadata.sourceDocumentId) {
        // 如果有源文档ID，基于它生成衍生ID
        metadata.globalId = await globalIdManager.generateDerivedId(
          metadata.sourceDocumentId,
          'derived_chunk',
          'vector'
        );
      } else {
        // 如果没有源文档ID，生成新的基础ID
        if (metadata.sourceDocumentType === 'daily_insight_hot' ||
            metadata.sourceDocumentType === 'dialogue_history') {
          metadata.globalId = await globalIdManager.generateUserInputId();
        } else {
          // 对于其他类型，生成衍生ID
          const parentId = await globalIdManager.generateUserInputId();
          metadata.globalId = await globalIdManager.generateDerivedId(
            parentId,
            'derived_chunk',
            'vector'
          );
        }
      }
    }

    // 更新全局ID注册表中的内容元数据
    await globalIdManager.updateContentMetadata(metadata.globalId, {
      contentType: 'vector',
      contentLength: vector.length
    });

    const chunkId = metadata.chunkId;
    const now = new Date();
    
    // 创建热存储项
    const item: HotStoreItem = {
      chunkId,
      vector: [...vector], // 复制向量数据
      metadata: { ...metadata },
      temperature: 1.0, // 新添加的数据温度最高
      lastAccess: now,
      accessCount: 1,
      addedAt: now
    };

    // 检查是否需要淘汰旧数据
    await this.checkEviction();
    
    // 添加到存储
    this.items.set(chunkId, item);
    this.updateAccessOrder(chunkId);
    
    // 保存到持久化存储
    await this.saveToStorage();
    
    console.log(`🔥 Hot Store添加向量: ${chunkId} (温度: ${item.temperature}, 全局ID: ${metadata.globalId})`);
    return chunkId;
  }

  async addBatch(items: Array<{ vector: number[]; metadata: VectorStoreMetadata }>): Promise<string[]> {
    const chunkIds: string[] = [];
    
    for (const item of items) {
      const chunkId = await this.add(item.vector, item.metadata);
      chunkIds.push(chunkId);
    }
    
    return chunkIds;
  }

  async search(queryVector: number[], topK: number, options?: SearchOptions): Promise<VectorSearchResult[]> {
    if (!this.initialized) {
      throw new VectorStoreError('Hot Store未初始化', 'NOT_INITIALIZED', 'hot');
    }

    const startTime = Date.now();
    const results: VectorSearchResult[] = [];
    
    // 计算所有向量的相似度
    for (const [chunkId, item] of this.items.entries()) {
      const similarity = this.calculateCosineSimilarity(queryVector, item.vector);
      
      // 应用相似度阈值过滤
      if (options?.similarityThreshold && similarity < options.similarityThreshold) {
        continue;
      }
      
      // 应用元数据过滤
      if (options?.filters && !this.matchesFilters(item.metadata, options.filters)) {
        continue;
      }
      
      results.push({
        chunkId,
        vector: item.vector,
        metadata: item.metadata,
        similarity,
        rank: 0 // 将在排序后设置
      });
      
      // 更新访问统计
      item.lastAccess = new Date();
      item.accessCount++;
      this.updateAccessOrder(chunkId);
    }
    
    // 排序结果
    results.sort((a, b) => {
      if (options?.sortBy === 'temperature') {
        const tempA = this.items.get(a.chunkId)?.temperature || 0;
        const tempB = this.items.get(b.chunkId)?.temperature || 0;
        return options.sortOrder === 'asc' ? tempA - tempB : tempB - tempA;
      }
      // 默认按相似度排序
      return b.similarity - a.similarity;
    });
    
    // 设置排名并限制结果数量
    const limitedResults = results.slice(0, Math.min(topK, options?.maxResults || topK));
    limitedResults.forEach((result, index) => {
      result.rank = index + 1;
    });
    
    const searchTime = Date.now() - startTime;
    console.log(`🔍 Hot Store搜索完成: ${limitedResults.length}/${results.length} 结果 (${searchTime}ms)`);
    
    return limitedResults;
  }

  async searchByMetadata(filters: MetadataFilter[], options?: SearchOptions): Promise<VectorSearchResult[]> {
    const results: VectorSearchResult[] = [];
    
    for (const [chunkId, item] of this.items.entries()) {
      if (this.matchesFilters(item.metadata, filters)) {
        results.push({
          chunkId,
          vector: item.vector,
          metadata: item.metadata,
          similarity: 1.0, // 元数据搜索不计算相似度
          rank: results.length + 1
        });
        
        // 更新访问统计
        item.lastAccess = new Date();
        item.accessCount++;
        this.updateAccessOrder(chunkId);
      }
    }
    
    return results.slice(0, options?.maxResults || results.length);
  }

  async update(chunkId: string, vector: number[], metadata: VectorStoreMetadata): Promise<void> {
    const item = this.items.get(chunkId);
    if (!item) {
      throw new VectorStoreError(`向量不存在: ${chunkId}`, 'NOT_FOUND', 'hot');
    }
    
    // 更新数据
    item.vector = [...vector];
    item.metadata = { ...metadata };
    item.lastAccess = new Date();
    item.accessCount++;
    
    // 提升温度（因为被更新了）
    item.temperature = Math.min(1.0, item.temperature + 0.2);
    
    this.updateAccessOrder(chunkId);
    await this.saveToStorage();
    
    console.log(`🔥 Hot Store更新向量: ${chunkId}`);
  }

  async delete(chunkId: string): Promise<void> {
    if (!this.items.has(chunkId)) {
      throw new VectorStoreError(`向量不存在: ${chunkId}`, 'NOT_FOUND', 'hot');
    }
    
    this.items.delete(chunkId);
    this.accessOrder = this.accessOrder.filter(id => id !== chunkId);
    
    await this.saveToStorage();
    console.log(`🗑️ Hot Store删除向量: ${chunkId}`);
  }

  async clear(): Promise<void> {
    this.items.clear();
    this.accessOrder = [];
    await this.saveToStorage();
    console.log('🧹 Hot Store已清空');
  }

  async getStats(): Promise<VectorStoreStats> {
    const now = new Date();
    const documentTypes: Record<string, number> = {};
    let totalAccessCount = 0;
    
    for (const item of this.items.values()) {
      const type = item.metadata.sourceDocumentType;
      documentTypes[type] = (documentTypes[type] || 0) + 1;
      totalAccessCount += item.accessCount;
    }
    
    return {
      totalVectors: this.items.size,
      dimensions: this.config.dimensions,
      storageSize: this.estimateStorageSize(),
      lastUpdated: now.toISOString(),
      documentTypeDistribution: documentTypes,
      averageQueryTime: 0, // TODO: 实现查询时间统计
      totalQueries: totalAccessCount,
      cacheHitRate: this.calculateCacheHitRate()
    };
  }

  async getHealth(): Promise<HealthStatus> {
    const stats = await this.getStats();
    const maxSize = this.config.lruEviction?.maxSize || 1000;
    const utilizationRate = stats.totalVectors / maxSize;
    
    let status: HealthStatus['status'] = 'healthy';
    const errors: string[] = [];
    
    if (utilizationRate > 0.9) {
      status = 'degraded';
      errors.push('存储空间使用率过高');
    }
    
    if (utilizationRate > 0.95) {
      status = 'unhealthy';
      errors.push('存储空间即将耗尽');
    }
    
    return {
      status,
      details: {
        vectorCount: stats.totalVectors,
        indexHealth: 'good',
        storageHealth: utilizationRate > 0.9 ? 'full' : 'good',
        lastMaintenance: this.lastMaintenanceTime.toISOString(),
        errors: errors.length > 0 ? errors : undefined
      }
    };
  }

  async optimize(): Promise<void> {
    console.log('🔧 Hot Store优化开始...');
    
    // 执行温度衰减
    const decayedCount = await this.performDecay();
    
    // 执行LRU淘汰
    const evictedCount = await this.performEviction();
    
    this.lastMaintenanceTime = new Date();
    await this.saveToStorage();
    
    console.log(`✅ Hot Store优化完成: 衰减${decayedCount}个, 淘汰${evictedCount}个`);
  }

  async compact(): Promise<void> {
    // Hot Store使用内存存储，压缩操作主要是清理无效数据
    const beforeSize = this.items.size;
    
    // 移除温度过低的项目
    const minTemp = this.config.temperatureDecay?.minTemperature || 0.1;
    for (const [chunkId, item] of this.items.entries()) {
      if (item.temperature < minTemp) {
        this.items.delete(chunkId);
        this.accessOrder = this.accessOrder.filter(id => id !== chunkId);
      }
    }
    
    const afterSize = this.items.size;
    console.log(`🗜️ Hot Store压缩完成: ${beforeSize} -> ${afterSize}`);
  }

  // Hot Store特定方法
  async updateTemperature(chunkId: string, temperature: number): Promise<void> {
    const item = this.items.get(chunkId);
    if (!item) {
      throw new VectorStoreError(`向量不存在: ${chunkId}`, 'NOT_FOUND', 'hot');
    }
    
    item.temperature = Math.max(0, Math.min(1, temperature));
    await this.saveToStorage();
  }

  async performDecay(): Promise<number> {
    if (!this.config.temperatureDecay?.enabled) return 0;
    
    const now = new Date();
    const hoursSinceLastDecay = (now.getTime() - this.lastDecayTime.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceLastDecay < (this.config.temperatureDecay.decayInterval || 6)) {
      return 0; // 还没到衰减时间
    }
    
    const decayRate = this.config.temperatureDecay.decayRate || 0.1;
    const minTemperature = this.config.temperatureDecay.minTemperature || 0.1;
    let decayedCount = 0;
    
    for (const item of this.items.values()) {
      const oldTemperature = item.temperature;
      item.temperature = Math.max(minTemperature, item.temperature * (1 - decayRate));
      
      if (item.temperature !== oldTemperature) {
        decayedCount++;
      }
    }
    
    this.lastDecayTime = now;
    console.log(`🌡️ 温度衰减完成: ${decayedCount}个向量`);
    return decayedCount;
  }

  async evictColdest(count: number): Promise<string[]> {
    // 按温度排序，选择最冷的项目
    const sortedItems = Array.from(this.items.entries())
      .sort(([, a], [, b]) => a.temperature - b.temperature);
    
    const evictedIds: string[] = [];
    const toEvict = sortedItems.slice(0, count);
    
    for (const [chunkId] of toEvict) {
      this.items.delete(chunkId);
      this.accessOrder = this.accessOrder.filter(id => id !== chunkId);
      evictedIds.push(chunkId);
    }
    
    console.log(`❄️ 淘汰最冷向量: ${evictedIds.length}个`);
    return evictedIds;
  }

  // 私有辅助方法
  private async checkEviction(): Promise<void> {
    const maxSize = this.config.lruEviction?.maxSize || 1000;
    if (this.items.size >= maxSize) {
      const batchSize = this.config.lruEviction?.evictionBatchSize || 50;
      await this.evictColdest(batchSize);
    }
  }

  private async performEviction(): Promise<number> {
    const maxSize = this.config.lruEviction?.maxSize || 1000;
    if (this.items.size <= maxSize) return 0;
    
    const excessCount = this.items.size - maxSize;
    const evictedIds = await this.evictColdest(excessCount);
    return evictedIds.length;
  }

  private updateAccessOrder(chunkId: string): void {
    // 移除旧位置
    this.accessOrder = this.accessOrder.filter(id => id !== chunkId);
    // 添加到最前面（最近访问）
    this.accessOrder.unshift(chunkId);
  }

  private calculateCosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private matchesFilters(metadata: VectorStoreMetadata, filters: MetadataFilter[]): boolean {
    return filters.every(filter => {
      const value = (metadata as any)[filter.field];
      
      switch (filter.operator) {
        case 'eq': return value === filter.value;
        case 'ne': return value !== filter.value;
        case 'gt': return value > filter.value;
        case 'gte': return value >= filter.value;
        case 'lt': return value < filter.value;
        case 'lte': return value <= filter.value;
        case 'in': return Array.isArray(filter.value) && filter.value.includes(value);
        case 'nin': return Array.isArray(filter.value) && !filter.value.includes(value);
        case 'contains': return Array.isArray(value) && value.includes(filter.value);
        default: return false;
      }
    });
  }

  private estimateStorageSize(): number {
    // 粗略估算存储大小（MB）
    const itemSize = this.config.dimensions * 4 + 1000; // 向量 + 元数据
    return (this.items.size * itemSize) / (1024 * 1024);
  }

  private calculateCacheHitRate(): number {
    // TODO: 实现缓存命中率计算
    return 0.85; // 临时返回值
  }

  private startMaintenanceTasks(): void {
    // 每小时执行一次维护任务
    setInterval(async () => {
      try {
        await this.optimize();
      } catch (error) {
        console.error('Hot Store维护任务失败:', error);
      }
    }, 60 * 60 * 1000); // 1小时
  }

  private async loadFromStorage(): Promise<void> {
    // TODO: 从IndexedDB加载数据
    console.log('📥 从存储加载Hot Store数据...');
  }

  private async saveToStorage(): Promise<void> {
    // TODO: 保存到IndexedDB
    console.log('💾 保存Hot Store数据...');
  }

  // 获取所有热数据项（用于迁移到Cold Store）
  getAllItems(): HotStoreItem[] {
    return Array.from(this.items.values());
  }

  // 批量删除项目（用于迁移后清理）
  async removeBatch(chunkIds: string[]): Promise<void> {
    for (const chunkId of chunkIds) {
      this.items.delete(chunkId);
      this.accessOrder = this.accessOrder.filter(id => id !== chunkId);
    }
    await this.saveToStorage();
  }
}
