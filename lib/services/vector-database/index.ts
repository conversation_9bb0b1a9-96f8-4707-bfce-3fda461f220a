// 双向量数据库系统主入口
// 统一导出所有组件和接口，提供简化的使用方式

// 核心接口
export * from './interfaces';

// 实现类
export { HotVectorStore } from './hot-store';
export { ColdVectorStore } from './cold-store';
export { DualVectorManager, createDualVectorManager } from './dual-vector-manager';

// 全局ID系统
export { GlobalIdManager, globalIdManager } from './global-id-system';

// 向后兼容适配器
export { LegacyVectorDatabaseAdapter, legacyVectorDatabase } from './legacy-adapter';

// TODO: 重构后重新启用配置管理
// export {
//   dualVectorConfigManager,
//   getDualVectorConfig,
//   getHotStoreConfig,
//   getColdStoreConfig
// } from '@/lib/config/dual-vector-config';

import { DualVectorManager } from './dual-vector-manager';
// import { dualVectorConfigManager } from '@/lib/config/dual-vector-config';

/**
 * 双向量数据库系统单例
 * 提供全局访问点和简化的API
 */
class DualVectorSystem {
  private static instance: DualVectorSystem;
  private manager: DualVectorManager | null = null;
  private initialized = false;

  private constructor() {}

  static getInstance(): DualVectorSystem {
    if (!DualVectorSystem.instance) {
      DualVectorSystem.instance = new DualVectorSystem();
    }
    return DualVectorSystem.instance;
  }

  /**
   * 初始化双向量系统
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🚀 初始化双向量数据库系统...');

      // 使用模拟配置
      const hotStoreConfig = { name: 'hot_store', type: 'hot', dimensions: 1536 };
      const coldStoreConfig = { name: 'cold_store', type: 'cold', dimensions: 1536 };

      // 跳过配置验证（使用模拟配置）
      console.log('⚠️ 使用模拟配置，跳过验证');

      // 创建管理器
      this.manager = new DualVectorManager(hotStoreConfig, coldStoreConfig);
      await this.manager.initialize();

      this.initialized = true;
      
      // 输出配置摘要
      console.log('✅ 双向量数据库系统初始化完成');
      console.log('📊 使用模拟配置');
    } catch (error) {
      console.error('❌ 双向量数据库系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取管理器实例
   */
  getManager(): DualVectorManager {
    if (!this.manager) {
      throw new Error('双向量系统未初始化，请先调用 initialize()');
    }
    return this.manager;
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 添加向量（简化API）
   */
  async addVector(vector: number[], metadata: any): Promise<string> {
    return await this.getManager().addVector(vector, metadata);
  }

  /**
   * 批量添加向量（简化API）
   */
  async addBatch(items: Array<{ vector: number[]; metadata: any }>): Promise<string[]> {
    return await this.getManager().addBatch(items);
  }

  /**
   * 搜索向量（简化API）
   */
  async searchVector(queryVector: number[], options: any = {}) {
    const defaultOptions = {
      searchStrategy: 'adaptive',
      maxResults: 10,
      hotStoreWeight: 0.6,
      coldStoreWeight: 0.4,
      mergeStrategy: 'score_based',
      ...options
    };

    return await this.getManager().searchVector(queryVector, defaultOptions);
  }

  /**
   * 获取系统统计
   */
  async getStats() {
    return await this.getManager().getSystemStats();
  }

  /**
   * 执行维护
   */
  async performMaintenance() {
    return await this.getManager().performMaintenance();
  }

  /**
   * 迁移遗留数据
   */
  async migrateFromLegacy(legacyData: any[]) {
    return await this.getManager().migrateFromLegacy(legacyData);
  }

  /**
   * 重置系统（仅用于测试）
   */
  async reset(): Promise<void> {
    if (this.manager) {
      await Promise.all([
        this.manager.hotStore.clear(),
        this.manager.coldStore.clear()
      ]);
    }
    this.initialized = false;
    this.manager = null;
    console.log('🔄 双向量系统已重置');
  }
}

// 导出单例实例
export const dualVectorSystem = DualVectorSystem.getInstance();

/**
 * 便捷函数：初始化双向量系统
 */
export async function initializeDualVectorSystem(): Promise<void> {
  await dualVectorSystem.initialize();
}

/**
 * 便捷函数：获取双向量管理器
 */
export function getDualVectorManager(): DualVectorManager {
  return dualVectorSystem.getManager();
}

/**
 * 便捷函数：添加向量
 */
export async function addVector(vector: number[], metadata: any): Promise<string> {
  return await dualVectorSystem.addVector(vector, metadata);
}

/**
 * 便捷函数：搜索向量
 */
export async function searchVector(queryVector: number[], options: any = {}) {
  return await dualVectorSystem.searchVector(queryVector, options);
}

/**
 * 便捷函数：获取系统统计
 */
export async function getSystemStats() {
  return await dualVectorSystem.getStats();
}

/**
 * 便捷函数：执行系统维护
 */
export async function performSystemMaintenance() {
  return await dualVectorSystem.performMaintenance();
}

/**
 * 工厂函数：创建独立的双向量管理器实例
 */
export function createIndependentDualVectorManager(
  hotStoreConfig?: any,
  coldStoreConfig?: any
): DualVectorManager {
  const hotConfig = hotStoreConfig || { name: 'hot_store', type: 'hot', dimensions: 1536 };
  const coldConfig = coldStoreConfig || { name: 'cold_store', type: 'cold', dimensions: 1536 };

  return new DualVectorManager(hotConfig, coldConfig);
}

/**
 * 类型守卫：检查是否为双向量搜索结果
 */
export function isDualSearchResult(result: any): result is any {
  return result && 
         typeof result === 'object' &&
         'hotResults' in result &&
         'coldResults' in result &&
         'mergedResults' in result &&
         'searchStats' in result;
}

/**
 * 实用函数：格式化搜索统计
 */
export function formatSearchStats(stats: any): string {
  return `搜索统计: 总时间${stats.totalTime}ms (Hot: ${stats.hotSearchTime}ms, Cold: ${stats.coldSearchTime}ms), 结果数: Hot=${stats.hotHits}, Cold=${stats.coldHits}`;
}

/**
 * 实用函数：格式化系统统计
 */
export function formatSystemStats(stats: any): string {
  const hotPercent = stats.dataDistribution.hotPercentage.toFixed(1);
  const coldPercent = stats.dataDistribution.coldPercentage.toFixed(1);
  
  return `系统统计: 总向量${stats.totalVectors}个, 分布: Hot=${hotPercent}% (${stats.hotStore.totalVectors}个), Cold=${coldPercent}% (${stats.coldStore.totalVectors}个), 状态: ${stats.systemHealth.status}`;
}

// 默认导出双向量系统单例
export default dualVectorSystem;
