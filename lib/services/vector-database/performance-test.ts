// 双向量数据库性能测试工具
// 测试Hot Store和Cold Store的性能表现，优化查询速度和存储效率

import { DualVectorManager } from './dual-vector-manager';
// TODO: 重构后重新启用
// import { dualVectorConfigManager } from '@/lib/config/dual-vector-config';
import { VectorStoreMetadata } from './interfaces';

export interface PerformanceTestConfig {
  vectorCount: number; // 测试向量数量
  dimensions: number; // 向量维度
  searchQueries: number; // 搜索查询数量
  batchSize: number; // 批量操作大小
  testDuration: number; // 测试持续时间（秒）
}

export interface PerformanceTestResult {
  testConfig: PerformanceTestConfig;
  
  // 添加操作性能
  addPerformance: {
    totalTime: number; // 总时间（毫秒）
    averageTime: number; // 平均时间（毫秒）
    throughput: number; // 吞吐量（向量/秒）
    hotStoreCount: number; // Hot Store中的向量数
    coldStoreCount: number; // Cold Store中的向量数
  };
  
  // 搜索操作性能
  searchPerformance: {
    totalTime: number;
    averageTime: number;
    throughput: number; // 查询/秒
    hotStoreHits: number; // Hot Store命中数
    coldStoreHits: number; // Cold Store命中数
    parallelSearches: number; // 并行搜索数
  };
  
  // 内存使用情况
  memoryUsage: {
    hotStoreSize: number; // MB
    coldStoreSize: number; // MB
    totalSize: number; // MB
    compressionRatio: number; // 压缩比率
  };
  
  // 系统健康状态
  systemHealth: {
    hotStoreHealth: string;
    coldStoreHealth: string;
    overallHealth: string;
    errors: string[];
  };
  
  timestamp: string;
}

/**
 * 双向量数据库性能测试器
 */
export class DualVectorPerformanceTester {
  private manager: DualVectorManager;
  private testVectors: Array<{ vector: number[]; metadata: VectorStoreMetadata }> = [];
  private queryVectors: number[][] = [];

  constructor() {
    const hotConfig = { name: 'hot_store', type: 'hot', dimensions: 1536 };
    const coldConfig = { name: 'cold_store', type: 'cold', dimensions: 1536 };
    this.manager = new DualVectorManager(hotConfig, coldConfig);
  }

  /**
   * 运行完整的性能测试套件
   */
  async runPerformanceTest(config: PerformanceTestConfig): Promise<PerformanceTestResult> {
    console.log('🚀 开始双向量数据库性能测试...');
    console.log('📊 测试配置:', config);

    const startTime = Date.now();

    try {
      // 初始化系统
      await this.manager.initialize();

      // 生成测试数据
      await this.generateTestData(config);

      // 执行添加性能测试
      const addPerformance = await this.testAddPerformance(config);

      // 执行搜索性能测试
      const searchPerformance = await this.testSearchPerformance(config);

      // 获取内存使用情况
      const memoryUsage = await this.getMemoryUsage();

      // 获取系统健康状态
      const systemHealth = await this.getSystemHealth();

      const result: PerformanceTestResult = {
        testConfig: config,
        addPerformance,
        searchPerformance,
        memoryUsage,
        systemHealth,
        timestamp: new Date().toISOString()
      };

      const totalTime = Date.now() - startTime;
      console.log(`✅ 性能测试完成，总耗时: ${totalTime}ms`);
      console.log('📈 测试结果:', this.formatResults(result));

      return result;
    } catch (error) {
      console.error('❌ 性能测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试添加操作性能
   */
  private async testAddPerformance(config: PerformanceTestConfig) {
    console.log('📝 测试添加操作性能...');

    const startTime = Date.now();
    let hotStoreCount = 0;
    let coldStoreCount = 0;

    // 批量添加向量
    for (let i = 0; i < this.testVectors.length; i += config.batchSize) {
      const batch = this.testVectors.slice(i, i + config.batchSize);
      
      for (const item of batch) {
        await this.manager.addVector(item.vector, item.metadata);
        
        // 统计分布
        if (item.metadata.sourceDocumentType === 'daily_insight_hot' ||
            item.metadata.sourceDocumentType === 'dialogue_history') {
          hotStoreCount++;
        } else {
          coldStoreCount++;
        }
      }
    }

    const totalTime = Date.now() - startTime;
    const averageTime = totalTime / config.vectorCount;
    const throughput = (config.vectorCount / totalTime) * 1000; // 向量/秒

    return {
      totalTime,
      averageTime,
      throughput,
      hotStoreCount,
      coldStoreCount
    };
  }

  /**
   * 测试搜索操作性能
   */
  private async testSearchPerformance(config: PerformanceTestConfig) {
    console.log('🔍 测试搜索操作性能...');

    const startTime = Date.now();
    let hotStoreHits = 0;
    let coldStoreHits = 0;
    let parallelSearches = 0;

    // 执行搜索查询
    for (const queryVector of this.queryVectors) {
      const searchResult = await this.manager.searchVector(queryVector, {
        searchStrategy: 'adaptive',
        maxResults: 10,
        hotStoreWeight: 0.6,
        coldStoreWeight: 0.4
      });

      hotStoreHits += searchResult.hotResults.length;
      coldStoreHits += searchResult.coldResults.length;
      
      if (searchResult.hotResults.length > 0 && searchResult.coldResults.length > 0) {
        parallelSearches++;
      }
    }

    const totalTime = Date.now() - startTime;
    const averageTime = totalTime / config.searchQueries;
    const throughput = (config.searchQueries / totalTime) * 1000; // 查询/秒

    return {
      totalTime,
      averageTime,
      throughput,
      hotStoreHits,
      coldStoreHits,
      parallelSearches
    };
  }

  /**
   * 获取内存使用情况
   */
  private async getMemoryUsage() {
    const [hotStats, coldStats] = await Promise.all([
      this.manager.hotStore.getStats(),
      this.manager.coldStore.getStats()
    ]);

    const hotStoreSize = hotStats.storageSize;
    const coldStoreSize = coldStats.storageSize;
    const totalSize = hotStoreSize + coldStoreSize;
    
    // 估算压缩比率（假设原始数据大小）
    const estimatedRawSize = (hotStats.totalVectors + coldStats.totalVectors) * 
                            (hotStats.dimensions * 4 + 1000) / (1024 * 1024); // MB
    const compressionRatio = estimatedRawSize > 0 ? totalSize / estimatedRawSize : 1.0;

    return {
      hotStoreSize,
      coldStoreSize,
      totalSize,
      compressionRatio
    };
  }

  /**
   * 获取系统健康状态
   */
  private async getSystemHealth() {
    const [hotHealth, coldHealth] = await Promise.all([
      this.manager.hotStore.getHealth(),
      this.manager.coldStore.getHealth()
    ]);

    const errors: string[] = [];
    if (hotHealth.details.errors) {
      errors.push(...hotHealth.details.errors.map(e => `Hot Store: ${e}`));
    }
    if (coldHealth.details.errors) {
      errors.push(...coldHealth.details.errors.map(e => `Cold Store: ${e}`));
    }

    let overallHealth = 'healthy';
    if (hotHealth.status === 'unhealthy' || coldHealth.status === 'unhealthy') {
      overallHealth = 'unhealthy';
    } else if (hotHealth.status === 'degraded' || coldHealth.status === 'degraded') {
      overallHealth = 'degraded';
    }

    return {
      hotStoreHealth: hotHealth.status,
      coldStoreHealth: coldHealth.status,
      overallHealth,
      errors
    };
  }

  /**
   * 生成测试数据
   */
  private async generateTestData(config: PerformanceTestConfig) {
    console.log('🎲 生成测试数据...');

    // 生成测试向量
    this.testVectors = [];
    for (let i = 0; i < config.vectorCount; i++) {
      const vector = this.generateRandomVector(config.dimensions);
      const metadata = this.generateTestMetadata(i);
      this.testVectors.push({ vector, metadata });
    }

    // 生成查询向量
    this.queryVectors = [];
    for (let i = 0; i < config.searchQueries; i++) {
      this.queryVectors.push(this.generateRandomVector(config.dimensions));
    }

    console.log(`✅ 生成了 ${this.testVectors.length} 个测试向量和 ${this.queryVectors.length} 个查询向量`);
  }

  /**
   * 生成随机向量
   */
  private generateRandomVector(dimensions: number): number[] {
    const vector: number[] = [];
    for (let i = 0; i < dimensions; i++) {
      vector.push(Math.random() * 2 - 1); // [-1, 1] 范围内的随机数
    }
    
    // 归一化向量
    const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return vector.map(val => val / norm);
  }

  /**
   * 生成测试元数据
   */
  private generateTestMetadata(index: number): VectorStoreMetadata {
    const documentTypes = ['daily_insight_hot', 'dialogue_history', 'user_profile', 'key_events'];
    const randomType = documentTypes[index % documentTypes.length] as any;
    
    const now = new Date();
    const createdAt = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000); // 最近30天内

    return {
      chunkId: `test_chunk_${index}`,
      sourceDocumentId: `test_doc_${Math.floor(index / 10)}`,
      sourceDocumentType: randomType,
      createdAt: createdAt.toISOString(),
      updatedAt: now.toISOString(),
      meaningScore: Math.random(),
      emotionalTags: [`emotion_${index % 5}`],
      cognitiveThemes: [`theme_${index % 3}`],
      accessCount: Math.floor(Math.random() * 10),
      lastAccessTime: now.toISOString()
    };
  }

  /**
   * 格式化测试结果
   */
  private formatResults(result: PerformanceTestResult): string {
    return `
性能测试结果摘要:
- 添加性能: ${result.addPerformance.throughput.toFixed(2)} 向量/秒
- 搜索性能: ${result.searchPerformance.throughput.toFixed(2)} 查询/秒
- 内存使用: ${result.memoryUsage.totalSize.toFixed(2)} MB
- 数据分布: Hot=${result.addPerformance.hotStoreCount}, Cold=${result.addPerformance.coldStoreCount}
- 系统健康: ${result.systemHealth.overallHealth}
    `;
  }

  /**
   * 清理测试数据
   */
  async cleanup() {
    console.log('🧹 清理测试数据...');
    await Promise.all([
      this.manager.hotStore.clear(),
      this.manager.coldStore.clear()
    ]);
    console.log('✅ 测试数据清理完成');
  }
}

/**
 * 预定义的测试配置
 */
export const TEST_CONFIGS = {
  small: {
    vectorCount: 100,
    dimensions: 384,
    searchQueries: 50,
    batchSize: 10,
    testDuration: 30
  },
  
  medium: {
    vectorCount: 1000,
    dimensions: 384,
    searchQueries: 200,
    batchSize: 50,
    testDuration: 60
  },
  
  large: {
    vectorCount: 5000,
    dimensions: 384,
    searchQueries: 500,
    batchSize: 100,
    testDuration: 120
  }
};

/**
 * 便捷函数：运行快速性能测试
 */
export async function runQuickPerformanceTest(): Promise<PerformanceTestResult> {
  const tester = new DualVectorPerformanceTester();
  try {
    return await tester.runPerformanceTest(TEST_CONFIGS.small);
  } finally {
    await tester.cleanup();
  }
}

/**
 * 便捷函数：运行完整性能测试
 */
export async function runFullPerformanceTest(): Promise<PerformanceTestResult> {
  const tester = new DualVectorPerformanceTester();
  try {
    return await tester.runPerformanceTest(TEST_CONFIGS.large);
  } finally {
    await tester.cleanup();
  }
}
