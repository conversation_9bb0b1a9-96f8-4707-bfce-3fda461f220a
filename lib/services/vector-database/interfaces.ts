// 双向量数据库系统接口定义
// 支持Hot Store和Cold Store的统一抽象层

export interface VectorStoreMetadata {
  chunkId: string;
  sourceDocumentId: string;
  sourceDocumentType: 'user_profile' | 'key_events' | 'daily_insight_cold' | 'daily_insight_hot' | 'dialogue_history';
  createdAt: string;
  updatedAt: string;
  
  // 扩展元数据
  globalId?: string; // YYYYMMDD-T### 格式的全局唯一ID
  parentChunkId?: string; // 父块ID，用于建立层次关系
  meaningScore?: number; // 意义分数 (0-1)
  emotionalTags?: string[]; // 情感标签
  cognitiveThemes?: string[]; // 认知主题
  
  // 访问统计
  accessCount?: number;
  lastAccessTime?: string;
  
  // 温度衰减相关（Hot Store专用）
  temperature?: number; // 温度值 (0-1)
  decayRate?: number; // 衰减率
  lastDecayTime?: string; // 最后衰减时间
}

export interface VectorSearchResult {
  chunkId: string;
  vector: number[];
  metadata: VectorStoreMetadata;
  similarity: number;
  rank: number;
}

export interface VectorStoreStats {
  totalVectors: number;
  dimensions: number;
  storageSize: number; // MB
  lastUpdated: string;
  
  // 类型分布统计
  documentTypeDistribution: Record<string, number>;
  
  // 性能统计
  averageQueryTime: number; // ms
  totalQueries: number;
  cacheHitRate?: number; // 缓存命中率（如果适用）
}

export interface VectorStoreConfig {
  name: string;
  type: 'hot' | 'cold';
  dimensions: number;
  
  // 存储配置
  maxVectors?: number;
  storageBackend: 'indexeddb' | 'memory' | 'file';
  
  // 性能配置
  enableCache?: boolean;
  cacheSize?: number;
  batchSize?: number;
  
  // Hot Store特定配置
  temperatureDecay?: {
    enabled: boolean;
    decayRate: number; // 每天的衰减率
    minTemperature: number; // 最低温度阈值
    decayInterval: number; // 衰减检查间隔（小时）
  };
  
  // LRU淘汰配置
  lruEviction?: {
    enabled: boolean;
    maxSize: number; // 最大向量数量
    evictionBatchSize: number; // 每次淘汰的数量
  };
  
  // Cold Store特定配置
  compression?: {
    enabled: boolean;
    algorithm: 'gzip' | 'lz4' | 'none';
  };
  
  indexing?: {
    algorithm: 'flat' | 'ivf' | 'hnsw';
    parameters?: Record<string, any>;
  };
}

/**
 * 统一向量数据库接口
 * 支持Hot Store和Cold Store的不同优化策略
 */
export interface IVectorStore {
  readonly config: VectorStoreConfig;
  readonly initialized: boolean;
  
  // 基础操作
  initialize(): Promise<void>;
  add(vector: number[], metadata: VectorStoreMetadata): Promise<string>;
  addBatch(items: Array<{ vector: number[]; metadata: VectorStoreMetadata }>): Promise<string[]>;
  update(chunkId: string, vector: number[], metadata: VectorStoreMetadata): Promise<void>;
  delete(chunkId: string): Promise<void>;
  clear(): Promise<void>;
  
  // 搜索操作
  search(queryVector: number[], topK: number, options?: SearchOptions): Promise<VectorSearchResult[]>;
  searchByMetadata(filters: MetadataFilter[], options?: SearchOptions): Promise<VectorSearchResult[]>;
  
  // 统计和监控
  getStats(): Promise<VectorStoreStats>;
  getHealth(): Promise<HealthStatus>;
  
  // 维护操作
  optimize(): Promise<void>; // 索引优化
  compact(): Promise<void>; // 存储压缩
  
  // Hot Store特定方法
  updateTemperature?(chunkId: string, temperature: number): Promise<void>;
  performDecay?(): Promise<number>; // 返回衰减的向量数量
  evictColdest?(count: number): Promise<string[]>; // 返回被淘汰的chunkId列表
  
  // Cold Store特定方法
  archive?(hotStoreData: VectorSearchResult[]): Promise<void>;
  deepSearch?(queryVector: number[], options: DeepSearchOptions): Promise<VectorSearchResult[]>;
}

export interface SearchOptions {
  similarityThreshold?: number;
  maxResults?: number;
  includeMetadata?: boolean;
  sortBy?: 'similarity' | 'temperature' | 'accessTime' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  
  // 过滤选项
  filters?: MetadataFilter[];
  
  // 性能选项
  useCache?: boolean;
  timeout?: number; // ms
}

export interface MetadataFilter {
  field: keyof VectorStoreMetadata;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains';
  value: any;
}

export interface DeepSearchOptions extends SearchOptions {
  searchDepth?: number; // 搜索深度
  expandResults?: boolean; // 是否扩展相关结果
  includeArchived?: boolean; // 是否包含归档数据
}

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    vectorCount: number;
    indexHealth: 'good' | 'rebuilding' | 'corrupted';
    storageHealth: 'good' | 'full' | 'error';
    lastMaintenance: string;
    errors?: string[];
  };
}

/**
 * 双向量数据库管理器接口
 * 协调Hot Store和Cold Store的操作
 */
export interface IDualVectorManager {
  readonly hotStore: IVectorStore;
  readonly coldStore: IVectorStore;
  readonly initialized: boolean;
  
  // 初始化
  initialize(): Promise<void>;
  
  // 智能路由操作
  addVector(vector: number[], metadata: VectorStoreMetadata): Promise<string>;
  searchVector(queryVector: number[], options: DualSearchOptions): Promise<DualSearchResult>;
  
  // 数据生命周期管理
  promoteToHot(chunkIds: string[]): Promise<void>; // 从Cold提升到Hot
  demoteToCold(chunkIds: string[]): Promise<void>; // 从Hot降级到Cold
  archiveOldData(olderThan: Date): Promise<number>; // 归档旧数据
  
  // 维护操作
  performMaintenance(): Promise<MaintenanceResult>;
  getSystemStats(): Promise<DualSystemStats>;
  
  // 数据迁移
  migrateFromLegacy(legacyData: any[]): Promise<MigrationResult>;
}

export interface DualSearchOptions extends SearchOptions {
  searchStrategy: 'hot_first' | 'cold_first' | 'parallel' | 'adaptive';
  hotStoreWeight?: number; // Hot Store结果权重 (0-1)
  coldStoreWeight?: number; // Cold Store结果权重 (0-1)
  mergeStrategy?: 'interleave' | 'score_based' | 'temperature_based';
}

export interface DualSearchResult {
  hotResults: VectorSearchResult[];
  coldResults: VectorSearchResult[];
  mergedResults: VectorSearchResult[];
  searchStats: {
    hotSearchTime: number;
    coldSearchTime: number;
    totalTime: number;
    hotHits: number;
    coldHits: number;
  };
}

export interface MaintenanceResult {
  hotStoreMaintenance: {
    vectorsDecayed: number;
    vectorsEvicted: number;
    indexOptimized: boolean;
  };
  coldStoreMaintenance: {
    vectorsArchived: number;
    storageCompacted: boolean;
    indexRebuilt: boolean;
  };
  totalTime: number;
  errors: string[];
}

export interface DualSystemStats {
  hotStore: VectorStoreStats;
  coldStore: VectorStoreStats;
  totalVectors: number;
  dataDistribution: {
    hotPercentage: number;
    coldPercentage: number;
  };
  systemHealth: HealthStatus;
}

export interface MigrationResult {
  totalRecords: number;
  migratedToHot: number;
  migratedToCold: number;
  errors: string[];
  migrationTime: number;
}

// 错误类型定义
export class VectorStoreError extends Error {
  constructor(
    message: string,
    public code: string,
    public store?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'VectorStoreError';
  }
}

export class DualVectorError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DualVectorError';
  }
}
