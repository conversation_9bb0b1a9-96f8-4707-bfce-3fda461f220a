// Cold Store - 长期记忆归档实现
// 大容量冷数据存储，优化用户画像、关键事件和历史对话的长期保存和深度检索

import { 
  IVectorStore, 
  VectorStoreConfig, 
  VectorStoreMetadata, 
  VectorSearchResult, 
  VectorStoreStats, 
  HealthStatus,
  SearchOptions,
  MetadataFilter,
  DeepSearchOptions,
  VectorStoreError 
} from './interfaces';
import { globalIdManager } from './global-id-system';

interface ColdStoreItem {
  chunkId: string;
  vector: number[];
  metadata: VectorStoreMetadata;
  archivedAt: Date;
  compressionRatio?: number; // 压缩比率
  indexPosition?: number; // 在索引中的位置
}

interface IndexNode {
  id: string;
  centroid: number[]; // 聚类中心
  children: string[]; // 子节点或叶子项目ID
  level: number; // 层级
  size: number; // 包含的向量数量
}

/**
 * Cold Store 实现
 * 专门用于长期存储用户画像、关键事件和历史对话数据
 * 优化存储效率和深度检索能力
 */
export class ColdVectorStore implements IVectorStore {
  private items: Map<string, ColdStoreItem> = new Map();
  private index: Map<string, IndexNode> = new Map(); // 层次化索引
  private rootNodes: string[] = []; // 根节点ID列表
  private lastCompactionTime: Date = new Date();
  private lastIndexRebuildTime: Date = new Date();
  
  public readonly config: VectorStoreConfig;
  public initialized = false;

  constructor(config: VectorStoreConfig) {
    this.config = {
      ...config,
      type: 'cold',
      // 确保Cold Store的默认配置
      compression: config.compression || {
        enabled: true,
        algorithm: 'gzip'
      },
      indexing: config.indexing || {
        algorithm: 'ivf', // Inverted File Index
        parameters: {
          nlist: 100, // 聚类数量
          nprobe: 10, // 搜索的聚类数量
          maxLeafSize: 50 // 叶子节点最大大小
        }
      }
    };
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('❄️ 初始化Cold Store...');
      
      // 初始化全局ID管理器
      await globalIdManager.initialize();
      
      // 从存储中恢复数据和索引
      await this.loadFromStorage();
      
      // 如果索引为空，构建初始索引
      if (this.index.size === 0 && this.items.size > 0) {
        await this.rebuildIndex();
      }
      
      this.initialized = true;
      console.log(`✅ Cold Store初始化完成，当前向量数: ${this.items.size}, 索引节点: ${this.index.size}`);
    } catch (error) {
      throw new VectorStoreError('Cold Store初始化失败', 'INIT_ERROR', 'cold', error);
    }
  }

  async add(vector: number[], metadata: VectorStoreMetadata): Promise<string> {
    if (!this.initialized) {
      throw new VectorStoreError('Cold Store未初始化', 'NOT_INITIALIZED', 'cold');
    }

    // 生成全局ID（如果没有提供）
    if (!metadata.globalId) {
      const parentId = await globalIdManager.generateUserInputId();
      metadata.globalId = await globalIdManager.generateDerivedId(
        parentId, 
        'derived_chunk',
        'vector'
      );
    }

    const chunkId = metadata.chunkId;
    const now = new Date();
    
    // 压缩向量（如果启用）
    let processedVector = vector;
    let compressionRatio = 1.0;
    
    if (this.config.compression?.enabled) {
      // TODO: 实现向量压缩算法
      // 这里暂时跳过压缩，保持原始向量
      processedVector = [...vector];
    }
    
    // 创建冷存储项
    const item: ColdStoreItem = {
      chunkId,
      vector: processedVector,
      metadata: { ...metadata },
      archivedAt: now,
      compressionRatio
    };

    // 添加到存储
    this.items.set(chunkId, item);
    
    // 更新索引
    await this.updateIndex(chunkId, processedVector);
    
    // 保存到持久化存储
    await this.saveToStorage();
    
    console.log(`❄️ Cold Store添加向量: ${chunkId} (压缩比: ${compressionRatio})`);
    return chunkId;
  }

  async addBatch(items: Array<{ vector: number[]; metadata: VectorStoreMetadata }>): Promise<string[]> {
    const chunkIds: string[] = [];
    
    // 批量添加以提高效率
    for (const item of items) {
      const chunkId = await this.add(item.vector, item.metadata);
      chunkIds.push(chunkId);
    }
    
    // 批量添加后重建索引以优化性能
    if (items.length > 10) {
      await this.rebuildIndex();
    }
    
    return chunkIds;
  }

  async search(queryVector: number[], topK: number, options?: SearchOptions): Promise<VectorSearchResult[]> {
    if (!this.initialized) {
      throw new VectorStoreError('Cold Store未初始化', 'NOT_INITIALIZED', 'cold');
    }

    const startTime = Date.now();
    
    // 使用索引进行快速搜索
    const candidateIds = await this.indexSearch(queryVector, topK * 2); // 获取更多候选
    
    const results: VectorSearchResult[] = [];
    
    // 计算候选向量的精确相似度
    for (const chunkId of candidateIds) {
      const item = this.items.get(chunkId);
      if (!item) continue;
      
      const similarity = this.calculateCosineSimilarity(queryVector, item.vector);
      
      // 应用相似度阈值过滤
      if (options?.similarityThreshold && similarity < options.similarityThreshold) {
        continue;
      }
      
      // 应用元数据过滤
      if (options?.filters && !this.matchesFilters(item.metadata, options.filters)) {
        continue;
      }
      
      results.push({
        chunkId,
        vector: item.vector,
        metadata: item.metadata,
        similarity,
        rank: 0 // 将在排序后设置
      });
    }
    
    // 排序结果
    results.sort((a, b) => b.similarity - a.similarity);
    
    // 设置排名并限制结果数量
    const limitedResults = results.slice(0, Math.min(topK, options?.maxResults || topK));
    limitedResults.forEach((result, index) => {
      result.rank = index + 1;
    });
    
    const searchTime = Date.now() - startTime;
    console.log(`🔍 Cold Store搜索完成: ${limitedResults.length}/${results.length} 结果 (${searchTime}ms)`);
    
    return limitedResults;
  }

  async deepSearch(queryVector: number[], options: DeepSearchOptions): Promise<VectorSearchResult[]> {
    console.log('🔍 执行Cold Store深度搜索...');
    
    const searchDepth = options.searchDepth || 3;
    const expandResults = options.expandResults || false;
    
    // 第一层：基础搜索
    let results = await this.search(queryVector, options.maxResults || 50, options);
    
    if (expandResults && results.length > 0) {
      // 第二层：基于结果扩展搜索
      const expandedResults = new Map<string, VectorSearchResult>();
      
      for (const result of results) {
        expandedResults.set(result.chunkId, result);
        
        // 查找相关的向量（基于元数据关联）
        const relatedResults = await this.findRelatedVectors(result, searchDepth);
        for (const related of relatedResults) {
          if (!expandedResults.has(related.chunkId)) {
            expandedResults.set(related.chunkId, related);
          }
        }
      }
      
      results = Array.from(expandedResults.values());
      
      // 重新排序扩展后的结果
      results.sort((a, b) => b.similarity - a.similarity);
      results.forEach((result, index) => {
        result.rank = index + 1;
      });
    }
    
    console.log(`🔍 深度搜索完成: ${results.length} 个结果`);
    return results.slice(0, options.maxResults || results.length);
  }

  async searchByMetadata(filters: MetadataFilter[], options?: SearchOptions): Promise<VectorSearchResult[]> {
    const results: VectorSearchResult[] = [];
    
    for (const [chunkId, item] of this.items.entries()) {
      if (this.matchesFilters(item.metadata, filters)) {
        results.push({
          chunkId,
          vector: item.vector,
          metadata: item.metadata,
          similarity: 1.0, // 元数据搜索不计算相似度
          rank: results.length + 1
        });
      }
    }
    
    return results.slice(0, options?.maxResults || results.length);
  }

  async update(chunkId: string, vector: number[], metadata: VectorStoreMetadata): Promise<void> {
    const item = this.items.get(chunkId);
    if (!item) {
      throw new VectorStoreError(`向量不存在: ${chunkId}`, 'NOT_FOUND', 'cold');
    }
    
    // 更新数据
    item.vector = [...vector];
    item.metadata = { ...metadata };
    
    // 更新索引
    await this.updateIndex(chunkId, vector);
    
    await this.saveToStorage();
    console.log(`❄️ Cold Store更新向量: ${chunkId}`);
  }

  async delete(chunkId: string): Promise<void> {
    if (!this.items.has(chunkId)) {
      throw new VectorStoreError(`向量不存在: ${chunkId}`, 'NOT_FOUND', 'cold');
    }
    
    this.items.delete(chunkId);
    
    // 从索引中移除
    await this.removeFromIndex(chunkId);
    
    await this.saveToStorage();
    console.log(`🗑️ Cold Store删除向量: ${chunkId}`);
  }

  async clear(): Promise<void> {
    this.items.clear();
    this.index.clear();
    this.rootNodes = [];
    await this.saveToStorage();
    console.log('🧹 Cold Store已清空');
  }

  async getStats(): Promise<VectorStoreStats> {
    const now = new Date();
    const documentTypes: Record<string, number> = {};
    
    for (const item of this.items.values()) {
      const type = item.metadata.sourceDocumentType;
      documentTypes[type] = (documentTypes[type] || 0) + 1;
    }
    
    return {
      totalVectors: this.items.size,
      dimensions: this.config.dimensions,
      storageSize: this.estimateStorageSize(),
      lastUpdated: now.toISOString(),
      documentTypeDistribution: documentTypes,
      averageQueryTime: 0, // TODO: 实现查询时间统计
      totalQueries: 0 // Cold Store查询频率较低
    };
  }

  async getHealth(): Promise<HealthStatus> {
    const stats = await this.getStats();
    const indexHealth = this.validateIndex();
    
    let status: HealthStatus['status'] = 'healthy';
    const errors: string[] = [];
    
    if (!indexHealth) {
      status = 'degraded';
      errors.push('索引需要重建');
    }
    
    if (stats.storageSize > 1000) { // 1GB
      status = 'degraded';
      errors.push('存储空间较大，建议压缩');
    }
    
    return {
      status,
      details: {
        vectorCount: stats.totalVectors,
        indexHealth: indexHealth ? 'good' : 'rebuilding',
        storageHealth: 'good',
        lastMaintenance: this.lastCompactionTime.toISOString(),
        errors: errors.length > 0 ? errors : undefined
      }
    };
  }

  async optimize(): Promise<void> {
    console.log('🔧 Cold Store优化开始...');
    
    // 重建索引
    await this.rebuildIndex();
    
    // 压缩存储
    await this.compact();
    
    console.log('✅ Cold Store优化完成');
  }

  async compact(): Promise<void> {
    console.log('🗜️ Cold Store压缩开始...');
    
    const beforeSize = this.estimateStorageSize();
    
    // TODO: 实现向量压缩和存储优化
    // 1. 压缩向量数据
    // 2. 合并相似的向量
    // 3. 清理无效索引节点
    
    this.lastCompactionTime = new Date();
    await this.saveToStorage();
    
    const afterSize = this.estimateStorageSize();
    console.log(`🗜️ Cold Store压缩完成: ${beforeSize.toFixed(2)}MB -> ${afterSize.toFixed(2)}MB`);
  }

  // Cold Store特定方法
  async archive(hotStoreData: VectorSearchResult[]): Promise<void> {
    console.log(`📦 归档Hot Store数据: ${hotStoreData.length} 个向量`);
    
    const items = hotStoreData.map(result => ({
      vector: result.vector,
      metadata: result.metadata
    }));
    
    await this.addBatch(items);
    console.log('✅ 归档完成');
  }

  // 私有辅助方法
  private async indexSearch(queryVector: number[], topK: number): Promise<string[]> {
    if (this.rootNodes.length === 0) {
      // 如果没有索引，返回所有项目ID
      return Array.from(this.items.keys()).slice(0, topK);
    }
    
    const candidates: string[] = [];
    const nprobe = this.config.indexing?.parameters?.nprobe || 10;
    
    // 搜索最相似的聚类
    const searchQueue: { nodeId: string; similarity: number }[] = [];
    
    for (const rootId of this.rootNodes) {
      const node = this.index.get(rootId);
      if (node) {
        const similarity = this.calculateCosineSimilarity(queryVector, node.centroid);
        searchQueue.push({ nodeId: rootId, similarity });
      }
    }
    
    // 按相似度排序
    searchQueue.sort((a, b) => b.similarity - a.similarity);
    
    // 搜索前nprobe个聚类
    for (let i = 0; i < Math.min(nprobe, searchQueue.length); i++) {
      const { nodeId } = searchQueue[i];
      const leafIds = await this.getLeafIds(nodeId);
      candidates.push(...leafIds);
    }
    
    return candidates.slice(0, topK);
  }

  private async getLeafIds(nodeId: string): Promise<string[]> {
    const node = this.index.get(nodeId);
    if (!node) return [];
    
    if (node.level === 0) {
      // 叶子节点，返回子项目ID
      return node.children;
    } else {
      // 内部节点，递归获取叶子ID
      const leafIds: string[] = [];
      for (const childId of node.children) {
        const childLeafIds = await this.getLeafIds(childId);
        leafIds.push(...childLeafIds);
      }
      return leafIds;
    }
  }

  private async findRelatedVectors(result: VectorSearchResult, depth: number): Promise<VectorSearchResult[]> {
    const related: VectorSearchResult[] = [];
    
    // 基于文档ID查找相关向量
    const sameDocumentVectors = Array.from(this.items.values())
      .filter(item => 
        item.metadata.sourceDocumentId === result.metadata.sourceDocumentId &&
        item.chunkId !== result.chunkId
      )
      .slice(0, depth);
    
    for (const item of sameDocumentVectors) {
      const similarity = this.calculateCosineSimilarity(result.vector, item.vector);
      related.push({
        chunkId: item.chunkId,
        vector: item.vector,
        metadata: item.metadata,
        similarity,
        rank: 0
      });
    }
    
    return related;
  }

  private calculateCosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private matchesFilters(metadata: VectorStoreMetadata, filters: MetadataFilter[]): boolean {
    return filters.every(filter => {
      const value = (metadata as any)[filter.field];
      
      switch (filter.operator) {
        case 'eq': return value === filter.value;
        case 'ne': return value !== filter.value;
        case 'gt': return value > filter.value;
        case 'gte': return value >= filter.value;
        case 'lt': return value < filter.value;
        case 'lte': return value <= filter.value;
        case 'in': return Array.isArray(filter.value) && filter.value.includes(value);
        case 'nin': return Array.isArray(filter.value) && !filter.value.includes(value);
        case 'contains': return Array.isArray(value) && value.includes(filter.value);
        default: return false;
      }
    });
  }

  private estimateStorageSize(): number {
    // 粗略估算存储大小（MB）
    const itemSize = this.config.dimensions * 4 + 1000; // 向量 + 元数据
    const indexSize = this.index.size * (this.config.dimensions * 4 + 200); // 索引节点
    return (this.items.size * itemSize + indexSize) / (1024 * 1024);
  }

  private validateIndex(): boolean {
    // 简单的索引验证
    return this.index.size > 0 && this.rootNodes.length > 0;
  }

  private async updateIndex(chunkId: string, vector: number[]): Promise<void> {
    // TODO: 实现增量索引更新
    // 暂时标记需要重建索引
    console.log(`📇 标记索引更新: ${chunkId}`);
  }

  private async removeFromIndex(chunkId: string): Promise<void> {
    // TODO: 实现从索引中移除项目
    console.log(`📇 从索引移除: ${chunkId}`);
  }

  private async rebuildIndex(): Promise<void> {
    console.log('🔄 重建Cold Store索引...');
    
    this.index.clear();
    this.rootNodes = [];
    
    if (this.items.size === 0) return;
    
    // TODO: 实现层次化索引构建算法
    // 1. K-means聚类
    // 2. 构建层次结构
    // 3. 创建索引节点
    
    this.lastIndexRebuildTime = new Date();
    console.log('✅ 索引重建完成');
  }

  private async loadFromStorage(): Promise<void> {
    // TODO: 从IndexedDB加载数据和索引
    console.log('📥 从存储加载Cold Store数据...');
  }

  private async saveToStorage(): Promise<void> {
    // TODO: 保存到IndexedDB
    console.log('💾 保存Cold Store数据...');
  }
}
