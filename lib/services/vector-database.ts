// 本地向量数据库管理器 - 现在使用双向量数据库架构
// 保持向后兼容性，内部使用新的Hot Store + Cold Store架构

import { VectorDatabase, VectorSearchCandidate, ChunkPair, StorageError } from '@/types/rag';
import { ragStorage } from '@/lib/storage/rag-storage';
import { RAG_CONFIG } from '@/lib/config/rag-config';
import { legacyVectorDatabase } from './vector-database/legacy-adapter';

class LocalVectorDatabase implements VectorDatabase {
  // 使用新的双向量数据库适配器作为后端
  private adapter = legacyVectorDatabase;

  public initialized = false;
  public totalVectors = 0;

  /**
   * 初始化向量数据库 - 现在使用双向量架构
   */
  async initialize(): Promise<void> {
    try {
      console.log('🚀 初始化向量数据库 (双向量架构)...');

      // 初始化双向量适配器
      await this.adapter.initialize();

      // 获取统计信息
      const stats = this.adapter.getStats();
      this.initialized = stats.initialized;
      this.totalVectors = stats.totalVectors;

      console.log(`✅ 向量数据库初始化成功，使用双向量架构，当前向量数: ${this.totalVectors}`);
    } catch (error) {
      throw new StorageError('向量数据库初始化失败', error);
    }
  }

  /**
   * 向量搜索 - 现在使用双向量架构
   */
  async search(queryVector: number[], topK: number): Promise<VectorSearchCandidate[]> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      // 使用双向量适配器进行搜索
      const candidates = await this.adapter.search(queryVector, topK);

      console.log(`🔍 双向量搜索完成，找到 ${candidates.length} 个候选项`);
      return candidates;
    } catch (error) {
      throw new StorageError('向量搜索失败', error);
    }
  }

  /**
   * 添加新向量 - 现在使用双向量架构
   */
  async add(vector: number[], metadata: any): Promise<string> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      // 使用双向量适配器添加向量
      const chunkId = await this.adapter.add(vector, metadata);

      // 更新统计信息
      const stats = this.adapter.getStats();
      this.totalVectors = stats.totalVectors;

      console.log(`➕ 双向量添加成功，chunkId: ${chunkId}`);
      return chunkId;
    } catch (error) {
      throw new StorageError('添加向量失败', error);
    }
  }

  /**
   * 更新向量 - 现在使用双向量架构
   */
  async update(id: string, vector: number[], metadata: any): Promise<void> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      await this.adapter.update(id, vector, metadata);
      console.log(`🔄 双向量更新成功，id: ${id}`);
    } catch (error) {
      throw new StorageError('向量更新失败', error);
    }
  }

  /**
   * 删除向量 - 现在使用双向量架构
   */
  async delete(id: string): Promise<void> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      await this.adapter.delete(id);

      // 更新统计信息
      const stats = this.adapter.getStats();
      this.totalVectors = stats.totalVectors;

      console.log(`🗑️ 双向量删除成功，id: ${id}`);
    } catch (error) {
      throw new StorageError('向量删除失败', error);
    }
  }

  /**
   * 清空所有向量 - 现在使用双向量架构
   */
  async clear(): Promise<void> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      await this.adapter.clear();

      // 重置统计信息
      this.totalVectors = 0;

      console.log('🗑️ 双向量数据库已清空');
    } catch (error) {
      throw new StorageError('清空向量数据库失败', error);
    }
  }

  /**
   * 重建整个向量索引 - 现在使用双向量架构
   */
  async rebuild(): Promise<void> {
    console.log('🔄 开始重建双向量索引...');

    try {
      await this.adapter.rebuild();

      // 更新统计信息
      const stats = this.adapter.getStats();
      this.totalVectors = stats.totalVectors;

      console.log('✅ 双向量索引重建完成');
    } catch (error) {
      throw new StorageError('重建向量索引失败', error);
    }
  }

  /**
   * 获取数据库统计信息 - 现在包含双向量架构信息
   */
  getStats() {
    const basicStats = this.adapter.getStats();
    return {
      ...basicStats,
      // 保持向后兼容的字段
      nextIndex: basicStats.totalVectors, // 使用总向量数作为nextIndex
      mappingSize: basicStats.totalVectors, // 映射大小等于向量总数
      // 新增双向量架构信息
      architecture: 'dual-vector',
      backend: 'hot-cold-store'
    };
  }

  /**
   * 获取详细统计信息 - 新增功能
   */
  async getDetailedStats() {
    return await this.adapter.getDetailedStats();
  }

  /**
   * 批量添加向量 - 现在使用双向量架构
   */
  async addBatch(vectors: Array<{ vector: number[]; metadata: any }>): Promise<string[]> {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    try {
      const chunkIds = await this.adapter.addBatch(vectors);

      // 更新统计信息
      const stats = this.adapter.getStats();
      this.totalVectors = stats.totalVectors;

      console.log(`➕ 双向量批量添加 ${chunkIds.length} 个向量成功`);
      return chunkIds;
    } catch (error) {
      throw new StorageError('批量添加向量失败', error);
    }
  }

  /**
   * 检查向量是否存在 - 现在使用双向量架构
   */
  async hasVector(chunkId: string): Promise<boolean> {
    if (!this.initialized) {
      return false;
    }

    try {
      // 使用适配器的搜索方法
      const searchResults = await this.adapter.search([1, 0, 0], 1000); // 使用虚拟向量搜索所有

      // 检查搜索结果中是否包含指定的chunkId
      const found = searchResults.some(result =>
        result.chunkPair && result.chunkPair.id === chunkId
      );

      return found;
    } catch (error) {
      console.error('检查向量存在性失败:', error);
      return false;
    }
  }

  /**
   * 执行系统维护 - 新增功能
   */
  async performMaintenance() {
    if (!this.initialized) {
      throw new StorageError('向量数据库未初始化');
    }

    return await this.adapter.performMaintenance();
  }
}

// 导出单例
export const vectorDatabase = new LocalVectorDatabase();
