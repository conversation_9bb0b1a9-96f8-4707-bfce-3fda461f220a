// 对话管理服务 - 核心业务逻辑

import { Message, ConversationState, DialogueBlock } from '@/types/conversation';
import { MemorySystem } from '@/types/memory';
import { v4 as uuidv4 } from 'uuid';
// 删除不再需要的导入

export class ConversationManager {
  private state: ConversationState = {
    messages: [],
    currentMode: 'quick',
    contextWindow: [],
    isProcessing: false
  };

  private memorySystem: MemorySystem | null = null;
  private dialogueBlocks: DialogueBlock[] = [];
  private dialogueBlocksCount: number = 0; // 添加：对话块计数器
  private isRefining: boolean = false; // 标记是否正在进行每日沉淀
  
  // 初始化对话管理器
  async initialize(): Promise<void> {
    try {
      // 只在客户端初始化
      if (typeof window !== 'undefined') {
        // 恢复对话状态
        await this.restoreConversationState();
        console.log('✅ 对话管理器初始化完成');
      }
    } catch (error) {
      console.error('Failed to initialize conversation manager:', error);
    }
  }

  // 恢复对话状态
  private async restoreConversationState(): Promise<void> {
    try {
      // 从对话历史文件恢复对话块计数
      const response = await fetch('/api/debug/conversation-state');
      if (response.ok) {
        const stateData = await response.json();

        // 恢复对话块计数 - 修复：直接设置计数而不是创建虚拟对话块
        if (stateData.dialogueBlocksCount > 0) {
          // 使用一个简单的计数器来跟踪对话块数量
          this.dialogueBlocksCount = stateData.dialogueBlocksCount;
          console.log(`🔄 恢复了 ${stateData.dialogueBlocksCount} 个对话块的计数`);

          // 添加调试信息
          console.log(`📊 当前对话块计数: ${this.dialogueBlocksCount}`);
          console.log(`🔍 是否应该触发每日洞察: ${this.shouldTriggerDailyInsight()}`);
        }

        // 检查是否需要立即触发分析
        if (this.shouldTriggerDailyInsight()) {
          console.log('🧠 检测到需要触发每日洞察，准备执行...');
          setTimeout(() => this.triggerDailyInsight(), 2000);
        }
      }
    } catch (error) {
      console.error('恢复对话状态失败:', error);
    }
  }
  
  // 删除不再需要的记忆同步方法
  
  // 处理用户消息
  async processUserMessage(content: string): Promise<Message> {
    this.state.isProcessing = true;
    
    try {
      // 创建用户消息
      const userMessage: Message = {
        id: uuidv4(),
        role: 'user',
        content,
        timestamp: new Date().toISOString()
      };
      
      // 添加到消息列表和上下文窗口
      this.addMessage(userMessage);
      
      // 生成AI响应（快速响应模式）
      const aiResponse = await this.generateResponse(userMessage);
      
      // 添加AI响应到消息列表
      this.addMessage(aiResponse);
      
      // 创建对话块
      const dialogueBlock: DialogueBlock = {
        userMessage,
        aiResponse
      };
      this.dialogueBlocks.push(dialogueBlock);

      // 修复：同时更新计数器
      this.dialogueBlocksCount++;
      console.log(`📊 新增对话块，当前计数: ${this.dialogueBlocksCount}`);

      // 检查是否需要触发分析（后台处理）
      if (this.shouldTriggerDailyInsight()) {
        console.log('🧠 触发每日洞察分析...');
        this.triggerDailyInsight();
      }

      if (this.shouldTriggerDeepRefinement()) {
        console.log('🔬 触发深度精炼分析...');
        this.triggerDeepRefinement();
      }
      
      return aiResponse;
    } finally {
      this.state.isProcessing = false;
    }
  }
  
  // 生成AI响应
  private async generateResponse(userMessage: Message): Promise<Message> {
    const startTime = Date.now();
    
    try {
      // 准备记忆上下文（只在客户端环境下）
      let memoryContext = {};
      if (typeof window !== 'undefined' && this.memorySystem) {
        memoryContext = {
          userProfile: this.memorySystem.userProfile,
          keyEvents: this.memorySystem.keyEvents,
          elementIndex: this.memorySystem.elementIndex
        };
      }
      
      // 调用API生成响应
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [
            ...this.state.contextWindow.map(msg => ({
              role: msg.role,
              content: msg.content
            })),
            { role: 'user', content: userMessage.content }
          ],
          isInInsightMode: this.isRefining, // 传递是否处于洞察模式
          memoryContext, // 发送记忆上下文
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate response');
      }
      
      // 读取流式响应
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let fullContent = '';
      
      // 创建AI消息（先创建，后更新内容）
      const aiMessage: Message = {
        id: uuidv4(),
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString(),
        metadata: {
          mode: 'quick',
          thinkingBudget: 0,
          processingTime: 0,
          contextSize: this.state.contextWindow.length
        }
      };
      
      if (reader) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');
            
            for (const line of lines) {
              if (line.startsWith('0:')) {
                const content = line.slice(2);
                if (content && content !== '""') {
                  try {
                    // 尝试解析为JSON字符串
                    const parsed = JSON.parse(content);
                    if (typeof parsed === 'string') {
                      fullContent += parsed;
                    }
                  } catch {
                    // 如果解析失败，直接使用内容
                    fullContent += content;
                  }
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }
      }
      
      const processingTime = Date.now() - startTime;
      
      // 更新AI消息的内容和处理时间
      aiMessage.content = fullContent || '抱歉，我现在无法回应。请稍后再试。';
      if (aiMessage.metadata) {
        aiMessage.metadata.processingTime = processingTime;
      }
      
      return aiMessage;
    } catch (error) {
      console.error('Failed to generate response:', error);
      
      // 返回错误消息
      return {
        id: uuidv4(),
        role: 'assistant',
        content: '抱歉，我现在无法处理你的消息。请稍后再试。',
        timestamp: new Date().toISOString(),
        metadata: {
          mode: 'quick',
          thinkingBudget: 0,
          processingTime: Date.now() - startTime,
          contextSize: 0
        }
      };
    }
  }
  
  // 添加消息到状态
  private addMessage(message: Message): void {
    this.state.messages.push(message);
    
    // 根据沉淀状态动态调整上下文窗口大小
    const windowSize = this.isRefining ? 8 : 6;
    this.state.contextWindow = this.state.messages.slice(-windowSize);
  }
  
  // 构建上下文窗口
  private buildContextWindow(): void {
    // 根据沉淀状态动态调整上下文窗口大小
    const windowSize = this.isRefining ? 8 : 6;
    this.state.contextWindow = this.state.messages.slice(-windowSize);
  }
  
  // 获取当前沉淀状态
  getRefiningStatus(): boolean {
    return this.isRefining;
  }
  
  // 检查是否需要触发每日洞察
  private shouldTriggerDailyInsight(): boolean {
    // 修复：使用计数器而不是数组长度
    const count = this.dialogueBlocksCount;
    const shouldTrigger = count >= 6 && count % 6 === 0;
    console.log(`🔍 每日洞察触发检查: 对话块数=${count}, 应该触发=${shouldTrigger}`);
    return shouldTrigger;
  }

  // 检查是否需要触发深度精炼
  private shouldTriggerDeepRefinement(): boolean {
    // 修复：使用计数器而不是数组长度
    const count = this.dialogueBlocksCount;
    const shouldTrigger = count >= 30 && count % 30 === 0;
    console.log(`🔍 深度精炼触发检查: 对话块数=${count}, 应该触发=${shouldTrigger}`);
    return shouldTrigger;
  }

  // 触发每日洞察（异步后台处理）
  private async triggerDailyInsight(): Promise<void> {
    console.log('🧠 触发每日洞察分析...');
    console.log(`📊 当前对话块数量: ${this.dialogueBlocksCount}, 数组长度: ${this.dialogueBlocks.length}`);

    setTimeout(async () => {
      try {
        // 获取最近6个对话块用于分析
        const recentBlocks = this.dialogueBlocks.slice(-6);
        console.log(`📝 准备分析 ${recentBlocks.length} 个对话块`);

        const response = await fetch('/api/daily-insight', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            dialogueBlocks: recentBlocks
          })
        });

        if (response.ok) {
          const result = await response.json();
          console.log('✅ 每日洞察完成:', result.message);
        } else {
          const errorText = await response.text();
          console.error('❌ 每日洞察API失败:', response.status, errorText);
        }
      } catch (error) {
        console.error('❌ 每日洞察网络错误:', error);
      }
    }, 1000); // 延迟1秒执行，避免阻塞用户交互
  }

  // 触发深度精炼（异步后台处理）
  private async triggerDeepRefinement(): Promise<void> {
    console.log('🔬 触发深度精炼分析...');

    setTimeout(async () => {
      try {
        const response = await fetch('/api/deep-refinement', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            triggerReason: '对话数量达到阈值'
          })
        });

        if (response.ok) {
          console.log('✅ 深度精炼完成');

          // 修复：清理已分析的对话块，同时更新计数器
          this.dialogueBlocks = this.dialogueBlocks.slice(-10);
          this.dialogueBlocksCount = Math.max(this.dialogueBlocksCount - 20, 10); // 保留最近10个
          console.log(`📊 深度精炼后对话块计数: ${this.dialogueBlocksCount}`);
        }
      } catch (error) {
        console.error('❌ 深度精炼失败:', error);
      }
    }, 2000); // 延迟2秒执行，避免阻塞用户交互
  }
  
  // 删除不再需要的记忆更新方法
  
  // 获取对话ID
  private getConversationId(): string {
    return `conv_${Date.now()}`;
  }
  
  // 获取当前状态
  getState(): ConversationState {
    return { ...this.state };
  }
  
  // 获取对话历史
  getMessages(): Message[] {
    return [...this.state.messages];
  }
  
  // 清空对话
  clearConversation(): void {
    this.state.messages = [];
    this.state.contextWindow = [];
    this.dialogueBlocks = [];
  }
}

// 导出单例
export const conversationManager = new ConversationManager(); 