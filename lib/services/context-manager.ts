// 上下文打包逻辑管理器
import {
  loadMemoryContext,
  loadPromptContext,
  getRecentDialogueHistory,
  meaningRAGSearch,
  MEMORY_FILES
} from "@/lib/storage/memory-manager";
import { RAGDebugParams } from "@/types/rag";

export interface DailyChatContext {
  systemPrompt: string;
  recentDialogue: string;
  contextMetadata: {
    dialogueRounds: number;
    hasRelevantEvents: boolean;
    memoryFilesLoaded: string[];
  };
}

export interface DailyInsightContext {
  systemPrompt: string;
  analysisContent: string;
  contextMetadata: {
    dialogueRounds: number;
    insightSources: string[];
    relevantEventsCount: number;
  };
}

export interface DeepRefinementContext {
  systemPrompt: string;
  analysisContent: string;
  contextMetadata: {
    timeSpan: string;
    totalDialogueRounds: number;
    memoryFilesAnalyzed: string[];
  };
}

/**
 * A. 日常对话模式的上下文打包
 */
export async function buildDailyChatContext(
  currentUserMessage: string,
  isInInsightMode: boolean = false,
  ragParams?: RAGDebugParams
): Promise<DailyChatContext> {
  console.log('📦 构建日常对话上下文...');
  
  // 1. 加载记忆上下文和提示词
  const [memory, prompts] = await Promise.all([
    loadMemoryContext(),
    loadPromptContext()
  ]);
  
  // 2. 根据是否处于洞察模式调整对话轮数
  const dialogueRounds = isInInsightMode ? 8 : 6;
  const recentDialogue = await getRecentDialogueHistory(dialogueRounds);
  
  // 3. 意义RAG检索相关关键事件（支持调试参数）
  let relevantEvents: string;
  if (ragParams) {
    console.log('🔧 使用RAG调试参数进行检索...');
    // 这里可以调用带调试参数的RAG检索
    relevantEvents = await meaningRAGSearch(currentUserMessage, MEMORY_FILES.KEY_EVENTS);
  } else {
    relevantEvents = await meaningRAGSearch(currentUserMessage, MEMORY_FILES.KEY_EVENTS);
  }
  
  // 4. 按照指定顺序构建系统提示词
  const systemPrompt = `
${memory.xiaoJingPersona}

${memory.userProfile}

${memory.mentalElements}

${prompts.dailyChatPrompt}

${memory.dailyInsightCold}

${memory.dailyInsightHot}

## 相关关键事件
${relevantEvents || '暂无相关关键事件'}

## 最近对话历史（${dialogueRounds}轮）
${recentDialogue || '暂无最近对话'}
`.trim();
  
  console.log('✅ 日常对话上下文构建完成');
  
  return {
    systemPrompt,
    recentDialogue,
    contextMetadata: {
      dialogueRounds,
      hasRelevantEvents: !!relevantEvents,
      memoryFilesLoaded: [
        MEMORY_FILES.XIAO_JING_PERSONA,
        MEMORY_FILES.USER_PROFILE,
        MEMORY_FILES.MENTAL_ELEMENTS,
        MEMORY_FILES.DAILY_INSIGHT_COLD,
        MEMORY_FILES.DAILY_INSIGHT_HOT
      ]
    }
  };
}

/**
 * B. 每日洞察模式的上下文打包
 */
export async function buildDailyInsightContext(): Promise<DailyInsightContext> {
  console.log('📦 构建每日洞察上下文...');
  
  // 1. 加载记忆上下文和提示词
  const [memory, prompts] = await Promise.all([
    loadMemoryContext(),
    loadPromptContext()
  ]);
  
  // 2. 获取最近6轮对话记录
  const recentDialogue = await getRecentDialogueHistory(6);
  
  // 3. 基于当前热洞察进行意义RAG检索
  const relevantEvents = await meaningRAGSearch(memory.dailyInsightHot, MEMORY_FILES.KEY_EVENTS);
  
  // 4. 按照指定顺序构建系统提示词
  const systemPrompt = `
${memory.xiaoJingPersona}

${memory.userProfile}

${memory.mentalElements}

${memory.dailyInsightHot}

${prompts.dailyInsightPrompt}

## 相关关键事件
${relevantEvents || '暂无相关关键事件'}
`.trim();
  
  // 5. 准备分析内容
  const analysisContent = `
## 需要分析的对话内容

${recentDialogue || '暂无最近对话内容'}

## 分析指导

请基于以上对话内容，生成今日的洞察补充。重点关注：
1. 用户的情感变化和模式
2. 认知发展的新迹象  
3. 与历史模式的连接或变化
4. 值得在未来关注的主题

请直接输出洞察内容，无需其他格式包装。
`.trim();
  
  console.log('✅ 每日洞察上下文构建完成');
  
  return {
    systemPrompt,
    analysisContent,
    contextMetadata: {
      dialogueRounds: 6,
      insightSources: [MEMORY_FILES.DAILY_INSIGHT_HOT],
      relevantEventsCount: relevantEvents ? relevantEvents.split('\n').length : 0
    }
  };
}

/**
 * C. 深度精炼模式的上下文打包
 */
export async function buildDeepRefinementContext(): Promise<DeepRefinementContext> {
  console.log('📦 构建深度精炼上下文...');
  
  // 1. 加载记忆上下文和提示词
  const [memory, prompts] = await Promise.all([
    loadMemoryContext(),
    loadPromptContext()
  ]);
  
  // 2. 获取最近7天的所有历史对话记录
  const extendedDialogue = await getRecentDialogueHistory(50); // 粗略估计7天的对话量
  
  // 3. 基于冷洞察进行完整的意义RAG检索
  const relevantEvents = await meaningRAGSearch(memory.dailyInsightCold, MEMORY_FILES.KEY_EVENTS);
  
  // 4. 按照指定顺序构建系统提示词
  const systemPrompt = `
${memory.xiaoJingPersona}

${memory.userProfile}

${memory.mentalElements}

${prompts.deepRefinementPrompt}

${memory.dailyInsightCold}

## 相关关键事件（完整摘录）
${relevantEvents || '暂无相关关键事件'}
`.trim();
  
  // 5. 准备分析内容
  const analysisContent = `
## 需要分析的对话历史（最近7天）

${extendedDialogue || '暂无历史对话内容'}

## 深度精炼任务

请基于累积的记忆和对话历史，进行最深层的认知整合。你需要输出严格按照以下JSON格式的结构化结果：

{
  "analysis_summary": {
    "time_period": "分析的时间范围",
    "key_themes": ["核心主题1", "核心主题2", "核心主题3"],
    "major_insights": "最重要的发现和洞察"
  },
  "user_profile_updates": {
    "updated_content": "更新后的完整用户画像内容（Markdown格式）"
  },
  "mental_elements_updates": {
    "updated_content": "更新后的完整心智要素结构内容（Markdown格式）"
  },
  "key_events_annotations": [
    {
      "event_reference": "事件标识",
      "new_interpretation": "新的理解角度",
      "deeper_meaning": "更深层的意义"
    }
  ]
}

请确保输出的是有效的JSON格式。
`.trim();
  
  console.log('✅ 深度精炼上下文构建完成');
  
  return {
    systemPrompt,
    analysisContent,
    contextMetadata: {
      timeSpan: '最近7天',
      totalDialogueRounds: 50,
      memoryFilesAnalyzed: [
        MEMORY_FILES.USER_PROFILE,
        MEMORY_FILES.MENTAL_ELEMENTS,
        MEMORY_FILES.DAILY_INSIGHT_COLD,
        MEMORY_FILES.KEY_EVENTS
      ]
    }
  };
}

/**
 * 上下文质量检查
 */
export function validateContext(context: DailyChatContext | DailyInsightContext | DeepRefinementContext): boolean {
  if (!context.systemPrompt || context.systemPrompt.trim().length === 0) {
    console.warn('⚠️ 系统提示词为空');
    return false;
  }
  
  if (context.systemPrompt.length > 50000) {
    console.warn('⚠️ 系统提示词过长，可能影响性能');
  }
  
  return true;
}

/**
 * 获取上下文统计信息
 */
export function getContextStats(context: DailyChatContext | DailyInsightContext | DeepRefinementContext) {
  return {
    systemPromptLength: context.systemPrompt.length,
    systemPromptWords: context.systemPrompt.split(/\s+/).length,
    metadata: context.contextMetadata
  };
}
