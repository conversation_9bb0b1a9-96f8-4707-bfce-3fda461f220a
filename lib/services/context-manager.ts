// 统一上下文打包工厂
import {
  loadMemoryContext,
  loadPromptContext,
  getRecentDialogueHistory,
  MEMORY_FILES
} from "@/lib/storage/memory-manager";
import { RAGDebugParams } from "@/types/rag";
import {
  getUnified<PERSON>GRetriever,
  RAGRetrievalDepth,
  RAGRetrievalMode,
  UnifiedRAGConfig
} from "./unified-rag-retriever";

// 上下文模式枚举
export enum ContextMode {
  DAILY_CHAT = 'daily_chat',
  DAILY_INSIGHT = 'daily_insight',
  DEEP_REFINEMENT = 'deep_refinement',
  THREE_ENGINE = 'three_engine'
}

// RAG检索深度枚举
export enum RAGDepth {
  BASIC = 'basic',
  EXTENDED = 'extended',
  FULL = 'full'
}

// 统一上下文配置
export interface UnifiedContextConfig {
  mode: ContextMode;
  dialogueRounds: number;
  ragDepth: RAGDepth;
  includeInsights: boolean;
  includeEvents: boolean;
  ragParams?: RAGDebugParams;
}

// 统一上下文结果接口
export interface UnifiedContext {
  systemPrompt: string;
  content: string; // 主要内容（对话历史或分析内容）
  metadata: {
    mode: ContextMode;
    dialogueRounds: number;
    ragDepth: RAGDepth;
    hasRelevantEvents: boolean;
    memoryFilesLoaded: string[];
    processingTime: number;
    timestamp: string;
  };
}

// 向后兼容的接口
export interface DailyChatContext {
  systemPrompt: string;
  recentDialogue: string;
  contextMetadata: {
    dialogueRounds: number;
    hasRelevantEvents: boolean;
    memoryFilesLoaded: string[];
  };
}

export interface DailyInsightContext {
  systemPrompt: string;
  analysisContent: string;
  contextMetadata: {
    dialogueRounds: number;
    insightSources: string[];
    relevantEventsCount: number;
  };
}

export interface DeepRefinementContext {
  systemPrompt: string;
  analysisContent: string;
  contextMetadata: {
    timeSpan: string;
    totalDialogueRounds: number;
    memoryFilesAnalyzed: string[];
  };
}

/**
 * 统一上下文打包工厂
 * 整合所有上下文构建逻辑，通过配置参数控制不同模式的差异
 */
export class UnifiedContextPackagingFactory {
  private static instance: UnifiedContextPackagingFactory;

  private constructor() {}

  static getInstance(): UnifiedContextPackagingFactory {
    if (!UnifiedContextPackagingFactory.instance) {
      UnifiedContextPackagingFactory.instance = new UnifiedContextPackagingFactory();
    }
    return UnifiedContextPackagingFactory.instance;
  }

  /**
   * 统一的上下文构建方法
   */
  async buildContext(
    userMessage: string,
    config: UnifiedContextConfig
  ): Promise<UnifiedContext> {
    const startTime = Date.now();
    console.log(`📦 构建${config.mode}上下文...`);

    // 1. 加载基础记忆和提示词
    const [memory, prompts] = await Promise.all([
      loadMemoryContext(),
      loadPromptContext()
    ]);

    // 2. 获取对话历史
    const recentDialogue = await getRecentDialogueHistory(config.dialogueRounds);

    // 3. 执行RAG检索（根据深度配置）
    let relevantEvents = '';
    if (config.includeEvents) {
      relevantEvents = await this.performRAGSearch(
        userMessage,
        memory,
        config.ragDepth,
        config.ragParams
      );
    }

    // 4. 构建系统提示词（根据模式）
    const systemPrompt = this.buildSystemPrompt(memory, prompts, config, relevantEvents);

    // 5. 构建主要内容（根据模式）
    const content = this.buildMainContent(recentDialogue, config, memory);

    const processingTime = Date.now() - startTime;
    console.log(`✅ ${config.mode}上下文构建完成 - ${processingTime}ms`);

    return {
      systemPrompt,
      content,
      metadata: {
        mode: config.mode,
        dialogueRounds: config.dialogueRounds,
        ragDepth: config.ragDepth,
        hasRelevantEvents: !!relevantEvents,
        memoryFilesLoaded: this.getLoadedMemoryFiles(config),
        processingTime,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 执行RAG检索（使用统一RAG检索器）
   */
  private async performRAGSearch(
    query: string,
    memory: any,
    depth: RAGDepth,
    ragParams?: RAGDebugParams
  ): Promise<string> {
    const retriever = getUnifiedRAGRetriever();

    // 将RAGDepth映射到RAGRetrievalDepth
    let retrievalDepth: RAGRetrievalDepth;
    let searchQuery = query;

    switch (depth) {
      case RAGDepth.BASIC:
        retrievalDepth = RAGRetrievalDepth.BASIC;
        break;

      case RAGDepth.EXTENDED:
        retrievalDepth = RAGRetrievalDepth.EXTENDED;
        // 扩展检索：基于热洞察进行检索
        searchQuery = memory.dailyInsightHot || query;
        break;

      case RAGDepth.FULL:
        retrievalDepth = RAGRetrievalDepth.FULL;
        // 全量检索：基于冷洞察进行完整检索
        searchQuery = memory.dailyInsightCold || query;
        break;

      default:
        retrievalDepth = RAGRetrievalDepth.BASIC;
    }

    // 构建RAG配置
    const ragConfig: UnifiedRAGConfig = {
      depth: retrievalDepth,
      mode: ragParams?.mode === 'Vector Only' ? RAGRetrievalMode.VECTOR_ONLY :
            ragParams?.mode === 'Keyword Only' ? RAGRetrievalMode.KEYWORD_ONLY :
            RAGRetrievalMode.HYBRID,
      maxResults: ragParams?.finalCount || 5,
      similarityThreshold: ragParams?.similarityThreshold || 0.3,
      sourceFiles: [MEMORY_FILES.KEY_EVENTS],
      debugParams: ragParams
    };

    // 执行检索
    const result = await retriever.retrieve(searchQuery, ragConfig);
    return result.results.join('\n');
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(
    memory: any,
    prompts: any,
    config: UnifiedContextConfig,
    relevantEvents: string
  ): string {
    const basePrompt = `
${memory.xiaoJingPersona}

${memory.userProfile}

${memory.mentalElements}`;

    switch (config.mode) {
      case ContextMode.DAILY_CHAT:
        return `${basePrompt}

${prompts.dailyChatPrompt}

${config.includeInsights ? memory.dailyInsightCold : ''}

${config.includeInsights ? memory.dailyInsightHot : ''}

## 相关关键事件
${relevantEvents || '暂无相关关键事件'}`.trim();

      case ContextMode.DAILY_INSIGHT:
        return `${basePrompt}

${memory.dailyInsightHot}

${prompts.dailyInsightPrompt}

## 相关关键事件
${relevantEvents || '暂无相关关键事件'}`.trim();

      case ContextMode.DEEP_REFINEMENT:
        return `${basePrompt}

${prompts.deepRefinementPrompt}

${memory.dailyInsightCold}

## 相关关键事件（完整摘录）
${relevantEvents || '暂无相关关键事件'}`.trim();

      case ContextMode.THREE_ENGINE:
        return `${basePrompt}

${prompts.systemPrompt}

## 相关关键事件
${relevantEvents || '暂无相关关键事件'}`.trim();

      default:
        return basePrompt;
    }
  }

  /**
   * 构建主要内容
   */
  private buildMainContent(
    recentDialogue: string,
    config: UnifiedContextConfig,
    memory: any
  ): string {
    switch (config.mode) {
      case ContextMode.DAILY_CHAT:
        return `## 最近对话历史（${config.dialogueRounds}轮）
${recentDialogue || '暂无最近对话'}`;

      case ContextMode.DAILY_INSIGHT:
        return `## 需要分析的对话内容

${recentDialogue || '暂无最近对话内容'}

## 分析指导

请基于以上对话内容，生成今日的洞察补充。重点关注：
1. 用户的情感变化和模式
2. 认知发展的新迹象
3. 与历史模式的连接或变化
4. 值得在未来关注的主题

请直接输出洞察内容，无需其他格式包装。`;

      case ContextMode.DEEP_REFINEMENT:
        return `## 需要分析的对话历史（最近7天）

${recentDialogue || '暂无历史对话内容'}

## 深度精炼任务

请基于累积的记忆和对话历史，进行最深层的认知整合。你需要输出严格按照以下JSON格式的结构化结果：

{
  "analysis_summary": {
    "time_period": "分析的时间范围",
    "key_themes": ["核心主题1", "核心主题2", "核心主题3"],
    "major_insights": "最重要的发现和洞察"
  },
  "user_profile_updates": {
    "updated_content": "更新后的完整用户画像内容（Markdown格式）"
  },
  "mental_elements_updates": {
    "updated_content": "更新后的完整心智要素结构内容（Markdown格式）"
  },
  "key_events_annotations": [
    {
      "event_reference": "事件标识",
      "new_interpretation": "新的理解角度",
      "deeper_meaning": "更深层的意义"
    }
  ]
}

请确保输出的是有效的JSON格式。`;

      case ContextMode.THREE_ENGINE:
        return recentDialogue || '暂无对话历史';

      default:
        return recentDialogue || '';
    }
  }

  /**
   * 获取已加载的记忆文件列表
   */
  private getLoadedMemoryFiles(config: UnifiedContextConfig): string[] {
    const baseFiles = [
      MEMORY_FILES.XIAO_JING_PERSONA,
      MEMORY_FILES.USER_PROFILE,
      MEMORY_FILES.MENTAL_ELEMENTS
    ];

    if (config.includeInsights) {
      baseFiles.push(MEMORY_FILES.DAILY_INSIGHT_COLD, MEMORY_FILES.DAILY_INSIGHT_HOT);
    }

    if (config.includeEvents) {
      baseFiles.push(MEMORY_FILES.KEY_EVENTS);
    }

    return baseFiles;
  }
}

/**
 * A. 日常对话模式的上下文打包（向后兼容）
 */
export async function buildDailyChatContext(
  currentUserMessage: string,
  isInInsightMode: boolean = false,
  ragParams?: RAGDebugParams
): Promise<DailyChatContext> {
  console.log('📦 构建日常对话上下文（使用统一工厂）...');

  // 使用统一上下文打包工厂
  const factory = UnifiedContextPackagingFactory.getInstance();
  const config: UnifiedContextConfig = {
    mode: ContextMode.DAILY_CHAT,
    dialogueRounds: isInInsightMode ? 8 : 6,
    ragDepth: RAGDepth.BASIC,
    includeInsights: true,
    includeEvents: true,
    ragParams
  };

  const unifiedContext = await factory.buildContext(currentUserMessage, config);

  // 转换为向后兼容的格式
  return {
    systemPrompt: unifiedContext.systemPrompt,
    recentDialogue: unifiedContext.content,
    contextMetadata: {
      dialogueRounds: unifiedContext.metadata.dialogueRounds,
      hasRelevantEvents: unifiedContext.metadata.hasRelevantEvents,
      memoryFilesLoaded: unifiedContext.metadata.memoryFilesLoaded
    }
  };
}

/**
 * B. 每日洞察模式的上下文打包（向后兼容）
 */
export async function buildDailyInsightContext(): Promise<DailyInsightContext> {
  console.log('📦 构建每日洞察上下文（使用统一工厂）...');

  // 使用统一上下文打包工厂
  const factory = UnifiedContextPackagingFactory.getInstance();
  const config: UnifiedContextConfig = {
    mode: ContextMode.DAILY_INSIGHT,
    dialogueRounds: 6,
    ragDepth: RAGDepth.EXTENDED,
    includeInsights: true,
    includeEvents: true
  };

  const unifiedContext = await factory.buildContext('', config);

  // 转换为向后兼容的格式
  return {
    systemPrompt: unifiedContext.systemPrompt,
    analysisContent: unifiedContext.content,
    contextMetadata: {
      dialogueRounds: unifiedContext.metadata.dialogueRounds,
      insightSources: [MEMORY_FILES.DAILY_INSIGHT_HOT],
      relevantEventsCount: unifiedContext.metadata.hasRelevantEvents ? 1 : 0
    }
  };
}

/**
 * C. 深度精炼模式的上下文打包（向后兼容）
 */
export async function buildDeepRefinementContext(): Promise<DeepRefinementContext> {
  console.log('📦 构建深度精炼上下文（使用统一工厂）...');

  // 使用统一上下文打包工厂
  const factory = UnifiedContextPackagingFactory.getInstance();
  const config: UnifiedContextConfig = {
    mode: ContextMode.DEEP_REFINEMENT,
    dialogueRounds: 50, // 粗略估计7天的对话量
    ragDepth: RAGDepth.FULL,
    includeInsights: true,
    includeEvents: true
  };

  const unifiedContext = await factory.buildContext('', config);

  // 转换为向后兼容的格式
  return {
    systemPrompt: unifiedContext.systemPrompt,
    analysisContent: unifiedContext.content,
    contextMetadata: {
      timeSpan: '最近7天',
      totalDialogueRounds: unifiedContext.metadata.dialogueRounds,
      memoryFilesAnalyzed: [
        MEMORY_FILES.USER_PROFILE,
        MEMORY_FILES.MENTAL_ELEMENTS,
        MEMORY_FILES.DAILY_INSIGHT_COLD,
        MEMORY_FILES.KEY_EVENTS
      ]
    }
  };
}

/**
 * 上下文质量检查
 */
export function validateContext(context: DailyChatContext | DailyInsightContext | DeepRefinementContext): boolean {
  if (!context.systemPrompt || context.systemPrompt.trim().length === 0) {
    console.warn('⚠️ 系统提示词为空');
    return false;
  }
  
  if (context.systemPrompt.length > 50000) {
    console.warn('⚠️ 系统提示词过长，可能影响性能');
  }
  
  return true;
}

/**
 * 获取上下文统计信息
 */
export function getContextStats(context: DailyChatContext | DailyInsightContext | DeepRefinementContext) {
  return {
    systemPromptLength: context.systemPrompt.length,
    systemPromptWords: context.systemPrompt.split(/\s+/).length,
    metadata: context.contextMetadata
  };
}
