/**
 * SelfMirror 意义索引流水线
 * 
 * 这个模块负责将原始文档转换为具有意义批注的向量化知识块
 */

import { 
  IndexingConfig, 
  IndexingResult, 
  ChunkMetadata, 
  ChunkingRules,
  MeaningAnnotationRules,
  IndexingProgress,
  IndexingError
} from '@/types/meaning-rag';
import { embeddingModel } from '@/lib/services/embedding-model';

/**
 * 核心的文档处理与索引函数
 * @param documentText 待处理的文档原文
 * @param indexingConfig 来自调试台的索引配置参数
 * @param onProgress 进度回调函数
 */
export async function processAndIndexDocument(
  documentText: string,
  indexingConfig: IndexingConfig,
  onProgress?: (progress: IndexingProgress) => void
): Promise<IndexingResult> {
  const startTime = Date.now();
  const documentId = generateDocumentId(documentText);
  
  console.log('🔧 开始文档索引处理...', {
    documentLength: documentText.length,
    config: indexingConfig
  });

  try {
    // 步骤1：文档预处理和验证
    updateProgress(onProgress, {
      status: 'processing',
      currentStep: '文档预处理',
      progress: 10,
      processedChunks: 0,
      totalChunks: 0,
      logs: [{ timestamp: new Date(), level: 'info', message: '开始文档预处理...' }]
    });

    const preprocessedText = await preprocessDocument(documentText);
    
    // 步骤2：根据调试台配置进行"母块"切分
    updateProgress(onProgress, {
      status: 'processing',
      currentStep: '文档分块',
      progress: 20,
      processedChunks: 0,
      totalChunks: 0,
      logs: [{ timestamp: new Date(), level: 'info', message: '开始文档分块...' }]
    });

    const parentChunks = await chunkDocument(preprocessedText, indexingConfig.chunkingRules);
    
    updateProgress(onProgress, {
      status: 'processing',
      currentStep: '生成意义子块',
      progress: 30,
      processedChunks: 0,
      totalChunks: parentChunks.length,
      logs: [{ timestamp: new Date(), level: 'info', message: `文档分块完成，共${parentChunks.length}个块` }]
    });

    // 步骤3：为每个母块生成"意义子块"和批注
    const processedChunks: ChunkMetadata[] = [];
    const errors: Array<{ chunkIndex: number; error: string }> = [];

    for (let i = 0; i < parentChunks.length; i++) {
      try {
        updateProgress(onProgress, {
          status: 'processing',
          currentStep: `处理第${i + 1}/${parentChunks.length}个块`,
          progress: 30 + (i / parentChunks.length) * 50,
          processedChunks: i,
          totalChunks: parentChunks.length,
          logs: []
        });

        const chunk = await processChunk(
          parentChunks[i],
          i,
          documentId,
          indexingConfig
        );
        
        processedChunks.push(chunk);
      } catch (error) {
        console.error(`处理块 ${i} 时出错:`, error);
        errors.push({
          chunkIndex: i,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    // 步骤4：向量化并存储
    updateProgress(onProgress, {
      status: 'processing',
      currentStep: '向量化存储',
      progress: 85,
      processedChunks: processedChunks.length,
      totalChunks: parentChunks.length,
      logs: [{ timestamp: new Date(), level: 'info', message: '开始向量化存储...' }]
    });

    const indexedChunks = await vectorizeAndStore(processedChunks);

    // 步骤5：生成统计信息
    const stats = generateIndexingStats(indexedChunks);
    
    const result: IndexingResult = {
      documentId,
      totalChunks: parentChunks.length,
      processedChunks: indexedChunks.length,
      failedChunks: errors.length,
      indexingTime: Date.now() - startTime,
      chunkMetadata: indexedChunks,
      stats,
      errors: errors.length > 0 ? errors : undefined
    };

    updateProgress(onProgress, {
      status: 'completed',
      currentStep: '索引完成',
      progress: 100,
      processedChunks: indexedChunks.length,
      totalChunks: parentChunks.length,
      logs: [{ 
        timestamp: new Date(), 
        level: 'info', 
        message: `索引完成！处理${indexedChunks.length}个块，耗时${result.indexingTime}ms` 
      }]
    });

    console.log('✅ 文档索引处理完成', result);
    return result;

  } catch (error) {
    console.error('💥 文档索引处理失败:', error);
    
    updateProgress(onProgress, {
      status: 'error',
      currentStep: '索引失败',
      progress: 0,
      processedChunks: 0,
      totalChunks: 0,
      logs: [{ 
        timestamp: new Date(), 
        level: 'error', 
        message: `索引失败: ${error instanceof Error ? error.message : '未知错误'}` 
      }]
    });

    throw new IndexingError('文档索引处理失败', error);
  }
}

/**
 * 文档预处理
 * TODO: 实现文档清理、格式标准化等预处理逻辑
 */
async function preprocessDocument(documentText: string): Promise<string> {
  // TODO: 实现以下预处理功能：
  // 1. 清理特殊字符和格式
  // 2. 标准化换行符
  // 3. 处理编码问题
  // 4. 提取结构化信息（标题、段落等）
  
  console.log('📝 文档预处理 (TODO: 实现详细逻辑)');
  
  // 临时实现：基础清理
  return documentText
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    .replace(/\n{3,}/g, '\n\n')
    .trim();
}

/**
 * 文档分块策略
 * TODO: 实现多种分块算法
 */
async function chunkDocument(
  documentText: string, 
  chunkingRules: ChunkingRules
): Promise<string[]> {
  console.log('🔪 文档分块处理', chunkingRules);
  
  // TODO: 实现以下分块策略：
  // 1. recursive: 递归分块（优先按段落，然后按句子）
  // 2. sentence: 按句子分块
  // 3. paragraph: 按段落分块  
  // 4. semantic: 语义分块（基于语义相似度）
  
  // 临时实现：简单的字符数分块
  const chunks: string[] = [];
  const { chunkSize, overlap } = chunkingRules;
  
  for (let i = 0; i < documentText.length; i += chunkSize - overlap) {
    const chunk = documentText.slice(i, i + chunkSize);
    if (chunk.trim().length > 0) {
      chunks.push(chunk.trim());
    }
  }
  
  console.log(`📊 分块完成，共生成 ${chunks.length} 个块`);
  return chunks;
}

/**
 * 处理单个块：生成意义子块和批注
 */
async function processChunk(
  parentChunk: string,
  chunkIndex: number,
  documentId: string,
  config: IndexingConfig
): Promise<ChunkMetadata> {
  const startTime = Date.now();
  const chunkId = `${documentId}_chunk_${chunkIndex}`;
  
  // 生成意义子块
  const childChunk = await generateMeaningfulChildChunk(
    parentChunk, 
    config.childChunkPrompt
  );
  
  // 生成意义批注
  const annotations = await generateMeaningAnnotations(
    parentChunk,
    childChunk,
    config.meaningAnnotationRules
  );
  
  const metadata: ChunkMetadata = {
    id: chunkId,
    documentId,
    chunkIndex,
    content: parentChunk,
    childChunk,
    emotionalTags: annotations.emotionalTags || [],
    cognitiveThemes: annotations.cognitiveThemes || [],
    keywords: annotations.keywords || [],
    importanceScore: annotations.importanceScore || 0,
    createdAt: new Date(),
    processingTime: Date.now() - startTime,
    tokenCount: estimateTokenCount(parentChunk)
  };
  
  return metadata;
}

/**
 * 生成意义子块
 * TODO: 实现AI驱动的意义提取算法
 */
async function generateMeaningfulChildChunk(
  parentChunk: string,
  childChunkPrompt: string
): Promise<string> {
  // TODO: 实现以下功能：
  // 1. 使用AI模型根据提示词生成意义子块
  // 2. 提取核心概念和关键信息
  // 3. 生成简洁但信息丰富的摘要
  // 4. 保持与原文的语义一致性
  
  console.log('🧠 生成意义子块 (TODO: 实现AI生成逻辑)');
  
  // 临时实现：简单摘要
  const sentences = parentChunk.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
  const summary = sentences.slice(0, 2).join('。') + '。';
  
  return summary.length > 100 ? summary.substring(0, 100) + '...' : summary;
}

/**
 * 生成意义批注
 * TODO: 实现多维度的意义分析
 */
async function generateMeaningAnnotations(
  parentChunk: string,
  childChunk: string,
  rules: MeaningAnnotationRules
): Promise<Partial<ChunkMetadata>> {
  // TODO: 实现以下批注功能：
  // 1. 情感分析：提取情感标签
  // 2. 主题分析：识别认知主题
  // 3. 重要性评分：计算内容重要性
  // 4. 关键词提取：识别核心概念
  // 5. 摘要生成：生成结构化摘要
  
  console.log('📝 生成意义批注 (TODO: 实现详细分析逻辑)', rules);
  
  // 临时实现：基础分析
  const annotations: Partial<ChunkMetadata> = {
    emotionalTags: extractBasicEmotions(parentChunk),
    cognitiveThemes: extractBasicThemes(parentChunk),
    keywords: extractBasicKeywords(parentChunk),
    summary: childChunk,
    importanceScore: calculateBasicImportance(parentChunk)
  };
  
  return annotations;
}

/**
 * 向量化并存储
 */
async function vectorizeAndStore(chunks: ChunkMetadata[]): Promise<ChunkMetadata[]> {
  console.log('🔢 开始向量化处理...');

  const vectorizedChunks: ChunkMetadata[] = [];

  // 确保embedding模型已加载
  try {
    await embeddingModel.ensureLoaded();
  } catch (error) {
    console.warn('⚠️ Embedding模型加载失败，使用模拟向量:', error);
    // 如果模型加载失败，使用模拟向量作为fallback
    return chunks.map(chunk => ({
      ...chunk,
      vector: new Array(384).fill(0).map(() => Math.random() - 0.5)
    }));
  }

  for (const chunk of chunks) {
    try {
      console.log(`📝 向量化块: ${chunk.id}`);

      // 使用真实的embedding模型进行向量化
      // 优先使用childChunk（意义子块），如果没有则使用原始内容
      const textToVectorize = chunk.childChunk || chunk.content;
      const vector = await embeddingModel.encode(textToVectorize);

      vectorizedChunks.push({
        ...chunk,
        vector: vector
      });

      console.log(`✅ 块 ${chunk.id} 向量化完成，维度: ${vector.length}`);
    } catch (error) {
      console.error(`向量化块 ${chunk.id} 失败:`, error);
      // 即使向量化失败，也保留块的其他信息，使用模拟向量
      vectorizedChunks.push({
        ...chunk,
        vector: new Array(384).fill(0).map(() => Math.random() - 0.5)
      });
    }
  }

  console.log(`✅ 向量化完成，处理 ${vectorizedChunks.length} 个块`);
  return vectorizedChunks;
}

// ==================== 辅助函数 ====================

function generateDocumentId(documentText: string): string {
  // 简单的文档ID生成（基于内容哈希）
  const hash = documentText.length.toString(36) + Date.now().toString(36);
  return `doc_${hash}`;
}

function updateProgress(
  onProgress: ((progress: IndexingProgress) => void) | undefined,
  progress: IndexingProgress
) {
  if (onProgress) {
    onProgress(progress);
  }
}

function generateIndexingStats(chunks: ChunkMetadata[]) {
  const totalTokens = chunks.reduce((sum, chunk) => sum + chunk.tokenCount, 0);
  const totalImportance = chunks.reduce((sum, chunk) => sum + chunk.importanceScore, 0);
  
  const allEmotions = chunks.flatMap(chunk => chunk.emotionalTags);
  const allThemes = chunks.flatMap(chunk => chunk.cognitiveThemes);
  
  return {
    averageChunkSize: chunks.length > 0 ? 
      chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / chunks.length : 0,
    averageImportanceScore: chunks.length > 0 ? totalImportance / chunks.length : 0,
    totalTokens,
    uniqueEmotions: [...new Set(allEmotions)],
    uniqueThemes: [...new Set(allThemes)]
  };
}

function estimateTokenCount(text: string): number {
  // 简单的token估算（中文按字符数，英文按单词数）
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
  return chineseChars + englishWords;
}

// 临时实现的基础分析函数
function extractBasicEmotions(text: string): string[] {
  const emotionKeywords = {
    '快乐': ['开心', '高兴', '愉快', '兴奋', '满足'],
    '悲伤': ['难过', '伤心', '沮丧', '失落', '痛苦'],
    '愤怒': ['生气', '愤怒', '恼火', '烦躁', '不满'],
    '焦虑': ['担心', '紧张', '焦虑', '不安', '恐惧'],
    '平静': ['平静', '安静', '放松', '宁静', '舒适']
  };
  
  const emotions: string[] = [];
  for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      emotions.push(emotion);
    }
  }
  
  return emotions.length > 0 ? emotions : ['中性'];
}

function extractBasicThemes(text: string): string[] {
  const themeKeywords = {
    '工作': ['工作', '职业', '事业', '项目', '任务'],
    '学习': ['学习', '知识', '技能', '课程', '研究'],
    '生活': ['生活', '日常', '家庭', '朋友', '社交'],
    '健康': ['健康', '运动', '饮食', '睡眠', '医疗'],
    '情感': ['感情', '爱情', '友情', '亲情', '关系']
  };
  
  const themes: string[] = [];
  for (const [theme, keywords] of Object.entries(themeKeywords)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      themes.push(theme);
    }
  }
  
  return themes.length > 0 ? themes : ['通用'];
}

function extractBasicKeywords(text: string): string[] {
  // 简单的关键词提取（基于词频）
  const words = text.match(/[\u4e00-\u9fff]{2,}|[a-zA-Z]{3,}/g) || [];
  const wordCount = new Map<string, number>();
  
  words.forEach(word => {
    wordCount.set(word, (wordCount.get(word) || 0) + 1);
  });
  
  return Array.from(wordCount.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([word]) => word);
}

function calculateBasicImportance(text: string): number {
  // 简单的重要性计算（基于长度、关键词密度等）
  const length = text.length;
  const sentences = text.split(/[。！？.!?]/).length;
  const avgSentenceLength = length / sentences;
  
  // 归一化到0-1范围
  return Math.min(1, (avgSentenceLength / 50) * 0.7 + (length / 1000) * 0.3);
}
