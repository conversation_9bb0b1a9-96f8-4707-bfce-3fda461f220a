/**
 * Simplified Context Packaging Factory - 简化上下文包装工厂
 * 移除复杂的多维度排序算法，专注于基本的优化和质量过滤
 * 
 * 核心设计理念：
 * 1. 简化原则：将语义理解和重要性识别的责任交给LLM在Integration Generator阶段处理
 * 2. 基础优化：保留去重、质量过滤、智能截断、内容压缩等基本功能
 * 3. 历史加权集成：与历史ID加权系统集成，接收预排序的上下文项目
 */

import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import { historicalWeightingSystem, WeightedSortingResult } from './historical-weighting-system';

/**
 * 简化上下文项接口
 */
export interface SimplifiedContextItem {
  id: string;
  parentChunkId: string;
  content: string;
  metadata: {
    globalId: string;
    sourceType: string;
    sourceDocumentId: string;
    createdAt: string;
    finalWeightedScore: number;      // 来自历史加权系统的分数
    retrievalCount: number;          // 检索次数
  };
  packagingMetrics: {
    contentLength: number;
    lastPackaged: string;
  };
}

/**
 * 简化包装配置
 */
export interface SimplifiedPackagingConfig {
  maxContextLength: number;                // 最大上下文长度
  maxContextItems: number;                 // 最大上下文项目数
  qualityThresholds: {
    minWeightedScore: number;              // 最小加权分数阈值
    minContentLength: number;              // 最小内容长度
  };
  optimizationSettings: {
    enableDuplicateRemoval: boolean;       // 启用去重优化
    enableContentCompression: boolean;     // 启用内容压缩
    enableSmartTruncation: boolean;        // 启用智能截断
  };
  enableRealTimeAdjustment: boolean;       // 启用实时参数调整
}

/**
 * 简化上下文包装结果
 */
export interface SimplifiedContextPackage {
  packageId: string;
  timestamp: string;
  finalContext: string;
  contextItems: SimplifiedContextItem[];
  packageMetadata: {
    totalLength: number;
    itemCount: number;
    averageWeightedScore: number;
    optimizationsApplied: string[];
    qualityScore: number;
  };
  packagingStats: {
    processingTime: number;
    itemsProcessed: number;
    itemsIncluded: number;
    itemsFiltered: number;
    duplicatesRemoved: number;
    compressionRatio: number;
  };
}

/**
 * 简化包装统计
 */
interface SimplifiedPackagingStats {
  totalPackages: number;
  averageProcessingTime: number;
  averageContextLength: number;
  averageQualityScore: number;
  optimizationEffectiveness: {
    duplicateRemoval: number;
    contentCompression: number;
    smartTruncation: number;
  };
}

/**
 * 简化上下文包装工厂实现
 */
export class SimplifiedContextPackagingFactory {
  private config: SimplifiedPackagingConfig;
  private stats: SimplifiedPackagingStats;
  private initialized: boolean = false;

  constructor(config?: Partial<SimplifiedPackagingConfig>) {
    this.config = {
      maxContextLength: 4000,
      maxContextItems: 10,
      qualityThresholds: {
        minWeightedScore: 0.1,
        minContentLength: 20
      },
      optimizationSettings: {
        enableDuplicateRemoval: true,
        enableContentCompression: true,
        enableSmartTruncation: true
      },
      enableRealTimeAdjustment: true,
      ...config
    };

    this.stats = {
      totalPackages: 0,
      averageProcessingTime: 0,
      averageContextLength: 0,
      averageQualityScore: 0,
      optimizationEffectiveness: {
        duplicateRemoval: 0,
        contentCompression: 0,
        smartTruncation: 0
      }
    };
  }

  /**
   * 初始化简化包装工厂
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('📦 初始化简化上下文包装工厂...');
      
      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();
      
      // 确保历史加权系统已初始化
      await historicalWeightingSystem.initialize();
      
      this.initialized = true;
      console.log('📦 简化上下文包装工厂初始化完成');
    } catch (error) {
      console.error('❌ 简化上下文包装工厂初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建简化上下文包装
   * 接收来自历史加权系统的预排序结果
   */
  async createContextPackage(
    weightedResults: WeightedSortingResult[],
    userMessage: string
  ): Promise<SimplifiedContextPackage> {
    if (!this.initialized) {
      await this.initialize();
    }

    const startTime = Date.now();

    try {
      console.log(`📦 开始创建简化上下文包装: ${weightedResults.length} 个加权结果`);
      
      // 生成包装ID
      const packageId = await globalIdManager.generateUserInputId();
      
      // 转换为简化上下文项
      const contextItems = this.convertToSimplifiedContextItems(weightedResults);
      
      // 应用质量过滤
      const filteredItems = this.applyQualityFiltering(contextItems);
      
      // 应用优化
      const optimizedItems = this.applyOptimizations(filteredItems);
      
      // 构建最终上下文
      const finalContext = this.buildFinalContext(optimizedItems);
      
      // 计算统计信息
      const processingTime = Date.now() - startTime;
      const packageMetadata = this.calculatePackageMetadata(optimizedItems, finalContext);
      const packagingStats = this.calculatePackagingStats(
        contextItems,
        optimizedItems,
        processingTime
      );
      
      // 构建上下文包装
      const contextPackage: SimplifiedContextPackage = {
        packageId,
        timestamp: new Date().toISOString(),
        finalContext,
        contextItems: optimizedItems,
        packageMetadata,
        packagingStats
      };
      
      // 更新统计信息
      this.updateStats(contextPackage);
      
      console.log(`📦 简化上下文包装创建完成: ${processingTime}ms, ${optimizedItems.length} 项目, ${finalContext.length} 字符`);
      
      return contextPackage;

    } catch (error) {
      console.error('❌ 简化上下文包装创建失败:', error);
      throw error;
    }
  }

  /**
   * 转换为简化上下文项
   */
  private convertToSimplifiedContextItems(weightedResults: WeightedSortingResult[]): SimplifiedContextItem[] {
    return weightedResults.map(async (result, index) => {
      const itemId = await globalIdManager.generateUserInputId();
      
      return {
        id: itemId,
        parentChunkId: result.parentChunkId,
        content: result.content,
        metadata: {
          globalId: result.parentChunkId,
          sourceType: 'weighted_retrieval',
          sourceDocumentId: '',
          createdAt: new Date().toISOString(),
          finalWeightedScore: result.finalWeightedScore,
          retrievalCount: result.retrievalCount
        },
        packagingMetrics: {
          contentLength: result.content.length,
          lastPackaged: new Date().toISOString()
        }
      };
    }).map((promise, index) => {
      // 同步处理，简化实现
      return {
        id: `simplified-item-${index}`,
        parentChunkId: weightedResults[index].parentChunkId,
        content: weightedResults[index].content,
        metadata: {
          globalId: weightedResults[index].parentChunkId,
          sourceType: 'weighted_retrieval',
          sourceDocumentId: '',
          createdAt: new Date().toISOString(),
          finalWeightedScore: weightedResults[index].finalWeightedScore,
          retrievalCount: weightedResults[index].retrievalCount
        },
        packagingMetrics: {
          contentLength: weightedResults[index].content.length,
          lastPackaged: new Date().toISOString()
        }
      };
    });
  }

  /**
   * 应用质量过滤
   */
  private applyQualityFiltering(items: SimplifiedContextItem[]): SimplifiedContextItem[] {
    return items.filter(item => {
      return (
        item.metadata.finalWeightedScore >= this.config.qualityThresholds.minWeightedScore &&
        item.content.length >= this.config.qualityThresholds.minContentLength
      );
    });
  }

  /**
   * 应用优化
   */
  private applyOptimizations(items: SimplifiedContextItem[]): SimplifiedContextItem[] {
    let optimizedItems = [...items];
    const optimizationsApplied: string[] = [];

    // 1. 去重优化
    if (this.config.optimizationSettings.enableDuplicateRemoval) {
      const beforeCount = optimizedItems.length;
      optimizedItems = this.removeDuplicates(optimizedItems);
      const afterCount = optimizedItems.length;
      
      if (beforeCount > afterCount) {
        optimizationsApplied.push('duplicate_removal');
        this.stats.optimizationEffectiveness.duplicateRemoval += beforeCount - afterCount;
      }
    }

    // 2. 智能截断优化
    if (this.config.optimizationSettings.enableSmartTruncation) {
      optimizedItems = this.applySmartTruncation(optimizedItems);
      optimizationsApplied.push('smart_truncation');
      this.stats.optimizationEffectiveness.smartTruncation++;
    }

    // 3. 内容压缩优化
    if (this.config.optimizationSettings.enableContentCompression) {
      optimizedItems = this.applyContentCompression(optimizedItems);
      optimizationsApplied.push('content_compression');
      this.stats.optimizationEffectiveness.contentCompression++;
    }

    // 限制项目数量
    if (optimizedItems.length > this.config.maxContextItems) {
      optimizedItems = optimizedItems.slice(0, this.config.maxContextItems);
    }

    return optimizedItems;
  }

  /**
   * 去重优化
   */
  private removeDuplicates(items: SimplifiedContextItem[]): SimplifiedContextItem[] {
    const seen = new Set<string>();
    const uniqueItems: SimplifiedContextItem[] = [];

    for (const item of items) {
      // 基于父块ID的去重
      if (!seen.has(item.parentChunkId)) {
        seen.add(item.parentChunkId);
        uniqueItems.push(item);
      }
    }

    return uniqueItems;
  }

  /**
   * 智能截断优化
   */
  private applySmartTruncation(items: SimplifiedContextItem[]): SimplifiedContextItem[] {
    let totalLength = 0;
    const truncatedItems: SimplifiedContextItem[] = [];

    for (const item of items) {
      const itemLength = item.content.length;

      if (totalLength + itemLength <= this.config.maxContextLength) {
        truncatedItems.push(item);
        totalLength += itemLength;
      } else {
        // 尝试截断内容
        const remainingLength = this.config.maxContextLength - totalLength;
        if (remainingLength > 100) { // 至少保留100字符
          const truncatedContent = this.smartTruncateContent(item.content, remainingLength);
          const truncatedItem = {
            ...item,
            content: truncatedContent,
            packagingMetrics: {
              ...item.packagingMetrics,
              contentLength: truncatedContent.length
            }
          };
          truncatedItems.push(truncatedItem);
          break;
        } else {
          break;
        }
      }
    }

    return truncatedItems;
  }

  /**
   * 内容压缩优化
   */
  private applyContentCompression(items: SimplifiedContextItem[]): SimplifiedContextItem[] {
    return items.map(item => ({
      ...item,
      content: this.compressContent(item.content),
      packagingMetrics: {
        ...item.packagingMetrics,
        contentLength: this.compressContent(item.content).length
      }
    }));
  }

  /**
   * 构建最终上下文
   */
  private buildFinalContext(items: SimplifiedContextItem[]): string {
    const contextParts: string[] = [];

    // 添加上下文头部
    contextParts.push('以下是相关的上下文信息：\n');

    // 添加每个上下文项（已经按历史加权分数排序）
    items.forEach((item, index) => {
      const prefix = `${index + 1}. `;
      const content = item.content.trim();
      const scoreInfo = ` (权重: ${item.metadata.finalWeightedScore.toFixed(3)})`;

      contextParts.push(`${prefix}${content}${scoreInfo}\n`);
    });

    // 添加上下文尾部
    if (items.length > 0) {
      contextParts.push('\n请基于以上信息回答用户的问题。');
    }

    return contextParts.join('');
  }

  /**
   * 智能截断内容
   */
  private smartTruncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) {
      return content;
    }

    // 尝试在句子边界截断
    const sentences = content.split(/[。！？.!?]/);
    let truncated = '';

    for (const sentence of sentences) {
      if (truncated.length + sentence.length + 1 <= maxLength - 3) {
        truncated += sentence + '。';
      } else {
        break;
      }
    }

    if (truncated.length === 0) {
      // 如果没有完整句子，直接截断
      truncated = content.substring(0, maxLength - 3);
    }

    return truncated + '...';
  }

  /**
   * 压缩内容
   */
  private compressContent(content: string): string {
    // 简单的内容压缩：移除多余空白和重复标点
    return content
      .replace(/\s+/g, ' ')           // 多个空白字符替换为单个空格
      .replace(/[。]{2,}/g, '。')      // 多个句号替换为单个
      .replace(/[！]{2,}/g, '！')      // 多个感叹号替换为单个
      .replace(/[？]{2,}/g, '？')      // 多个问号替换为单个
      .trim();
  }

  /**
   * 计算包装元数据
   */
  private calculatePackageMetadata(
    items: SimplifiedContextItem[],
    finalContext: string
  ): SimplifiedContextPackage['packageMetadata'] {
    const totalWeightedScore = items.reduce((sum, item) => sum + item.metadata.finalWeightedScore, 0);
    const averageWeightedScore = items.length > 0 ? totalWeightedScore / items.length : 0;

    const qualityScore = averageWeightedScore; // 简化质量评分

    return {
      totalLength: finalContext.length,
      itemCount: items.length,
      averageWeightedScore: Math.round(averageWeightedScore * 1000) / 1000,
      optimizationsApplied: this.getAppliedOptimizations(),
      qualityScore: Math.round(qualityScore * 1000) / 1000
    };
  }

  /**
   * 计算包装统计
   */
  private calculatePackagingStats(
    originalItems: SimplifiedContextItem[],
    finalItems: SimplifiedContextItem[],
    processingTime: number
  ): SimplifiedContextPackage['packagingStats'] {
    const duplicatesRemoved = originalItems.length - finalItems.length;
    const originalLength = originalItems.reduce((sum, item) => sum + item.content.length, 0);
    const finalLength = finalItems.reduce((sum, item) => sum + item.content.length, 0);
    const compressionRatio = originalLength > 0 ? finalLength / originalLength : 1;

    return {
      processingTime,
      itemsProcessed: originalItems.length,
      itemsIncluded: finalItems.length,
      itemsFiltered: Math.max(0, originalItems.length - finalItems.length),
      duplicatesRemoved: Math.max(0, duplicatesRemoved),
      compressionRatio: Math.round(compressionRatio * 1000) / 1000
    };
  }

  /**
   * 获取应用的优化
   */
  private getAppliedOptimizations(): string[] {
    const optimizations: string[] = [];

    if (this.config.optimizationSettings.enableDuplicateRemoval) {
      optimizations.push('duplicate_removal');
    }
    if (this.config.optimizationSettings.enableContentCompression) {
      optimizations.push('content_compression');
    }
    if (this.config.optimizationSettings.enableSmartTruncation) {
      optimizations.push('smart_truncation');
    }

    return optimizations;
  }

  /**
   * 更新统计信息
   */
  private updateStats(contextPackage: SimplifiedContextPackage): void {
    this.stats.totalPackages++;

    // 更新平均处理时间
    this.stats.averageProcessingTime =
      (this.stats.averageProcessingTime * (this.stats.totalPackages - 1) +
       contextPackage.packagingStats.processingTime) / this.stats.totalPackages;

    // 更新平均上下文长度
    this.stats.averageContextLength =
      (this.stats.averageContextLength * (this.stats.totalPackages - 1) +
       contextPackage.packageMetadata.totalLength) / this.stats.totalPackages;

    // 更新平均质量分数
    this.stats.averageQualityScore =
      (this.stats.averageQualityScore * (this.stats.totalPackages - 1) +
       contextPackage.packageMetadata.qualityScore) / this.stats.totalPackages;
  }

  /**
   * 更新配置参数（实时调整）
   */
  updateConfig(newConfig: Partial<SimplifiedPackagingConfig>): void {
    if (!this.config.enableRealTimeAdjustment) {
      console.warn('⚠️ 实时参数调整未启用');
      return;
    }

    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    console.log('📦 简化包装配置已更新:', {
      old: oldConfig,
      new: this.config,
      changes: Object.keys(newConfig)
    });
  }

  /**
   * 获取包装统计信息
   */
  getPackagingStats(): SimplifiedPackagingStats {
    return { ...this.stats };
  }
}

// 导出默认实例
export const simplifiedContextPackagingFactory = new SimplifiedContextPackagingFactory();
