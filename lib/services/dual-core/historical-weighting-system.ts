/**
 * Historical ID Weighting System - 历史ID加权系统
 * 基于父块历史ID的重复出现模式实现简化而有效的排序优化
 * 
 * 核心设计理念：
 * 1. 简化原则：移除复杂的多维度排序算法，将语义理解交给LLM处理
 * 2. 历史ID加权：基于父块历史ID的重复出现模式实现排序优化
 * 3. 自然噪声抑制：通过历史加权机制自动过滤embedding检索中的噪声项目
 */

import { globalIdManager } from '@/lib/services/vector-database/global-id-system';

/**
 * 历史加权缓存项接口
 */
export interface HistoricalWeightingCache {
  parentChunkId: string;           // 父块历史ID
  currentRanking: number;          // 当前检索排名
  previousRankings: number[];      // 历史排名记录（最多保留2次）
  finalWeightedScore: number;      // 最终加权分数
  lastRetrievalTime: string;       // 最后检索时间
  retrievalCount: number;          // 检索次数统计
  metadata: {
    globalId: string;
    sourceType: string;
    sourceDocumentId: string;
    createdAt: string;
  };
}

/**
 * 子块到父块ID映射接口
 */
export interface ChunkMapping {
  subChunkId: string;              // 子块ID（embedding检索单位）
  parentChunkId: string;           // 父块历史ID
  content: string;                 // 子块内容
  lastMappedAt: string;           // 最后映射时间
}

/**
 * 历史加权配置接口
 */
export interface HistoricalWeightingConfig {
  maxHistoryDepth: number;                    // 历史记录深度（默认3）
  decayCoefficients: number[];                // 衰减系数 [1.0, 0.8, 0.6]
  topicChangeThreshold: number;               // 话题转换阈值（默认0.5）
  noiseEliminationThreshold: number;          // 噪声淘汰阈值（默认0.1）
  cacheCleanupInterval: number;               // 缓存清理间隔（默认1小时）
  maxCacheSize: number;                       // 最大缓存大小（默认1000）
  enableRealTimeAdjustment: boolean;          // 启用实时参数调整
}

/**
 * 检索结果项接口
 */
export interface RetrievalItem {
  subChunkId: string;              // 子块ID
  content: string;                 // 内容
  similarity: number;              // 相似度分数
  ranking: number;                 // 检索排名
}

/**
 * 加权排序结果接口
 */
export interface WeightedSortingResult {
  parentChunkId: string;           // 父块历史ID
  content: string;                 // 聚合内容
  finalWeightedScore: number;      // 最终加权分数
  currentRanking: number;          // 当前排名
  historicalRankings: number[];    // 历史排名
  retrievalCount: number;          // 检索次数
  isNoiseCandidate: boolean;       // 是否为噪声候选
}

/**
 * 话题转换检测结果接口
 */
export interface TopicChangeDetection {
  isTopicChanged: boolean;         // 是否发生话题转换
  unmatchedRatio: number;          // 未匹配比例
  threshold: number;               // 检测阈值
  previousTopicIds: string[];      // 上一话题的ID列表
  currentTopicIds: string[];       // 当前话题的ID列表
}

/**
 * 系统统计信息接口
 */
interface WeightingStats {
  totalRetrievals: number;         // 总检索次数
  totalParentChunks: number;       // 总父块数量
  averageWeightedScore: number;    // 平均加权分数
  topicChangeCount: number;        // 话题转换次数
  noiseEliminationCount: number;   // 噪声淘汰次数
  cacheHitRate: number;           // 缓存命中率
  lastCleanupTime: string;        // 最后清理时间
}

/**
 * 历史ID加权系统实现
 */
export class HistoricalWeightingSystem {
  private config: HistoricalWeightingConfig;
  private weightingCache: Map<string, HistoricalWeightingCache>;
  private chunkMappings: Map<string, ChunkMapping>;
  private stats: WeightingStats;
  private initialized: boolean = false;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config?: Partial<HistoricalWeightingConfig>) {
    this.config = {
      maxHistoryDepth: 3,
      decayCoefficients: [1.0, 0.8, 0.6],
      topicChangeThreshold: 0.5,
      noiseEliminationThreshold: 0.1,
      cacheCleanupInterval: 3600000, // 1小时
      maxCacheSize: 1000,
      enableRealTimeAdjustment: true,
      ...config
    };

    this.weightingCache = new Map();
    this.chunkMappings = new Map();
    this.stats = {
      totalRetrievals: 0,
      totalParentChunks: 0,
      averageWeightedScore: 0,
      topicChangeCount: 0,
      noiseEliminationCount: 0,
      cacheHitRate: 0,
      lastCleanupTime: new Date().toISOString()
    };
  }

  /**
   * 初始化历史加权系统
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔄 初始化历史ID加权系统...');
      
      // 确保全局ID管理器已初始化
      await globalIdManager.initialize();
      
      // 启动定期清理
      this.startCleanupTimer();
      
      this.initialized = true;
      console.log('🔄 历史ID加权系统初始化完成');
    } catch (error) {
      console.error('❌ 历史ID加权系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理检索结果并进行历史加权排序
   */
  async processRetrievalResults(
    retrievalItems: RetrievalItem[],
    userQuery: string
  ): Promise<WeightedSortingResult[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      console.log(`🔄 开始处理检索结果: ${retrievalItems.length} 个项目`);
      
      // 1. 映射子块到父块ID
      const parentChunkMappings = await this.mapSubChunksToParentChunks(retrievalItems);
      
      // 2. 检测话题转换
      const topicChange = this.detectTopicChange(parentChunkMappings);
      if (topicChange.isTopicChanged) {
        console.log(`🔄 检测到话题转换，清零历史加权缓存`);
        this.resetWeightingCache();
        this.stats.topicChangeCount++;
      }
      
      // 3. 更新历史加权缓存
      this.updateWeightingCache(parentChunkMappings);
      
      // 4. 计算最终加权分数
      const weightedResults = this.calculateWeightedScores(parentChunkMappings);
      
      // 5. 执行噪声淘汰
      const filteredResults = this.eliminateNoise(weightedResults);
      
      // 6. 按加权分数排序
      const sortedResults = filteredResults.sort((a, b) => b.finalWeightedScore - a.finalWeightedScore);
      
      // 7. 更新统计信息
      this.updateStats(sortedResults);
      
      console.log(`🔄 历史加权排序完成: ${sortedResults.length} 个结果`);
      
      return sortedResults;

    } catch (error) {
      console.error('❌ 历史加权排序失败:', error);
      throw error;
    }
  }

  /**
   * 映射子块到父块ID
   */
  private async mapSubChunksToParentChunks(
    retrievalItems: RetrievalItem[]
  ): Promise<Map<string, { items: RetrievalItem[], content: string }>> {
    const parentChunkMappings = new Map<string, { items: RetrievalItem[], content: string }>();

    for (const item of retrievalItems) {
      // 检查是否已有映射
      let mapping = this.chunkMappings.get(item.subChunkId);
      
      if (!mapping) {
        // 创建新的映射关系
        // 这里假设子块ID可以通过某种规则映射到父块ID
        // 实际实现中可能需要查询数据库或使用其他映射逻辑
        const parentChunkId = await this.deriveParentChunkId(item.subChunkId);
        
        mapping = {
          subChunkId: item.subChunkId,
          parentChunkId,
          content: item.content,
          lastMappedAt: new Date().toISOString()
        };
        
        this.chunkMappings.set(item.subChunkId, mapping);
      }

      // 聚合到父块
      if (!parentChunkMappings.has(mapping.parentChunkId)) {
        parentChunkMappings.set(mapping.parentChunkId, {
          items: [],
          content: ''
        });
      }

      const parentMapping = parentChunkMappings.get(mapping.parentChunkId)!;
      parentMapping.items.push(item);
      
      // 聚合内容（简单拼接，实际可能需要更智能的聚合策略）
      if (parentMapping.content) {
        parentMapping.content += '\n' + item.content;
      } else {
        parentMapping.content = item.content;
      }
    }

    return parentChunkMappings;
  }

  /**
   * 从子块ID推导父块ID
   */
  private async deriveParentChunkId(subChunkId: string): Promise<string> {
    // 这里实现子块ID到父块ID的映射逻辑
    // 可能需要查询全局ID系统或使用特定的命名规则
    
    // 简化实现：假设子块ID包含父块信息
    // 实际实现中可能需要更复杂的逻辑
    if (subChunkId.includes('-sub-')) {
      return subChunkId.split('-sub-')[0];
    }
    
    // 如果无法推导，生成一个新的父块ID
    return await globalIdManager.generateUserInputId();
  }

  /**
   * 检测话题转换
   */
  private detectTopicChange(
    currentMappings: Map<string, { items: RetrievalItem[], content: string }>
  ): TopicChangeDetection {
    const currentTopicIds = Array.from(currentMappings.keys());
    const previousTopicIds = Array.from(this.weightingCache.keys());
    
    if (previousTopicIds.length === 0) {
      return {
        isTopicChanged: false,
        unmatchedRatio: 0,
        threshold: this.config.topicChangeThreshold,
        previousTopicIds,
        currentTopicIds
      };
    }

    // 计算未匹配的比例
    const unmatchedCount = currentTopicIds.filter(id => !previousTopicIds.includes(id)).length;
    const unmatchedRatio = unmatchedCount / currentTopicIds.length;
    
    const isTopicChanged = unmatchedRatio > this.config.topicChangeThreshold;

    return {
      isTopicChanged,
      unmatchedRatio,
      threshold: this.config.topicChangeThreshold,
      previousTopicIds,
      currentTopicIds
    };
  }

  /**
   * 重置加权缓存
   */
  private resetWeightingCache(): void {
    this.weightingCache.clear();
    console.log('🔄 历史加权缓存已重置');
  }

  /**
   * 更新历史加权缓存
   */
  private updateWeightingCache(
    parentChunkMappings: Map<string, { items: RetrievalItem[], content: string }>
  ): void {
    const currentTime = new Date().toISOString();

    for (const [parentChunkId, mapping] of parentChunkMappings) {
      // 计算当前排名（基于最佳子块排名）
      const bestRanking = Math.min(...mapping.items.map(item => item.ranking));
      
      let cacheItem = this.weightingCache.get(parentChunkId);
      
      if (!cacheItem) {
        // 创建新的缓存项
        cacheItem = {
          parentChunkId,
          currentRanking: bestRanking,
          previousRankings: [],
          finalWeightedScore: 0,
          lastRetrievalTime: currentTime,
          retrievalCount: 1,
          metadata: {
            globalId: parentChunkId,
            sourceType: 'retrieval',
            sourceDocumentId: '',
            createdAt: currentTime
          }
        };
      } else {
        // 更新现有缓存项
        // 将当前排名移到历史记录
        cacheItem.previousRankings.unshift(cacheItem.currentRanking);
        
        // 保持历史记录深度
        if (cacheItem.previousRankings.length > this.config.maxHistoryDepth - 1) {
          cacheItem.previousRankings = cacheItem.previousRankings.slice(0, this.config.maxHistoryDepth - 1);
        }
        
        // 更新当前排名
        cacheItem.currentRanking = bestRanking;
        cacheItem.lastRetrievalTime = currentTime;
        cacheItem.retrievalCount++;
      }

      this.weightingCache.set(parentChunkId, cacheItem);
    }

    // 对未在当前检索中出现的项目进行衰减
    this.applyDecayToUnretrievedItems(Array.from(parentChunkMappings.keys()));
  }

  /**
   * 对未检索到的项目应用衰减
   */
  private applyDecayToUnretrievedItems(currentParentChunkIds: string[]): void {
    for (const [parentChunkId, cacheItem] of this.weightingCache) {
      if (!currentParentChunkIds.includes(parentChunkId)) {
        // 应用0.8衰减系数
        cacheItem.finalWeightedScore *= 0.8;
        console.log(`🔄 对未检索项目应用衰减: ${parentChunkId} (新分数: ${cacheItem.finalWeightedScore.toFixed(3)})`);
      }
    }
  }

  /**
   * 计算最终加权分数
   * 算法：最终权重 = 当前检索排名×1.0 + 前一次检索排名×0.8 + 前两次检索排名×0.6
   */
  private calculateWeightedScores(
    parentChunkMappings: Map<string, { items: RetrievalItem[], content: string }>
  ): WeightedSortingResult[] {
    const results: WeightedSortingResult[] = [];

    for (const [parentChunkId, mapping] of parentChunkMappings) {
      const cacheItem = this.weightingCache.get(parentChunkId)!;

      // 计算加权分数
      let weightedScore = 0;
      const coefficients = this.config.decayCoefficients;

      // 当前排名权重
      weightedScore += (1 / cacheItem.currentRanking) * coefficients[0];

      // 历史排名权重
      for (let i = 0; i < cacheItem.previousRankings.length && i < coefficients.length - 1; i++) {
        const historicalRanking = cacheItem.previousRankings[i];
        weightedScore += (1 / historicalRanking) * coefficients[i + 1];
      }

      // 更新缓存中的最终分数
      cacheItem.finalWeightedScore = weightedScore;

      results.push({
        parentChunkId,
        content: mapping.content,
        finalWeightedScore: weightedScore,
        currentRanking: cacheItem.currentRanking,
        historicalRankings: [...cacheItem.previousRankings],
        retrievalCount: cacheItem.retrievalCount,
        isNoiseCandidate: weightedScore < this.config.noiseEliminationThreshold
      });
    }

    return results;
  }

  /**
   * 执行噪声淘汰
   */
  private eliminateNoise(results: WeightedSortingResult[]): WeightedSortingResult[] {
    const filteredResults = results.filter(result => !result.isNoiseCandidate);

    const eliminatedCount = results.length - filteredResults.length;
    if (eliminatedCount > 0) {
      console.log(`🔄 噪声淘汰: 移除 ${eliminatedCount} 个低权重项目`);
      this.stats.noiseEliminationCount += eliminatedCount;

      // 从缓存中移除被淘汰的项目
      for (const result of results) {
        if (result.isNoiseCandidate) {
          this.weightingCache.delete(result.parentChunkId);
        }
      }
    }

    return filteredResults;
  }

  /**
   * 更新统计信息
   */
  private updateStats(results: WeightedSortingResult[]): void {
    this.stats.totalRetrievals++;
    this.stats.totalParentChunks = this.weightingCache.size;

    if (results.length > 0) {
      const totalScore = results.reduce((sum, result) => sum + result.finalWeightedScore, 0);
      this.stats.averageWeightedScore = totalScore / results.length;
    }

    // 计算缓存命中率
    const hitCount = results.filter(result => result.retrievalCount > 1).length;
    this.stats.cacheHitRate = results.length > 0 ? hitCount / results.length : 0;
  }

  /**
   * 启动定期清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.config.cacheCleanupInterval);
  }

  /**
   * 执行缓存清理
   */
  private performCleanup(): void {
    const currentTime = Date.now();
    const cleanupThreshold = this.config.cacheCleanupInterval * 2; // 2倍清理间隔作为过期阈值

    let cleanedCount = 0;

    for (const [parentChunkId, cacheItem] of this.weightingCache) {
      const lastRetrievalTime = new Date(cacheItem.lastRetrievalTime).getTime();

      if (currentTime - lastRetrievalTime > cleanupThreshold) {
        this.weightingCache.delete(parentChunkId);
        cleanedCount++;
      }
    }

    // 清理子块映射
    for (const [subChunkId, mapping] of this.chunkMappings) {
      const lastMappedTime = new Date(mapping.lastMappedAt).getTime();

      if (currentTime - lastMappedTime > cleanupThreshold) {
        this.chunkMappings.delete(subChunkId);
      }
    }

    this.stats.lastCleanupTime = new Date().toISOString();

    if (cleanedCount > 0) {
      console.log(`🔄 缓存清理完成: 清理 ${cleanedCount} 个过期项目`);
    }
  }

  /**
   * 实时更新配置参数
   */
  updateConfig(newConfig: Partial<HistoricalWeightingConfig>): void {
    if (!this.config.enableRealTimeAdjustment) {
      console.warn('⚠️ 实时参数调整未启用');
      return;
    }

    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    console.log('🔄 历史加权配置已更新:', {
      old: oldConfig,
      new: this.config,
      changes: Object.keys(newConfig)
    });
  }

  /**
   * 获取系统统计信息
   */
  getStats(): WeightingStats {
    return { ...this.stats };
  }

  /**
   * 获取当前缓存状态
   */
  getCacheStatus(): {
    cacheSize: number;
    mappingSize: number;
    topParentChunks: Array<{
      parentChunkId: string;
      finalWeightedScore: number;
      retrievalCount: number;
    }>;
  } {
    const topParentChunks = Array.from(this.weightingCache.values())
      .sort((a, b) => b.finalWeightedScore - a.finalWeightedScore)
      .slice(0, 10)
      .map(item => ({
        parentChunkId: item.parentChunkId,
        finalWeightedScore: item.finalWeightedScore,
        retrievalCount: item.retrievalCount
      }));

    return {
      cacheSize: this.weightingCache.size,
      mappingSize: this.chunkMappings.size,
      topParentChunks
    };
  }

  /**
   * 销毁系统，清理资源
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    this.weightingCache.clear();
    this.chunkMappings.clear();
    this.initialized = false;

    console.log('🔄 历史ID加权系统已销毁');
  }
}

// 导出默认实例
export const historicalWeightingSystem = new HistoricalWeightingSystem();
