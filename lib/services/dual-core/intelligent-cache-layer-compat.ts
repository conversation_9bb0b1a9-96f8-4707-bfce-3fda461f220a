/**
 * Intelligent Cache Layer - 向后兼容包装器
 * 
 * ⚠️ 术语澄清说明：
 * 此模块已重命名为"检索结果优化器(RetrievalResultOptimizer)"以避免与传统"缓存"概念混淆。
 * 实际功能：历史加权衰减排序、噪声抑制、归零重置等检索结果优化算法
 * 
 * 为保持向后兼容性，此文件保留原有接口，内部委托给新的RetrievalResultOptimizer实现。
 */

import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import { 
  RetrievalResultOptimizer, 
  OptimizationItem, 
  OptimizationConfig,
  OptimizationStats
} from './retrieval-result-optimizer';

/**
 * 缓存项接口（向后兼容）
 * @deprecated 请使用 OptimizationItem 替代
 */
export interface CacheItem {
  id: string;
  parentChunkId: string;
  content: string;
  vector?: number[];
  metadata: {
    globalId: string;
    sourceType: string;
    sourceDocumentId: string;
    createdAt: string;
    lastAccessedAt: string;
    accessCount: number;
    relevanceScore: number;
    semanticScore: number;
    temporalScore: number;
  };
  cacheMetrics: {
    hitCount: number;
    missCount: number;
    weightedScore: number;
    decayFactor: number;
    noiseLevel: number;
    lastDecayTime: string;
  };
}

/**
 * 智能缓存配置（向后兼容）
 * @deprecated 请使用 OptimizationConfig 替代
 */
export interface IntelligentCacheConfig {
  maxCacheSize: number;
  decayCoefficients: {
    primary: number;    // 主要衰减系数 (默认: 1.0)
    secondary: number;  // 次要衰减系数 (默认: 0.8)
    tertiary: number;   // 三级衰减系数 (默认: 0.6)
  };
  tailEliminationThreshold: number;  // 尾部淘汰阈值 (默认: 0.1)
  noiseSuppressionThreshold: number; // 噪声抑制阈值 (默认: 0.05)
  zeroResetThreshold: number;        // 归零重置阈值 (默认: 0.02)
  noveltyThreshold: number;          // 新颖性阈值 (默认: 0.7)
  decayInterval: number;             // 衰减间隔 (毫秒, 默认: 300000 = 5分钟)
  enableRealTimeAdjustment: boolean; // 启用实时参数调整
}

/**
 * 缓存统计信息（向后兼容）
 * @deprecated 请使用 OptimizationStats 替代
 */
export interface CacheStats {
  totalItems: number;
  hitRate: number;
  missRate: number;
  averageWeightedScore: number;
  decayOperations: number;
  eliminationOperations: number;
  resetOperations: number;
  noiseSuppressionOperations: number;
  lastOptimizationTime: string;
}

/**
 * 智能缓存层实现（向后兼容包装器）
 * 
 * ⚠️ 重要说明：此类现在是RetrievalResultOptimizer的向后兼容包装器
 * 实际的检索结果优化逻辑已迁移到RetrievalResultOptimizer类中
 * 
 * @deprecated 请直接使用 RetrievalResultOptimizer 类
 */
export class IntelligentCacheLayer {
  private optimizer: RetrievalResultOptimizer;
  private legacyConfig: IntelligentCacheConfig;

  constructor(config?: Partial<IntelligentCacheConfig>) {
    // 保存传统配置格式
    this.legacyConfig = {
      maxCacheSize: 1000,
      decayCoefficients: {
        primary: 1.0,
        secondary: 0.8,
        tertiary: 0.6
      },
      tailEliminationThreshold: 0.1,
      noiseSuppressionThreshold: 0.05,
      zeroResetThreshold: 0.02,
      noveltyThreshold: 0.7,
      decayInterval: 300000, // 5分钟
      enableRealTimeAdjustment: true,
      ...config
    };

    // 转换为新的优化配置格式
    const optimizationConfig: Partial<OptimizationConfig> = {
      maxOptimizationItems: this.legacyConfig.maxCacheSize,
      decayInterval: this.legacyConfig.decayInterval,
      decayRate: this.legacyConfig.decayCoefficients.secondary,
      noiseThreshold: this.legacyConfig.noiseSuppressionThreshold,
      zeroResetThreshold: this.legacyConfig.zeroResetThreshold,
      qualityThreshold: this.legacyConfig.noveltyThreshold,
      enableRealTimeOptimization: this.legacyConfig.enableRealTimeAdjustment
    };

    // 创建新的优化器实例
    this.optimizer = new RetrievalResultOptimizer(optimizationConfig);
    
    console.log('⚠️ IntelligentCacheLayer已重构为RetrievalResultOptimizer的兼容包装器');
  }

  /**
   * 初始化智能缓存层（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.initialize() 替代
   */
  async initialize(): Promise<void> {
    console.log('🧠 初始化智能缓存层（兼容模式）...');
    
    try {
      // 委托给新的优化器
      await this.optimizer.initialize();
      
      console.log('🧠 智能缓存层初始化完成（已迁移到RetrievalResultOptimizer）');
    } catch (error) {
      console.error('❌ 智能缓存层初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加缓存项（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.optimizeRetrievalResults() 替代
   */
  async addCacheItem(
    parentChunkId: string,
    content: string,
    metadata: any,
    vector?: number[]
  ): Promise<string> {
    console.log('⚠️ addCacheItem已弃用，建议使用RetrievalResultOptimizer.optimizeRetrievalResults()');
    
    try {
      // 转换为新的格式并委托给优化器
      const mockResult = {
        id: parentChunkId,
        parentChunkId,
        content,
        vector,
        similarity: metadata.relevanceScore || 0.5,
        sourceType: metadata.sourceType || 'unknown',
        sourceDocumentId: metadata.sourceDocumentId || ''
      };

      // 使用优化器处理
      const optimizedResults = await this.optimizer.optimizeRetrievalResults([mockResult]);
      
      if (optimizedResults.length > 0) {
        const optimizedItem = optimizedResults[0];
        console.log(`🧠 项目已通过优化器处理: ${parentChunkId}`);
        return optimizedItem.id || parentChunkId;
      } else {
        console.log(`🧠 项目被优化器过滤: ${parentChunkId}`);
        return parentChunkId;
      }
    } catch (error) {
      console.error('❌ 添加缓存项失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存项（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.getOptimizationItem() 替代
   */
  async getCacheItem(cacheId: string): Promise<CacheItem | null> {
    console.log('⚠️ getCacheItem已弃用，建议使用RetrievalResultOptimizer.getOptimizationItem()');
    
    try {
      // 委托给优化器
      const optimizationItem = await this.optimizer.getOptimizationItem(cacheId);
      
      if (optimizationItem) {
        // 转换为旧格式
        const legacyItem: CacheItem = {
          id: optimizationItem.id,
          parentChunkId: optimizationItem.parentChunkId,
          content: optimizationItem.content,
          vector: optimizationItem.vector,
          metadata: optimizationItem.metadata,
          cacheMetrics: {
            hitCount: optimizationItem.optimizationMetrics.hitCount,
            missCount: optimizationItem.optimizationMetrics.missCount,
            weightedScore: optimizationItem.optimizationMetrics.weightedScore,
            decayFactor: optimizationItem.optimizationMetrics.decayFactor,
            noiseLevel: optimizationItem.optimizationMetrics.noiseLevel,
            lastDecayTime: optimizationItem.optimizationMetrics.lastDecayTime
          }
        };
        
        console.log(`🧠 优化项命中: ${cacheId} (权重: ${legacyItem.cacheMetrics.weightedScore.toFixed(3)})`);
        return legacyItem;
      } else {
        console.log(`🧠 优化项未命中: ${cacheId}`);
        return null;
      }
    } catch (error) {
      console.error('❌ 获取缓存项失败:', error);
      return null;
    }
  }

  /**
   * 获取统计信息（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.getOptimizationStats() 替代
   */
  getCacheStats(): CacheStats {
    console.log('⚠️ getCacheStats已弃用，建议使用RetrievalResultOptimizer.getOptimizationStats()');
    
    const optimizationStats = this.optimizer.getOptimizationStats();
    
    // 转换为旧格式
    return {
      totalItems: optimizationStats.totalItems,
      hitRate: optimizationStats.hitRate,
      missRate: optimizationStats.missRate,
      averageWeightedScore: optimizationStats.averageWeightedScore,
      decayOperations: 0, // 这些在新架构中不再单独跟踪
      eliminationOperations: 0,
      resetOperations: 0,
      noiseSuppressionOperations: optimizationStats.noiseFilteredItems,
      lastOptimizationTime: new Date().toISOString()
    };
  }

  /**
   * 获取配置信息（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.getOptimizationConfig() 替代
   */
  getConfig(): IntelligentCacheConfig {
    return { ...this.legacyConfig };
  }

  /**
   * 更新配置（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.updateOptimizationConfig() 替代
   */
  updateConfig(newConfig: Partial<IntelligentCacheConfig>): void {
    console.log('⚠️ updateConfig已弃用，建议使用RetrievalResultOptimizer.updateOptimizationConfig()');
    
    // 更新传统配置
    this.legacyConfig = { ...this.legacyConfig, ...newConfig };
    
    // 转换并更新优化器配置
    const optimizationConfig: Partial<OptimizationConfig> = {
      maxOptimizationItems: newConfig.maxCacheSize,
      decayInterval: newConfig.decayInterval,
      decayRate: newConfig.decayCoefficients?.secondary,
      noiseThreshold: newConfig.noiseSuppressionThreshold,
      zeroResetThreshold: newConfig.zeroResetThreshold,
      qualityThreshold: newConfig.noveltyThreshold,
      enableRealTimeOptimization: newConfig.enableRealTimeAdjustment
    };
    
    this.optimizer.updateOptimizationConfig(optimizationConfig);
    
    console.log('🧠 智能缓存层配置已更新（通过优化器）:', newConfig);
  }

  /**
   * 清空缓存（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.destroy() 和重新初始化替代
   */
  clearCache(): void {
    console.log('⚠️ clearCache已弃用，建议使用RetrievalResultOptimizer的重新初始化');
    // 重新创建优化器实例来清空状态
    const currentConfig = this.optimizer.getOptimizationConfig();
    this.optimizer.destroy();
    this.optimizer = new RetrievalResultOptimizer(currentConfig);
    console.log('🧠 智能缓存层已清空（通过重新创建优化器）');
  }

  /**
   * 销毁缓存层（向后兼容）
   * @deprecated 请使用 RetrievalResultOptimizer.destroy() 替代
   */
  destroy(): void {
    console.log('🧠 销毁智能缓存层（兼容模式）...');
    this.optimizer.destroy();
  }
}

// 导出默认实例（向后兼容）
export const intelligentCacheLayer = new IntelligentCacheLayer();

// 导出类型别名以保持向后兼容
export type { OptimizationItem as CacheItem };
export type { OptimizationConfig as IntelligentCacheConfig };
export type { OptimizationStats as CacheStats };

// 导出新的优化器类作为推荐替代
export { RetrievalResultOptimizer } from './retrieval-result-optimizer';
