/**
 * 统一RAG检索抽象层
 * 整合分散的RAG检索逻辑，提供统一的检索接口
 */

import { meaningRAGSearch, MEMORY_FILES } from "@/lib/storage/memory-manager";
import { RAGDebugParams } from "@/types/rag";

// RAG检索深度枚举
export enum RAGRetrievalDepth {
  BASIC = 'basic',
  EXTENDED = 'extended', 
  FULL = 'full'
}

// RAG检索模式枚举
export enum RAGRetrievalMode {
  KEYWORD_ONLY = 'keyword_only',
  VECTOR_ONLY = 'vector_only',
  HYBRID = 'hybrid'
}

// 统一RAG检索配置
export interface UnifiedRAGConfig {
  depth: RAGRetrievalDepth;
  mode: RAGRetrievalMode;
  maxResults: number;
  similarityThreshold: number;
  sourceFiles: string[];
  debugParams?: RAGDebugParams;
}

// RAG检索结果
export interface RAGRetrievalResult {
  results: string[];
  metadata: {
    totalCandidates: number;
    filteredResults: number;
    processingTime: number;
    sourceFiles: string[];
    depth: RAGRetrievalDepth;
    mode: RAGRetrievalMode;
  };
}

/**
 * 统一RAG检索器
 */
export class UnifiedRAGRetriever {
  private static instance: UnifiedRAGRetriever;

  private constructor() {}

  static getInstance(): UnifiedRAGRetriever {
    if (!UnifiedRAGRetriever.instance) {
      UnifiedRAGRetriever.instance = new UnifiedRAGRetriever();
    }
    return UnifiedRAGRetriever.instance;
  }

  /**
   * 统一的RAG检索方法
   */
  async retrieve(
    query: string,
    config: UnifiedRAGConfig
  ): Promise<RAGRetrievalResult> {
    const startTime = Date.now();
    console.log(`🔍 开始统一RAG检索 - 深度: ${config.depth}, 模式: ${config.mode}`);

    const results: string[] = [];
    let totalCandidates = 0;

    try {
      // 根据深度和模式执行不同的检索策略
      for (const sourceFile of config.sourceFiles) {
        const fileResults = await this.retrieveFromFile(
          query,
          sourceFile,
          config
        );
        
        if (fileResults) {
          results.push(fileResults);
          totalCandidates++;
        }
      }

      // 应用结果过滤和排序
      const filteredResults = this.filterAndSortResults(results, config);

      const processingTime = Date.now() - startTime;
      console.log(`✅ 统一RAG检索完成 - ${filteredResults.length}个结果 - ${processingTime}ms`);

      return {
        results: filteredResults,
        metadata: {
          totalCandidates,
          filteredResults: filteredResults.length,
          processingTime,
          sourceFiles: config.sourceFiles,
          depth: config.depth,
          mode: config.mode
        }
      };

    } catch (error) {
      console.error('❌ 统一RAG检索失败:', error);
      return {
        results: [],
        metadata: {
          totalCandidates: 0,
          filteredResults: 0,
          processingTime: Date.now() - startTime,
          sourceFiles: config.sourceFiles,
          depth: config.depth,
          mode: config.mode
        }
      };
    }
  }

  /**
   * 从单个文件检索
   */
  private async retrieveFromFile(
    query: string,
    sourceFile: string,
    config: UnifiedRAGConfig
  ): Promise<string> {
    switch (config.mode) {
      case RAGRetrievalMode.KEYWORD_ONLY:
        return this.keywordSearch(query, sourceFile, config);
      
      case RAGRetrievalMode.VECTOR_ONLY:
        // TODO: 实现向量检索
        console.log('⚠️ 向量检索暂未实现，回退到关键词检索');
        return this.keywordSearch(query, sourceFile, config);
      
      case RAGRetrievalMode.HYBRID:
        // TODO: 实现混合检索
        console.log('⚠️ 混合检索暂未实现，回退到关键词检索');
        return this.keywordSearch(query, sourceFile, config);
      
      default:
        return this.keywordSearch(query, sourceFile, config);
    }
  }

  /**
   * 关键词搜索（基于现有的meaningRAGSearch）
   */
  private async keywordSearch(
    query: string,
    sourceFile: string,
    config: UnifiedRAGConfig
  ): Promise<string> {
    // 根据深度调整查询策略
    let searchQuery = query;
    
    switch (config.depth) {
      case RAGRetrievalDepth.BASIC:
        // 基础检索：直接使用原始查询
        break;
      
      case RAGRetrievalDepth.EXTENDED:
        // 扩展检索：可以添加更多上下文
        searchQuery = query;
        break;
      
      case RAGRetrievalDepth.FULL:
        // 全量检索：使用更广泛的搜索策略
        searchQuery = query;
        break;
    }

    return meaningRAGSearch(searchQuery, sourceFile);
  }

  /**
   * 过滤和排序结果
   */
  private filterAndSortResults(
    results: string[],
    config: UnifiedRAGConfig
  ): string[] {
    // 移除空结果
    const nonEmptyResults = results.filter(result => result && result.trim().length > 0);
    
    // 去重
    const uniqueResults = Array.from(new Set(nonEmptyResults));
    
    // 限制结果数量
    return uniqueResults.slice(0, config.maxResults);
  }

  /**
   * 便捷方法：基础检索
   */
  async basicRetrieve(query: string, sourceFile: string = MEMORY_FILES.KEY_EVENTS): Promise<string[]> {
    const config: UnifiedRAGConfig = {
      depth: RAGRetrievalDepth.BASIC,
      mode: RAGRetrievalMode.KEYWORD_ONLY,
      maxResults: 5,
      similarityThreshold: 0.3,
      sourceFiles: [sourceFile]
    };

    const result = await this.retrieve(query, config);
    return result.results;
  }

  /**
   * 便捷方法：扩展检索
   */
  async extendedRetrieve(query: string, sourceFiles: string[] = [MEMORY_FILES.KEY_EVENTS]): Promise<string[]> {
    const config: UnifiedRAGConfig = {
      depth: RAGRetrievalDepth.EXTENDED,
      mode: RAGRetrievalMode.KEYWORD_ONLY,
      maxResults: 10,
      similarityThreshold: 0.2,
      sourceFiles
    };

    const result = await this.retrieve(query, config);
    return result.results;
  }

  /**
   * 便捷方法：全量检索
   */
  async fullRetrieve(query: string): Promise<string[]> {
    const config: UnifiedRAGConfig = {
      depth: RAGRetrievalDepth.FULL,
      mode: RAGRetrievalMode.KEYWORD_ONLY,
      maxResults: 20,
      similarityThreshold: 0.1,
      sourceFiles: [
        MEMORY_FILES.KEY_EVENTS,
        MEMORY_FILES.DIALOGUE_HISTORY,
        MEMORY_FILES.DAILY_INSIGHT_COLD
      ]
    };

    const result = await this.retrieve(query, config);
    return result.results;
  }
}

// 便捷函数：获取统一RAG检索器实例
export function getUnifiedRAGRetriever(): UnifiedRAGRetriever {
  return UnifiedRAGRetriever.getInstance();
}

// 便捷函数：快速检索
export async function quickRAGSearch(
  query: string,
  depth: RAGRetrievalDepth = RAGRetrievalDepth.BASIC,
  sourceFile: string = MEMORY_FILES.KEY_EVENTS
): Promise<string[]> {
  const retriever = getUnifiedRAGRetriever();
  
  switch (depth) {
    case RAGRetrievalDepth.BASIC:
      return retriever.basicRetrieve(query, sourceFile);
    case RAGRetrievalDepth.EXTENDED:
      return retriever.extendedRetrieve(query, [sourceFile]);
    case RAGRetrievalDepth.FULL:
      return retriever.fullRetrieve(query);
    default:
      return retriever.basicRetrieve(query, sourceFile);
  }
}
