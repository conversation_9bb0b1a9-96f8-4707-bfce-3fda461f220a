/**
 * System Health Checker Initialization - 系统健康检查器初始化
 * 
 * 自动启动系统健康检查并设置事件监听
 */

import { systemHealthChecker } from './system-health-checker';
import { configHotReloadService } from '@/lib/config/config-hot-reload';
import { performanceMonitor } from '@/lib/monitoring/performance-monitor';
import { unifiedErrorHandler } from '@/lib/error/unified-error-handler';

/**
 * 初始化系统健康检查
 */
export function initializeSystemHealthChecker(): void {
  console.log('🏥 初始化系统健康检查器...');

  try {
    // 启动健康检查
    systemHealthChecker.startHealthChecking();

    // 设置健康检查事件监听
    setupHealthCheckEventListeners();

    // 设置配置变更监听
    setupConfigListeners();

    // 设置与其他系统的集成
    setupSystemIntegrations();

    console.log('✅ 系统健康检查器初始化完成');

  } catch (error) {
    console.error('❌ 系统健康检查器初始化失败:', error);
    throw error;
  }
}

/**
 * 设置健康检查事件监听器
 */
function setupHealthCheckEventListeners(): void {
  // 监听健康检查启动事件
  systemHealthChecker.on('healthCheckingStarted', () => {
    console.log('🚀 系统健康检查已启动');
  });

  // 监听健康检查停止事件
  systemHealthChecker.on('healthCheckingStopped', () => {
    console.log('⏹️ 系统健康检查已停止');
  });

  // 监听健康检查完成事件
  systemHealthChecker.on('healthCheckCompleted', (results) => {
    const totalChecks = results.size;
    const healthyChecks = Array.from(results.values()).filter(r => r.status === 'HEALTHY').length;
    const criticalChecks = Array.from(results.values()).filter(r => r.status === 'CRITICAL').length;
    
    console.log(`🔍 健康检查完成: ${healthyChecks}/${totalChecks} 健康${criticalChecks > 0 ? `, ${criticalChecks} 个严重问题` : ''}`);
    
    // 如果有严重问题，记录详细信息
    if (criticalChecks > 0) {
      const criticalResults = Array.from(results.entries())
        .filter(([_, result]) => result.status === 'CRITICAL');
      
      criticalResults.forEach(([checkId, result]) => {
        console.error(`🚨 严重健康问题 [${checkId}]: ${result.message}`);
      });
    }
  });

  // 监听严重状态检测事件
  systemHealthChecker.on('criticalStatusDetected', ({ check, result }) => {
    console.error(`🚨 检测到严重状态: ${check.name} - ${result.message}`);
    
    // 可以在这里集成外部告警系统
    // 例如发送邮件、短信、Slack通知等
  });

  // 监听恢复操作完成事件
  systemHealthChecker.on('recoveryActionCompleted', ({ action, success, manual }) => {
    const actionType = manual ? '手动' : '自动';
    const status = success ? '成功' : '失败';
    console.log(`🔧 ${actionType}恢复操作${status}: ${action.name}`);
  });

  // 监听恢复操作错误事件
  systemHealthChecker.on('recoveryActionError', ({ action, error, manual }) => {
    const actionType = manual ? '手动' : '自动';
    console.error(`❌ ${actionType}恢复操作异常: ${action.name}`, error);
  });

  // 监听健康报告生成事件
  systemHealthChecker.on('healthReportGenerated', (report) => {
    console.log(`📋 健康报告已生成: 总分 ${report.overallScore}/100, 状态 ${report.overallStatus}`);
    
    // 如果健康分数较低，记录警告
    if (report.overallScore < 70) {
      console.warn(`⚠️ 系统健康分数较低: ${report.overallScore}/100`);
    }
    
    // 记录活跃警报数量
    if (report.alerts.length > 0) {
      const criticalAlerts = report.alerts.filter(a => a.level === 'critical').length;
      const warningAlerts = report.alerts.filter(a => a.level === 'warning').length;
      console.log(`🚨 活跃警报: ${report.alerts.length} 个 (严重: ${criticalAlerts}, 警告: ${warningAlerts})`);
    }
  });
}

/**
 * 设置配置变更监听器
 */
function setupConfigListeners(): void {
  // 监听健康检查配置变更
  configHotReloadService.on('healthConfigChanged', ({ key, newValue }) => {
    console.log(`🏥 健康检查配置变更: ${key} = ${JSON.stringify(newValue)}`);
    
    // 根据配置变更调整健康检查行为
    if (key === 'health.monitoring.enabled') {
      if (newValue) {
        systemHealthChecker.startHealthChecking();
      } else {
        systemHealthChecker.stopHealthChecking();
      }
    } else if (key === 'health.autoRecovery.enabled') {
      // 可以动态调整自动恢复配置
      console.log(`🔧 自动恢复${newValue ? '启用' : '禁用'}`);
    }
  });
}

/**
 * 设置与其他系统的集成
 */
function setupSystemIntegrations(): void {
  // 与性能监控器集成
  performanceMonitor.on('alertCreated', (alert) => {
    // 当性能监控器创建警报时，触发健康检查
    if (alert.level === 'critical') {
      console.log('🔍 性能警报触发健康检查');
      systemHealthChecker.runAllHealthChecks().catch(error => {
        console.error('❌ 性能警报触发的健康检查失败:', error);
      });
    }
  });

  // 与错误处理器集成
  unifiedErrorHandler.on('criticalErrorDetected', (error) => {
    // 当检测到严重错误时，触发健康检查
    console.log('🔍 严重错误触发健康检查');
    systemHealthChecker.runAllHealthChecks().catch(checkError => {
      console.error('❌ 严重错误触发的健康检查失败:', checkError);
    });
  });

  // 监听系统资源变化
  setInterval(() => {
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    
    // 如果内存使用率过高，触发内存清理恢复操作
    if (memoryUsagePercent > 90) {
      console.log('🧹 内存使用率过高，触发自动清理');
      systemHealthChecker.executeRecoveryAction('memory-cleanup').catch(error => {
        console.error('❌ 自动内存清理失败:', error);
      });
    }
  }, 30000); // 每30秒检查一次
}

/**
 * 获取系统健康检查状态
 */
export function getSystemHealthCheckerStatus(): {
  initialized: boolean;
  running: boolean;
  uptime: number;
  totalChecks: number;
  enabledChecks: number;
} {
  const status = systemHealthChecker.getHealthCheckingStatus();
  
  return {
    initialized: true,
    running: status.isRunning,
    uptime: status.uptime,
    totalChecks: status.totalChecks,
    enabledChecks: status.enabledChecks
  };
}

/**
 * 手动触发系统健康检查操作
 */
export async function triggerSystemHealthCheckOperations(): Promise<{
  healthCheck: any;
  healthReport: any;
  systemDiagnostic: any;
}> {
  console.log('🏥 手动触发系统健康检查操作...');
  
  try {
    // 运行健康检查
    const healthCheckResults = await systemHealthChecker.runAllHealthChecks();
    
    // 生成健康报告
    const healthReport = await systemHealthChecker.generateHealthReport();
    
    // 执行系统诊断
    const systemDiagnostic = {
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      processId: process.pid,
      nodeVersion: process.version,
      platform: process.platform
    };
    
    return {
      healthCheck: {
        totalChecks: healthCheckResults.size,
        results: Array.from(healthCheckResults.entries()).map(([id, result]) => ({
          id,
          status: result.status,
          message: result.message,
          timestamp: result.timestamp
        }))
      },
      healthReport: {
        overallStatus: healthReport.overallStatus,
        overallScore: healthReport.overallScore,
        alertsCount: healthReport.alerts.length,
        recommendations: healthReport.recommendations.slice(0, 3)
      },
      systemDiagnostic
    };
  } catch (error) {
    console.error('❌ 系统健康检查操作失败:', error);
    throw error;
  }
}

/**
 * 测试系统健康检查功能
 */
export async function testSystemHealthChecker(): Promise<{
  healthCheckTest: any;
  recoveryActionTest: any;
  integrationTest: any;
}> {
  console.log('🧪 测试系统健康检查功能...');
  
  try {
    // 测试健康检查
    const healthCheckResults = await systemHealthChecker.runAllHealthChecks();
    const healthCheckTest = {
      success: healthCheckResults.size > 0,
      totalChecks: healthCheckResults.size,
      healthyChecks: Array.from(healthCheckResults.values()).filter(r => r.status === 'HEALTHY').length,
      criticalChecks: Array.from(healthCheckResults.values()).filter(r => r.status === 'CRITICAL').length
    };

    // 测试恢复操作
    const recoveryActions = systemHealthChecker.getAvailableRecoveryActions();
    const testAction = recoveryActions.find(action => action.id === 'memory-cleanup');
    let recoveryActionTest = { success: false, actionId: 'memory-cleanup', executed: false };
    
    if (testAction) {
      try {
        const recoveryResult = await systemHealthChecker.executeRecoveryAction('memory-cleanup');
        recoveryActionTest = {
          success: true,
          actionId: 'memory-cleanup',
          executed: true,
          result: recoveryResult
        };
      } catch (error) {
        recoveryActionTest = {
          success: false,
          actionId: 'memory-cleanup',
          executed: true,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }

    // 测试系统集成
    const integrationTest = {
      performanceMonitorIntegration: typeof performanceMonitor !== 'undefined',
      errorHandlerIntegration: typeof unifiedErrorHandler !== 'undefined',
      configServiceIntegration: typeof configHotReloadService !== 'undefined',
      eventListenersSetup: true
    };

    return {
      healthCheckTest,
      recoveryActionTest,
      integrationTest
    };
    
  } catch (error) {
    console.error('❌ 系统健康检查测试失败:', error);
    throw error;
  }
}

/**
 * 销毁系统健康检查器
 */
export function destroySystemHealthChecker(): void {
  console.log('🏥 销毁系统健康检查器...');
  
  // 移除配置监听器
  configHotReloadService.removeAllListeners('healthConfigChanged');
  
  // 移除性能监控器集成
  performanceMonitor.removeAllListeners('alertCreated');
  
  // 移除错误处理器集成
  unifiedErrorHandler.removeAllListeners('criticalErrorDetected');
  
  // 销毁健康检查器
  systemHealthChecker.destroy();
  
  console.log('✅ 系统健康检查器已销毁');
}

// 自动初始化
initializeSystemHealthChecker();
