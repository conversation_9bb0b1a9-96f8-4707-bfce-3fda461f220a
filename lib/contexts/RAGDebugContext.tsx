"use client"

import React, { createContext, useContext, useState, ReactNode } from 'react'
import { RAGDebugParams } from '@/types/rag'

interface RAGDebugContextType {
  debugParams: RAGDebugParams | null
  setDebugParams: (params: RAGDebugParams | null) => void
  isDebugMode: boolean
  setIsDebugMode: (enabled: boolean) => void
}

const RAGDebugContext = createContext<RAGDebugContextType | undefined>(undefined)

export function RAGDebugProvider({ children }: { children: ReactNode }) {
  const [debugParams, setDebugParams] = useState<RAGDebugParams | null>(null)
  const [isDebugMode, setIsDebugMode] = useState(false)

  return (
    <RAGDebugContext.Provider
      value={{
        debugParams,
        setDebugParams,
        isDebugMode,
        setIsDebugMode
      }}
    >
      {children}
    </RAGDebugContext.Provider>
  )
}

export function useRAGDebug() {
  const context = useContext(RAGDebugContext)
  if (context === undefined) {
    throw new Error('useRAGDebug must be used within a RAGDebugProvider')
  }
  return context
}
