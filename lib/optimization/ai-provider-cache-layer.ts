/**
 * AI Provider Cache Layer - AI提供商缓存层
 * 
 * 专门负责AI响应的缓存管理：
 * - 智能缓存键生成
 * - TTL管理和过期策略
 * - 缓存命中率优化
 * - 内存使用控制
 */

import { createHash } from 'crypto';

/**
 * 缓存项接口
 */
export interface CacheItem {
  key: string;
  data: any;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessTime: number;
  metadata: {
    model: string;
    promptHash: string;
    responseSize: number;
    processingTime: number;
  };
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  maxSize: number;           // 最大缓存项数量
  defaultTTL: number;        // 默认TTL（毫秒）
  maxMemoryMB: number;       // 最大内存使用（MB）
  cleanupInterval: number;   // 清理间隔（毫秒）
  enableMetrics: boolean;    // 启用指标收集
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  totalItems: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  memoryUsageMB: number;
  averageResponseSize: number;
  lastCleanupTime: string;
  topModels: Array<{ model: string; count: number }>;
}

/**
 * AI提供商缓存层实现
 */
export class AIProviderCacheLayer {
  private cache: Map<string, CacheItem> = new Map();
  private config: CacheConfig;
  private stats = {
    hitCount: 0,
    missCount: 0,
    lastCleanupTime: new Date().toISOString()
  };
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 1000,
      defaultTTL: 3600000, // 1小时
      maxMemoryMB: 100,
      cleanupInterval: 300000, // 5分钟
      enableMetrics: true,
      ...config
    };

    this.startCleanupTimer();
    console.log('🧠 AI提供商缓存层已初始化');
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(
    model: string,
    prompt: string,
    options: any = {}
  ): string {
    const normalizedOptions = {
      temperature: options.temperature || 0.7,
      maxTokens: options.maxTokens || 2048,
      // 只包含影响输出的关键参数
    };

    const keyData = {
      model,
      prompt: prompt.trim(),
      options: normalizedOptions
    };

    const keyString = JSON.stringify(keyData);
    return createHash('sha256').update(keyString).digest('hex').substring(0, 16);
  }

  /**
   * 获取缓存项
   */
  async get(key: string): Promise<any | null> {
    const item = this.cache.get(key);
    
    if (!item) {
      this.stats.missCount++;
      return null;
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      this.cache.delete(key);
      this.stats.missCount++;
      return null;
    }

    // 更新访问统计
    item.accessCount++;
    item.lastAccessTime = Date.now();
    this.stats.hitCount++;

    console.log(`🎯 AI缓存命中: ${key} (访问次数: ${item.accessCount})`);
    return item.data;
  }

  /**
   * 设置缓存项
   */
  async set(
    key: string,
    data: any,
    metadata: {
      model: string;
      promptHash: string;
      processingTime: number;
    },
    ttl?: number
  ): Promise<void> {
    // 检查内存使用
    if (this.getMemoryUsageMB() > this.config.maxMemoryMB) {
      await this.evictLRU();
    }

    // 检查缓存大小
    if (this.cache.size >= this.config.maxSize) {
      await this.evictLRU();
    }

    const responseSize = JSON.stringify(data).length;
    const item: CacheItem = {
      key,
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL,
      accessCount: 0,
      lastAccessTime: Date.now(),
      metadata: {
        ...metadata,
        responseSize
      }
    };

    this.cache.set(key, item);
    console.log(`💾 AI响应已缓存: ${key} (大小: ${responseSize}字节)`);
  }

  /**
   * 带缓存的AI文本生成
   */
  async generateTextWithCache(
    aiProvider: any,
    prompt: string,
    options: any = {}
  ): Promise<{
    text: string;
    cached: boolean;
    processingTime: number;
    cacheKey: string;
  }> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(options.model || 'default', prompt, options);
    
    // 尝试从缓存获取
    const cachedResult = await this.get(cacheKey);
    if (cachedResult) {
      return {
        text: cachedResult.text,
        cached: true,
        processingTime: Date.now() - startTime,
        cacheKey
      };
    }

    // 调用AI提供商
    const aiStartTime = Date.now();
    const result = await aiProvider.generateText(prompt, options);
    const aiProcessingTime = Date.now() - aiStartTime;

    // 缓存结果
    await this.set(cacheKey, { text: result.text }, {
      model: options.model || 'default',
      promptHash: createHash('sha256').update(prompt).digest('hex').substring(0, 8),
      processingTime: aiProcessingTime
    });

    return {
      text: result.text,
      cached: false,
      processingTime: Date.now() - startTime,
      cacheKey
    };
  }

  /**
   * 带缓存的AI流式生成
   */
  async generateStreamWithCache(
    aiProvider: any,
    prompt: string,
    options: any = {}
  ): Promise<{
    stream?: AsyncIterable<string>;
    text?: string;
    cached: boolean;
    processingTime: number;
    cacheKey: string;
  }> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(options.model || 'default', prompt, options);
    
    // 尝试从缓存获取
    const cachedResult = await this.get(cacheKey);
    if (cachedResult) {
      return {
        text: cachedResult.text,
        cached: true,
        processingTime: Date.now() - startTime,
        cacheKey
      };
    }

    // 调用AI提供商流式生成
    const aiStartTime = Date.now();
    const result = await aiProvider.generateStream(prompt, options);
    
    // 收集流式内容用于缓存
    let fullText = '';
    const cachedStream = async function* () {
      for await (const chunk of result.stream) {
        fullText += chunk;
        yield chunk;
      }
    };

    // 在流结束后缓存完整内容
    const originalStream = result.stream;
    const self = this;
    const wrappedStream = async function* () {
      for await (const chunk of cachedStream()) {
        yield chunk;
      }

      // 流结束后缓存
      const aiProcessingTime = Date.now() - aiStartTime;
      await self.set(cacheKey, { text: fullText }, {
        model: options.model || 'default',
        promptHash: createHash('sha256').update(prompt).digest('hex').substring(0, 8),
        processingTime: aiProcessingTime
      });
    };

    return {
      stream: wrappedStream(),
      cached: false,
      processingTime: Date.now() - startTime,
      cacheKey
    };
  }

  /**
   * 检查项目是否过期
   */
  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.timestamp > item.ttl;
  }

  /**
   * LRU淘汰策略
   */
  private async evictLRU(): Promise<void> {
    if (this.cache.size === 0) return;

    // 找到最少使用的项目
    let lruKey = '';
    let lruTime = Date.now();

    Array.from(this.cache.entries()).forEach(([key, item]) => {
      if (item.lastAccessTime < lruTime) {
        lruTime = item.lastAccessTime;
        lruKey = key;
      }
    });

    if (lruKey) {
      this.cache.delete(lruKey);
      console.log(`🗑️ LRU淘汰缓存项: ${lruKey}`);
    }
  }

  /**
   * 清理过期项目
   */
  private async cleanup(): Promise<void> {
    let cleanedCount = 0;

    Array.from(this.cache.entries()).forEach(([key, item]) => {
      if (this.isExpired(item)) {
        this.cache.delete(key);
        cleanedCount++;
      }
    });

    this.stats.lastCleanupTime = new Date().toISOString();
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个过期缓存项`);
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 获取内存使用量（MB）
   */
  private getMemoryUsageMB(): number {
    let totalSize = 0;
    Array.from(this.cache.values()).forEach(item => {
      totalSize += item.metadata.responseSize;
    });
    return totalSize / (1024 * 1024);
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): CacheStats {
    const totalRequests = this.stats.hitCount + this.stats.missCount;
    const hitRate = totalRequests > 0 ? this.stats.hitCount / totalRequests : 0;
    
    // 统计模型使用情况
    const modelCounts = new Map<string, number>();
    let totalResponseSize = 0;

    Array.from(this.cache.values()).forEach(item => {
      const model = item.metadata.model;
      modelCounts.set(model, (modelCounts.get(model) || 0) + 1);
      totalResponseSize += item.metadata.responseSize;
    });
    
    const topModels = Array.from(modelCounts.entries())
      .map(([model, count]) => ({ model, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalItems: this.cache.size,
      hitCount: this.stats.hitCount,
      missCount: this.stats.missCount,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsageMB: Math.round(this.getMemoryUsageMB() * 100) / 100,
      averageResponseSize: this.cache.size > 0 ? Math.round(totalResponseSize / this.cache.size) : 0,
      lastCleanupTime: this.stats.lastCleanupTime,
      topModels
    };
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.stats.hitCount = 0;
    this.stats.missCount = 0;
    console.log('🧹 AI提供商缓存已清空');
  }

  /**
   * 销毁缓存层
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
    console.log('🧠 AI提供商缓存层已销毁');
  }
}

// 导出单例实例
export const aiProviderCacheLayer = new AIProviderCacheLayer();
