/**
 * 优化后的API处理器
 * 移除重复缓存，专注于请求/响应处理
 * 基于SelfMirror缓存架构分析的优化建议
 */

import { unifiedContextManager } from '@/lib/services/unified-context/unified-context-manager';
import { getDefaultAIProvider } from '@/lib/ai/ai-provider-factory';

/**
 * 响应元数据接口
 */
export interface ResponseMetadata {
  requestId: string;
  processingTime: number;
  version: string;
  cacheHits: {
    contextCache: boolean;
    aiProviderCache: boolean;
    vectorRetrievalCache: boolean;
  };
  optimizationApplied: string[];
}

/**
 * 优化后的API响应处理器
 * 移除了重复的缓存逻辑，专注于请求/响应处理
 */
export class OptimizedAPIHandler {
  /**
   * 成功响应处理（无缓存）
   */
  static success<T>(data: T, metadata: ResponseMetadata): Response {
    const responseData = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        ...metadata
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'X-Request-ID': metadata.requestId,
        'X-Processing-Time': String(metadata.processingTime),
        'X-Cache-Status': this.formatCacheStatus(metadata.cacheHits)
      }
    });
  }

  /**
   * 错误响应处理
   */
  static error(error: unknown, context: string): Response {
    const errorInfo = this.analyzeError(error);
    
    console.error(`❌ [${context}] ${errorInfo.message}`, {
      code: errorInfo.code,
      context: errorInfo.context,
      timestamp: new Date().toISOString()
    });

    const responseData = {
      success: false,
      error: {
        message: errorInfo.message,
        code: errorInfo.code,
        context,
        suggestions: errorInfo.suggestions,
        timestamp: new Date().toISOString()
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status: errorInfo.status,
      headers: {
        'Content-Type': 'application/json',
        'X-Error-Code': errorInfo.code,
        'X-Error-Context': context
      }
    });
  }

  /**
   * 流式响应处理
   */
  static stream(generator: AsyncGenerator<string>, metadata: ResponseMetadata): Response {
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        
        try {
          // 发送初始元数据
          const initData = `data: ${JSON.stringify({
            type: 'init',
            requestId: metadata.requestId,
            timestamp: new Date().toISOString(),
            cacheHits: metadata.cacheHits
          })}\n\n`;
          controller.enqueue(encoder.encode(initData));

          // 流式发送数据
          for await (const chunk of generator) {
            const data = `data: ${JSON.stringify({
              type: 'text',
              content: chunk,
              timestamp: new Date().toISOString(),
              requestId: metadata.requestId
            })}\n\n`;
            controller.enqueue(encoder.encode(data));
          }

          // 发送结束标记
          const endData = `data: ${JSON.stringify({
            type: 'end',
            requestId: metadata.requestId,
            timestamp: new Date().toISOString(),
            metadata
          })}\n\n`;
          controller.enqueue(encoder.encode(endData));

        } catch (error) {
          const errorData = `data: ${JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : String(error),
            requestId: metadata.requestId,
            timestamp: new Date().toISOString()
          })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
          controller.error(error);
        } finally {
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Request-ID': metadata.requestId,
        'X-Cache-Status': this.formatCacheStatus(metadata.cacheHits)
      }
    });
  }

  /**
   * 格式化缓存状态
   */
  private static formatCacheStatus(cacheHits: ResponseMetadata['cacheHits']): string {
    const hits = Object.entries(cacheHits)
      .filter(([_, hit]) => hit)
      .map(([layer, _]) => layer);
    
    return hits.length > 0 ? `HIT:${hits.join(',')}` : 'MISS';
  }

  /**
   * 分析错误信息
   */
  private static analyzeError(error: unknown): {
    message: string;
    code: string;
    status: number;
    context?: Record<string, any>;
    suggestions?: string[];
  } {
    if (error instanceof Error) {
      if (error.name === 'ValidationError') {
        return {
          message: error.message,
          code: 'VALIDATION_ERROR',
          status: 400,
          suggestions: ['请检查输入参数的格式和内容', '参考API文档了解正确的参数格式']
        };
      }

      if (error.name === 'AIError') {
        return {
          message: 'AI服务暂时不可用',
          code: 'AI_SERVICE_ERROR',
          status: 503,
          context: { originalError: error.message },
          suggestions: ['请稍后重试', '如果问题持续存在，请联系技术支持']
        };
      }

      return {
        message: error.message,
        code: 'INTERNAL_ERROR',
        status: 500,
        suggestions: ['请稍后重试', '如果问题持续存在，请联系技术支持']
      };
    }

    return {
      message: '服务暂时不可用',
      code: 'UNKNOWN_ERROR',
      status: 500,
      context: { error: String(error) },
      suggestions: ['请稍后重试']
    };
  }

  /**
   * 生成请求ID
   */
  static generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * AI提供商缓存层
 * 专门负责缓存AI提供商的原始响应
 */
export class AIProviderCacheLayer {
  private static responseCache = new Map<string, CachedAIResponse>();
  private static readonly MAX_CACHE_SIZE = 500;
  private static readonly CACHE_TTL = 3600000; // 1小时

  /**
   * 带缓存的AI文本生成
   */
  static async generateTextWithCache(
    prompt: string,
    options: any = {}
  ): Promise<{ text: string; fromCache: boolean }> {
    const cacheKey = this.generateCacheKey(prompt, options);
    
    // 检查缓存
    const cached = this.responseCache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      console.log(`🎯 AI提供商缓存命中: ${cacheKey.substring(0, 16)}...`);
      return { text: cached.text, fromCache: true };
    }

    // 调用AI提供商
    const aiProvider = await getDefaultAIProvider();
    const text = await aiProvider.generateText(prompt, options);

    // 缓存结果
    this.setCachedResponse(cacheKey, text);

    console.log(`🎯 AI提供商缓存设置: ${cacheKey.substring(0, 16)}...`);
    return { text, fromCache: false };
  }

  /**
   * 带缓存的AI流式生成
   */
  static async generateStreamWithCache(
    prompt: string,
    options: any = {}
  ): Promise<{ stream: AsyncGenerator<string>; fromCache: boolean }> {
    const cacheKey = this.generateCacheKey(prompt, options);
    
    // 检查缓存
    const cached = this.responseCache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      console.log(`🎯 AI提供商流式缓存命中: ${cacheKey.substring(0, 16)}...`);
      return { 
        stream: this.createStreamFromCache(cached.text), 
        fromCache: true 
      };
    }

    // 调用AI提供商
    const aiProvider = await getDefaultAIProvider();
    const stream = aiProvider.generateStream(prompt, options);

    // 收集流式响应并缓存
    const { cachedStream, fullText } = this.createCachingStream(stream, cacheKey);

    console.log(`🎯 AI提供商流式缓存设置: ${cacheKey.substring(0, 16)}...`);
    return { stream: cachedStream, fromCache: false };
  }

  /**
   * 生成缓存键
   */
  private static generateCacheKey(prompt: string, options: any): string {
    const optionsStr = JSON.stringify(options, Object.keys(options).sort());
    const content = prompt + optionsStr;
    
    // 简单哈希函数
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return `ai-cache-${Math.abs(hash).toString(36)}`;
  }

  /**
   * 检查缓存是否有效
   */
  private static isCacheValid(cached: CachedAIResponse): boolean {
    return Date.now() - cached.timestamp < this.CACHE_TTL;
  }

  /**
   * 设置缓存响应
   */
  private static setCachedResponse(key: string, text: string): void {
    // 缓存大小控制
    if (this.responseCache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestCache();
    }

    this.responseCache.set(key, {
      text,
      timestamp: Date.now()
    });
  }

  /**
   * 淘汰最旧的缓存项
   */
  private static evictOldestCache(): void {
    const entries = Array.from(this.responseCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    // 淘汰最旧的20%
    const toEvict = Math.floor(entries.length * 0.2);
    for (let i = 0; i < toEvict; i++) {
      this.responseCache.delete(entries[i][0]);
    }

    console.log(`🧹 AI提供商缓存清理: 淘汰了 ${toEvict} 个旧缓存项`);
  }

  /**
   * 从缓存创建流
   */
  private static async* createStreamFromCache(text: string): AsyncGenerator<string> {
    // 模拟流式输出
    const chunks = text.split(' ');
    for (const chunk of chunks) {
      yield chunk + ' ';
      await new Promise(resolve => setTimeout(resolve, 10)); // 模拟延迟
    }
  }

  /**
   * 创建缓存流包装器
   */
  private static createCachingStream(
    originalStream: AsyncIterable<string>,
    cacheKey: string
  ): { cachedStream: AsyncGenerator<string>; fullText: Promise<string> } {
    let fullText = '';
    
    const cachedStream = async function* () {
      for await (const chunk of originalStream) {
        fullText += chunk;
        yield chunk;
      }
    }();

    const fullTextPromise = (async () => {
      for await (const _ of cachedStream) {
        // 消费流以收集完整文本
      }
      return fullText;
    })().then(text => {
      // 缓存完整文本
      AIProviderCacheLayer.setCachedResponse(cacheKey, text);
      return text;
    });

    return { cachedStream, fullText: fullTextPromise };
  }

  /**
   * 获取缓存统计
   */
  static getCacheStats() {
    return {
      cacheSize: this.responseCache.size,
      maxCacheSize: this.MAX_CACHE_SIZE,
      cacheTTL: this.CACHE_TTL
    };
  }
}

/**
 * 缓存的AI响应接口
 */
interface CachedAIResponse {
  text: string;
  timestamp: number;
}

// 导出优化后的便捷方法
export const OptimizedApiResponse = {
  success: OptimizedAPIHandler.success.bind(OptimizedAPIHandler),
  error: OptimizedAPIHandler.error.bind(OptimizedAPIHandler),
  stream: OptimizedAPIHandler.stream.bind(OptimizedAPIHandler),
  generateRequestId: OptimizedAPIHandler.generateRequestId.bind(OptimizedAPIHandler)
};
