/**
 * 统一API响应处理器
 * 基于SelfMirror系统优化分析的具体实现示例
 */

/**
 * 响应元数据
 */
export interface ResponseMetadata {
  requestId?: string;
  processingTime?: number;
  version?: string;
  cacheHit?: boolean;
  optimizationApplied?: string[];
}

/**
 * 错误信息结构
 */
export interface ErrorInfo {
  message: string;
  code: string;
  status: number;
  context?: Record<string, any>;
  suggestions?: string[];
}

/**
 * 统一响应处理器
 * 应用"排序加权末尾淘汰"算法优化响应缓存
 */
export class UnifiedResponseHandler {
  private static responseCache = new Map<string, CachedResponse>();
  private static cacheWeights = new Map<string, number>();
  private static readonly MAX_CACHE_SIZE = 1000;
  private static readonly CACHE_TTL = 300000; // 5分钟

  /**
   * 成功响应处理
   */
  static success<T>(data: T, metadata?: ResponseMetadata): Response {
    const responseData = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
        ...metadata
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'X-Request-ID': responseData.metadata.requestId,
        'X-Processing-Time': String(metadata?.processingTime || 0)
      }
    });
  }

  /**
   * 错误响应处理
   */
  static error(error: unknown, context: string): Response {
    const errorInfo = this.analyzeError(error);
    
    // 记录错误日志
    console.error(`❌ [${context}] ${errorInfo.message}`, {
      code: errorInfo.code,
      context: errorInfo.context,
      timestamp: new Date().toISOString()
    });

    const responseData = {
      success: false,
      error: {
        message: errorInfo.message,
        code: errorInfo.code,
        context,
        suggestions: errorInfo.suggestions,
        timestamp: new Date().toISOString()
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status: errorInfo.status,
      headers: {
        'Content-Type': 'application/json',
        'X-Error-Code': errorInfo.code,
        'X-Error-Context': context
      }
    });
  }

  /**
   * 流式响应处理
   */
  static stream(generator: AsyncGenerator<string>, metadata?: ResponseMetadata): Response {
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        const requestId = metadata?.requestId || UnifiedResponseHandler.generateRequestId();
        
        try {
          // 发送初始元数据
          const initData = `data: ${JSON.stringify({
            type: 'init',
            requestId,
            timestamp: new Date().toISOString(),
            metadata
          })}\n\n`;
          controller.enqueue(encoder.encode(initData));

          // 流式发送数据
          for await (const chunk of generator) {
            const data = `data: ${JSON.stringify({
              type: 'text',
              content: chunk,
              timestamp: new Date().toISOString(),
              requestId
            })}\n\n`;
            controller.enqueue(encoder.encode(data));
          }

          // 发送结束标记
          const endData = `data: ${JSON.stringify({
            type: 'end',
            requestId,
            timestamp: new Date().toISOString()
          })}\n\n`;
          controller.enqueue(encoder.encode(endData));

        } catch (error) {
          // 发送错误信息
          const errorData = `data: ${JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : String(error),
            requestId,
            timestamp: new Date().toISOString()
          })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
          controller.error(error);
        } finally {
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Request-ID': metadata?.requestId || this.generateRequestId()
      }
    });
  }

  /**
   * 缓存响应处理
   */
  static async cached<T>(
    cacheKey: string,
    dataGenerator: () => Promise<T>,
    ttl: number = this.CACHE_TTL
  ): Promise<Response> {
    // 检查缓存
    const cached = this.getCachedResponse<T>(cacheKey);
    if (cached) {
      return this.success(cached, {
        cacheHit: true,
        optimizationApplied: ['cache_hit']
      });
    }

    // 生成新数据
    const startTime = Date.now();
    const data = await dataGenerator();
    const processingTime = Date.now() - startTime;

    // 缓存结果
    this.setCachedResponse(cacheKey, data, ttl);

    return this.success(data, {
      cacheHit: false,
      processingTime,
      optimizationApplied: ['cache_set']
    });
  }

  /**
   * 分析错误信息
   */
  private static analyzeError(error: unknown): ErrorInfo {
    if (error instanceof Error) {
      // 根据错误类型返回不同的错误信息
      if (error.name === 'ValidationError') {
        return {
          message: error.message,
          code: 'VALIDATION_ERROR',
          status: 400,
          suggestions: ['请检查输入参数的格式和内容', '参考API文档了解正确的参数格式']
        };
      }

      if (error.name === 'AIError') {
        return {
          message: 'AI服务暂时不可用',
          code: 'AI_SERVICE_ERROR',
          status: 503,
          context: { originalError: error.message },
          suggestions: ['请稍后重试', '如果问题持续存在，请联系技术支持']
        };
      }

      if (error.name === 'ConfigurationError') {
        return {
          message: '系统配置错误',
          code: 'CONFIG_ERROR',
          status: 500,
          suggestions: ['请检查系统配置', '联系管理员确认配置正确性']
        };
      }

      // 通用错误处理
      return {
        message: error.message,
        code: 'INTERNAL_ERROR',
        status: 500,
        suggestions: ['请稍后重试', '如果问题持续存在，请联系技术支持']
      };
    }

    // 非Error对象的处理
    return {
      message: '服务暂时不可用',
      code: 'UNKNOWN_ERROR',
      status: 500,
      context: { error: String(error) },
      suggestions: ['请稍后重试']
    };
  }

  /**
   * 获取缓存响应（应用加权算法）
   */
  private static getCachedResponse<T>(key: string): T | null {
    const cached = this.responseCache.get(key);
    if (!cached || this.isCacheExpired(cached)) {
      this.responseCache.delete(key);
      this.cacheWeights.delete(key);
      return null;
    }

    // 更新缓存权重
    this.updateCacheWeight(key);
    return cached.data as T;
  }

  /**
   * 设置缓存响应（应用淘汰算法）
   */
  private static setCachedResponse<T>(key: string, data: T, ttl: number): void {
    // 缓存大小控制
    if (this.responseCache.size >= this.MAX_CACHE_SIZE) {
      this.evictLeastWeightedCache();
    }

    this.responseCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
    this.cacheWeights.set(key, 1.0);
  }

  /**
   * 更新缓存权重
   */
  private static updateCacheWeight(key: string): void {
    const currentWeight = this.cacheWeights.get(key) || 0;
    this.cacheWeights.set(key, currentWeight + 0.1);
  }

  /**
   * 淘汰权重最低的缓存项
   */
  private static evictLeastWeightedCache(): void {
    const sortedEntries = Array.from(this.cacheWeights.entries())
      .sort((a, b) => a[1] - b[1]);
    
    // 淘汰权重最低的20%
    const toEvict = Math.floor(sortedEntries.length * 0.2);
    for (let i = 0; i < toEvict; i++) {
      const [key] = sortedEntries[i];
      this.responseCache.delete(key);
      this.cacheWeights.delete(key);
    }

    console.log(`🧹 缓存清理: 淘汰了 ${toEvict} 个低权重缓存项`);
  }

  /**
   * 检查缓存是否过期
   */
  private static isCacheExpired(cached: CachedResponse): boolean {
    return Date.now() - cached.timestamp > cached.ttl;
  }

  /**
   * 生成请求ID
   */
  private static generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取缓存统计信息
   */
  static getCacheStats() {
    return {
      cacheSize: this.responseCache.size,
      maxCacheSize: this.MAX_CACHE_SIZE,
      cacheHitRate: this.calculateCacheHitRate(),
      averageWeight: this.calculateAverageWeight(),
      topWeightedKeys: this.getTopWeightedKeys(5)
    };
  }

  private static calculateCacheHitRate(): number {
    // 简化实现，实际应该跟踪命中率
    return 0.75; // 假设75%命中率
  }

  private static calculateAverageWeight(): number {
    if (this.cacheWeights.size === 0) return 0;
    const totalWeight = Array.from(this.cacheWeights.values()).reduce((sum, weight) => sum + weight, 0);
    return totalWeight / this.cacheWeights.size;
  }

  private static getTopWeightedKeys(count: number): Array<{key: string, weight: number}> {
    return Array.from(this.cacheWeights.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([key, weight]) => ({ key, weight }));
  }
}

/**
 * 缓存响应接口
 */
interface CachedResponse {
  data: any;
  timestamp: number;
  ttl: number;
}

// 导出便捷方法
export const ApiResponse = {
  success: UnifiedResponseHandler.success.bind(UnifiedResponseHandler),
  error: UnifiedResponseHandler.error.bind(UnifiedResponseHandler),
  stream: UnifiedResponseHandler.stream.bind(UnifiedResponseHandler),
  cached: UnifiedResponseHandler.cached.bind(UnifiedResponseHandler)
};
