/**
 * 统一API响应处理器
 *
 * ⚠️ 优化说明：已移除冗余的API层响应缓存
 * 缓存职责已分配给专门的层级：
 * - AI提供商缓存：AIProviderCacheLayer
 * - 上下文缓存：UnifiedContextManager
 * - 检索优化：RetrievalResultOptimizer
 *
 * 此类现在专注于请求/响应处理，不再包含缓存逻辑
 */

/**
 * 响应元数据
 */
export interface ResponseMetadata {
  requestId?: string;
  processingTime?: number;
  version?: string;
  cacheHits?: {
    contextCache?: boolean;
    aiProviderCache?: boolean;
    vectorRetrievalCache?: boolean;
  };
  optimizationApplied?: string[];
}

/**
 * 错误信息结构
 */
export interface ErrorInfo {
  message: string;
  code: string;
  status: number;
  context?: Record<string, any>;
  suggestions?: string[];
}

/**
 * 统一响应处理器（已移除冗余缓存）
 * 专注于请求/响应格式化和错误处理
 */
export class UnifiedResponseHandler {
  // 移除了冗余的缓存相关静态变量

  /**
   * 成功响应处理（已优化，移除冗余缓存）
   */
  static success<T>(data: T, metadata?: ResponseMetadata): Response {
    const responseData = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
        ...metadata
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'X-Request-ID': responseData.metadata.requestId,
        'X-Processing-Time': String(metadata?.processingTime || 0),
        'X-Cache-Status': this.formatCacheStatus(metadata?.cacheHits)
      }
    });
  }

  /**
   * 格式化缓存状态信息
   */
  private static formatCacheStatus(cacheHits?: ResponseMetadata['cacheHits']): string {
    if (!cacheHits) return 'NO_CACHE_INFO';

    const hits = Object.entries(cacheHits)
      .filter(([_, hit]) => hit)
      .map(([layer, _]) => layer);

    return hits.length > 0 ? `HIT:${hits.join(',')}` : 'MISS';
  }

  /**
   * 错误响应处理
   */
  static error(error: unknown, context: string): Response {
    const errorInfo = this.analyzeError(error);
    
    // 记录错误日志
    console.error(`❌ [${context}] ${errorInfo.message}`, {
      code: errorInfo.code,
      context: errorInfo.context,
      timestamp: new Date().toISOString()
    });

    const responseData = {
      success: false,
      error: {
        message: errorInfo.message,
        code: errorInfo.code,
        context,
        suggestions: errorInfo.suggestions,
        timestamp: new Date().toISOString()
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status: errorInfo.status,
      headers: {
        'Content-Type': 'application/json',
        'X-Error-Code': errorInfo.code,
        'X-Error-Context': context
      }
    });
  }

  /**
   * 流式响应处理
   */
  static stream(generator: AsyncGenerator<string>, metadata?: ResponseMetadata): Response {
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        const requestId = metadata?.requestId || UnifiedResponseHandler.generateRequestId();
        
        try {
          // 发送初始元数据
          const initData = `data: ${JSON.stringify({
            type: 'init',
            requestId,
            timestamp: new Date().toISOString(),
            metadata
          })}\n\n`;
          controller.enqueue(encoder.encode(initData));

          // 流式发送数据
          for await (const chunk of generator) {
            const data = `data: ${JSON.stringify({
              type: 'text',
              content: chunk,
              timestamp: new Date().toISOString(),
              requestId
            })}\n\n`;
            controller.enqueue(encoder.encode(data));
          }

          // 发送结束标记
          const endData = `data: ${JSON.stringify({
            type: 'end',
            requestId,
            timestamp: new Date().toISOString()
          })}\n\n`;
          controller.enqueue(encoder.encode(endData));

        } catch (error) {
          // 发送错误信息
          const errorData = `data: ${JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : String(error),
            requestId,
            timestamp: new Date().toISOString()
          })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
          controller.error(error);
        } finally {
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Request-ID': metadata?.requestId || this.generateRequestId()
      }
    });
  }

  // ⚠️ cached方法已移除 - 缓存职责已分配给专门的层级
  // 如需缓存功能，请使用：
  // - AIProviderCacheLayer.generateTextWithCache() 用于AI响应缓存
  // - UnifiedContextManager 内置上下文缓存
  // - RetrievalResultOptimizer 用于检索结果优化

  /**
   * 分析错误信息
   */
  private static analyzeError(error: unknown): ErrorInfo {
    if (error instanceof Error) {
      // 根据错误类型返回不同的错误信息
      if (error.name === 'ValidationError') {
        return {
          message: error.message,
          code: 'VALIDATION_ERROR',
          status: 400,
          suggestions: ['请检查输入参数的格式和内容', '参考API文档了解正确的参数格式']
        };
      }

      if (error.name === 'AIError') {
        return {
          message: 'AI服务暂时不可用',
          code: 'AI_SERVICE_ERROR',
          status: 503,
          context: { originalError: error.message },
          suggestions: ['请稍后重试', '如果问题持续存在，请联系技术支持']
        };
      }

      if (error.name === 'ConfigurationError') {
        return {
          message: '系统配置错误',
          code: 'CONFIG_ERROR',
          status: 500,
          suggestions: ['请检查系统配置', '联系管理员确认配置正确性']
        };
      }

      // 通用错误处理
      return {
        message: error.message,
        code: 'INTERNAL_ERROR',
        status: 500,
        suggestions: ['请稍后重试', '如果问题持续存在，请联系技术支持']
      };
    }

    // 非Error对象的处理
    return {
      message: '服务暂时不可用',
      code: 'UNKNOWN_ERROR',
      status: 500,
      context: { error: String(error) },
      suggestions: ['请稍后重试']
    };
  }

  // ⚠️ 所有缓存相关方法已移除
  // 缓存职责已重新分配给专门的层级：
  // - AI提供商缓存：lib/optimization/optimized-api-handler.ts -> AIProviderCacheLayer
  // - 上下文缓存：lib/services/unified-context/unified-context-manager.ts
  // - 检索优化：lib/services/dual-core/retrieval-result-optimizer.ts

  /**
   * 生成请求ID
   */
  private static generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // ⚠️ getCacheStats方法已移除
  // 如需缓存统计信息，请使用：
  // - AIProviderCacheLayer.getCacheStats() 用于AI提供商缓存统计
  // - RetrievalResultOptimizer.getOptimizationStats() 用于检索优化统计
}

// 导出便捷方法（已移除cached方法）
export const ApiResponse = {
  success: UnifiedResponseHandler.success.bind(UnifiedResponseHandler),
  error: UnifiedResponseHandler.error.bind(UnifiedResponseHandler),
  stream: UnifiedResponseHandler.stream.bind(UnifiedResponseHandler)
  // cached方法已移除 - 请使用专门的缓存层
};
