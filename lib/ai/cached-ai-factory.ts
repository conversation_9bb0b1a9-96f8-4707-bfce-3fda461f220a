/**
 * Cached AI Factory - 带缓存的AI工厂
 * 
 * 在原有AI工厂基础上添加缓存层：
 * - 透明的缓存集成
 * - 保持原有接口兼容性
 * - 智能缓存策略
 */

import { AIProvider, AIFactoryConfig, HealthCheckResult } from './types';
import { AIFactory } from './factory';
import { aiProviderCacheLayer } from '@/lib/optimization/ai-provider-cache-layer';
import { getSelfMirrorConfig } from '@/lib/config/config-layers/selfmirror-config';
import { configHotReloadService } from '@/lib/config/config-hot-reload';
import { withErrorHandling, createAIProviderError } from '@/lib/error/error-types';

/**
 * 带缓存的AI提供商包装器
 */
class CachedAIProvider implements AIProvider {
  constructor(
    private originalProvider: AIProvider,
    private enableCache: boolean = true
  ) {}

  get name(): string {
    return this.originalProvider.name;
  }

  get config(): any {
    return (this.originalProvider as any).config || {};
  }

  /**
   * 带缓存的文本生成
   */
  async generateText(prompt: string, options: any = {}): Promise<string> {
    return withErrorHandling(
      async () => {
        if (!this.enableCache) {
          return this.originalProvider.generateText(prompt, options);
        }

        const result = await aiProviderCacheLayer.generateTextWithCache(
          this.originalProvider,
          prompt,
          { ...options, model: this.name }
        );

        console.log(`🤖 AI文本生成 [${this.name}]: ${result.cached ? '缓存命中' : '新生成'} - ${result.processingTime}ms`);

        return result.text;
      },
      {
        component: 'CachedAIProvider',
        operation: 'generateText',
        metadata: {
          provider: this.name,
          promptLength: prompt.length,
          cacheEnabled: this.enableCache
        }
      },
      {
        enableRecovery: true,
        fallbackValue: '抱歉，AI服务暂时不可用，请稍后重试。'
      }
    );
  }

  /**
   * 带缓存的流式生成
   */
  generateStream(prompt: string, options: any = {}): AsyncIterable<string> {
    if (!this.enableCache) {
      return this.originalProvider.generateStream(prompt, options);
    }

    // 对于流式生成，暂时不使用缓存，直接返回原始提供商的流
    // TODO: 实现流式缓存逻辑
    return this.originalProvider.generateStream(prompt, options);
  }

  /**
   * 模拟流式输出（用于缓存命中的情况）
   */
  private async* simulateStream(text: string): AsyncIterable<string> {
    const chunkSize = 10; // 每次输出10个字符
    const delay = 50; // 50ms延迟，模拟真实流式效果
    
    for (let i = 0; i < text.length; i += chunkSize) {
      const chunk = text.slice(i, i + chunkSize);
      yield chunk;
      
      // 添加延迟以模拟流式效果
      if (i + chunkSize < text.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResult> {
    return this.originalProvider.healthCheck();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return aiProviderCacheLayer.getCacheStats();
  }
}

/**
 * 带缓存的AI工厂
 */
export class CachedAIFactory {
  private static instance: CachedAIFactory | null = null;
  private aiFactory: AIFactory;
  private cacheEnabled: boolean;

  private constructor(config?: AIFactoryConfig & { enableCache?: boolean }) {
    // 从配置系统获取AI配置
    const selfMirrorConfig = getSelfMirrorConfig();

    this.aiFactory = AIFactory.getInstance(config);
    this.cacheEnabled = config?.enableCache ?? selfMirrorConfig.ai.cache.enabled;

    // 监听配置变更
    this.setupConfigListeners();

    console.log(`🧠 带缓存的AI工厂已初始化 (缓存: ${this.cacheEnabled ? '启用' : '禁用'})`);
  }

  /**
   * 设置配置变更监听器
   */
  private setupConfigListeners(): void {
    // 监听AI配置变更
    configHotReloadService.on('aiConfigChanged', ({ key, newValue }) => {
      console.log(`🔄 AI工厂配置变更: ${key} = ${JSON.stringify(newValue)}`);

      if (key === 'ai.cache.enabled') {
        this.setCacheEnabled(newValue);
      }
    });

    // 监听缓存配置变更
    configHotReloadService.on('cacheConfigChanged', ({ key, newValue }) => {
      console.log(`🔄 缓存配置变更: ${key} = ${JSON.stringify(newValue)}`);

      // 更新缓存层配置
      if (key.startsWith('ai.cache.')) {
        this.updateCacheConfig();
      }
    });
  }

  /**
   * 更新缓存配置
   */
  private updateCacheConfig(): void {
    const config = getSelfMirrorConfig();
    // 这里可以更新aiProviderCacheLayer的配置
    console.log('🔄 AI缓存配置已更新');
  }

  /**
   * 获取带缓存的AI工厂单例
   */
  static getInstance(config?: AIFactoryConfig & { enableCache?: boolean }): CachedAIFactory {
    if (!CachedAIFactory.instance) {
      CachedAIFactory.instance = new CachedAIFactory(config);
    }
    return CachedAIFactory.instance;
  }

  /**
   * 重置单例
   */
  static reset(): void {
    CachedAIFactory.instance = null;
    AIFactory.reset();
  }

  /**
   * 创建带缓存的AI提供商
   */
  async createProvider(providerName?: 'gemini' | 'doubao'): Promise<CachedAIProvider> {
    const originalProvider = await this.aiFactory.createProvider(providerName);
    return new CachedAIProvider(originalProvider, this.cacheEnabled);
  }

  /**
   * 获取默认的带缓存AI提供商
   */
  async getDefaultProvider(): Promise<CachedAIProvider> {
    const originalProvider = await this.aiFactory.createProvider();
    return new CachedAIProvider(originalProvider, this.cacheEnabled);
  }

  /**
   * 获取可用的提供商列表
   */
  getAvailableProviders(): string[] {
    return this.aiFactory.getAvailableProviders();
  }

  /**
   * 切换提供商
   */
  async switchProvider(providerName: 'gemini' | 'doubao'): Promise<CachedAIProvider> {
    const originalProvider = await this.aiFactory.switchProvider(providerName);
    return new CachedAIProvider(originalProvider, this.cacheEnabled);
  }

  /**
   * 启用/禁用缓存
   */
  setCacheEnabled(enabled: boolean): void {
    this.cacheEnabled = enabled;
    console.log(`🧠 AI缓存已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取缓存状态
   */
  isCacheEnabled(): boolean {
    return this.cacheEnabled;
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return aiProviderCacheLayer.getCacheStats();
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    aiProviderCacheLayer.clear();
    console.log('🧹 AI提供商缓存已清空');
  }

  /**
   * 获取工厂状态
   */
  getStatus() {
    return {
      cacheEnabled: this.cacheEnabled,
      availableProviders: this.getAvailableProviders(),
      cacheStats: this.getCacheStats()
    };
  }
}

// 导出便捷函数
export async function getDefaultCachedAIProvider(): Promise<CachedAIProvider> {
  const factory = CachedAIFactory.getInstance();
  return factory.getDefaultProvider();
}

export async function createCachedAIProvider(providerName?: 'gemini' | 'doubao'): Promise<CachedAIProvider> {
  const factory = CachedAIFactory.getInstance();
  return factory.createProvider(providerName);
}

// 导出单例工厂实例
export const cachedAIFactory = CachedAIFactory.getInstance();
