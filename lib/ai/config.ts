/**
 * SelfMirror AI配置管理
 * 基于环境变量的简单配置系统
 */

import { AIFactoryConfig, AIError, AI_ERROR_CODES } from './types';

// 默认配置
const DEFAULT_CONFIG = {
  timeout: 60000, // 增加到60秒以适应代理延迟
  retryAttempts: 2, // 减少重试次数避免过长等待
  defaultModels: {
    gemini: 'gemini-1.5-flash',
    doubao: 'doubao-lite-4k'
  }
};

/**
 * 获取AI工厂配置
 */
export function getAIConfig(): AIFactoryConfig {
  const provider = (process.env.AI_PROVIDER || 'gemini') as 'gemini' | 'doubao';
  
  // 根据提供商获取API密钥
  let apiKey: string;
  switch (provider) {
    case 'gemini':
      apiKey = process.env.GEMINI_API_KEY || '';
      break;
    case 'doubao':
      apiKey = process.env.DOUBAO_API_KEY || '';
      break;
    default:
      throw new AIError(
        `Unsupported AI provider: ${provider}`,
        AI_ERROR_CODES.PROVIDER_NOT_FOUND
      );
  }

  if (!apiKey) {
    throw new AIError(
      `API key missing for provider: ${provider}`,
      AI_ERROR_CODES.API_KEY_MISSING,
      provider
    );
  }

  return {
    provider,
    apiKey,
    proxyUrl: process.env.PROXY_URL,
    defaultModel: DEFAULT_CONFIG.defaultModels[provider],
    timeout: parseInt(process.env.AI_TIMEOUT || String(DEFAULT_CONFIG.timeout)),
    retryAttempts: parseInt(process.env.AI_RETRY_ATTEMPTS || String(DEFAULT_CONFIG.retryAttempts))
  };
}

/**
 * 验证配置
 */
export function validateConfig(config: AIFactoryConfig): boolean {
  if (!config.provider || !config.apiKey) {
    return false;
  }
  
  if (!['gemini', 'doubao'].includes(config.provider)) {
    return false;
  }
  
  return true;
}

/**
 * 获取代理配置
 */
export function getProxyConfig(): { agent?: any } {
  const proxyUrl = process.env.PROXY_URL;
  if (!proxyUrl) {
    return {};
  }

  try {
    // 使用undici的ProxyAgent进行代理配置
    const { ProxyAgent } = require('undici');
    const proxyAgent = new ProxyAgent(proxyUrl);

    console.log(`🌐 使用代理: ${proxyUrl}`);
    return { agent: proxyAgent };
  } catch (error) {
    console.warn('⚠️ 代理配置失败，使用直连:', error);
    return {};
  }
}

/**
 * 获取环境信息
 */
export function getEnvironmentInfo() {
  return {
    nodeEnv: process.env.NODE_ENV || 'development',
    hasGeminiKey: !!process.env.GEMINI_API_KEY,
    hasDoubaoKey: !!process.env.DOUBAO_API_KEY,
    hasProxy: !!process.env.PROXY_URL,
    currentProvider: process.env.AI_PROVIDER || 'gemini'
  };
}
