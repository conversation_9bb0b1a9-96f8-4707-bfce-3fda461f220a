/**
 * Doubao AI提供商实现
 * 基于字节跳动豆包API
 */

import { AIProvider, GenerateOptions, HealthCheckResult, AIError, AI_ERROR_CODES } from '../types';
import { getProxyConfig } from '../config';

export class DoubaoProvider implements AIProvider {
  public readonly name = 'doubao';
  private config: {
    apiKey: string;
    defaultModel: string;
    timeout: number;
    retryAttempts: number;
    baseUrl: string;
  };

  constructor(config: {
    apiKey: string;
    defaultModel?: string;
    timeout?: number;
    retryAttempts?: number;
  }) {
    this.config = {
      apiKey: config.apiKey,
      defaultModel: config.defaultModel || 'doubao-lite-4k',
      timeout: config.timeout || 30000,
      retryAttempts: config.retryAttempts || 3,
      baseUrl: 'https://ark.cn-beijing.volces.com/api/v3'
    };
  }

  /**
   * 生成文本（非流式）
   */
  async generateText(prompt: string, options?: GenerateOptions): Promise<string> {
    const model = options?.model || this.config.defaultModel;
    
    try {
      const response = await this.makeRequest({
        model,
        messages: [
          {
            role: 'system',
            content: options?.systemPrompt || '你是一个智能助手，请用中文回答用户的问题。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: options?.maxTokens || 500,
        temperature: options?.temperature || 0.7,
        stream: false
      });

      if (!response.choices || response.choices.length === 0) {
        throw new Error('No response from Doubao API');
      }

      return response.choices[0].message.content;
    } catch (error) {
      throw this.handleError(error, 'generateText');
    }
  }

  /**
   * 生成流式文本
   */
  async* generateStream(prompt: string, options?: GenerateOptions): AsyncIterable<string> {
    const model = options?.model || this.config.defaultModel;
    
    try {
      const response = await this.makeStreamRequest({
        model,
        messages: [
          {
            role: 'system',
            content: options?.systemPrompt || '你是一个智能助手，请用中文回答用户的问题。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: options?.maxTokens || 500,
        temperature: options?.temperature || 0.7,
        stream: true
      });

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get stream reader');
      }

      const decoder = new TextDecoder();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n').filter(line => line.trim());

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                return;
              }

              try {
                const parsed = JSON.parse(data);
                if (parsed.choices && parsed.choices[0]?.delta?.content) {
                  yield parsed.choices[0].delta.content;
                }
              } catch (parseError) {
                console.warn('Failed to parse SSE data:', data);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      throw this.handleError(error, 'generateStream');
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // 发送简单的测试请求
      await this.generateText('Hello', { maxTokens: 10 });
      
      const latency = Date.now() - startTime;
      
      return {
        isHealthy: true,
        latency,
        metadata: {
          provider: this.name,
          model: this.config.defaultModel,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        isHealthy: false,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          provider: this.name,
          model: this.config.defaultModel,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * 发送HTTP请求
   */
  private async makeRequest(payload: any): Promise<any> {
    const proxyConfig = getProxyConfig();
    
    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(this.config.timeout),
      ...proxyConfig
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * 发送流式HTTP请求
   */
  private async makeStreamRequest(payload: any): Promise<Response> {
    const proxyConfig = getProxyConfig();
    
    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(this.config.timeout),
      ...proxyConfig
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  }

  /**
   * 错误处理
   */
  private handleError(error: unknown, operation: string): AIError {
    console.error(`❌ Doubao ${operation} 失败:`, error);

    if (error instanceof Error) {
      // 检查常见错误类型
      if (error.message.includes('API key') || error.message.includes('401')) {
        return new AIError(
          'Invalid or missing Doubao API key',
          AI_ERROR_CODES.API_KEY_MISSING,
          this.name,
          error
        );
      }
      
      if (error.message.includes('rate limit') || error.message.includes('429')) {
        return new AIError(
          'Doubao API rate limit exceeded',
          AI_ERROR_CODES.RATE_LIMIT,
          this.name,
          error
        );
      }
      
      if (error.message.includes('timeout') || error.name === 'TimeoutError') {
        return new AIError(
          'Doubao API request timeout',
          AI_ERROR_CODES.TIMEOUT,
          this.name,
          error
        );
      }
      
      if (error.message.includes('fetch') || error.message.includes('network')) {
        return new AIError(
          'Network error connecting to Doubao API',
          AI_ERROR_CODES.NETWORK_ERROR,
          this.name,
          error
        );
      }
    }

    // 默认错误
    return new AIError(
      `Doubao ${operation} failed: ${error instanceof Error ? error.message : String(error)}`,
      AI_ERROR_CODES.UNKNOWN,
      this.name,
      error instanceof Error ? error : undefined
    );
  }
}
