/**
 * Gemini AI提供商实现
 * 基于Google Generative AI SDK
 */

import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateText, streamText } from "ai";
import { AIProvider, GenerateOptions, HealthCheckResult, AIError, AI_ERROR_CODES, StreamChunk } from '../types';
import { getProxyConfig } from '../config';

export class GeminiProvider implements AIProvider {
  public readonly name = 'gemini';
  private google: any;
  private config: {
    apiKey: string;
    defaultModel: string;
    timeout: number;
    retryAttempts: number;
  };

  constructor(config: {
    apiKey: string;
    defaultModel?: string;
    timeout?: number;
    retryAttempts?: number;
  }) {
    this.config = {
      apiKey: config.apiKey,
      defaultModel: config.defaultModel || 'gemini-1.5-flash',
      timeout: config.timeout || 30000,
      retryAttempts: config.retryAttempts || 3
    };

    // 初始化Google AI SDK
    const proxyConfig = getProxyConfig();
    this.google = createGoogleGenerativeAI({
      apiKey: this.config.apiKey,
      fetch: proxyConfig.agent ? (input: RequestInfo | URL, init?: RequestInit) => {
        const url = typeof input === 'string' ? input : input.toString();
        return fetch(url, {
          ...init,
          // 代理配置在Node.js环境中处理
          ...(proxyConfig.agent ? { dispatcher: proxyConfig.agent } as any : {})
        });
      } : undefined
    });
  }

  /**
   * 生成文本（非流式）
   */
  async generateText(prompt: string, options?: GenerateOptions): Promise<string> {
    const model = options?.model || this.config.defaultModel;
    
    try {
      const result = await generateText({
        model: this.google(model),
        prompt,
        maxTokens: options?.maxTokens || 500,
        temperature: options?.temperature || 0.7,
      });

      return result.text;
    } catch (error) {
      throw this.handleError(error, 'generateText');
    }
  }

  /**
   * 生成流式文本
   */
  async* generateStream(prompt: string, options?: GenerateOptions): AsyncIterable<string> {
    const model = options?.model || this.config.defaultModel;
    
    try {
      const result = streamText({
        model: this.google(model),
        prompt,
        maxTokens: options?.maxTokens || 500,
        temperature: options?.temperature || 0.7,
      });

      for await (const chunk of result.textStream) {
        yield chunk;
      }
    } catch (error) {
      throw this.handleError(error, 'generateStream');
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // 发送简单的测试请求
      await this.generateText('Hello', { maxTokens: 10 });
      
      const latency = Date.now() - startTime;
      
      return {
        isHealthy: true,
        latency,
        metadata: {
          provider: this.name,
          model: this.config.defaultModel,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        isHealthy: false,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          provider: this.name,
          model: this.config.defaultModel,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: unknown, operation: string): AIError {
    console.error(`❌ Gemini ${operation} 失败:`, error);

    if (error instanceof Error) {
      // 检查常见错误类型
      if (error.message.includes('API key')) {
        return new AIError(
          'Invalid or missing Gemini API key',
          AI_ERROR_CODES.API_KEY_MISSING,
          this.name,
          error
        );
      }
      
      if (error.message.includes('rate limit') || error.message.includes('quota')) {
        return new AIError(
          'Gemini API rate limit exceeded',
          AI_ERROR_CODES.RATE_LIMIT,
          this.name,
          error
        );
      }
      
      if (error.message.includes('timeout') || error.message.includes('TIMEOUT')) {
        return new AIError(
          'Gemini API request timeout',
          AI_ERROR_CODES.TIMEOUT,
          this.name,
          error
        );
      }
      
      if (error.message.includes('network') || error.message.includes('fetch')) {
        return new AIError(
          'Network error connecting to Gemini API',
          AI_ERROR_CODES.NETWORK_ERROR,
          this.name,
          error
        );
      }
    }

    // 默认错误
    return new AIError(
      `Gemini ${operation} failed: ${error instanceof Error ? error.message : String(error)}`,
      AI_ERROR_CODES.UNKNOWN,
      this.name,
      error instanceof Error ? error : undefined
    );
  }
}
