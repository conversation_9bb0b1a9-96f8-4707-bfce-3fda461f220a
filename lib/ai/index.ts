/**
 * SelfMirror AI模块主入口
 * 导出所有核心接口和实现
 */

// 核心类型
export type {
  AIProvider,
  GenerateOptions,
  HealthCheckResult,
  AIFactoryConfig,
  StreamChunk
} from './types';

// 错误类和常量
export {
  AIError,
  AI_ERROR_CODES
} from './types';

// AI工厂
export {
  AIFactory,
  getDefaultAIProvider,
  createAIProvider
} from './factory';

// 提供商实现
export { GeminiProvider } from './providers/gemini';
export { DoubaoProvider } from './providers/doubao';

// 配置管理
export {
  getAIConfig,
  validateConfig,
  getProxyConfig,
  getEnvironmentInfo
} from './config';

// 便捷的默认导出
import { getDefaultAIProvider } from './factory';
export default getDefaultAIProvider;
