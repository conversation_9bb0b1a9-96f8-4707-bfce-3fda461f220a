/**
 * SelfMirror AI工厂
 * 统一的AI提供商管理和创建
 */

import { AIProvider, AIFactoryConfig, AIError, AI_ERROR_CODES } from './types';
import { GeminiProvider } from './providers/gemini';
import { DoubaoProvider } from './providers/doubao';
import { getAIConfig, validateConfig } from './config';

export class AIFactory {
  private static instance: AIFactory | null = null;
  private providers: Map<string, AIProvider> = new Map();
  private currentProvider: AIProvider | null = null;
  private config: AIFactoryConfig;

  private constructor(config?: AIFactoryConfig) {
    this.config = config || getAIConfig();
    
    if (!validateConfig(this.config)) {
      throw new AIError(
        'Invalid AI factory configuration',
        AI_ERROR_CODES.PROVIDER_NOT_FOUND
      );
    }
  }

  /**
   * 获取AI工厂单例
   */
  static getInstance(config?: AIFactoryConfig): AIFactory {
    if (!AIFactory.instance) {
      AIFactory.instance = new AIFactory(config);
    }
    return AIFactory.instance;
  }

  /**
   * 重置单例（主要用于测试）
   */
  static reset(): void {
    AIFactory.instance = null;
  }

  /**
   * 创建AI提供商
   */
  async createProvider(providerName?: 'gemini' | 'doubao'): Promise<AIProvider> {
    const name = providerName || this.config.provider;
    
    // 检查缓存
    if (this.providers.has(name)) {
      return this.providers.get(name)!;
    }

    let provider: AIProvider;

    try {
      switch (name) {
        case 'gemini':
          provider = new GeminiProvider({
            apiKey: name === 'gemini' ? this.config.apiKey : process.env.GEMINI_API_KEY || '',
            defaultModel: this.config.defaultModel,
            timeout: this.config.timeout,
            retryAttempts: this.config.retryAttempts
          });
          break;

        case 'doubao':
          provider = new DoubaoProvider({
            apiKey: name === 'doubao' ? this.config.apiKey : process.env.DOUBAO_API_KEY || '',
            defaultModel: this.config.defaultModel,
            timeout: this.config.timeout,
            retryAttempts: this.config.retryAttempts
          });
          break;

        default:
          throw new AIError(
            `Unsupported provider: ${name}`,
            AI_ERROR_CODES.PROVIDER_NOT_FOUND
          );
      }

      // 缓存提供商
      this.providers.set(name, provider);
      
      // 设置为当前提供商（如果是默认的）
      if (name === this.config.provider) {
        this.currentProvider = provider;
      }

      console.log(`✅ AI提供商 ${name} 创建成功`);
      return provider;

    } catch (error) {
      console.error(`❌ 创建AI提供商 ${name} 失败:`, error);
      throw error instanceof AIError ? error : new AIError(
        `Failed to create provider ${name}: ${error instanceof Error ? error.message : String(error)}`,
        AI_ERROR_CODES.UNKNOWN,
        name
      );
    }
  }

  /**
   * 获取当前提供商
   */
  async getCurrentProvider(): Promise<AIProvider> {
    if (!this.currentProvider) {
      this.currentProvider = await this.createProvider();
    }
    return this.currentProvider;
  }

  /**
   * 切换提供商
   */
  async switchProvider(providerName: 'gemini' | 'doubao'): Promise<AIProvider> {
    console.log(`🔄 切换AI提供商到: ${providerName}`);
    
    const provider = await this.createProvider(providerName);
    this.currentProvider = provider;
    
    // 更新配置
    this.config.provider = providerName;
    
    return provider;
  }

  /**
   * 获取所有可用的提供商
   */
  getAvailableProviders(): string[] {
    return ['gemini', 'doubao'];
  }

  /**
   * 检查提供商健康状态
   */
  async checkProviderHealth(providerName?: 'gemini' | 'doubao'): Promise<{
    provider: string;
    isHealthy: boolean;
    latency?: number;
    error?: string;
  }> {
    const name = providerName || this.config.provider;
    
    try {
      const provider = await this.createProvider(name);
      const result = await provider.healthCheck();
      
      return {
        provider: name,
        isHealthy: result.isHealthy,
        latency: result.latency,
        error: result.error
      };
    } catch (error) {
      return {
        provider: name,
        isHealthy: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 获取配置信息
   */
  getConfig(): AIFactoryConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AIFactoryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 清除缓存的提供商，强制重新创建
    this.providers.clear();
    this.currentProvider = null;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.providers.clear();
    this.currentProvider = null;
  }
}

// 便捷函数：获取默认AI提供商
export async function getDefaultAIProvider(): Promise<AIProvider> {
  const factory = AIFactory.getInstance();
  return factory.getCurrentProvider();
}

// 便捷函数：创建特定提供商
export async function createAIProvider(providerName: 'gemini' | 'doubao'): Promise<AIProvider> {
  const factory = AIFactory.getInstance();
  return factory.createProvider(providerName);
}
