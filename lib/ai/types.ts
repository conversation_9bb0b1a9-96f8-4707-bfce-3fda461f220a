/**
 * SelfMirror AI模块类型定义
 * 简洁、统一的AI接口类型
 */

// 生成选项
export interface GenerateOptions {
  maxTokens?: number;
  temperature?: number;
  model?: string;
  systemPrompt?: string;
}

// AI提供商接口
export interface AIProvider {
  name: string;
  generateText(prompt: string, options?: GenerateOptions): Promise<string>;
  generateStream(prompt: string, options?: GenerateOptions): AsyncIterable<string>;
  healthCheck(): Promise<HealthCheckResult>;
}

// 健康检查结果
export interface HealthCheckResult {
  isHealthy: boolean;
  latency?: number;
  error?: string;
  metadata?: Record<string, any>;
}

// AI工厂配置
export interface AIFactoryConfig {
  provider: 'gemini' | 'doubao';
  apiKey: string;
  proxyUrl?: string;
  defaultModel?: string;
  timeout?: number;
  retryAttempts?: number;
}

// 错误类型
export class AIError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'AIError';
  }
}

// 错误代码
export const AI_ERROR_CODES = {
  PROVIDER_NOT_FOUND: 'PROVIDER_NOT_FOUND',
  API_KEY_MISSING: 'API_KEY_MISSING',
  NETWORK_ERROR: 'NETWORK_ERROR',
  RATE_LIMIT: 'RATE_LIMIT',
  INVALID_RESPONSE: 'INVALID_RESPONSE',
  TIMEOUT: 'TIMEOUT',
  UNKNOWN: 'UNKNOWN'
} as const;

// 流式响应块
export interface StreamChunk {
  text: string;
  done: boolean;
  metadata?: Record<string, any>;
}
