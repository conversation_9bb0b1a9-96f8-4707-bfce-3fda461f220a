/**
 * Unified API Foundation - 统一API基础架构
 * 
 * 核心功能：
 * - 统一的请求/响应处理流程
 * - 标准化的错误处理和日志记录
 * - 可插拔的验证和转换机制
 * - 性能监控和优化
 */

// 移除未使用的导入，由具体处理器负责导入所需服务

/**
 * HTTP方法枚举
 */
export enum HTTPMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH'
}

/**
 * API请求上下文接口
 */
export interface APIRequestContext {
  requestId: string;
  method: HTTPMethod;
  path: string;
  headers: Record<string, string>;
  query: Record<string, string>;
  body?: any;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
}

/**
 * API响应元数据接口
 */
export interface APIResponseMetadata {
  requestId: string;
  processingTime: number;
  version: string;
  cacheHits: {
    contextCache: boolean;
    aiProviderCache: boolean;
    vectorRetrievalCache: boolean;
  };
  optimizationApplied: string[];
  performanceMetrics?: {
    validationTime: number;
    processingTime: number;
    responseTime: number;
  };
}

/**
 * API处理结果接口
 */
export interface APIProcessingResult<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: APIResponseMetadata;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * 统一API处理器抽象基类
 */
export abstract class UnifiedAPIHandler<TRequest, TResponse> {
  abstract readonly endpoint: string;
  abstract readonly method: HTTPMethod;
  abstract readonly version: string;

  /**
   * 统一的请求处理流程
   */
  async handle(request: Request): Promise<Response> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    
    let validationTime = 0;
    let processingTime = 0;
    let responseTime = 0;

    try {
      // 1. 请求预处理
      const context = await this.createRequestContext(request, requestId);
      
      // 2. 请求验证
      const validationStart = Date.now();
      const validatedRequest = await this.validateRequest(context);
      validationTime = Date.now() - validationStart;
      
      if (!validatedRequest.isValid) {
        return this.createErrorResponse(
          'VALIDATION_ERROR',
          `请求验证失败: ${validatedRequest.errors.join(', ')}`,
          400,
          requestId,
          { validationErrors: validatedRequest.errors }
        );
      }

      // 3. 业务逻辑处理
      const processingStart = Date.now();
      const result = await this.processRequest(context);
      processingTime = Date.now() - processingStart;
      
      // 4. 响应后处理
      const responseStart = Date.now();
      const finalResponse = await this.postProcessResponse(result, context);
      responseTime = Date.now() - responseStart;
      
      // 5. 创建成功响应
      const totalTime = Date.now() - startTime;
      return this.createSuccessResponse(finalResponse, {
        requestId,
        processingTime: totalTime,
        version: this.version,
        cacheHits: result.cacheHits || {
          contextCache: false,
          aiProviderCache: false,
          vectorRetrievalCache: false
        },
        optimizationApplied: result.optimizationApplied || [],
        performanceMetrics: {
          validationTime,
          processingTime,
          responseTime
        }
      });

    } catch (error) {
      const totalTime = Date.now() - startTime;
      console.error(`❌ API处理失败 [${this.endpoint}]:`, error);
      
      return this.createErrorResponse(
        'INTERNAL_ERROR',
        error instanceof Error ? error.message : '服务暂时不可用',
        500,
        requestId,
        { 
          endpoint: this.endpoint,
          processingTime: totalTime,
          performanceMetrics: { validationTime, processingTime, responseTime }
        }
      );
    }
  }

  /**
   * 抽象方法：子类必须实现
   */
  protected abstract validateRequest(context: APIRequestContext): Promise<ValidationResult>;
  protected abstract processRequest(context: APIRequestContext): Promise<{
    data: TResponse;
    cacheHits?: APIResponseMetadata['cacheHits'];
    optimizationApplied?: string[];
  }>;

  /**
   * 可选的钩子方法：子类可以重写
   */
  protected async postProcessResponse(
    result: { data: TResponse; cacheHits?: any; optimizationApplied?: string[] },
    context: APIRequestContext
  ): Promise<TResponse> {
    return result.data;
  }

  /**
   * 创建请求上下文
   */
  protected async createRequestContext(request: Request, requestId: string): Promise<APIRequestContext> {
    const url = new URL(request.url);
    const headers: Record<string, string> = {};
    const query: Record<string, string> = {};
    
    // 提取headers
    request.headers.forEach((value, key) => {
      headers[key] = value;
    });
    
    // 提取query参数
    url.searchParams.forEach((value, key) => {
      query[key] = value;
    });
    
    // 解析body
    let body: any = undefined;
    if (request.method !== 'GET' && request.method !== 'DELETE') {
      try {
        const contentType = headers['content-type'] || '';
        if (contentType.includes('application/json')) {
          body = await request.json();
        } else if (contentType.includes('text/')) {
          body = await request.text();
        }
      } catch (error) {
        console.warn('⚠️ 解析请求体失败:', error);
      }
    }

    return {
      requestId,
      method: request.method as HTTPMethod,
      path: url.pathname,
      headers,
      query,
      body,
      timestamp: new Date(),
      userAgent: headers['user-agent'],
      ip: headers['x-forwarded-for'] || headers['x-real-ip']
    };
  }

  /**
   * 创建成功响应
   */
  protected createSuccessResponse(data: TResponse, metadata: APIResponseMetadata): Response {
    const responseData = {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        ...metadata
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'X-Request-ID': metadata.requestId,
        'X-Processing-Time': String(metadata.processingTime),
        'X-API-Version': metadata.version,
        'X-Cache-Status': this.formatCacheStatus(metadata.cacheHits)
      }
    });
  }

  /**
   * 创建错误响应
   */
  protected createErrorResponse(
    code: string,
    message: string,
    status: number,
    requestId: string,
    details?: any
  ): Response {
    const responseData = {
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: new Date().toISOString(),
        requestId
      }
    };

    return new Response(JSON.stringify(responseData, null, 2), {
      status,
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
        'X-Error-Code': code
      }
    });
  }

  /**
   * 格式化缓存状态
   */
  protected formatCacheStatus(cacheHits: APIResponseMetadata['cacheHits']): string {
    const hits = Object.entries(cacheHits)
      .filter(([_, hit]) => hit)
      .map(([layer, _]) => layer);
    
    return hits.length > 0 ? `HIT:${hits.join(',')}` : 'MISS';
  }

  /**
   * 生成请求ID
   */
  protected generateRequestId(): string {
    return `api-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 记录性能指标
   */
  protected recordMetrics(
    requestId: string,
    processingTime: number,
    success: boolean,
    endpoint: string
  ): void {
    // 这里可以集成到监控系统
    console.log(`📊 API指标 [${endpoint}]: ${processingTime}ms, 成功: ${success}, ID: ${requestId}`);
  }
}

/**
 * 流式API处理器抽象基类
 */
export abstract class UnifiedStreamAPIHandler<TRequest> extends UnifiedAPIHandler<TRequest, ReadableStream> {
  
  /**
   * 创建流式响应
   */
  protected createStreamResponse(
    generator: AsyncGenerator<string>,
    metadata: APIResponseMetadata
  ): Response {
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        
        try {
          // 发送初始元数据
          const initData = `data: ${JSON.stringify({
            type: 'init',
            requestId: metadata.requestId,
            timestamp: new Date().toISOString(),
            metadata
          })}\n\n`;
          controller.enqueue(encoder.encode(initData));

          // 流式发送数据
          for await (const chunk of generator) {
            const data = `data: ${JSON.stringify({
              type: 'text',
              content: chunk,
              timestamp: new Date().toISOString(),
              requestId: metadata.requestId
            })}\n\n`;
            controller.enqueue(encoder.encode(data));
          }

          // 发送结束标记
          const endData = `data: ${JSON.stringify({
            type: 'end',
            requestId: metadata.requestId,
            timestamp: new Date().toISOString(),
            metadata
          })}\n\n`;
          controller.enqueue(encoder.encode(endData));

        } catch (error) {
          const errorData = `data: ${JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : String(error),
            requestId: metadata.requestId,
            timestamp: new Date().toISOString()
          })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
          controller.error(error);
        } finally {
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Request-ID': metadata.requestId,
        'X-API-Version': metadata.version,
        'X-Cache-Status': this.formatCacheStatus(metadata.cacheHits)
      }
    });
  }
}
