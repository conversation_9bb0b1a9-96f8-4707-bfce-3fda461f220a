/**
 * Chat API Handler - 聊天API处理器
 * 
 * 基于UnifiedAPIHandler实现的聊天功能处理器
 * 集成三引擎架构和统一上下文管理
 */

import { 
  UnifiedStreamAPIHandler, 
  HTTPMethod, 
  APIRequestContext, 
  ValidationResult,
  APIResponseMetadata
} from '../unified-api-foundation';
import { unifiedContextManager } from '@/lib/services/unified-context/unified-context-manager';
import { threeEngineWorkflowCoordinator } from '@/lib/services/three-engine/workflow-coordinator';
import { globalIdManager } from '@/lib/services/vector-database/global-id-system';
import { getDefaultCachedAIProvider } from '@/lib/ai/cached-ai-factory';
import { processChatRequest, ChatPipelineInput } from '@/lib/data/pipelines/chat-pipeline';
import { cacheCoordinator, CacheLayerType } from '@/lib/cache/cache-coordinator';
import { MonitorAPI, recordCacheHit, recordThroughput } from '@/lib/monitoring/performance-decorators';
import { performanceMonitor, MetricType } from '@/lib/monitoring/performance-monitor';

/**
 * 聊天请求接口
 */
export interface ChatRequest {
  message: string;
  sessionId?: string;
  userId?: string;
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
  };
  context?: {
    includeHistory?: boolean;
    historyLength?: number;
    includeProfile?: boolean;
  };
}

/**
 * 聊天响应接口
 */
export interface ChatResponse {
  message: string;
  sessionId: string;
  messageId: string;
  timestamp: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  context?: {
    retrievedChunks: number;
    contextLength: number;
    processingSteps: string[];
  };
}

/**
 * 聊天API处理器实现
 */
export class ChatAPIHandler extends UnifiedStreamAPIHandler<ChatRequest> {
  readonly endpoint = '/api/v2/chat';
  readonly method = HTTPMethod.POST;
  readonly version = '2.0.0';

  /**
   * 验证聊天请求（使用统一数据管道）
   */
  protected async validateRequest(context: APIRequestContext): Promise<ValidationResult> {
    try {
      console.log(`🔍 开始验证聊天请求: ${context.requestId}`);

      // 使用统一数据管道进行验证和预处理
      const pipelineResult = await processChatRequest(
        context.body as ChatPipelineInput,
        context.requestId,
        'chat-api-validation'
      );

      if (pipelineResult.success) {
        // 将处理后的数据存回context，供后续使用
        context.body = pipelineResult.data;

        console.log(`✅ 聊天请求验证成功: ${context.requestId}`);
        return {
          isValid: true,
          errors: [],
          warnings: pipelineResult.warnings
        };
      } else {
        console.error(`❌ 聊天请求验证失败: ${context.requestId}`, pipelineResult.errors);
        return {
          isValid: false,
          errors: pipelineResult.errors,
          warnings: pipelineResult.warnings
        };
      }

    } catch (error) {
      console.error('❌ 聊天请求验证异常:', error);
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : '验证过程发生异常'],
        warnings: []
      };
    }
  }

  /**
   * 处理聊天请求
   */
  protected async processRequest(context: APIRequestContext): Promise<{
    data: ReadableStream;
    cacheHits: APIResponseMetadata['cacheHits'];
    optimizationApplied: string[];
  }> {
    const request = context.body as ChatRequest;
    const startTime = Date.now();
    
    let cacheHits = {
      contextCache: false,
      aiProviderCache: false,
      vectorRetrievalCache: false
    };
    
    let optimizationApplied: string[] = [];

    try {
      // 1. 生成用户输入ID
      const userInputId = await globalIdManager.generateUserInputId();

      // 2. 构建统一上下文
      const contextOptions = {
        includeHistory: request.context?.includeHistory ?? true,
        historyLength: request.context?.historyLength ?? 6,
        includeProfile: request.context?.includeProfile ?? true,
        sessionId: request.sessionId,
        userId: request.userId
      };

      const unifiedContext = await unifiedContextManager.processContextRequest({
        requestId: userInputId,
        moduleType: 'integration_generator' as const,
        timestamp: new Date(),
        userMessage: request.message,
        sessionId: request.sessionId || 'default-session',
        conversationConfig: {
          conversationRounds: contextOptions.historyLength || 6,
          enableHistoricalRetrieval: contextOptions.includeHistory,
          historicalSearchDepth: 50,
          filterEmptyMessages: true,
          filterSystemMessages: false,
          recentnessWeight: 0.3,
          relevanceWeight: 0.7
        },
        contextConfig: {
          maxContextLength: 8000,
          maxContextItems: 50,
          includeUserProfile: contextOptions.includeProfile,
          includeRecentHistory: contextOptions.includeHistory,
          includeMentalModel: true,
          includeSystemPrompts: true,
          includeRAGContext: true,
          compressionLevel: 'moderate' as const,
          enableDuplicateRemoval: true,
          enableContentCompression: true,
          enableSmartTruncation: true,
          qualityThresholds: {
            minWeightedScore: 0.1,
            minContentLength: 10,
            minRelevanceScore: 0.3
          }
        }
      });

      // 检查是否有缓存命中（通过处理时间判断）
      if (unifiedContext.processingStats.totalProcessingTime < 100) {
        cacheHits.contextCache = true;
        optimizationApplied.push('context_cache_hit');
      }

      // 使用缓存协调器检查多层缓存
      const cacheKey = `chat-${userInputId}`;
      const coordinatedCache = await cacheCoordinator.coordinatedGet(cacheKey, [
        CacheLayerType.AI_PROVIDER,
        CacheLayerType.CONTEXT,
        CacheLayerType.VECTOR_RETRIEVAL
      ]);

      if (coordinatedCache.value) {
        console.log(`🎯 缓存协调命中: ${cacheKey} | 来源: ${coordinatedCache.source}`);
        // 记录缓存命中
        recordCacheHit('ChatAPI', 'coordinated', true);
        // 可以根据需要使用缓存的响应
        optimizationApplied.push(`coordinated_cache_hit_${coordinatedCache.source}`);
      } else {
        // 记录缓存未命中
        recordCacheHit('ChatAPI', 'coordinated', false);
      }

      // 3. 使用三引擎工作流处理
      const workflowRequest = {
        userMessage: request.message,
        userInputId,
        unifiedContext: unifiedContext.packagedContext.finalContext,
        options: {
          model: request.options?.model,
          temperature: request.options?.temperature,
          maxTokens: request.options?.maxTokens,
          stream: true // 强制使用流式响应
        }
      };

      const workflowResponse = await threeEngineWorkflowCoordinator.executeWorkflow(workflowRequest);

      // 检查工作流中的缓存命中
      if (workflowResponse.cacheHits?.aiProvider) {
        cacheHits.aiProviderCache = true;
        optimizationApplied.push('ai_provider_cache_hit');
      }

      if (workflowResponse.cacheHits?.vectorRetrieval) {
        cacheHits.vectorRetrievalCache = true;
        optimizationApplied.push('vector_retrieval_cache_hit');
      }

      // 4. 创建流式响应生成器
      const responseGenerator = this.createResponseGenerator(
        workflowResponse,
        userInputId,
        request,
        startTime
      );

      const stream = new ReadableStream({
        async start(controller) {
          const encoder = new TextEncoder();
          
          try {
            for await (const chunk of responseGenerator) {
              controller.enqueue(encoder.encode(chunk));
            }
          } catch (error) {
            console.error('❌ 流式响应生成失败:', error);
            const errorData = `data: ${JSON.stringify({
              type: 'error',
              error: error instanceof Error ? error.message : String(error),
              requestId: context.requestId,
              timestamp: new Date().toISOString()
            })}\n\n`;
            controller.enqueue(encoder.encode(errorData));
            controller.error(error);
          } finally {
            controller.close();
          }
        }
      });

      // 记录性能指标
      const processingTime = Date.now() - startTime;
      performanceMonitor.recordMetric(
        MetricType.AI_PROCESSING_TIME,
        processingTime,
        'ChatAPI',
        'ai_generation',
        {
          requestId: context.requestId,
          cacheHits: Object.keys(cacheHits).length,
          optimizations: optimizationApplied.length
        }
      );

      // 记录吞吐量（简化计算）
      recordThroughput('ChatAPI', 'chat_requests', 1000 / processingTime);

      return {
        data: stream,
        cacheHits,
        optimizationApplied
      };

    } catch (error) {
      console.error('❌ 聊天请求处理失败:', error);
      throw error;
    }
  }

  /**
   * 创建响应生成器
   */
  private async* createResponseGenerator(
    workflowResponse: any,
    userInputId: string,
    request: ChatRequest,
    startTime: number
  ): AsyncGenerator<string> {
    try {
      // 发送初始化消息
      yield `data: ${JSON.stringify({
        type: 'init',
        sessionId: request.sessionId || 'default',
        messageId: userInputId,
        timestamp: new Date().toISOString(),
        model: request.options?.model || 'gemini-pro'
      })}\n\n`;

      // 流式发送AI响应
      if (workflowResponse.stream) {
        for await (const chunk of workflowResponse.stream) {
          yield `data: ${JSON.stringify({
            type: 'text',
            content: chunk,
            timestamp: new Date().toISOString()
          })}\n\n`;
        }
      } else if (workflowResponse.response) {
        // 如果不是流式，分块发送
        const text = workflowResponse.response;
        const chunkSize = 50;
        for (let i = 0; i < text.length; i += chunkSize) {
          const chunk = text.slice(i, i + chunkSize);
          yield `data: ${JSON.stringify({
            type: 'text',
            content: chunk,
            timestamp: new Date().toISOString()
          })}\n\n`;
          
          // 添加小延迟以模拟流式效果
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      // 发送完成消息
      const processingTime = Date.now() - startTime;
      yield `data: ${JSON.stringify({
        type: 'end',
        messageId: userInputId,
        processingTime,
        usage: workflowResponse.usage || {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0
        },
        context: {
          retrievedChunks: workflowResponse.retrievedChunks || 0,
          contextLength: workflowResponse.contextLength || 0,
          processingSteps: workflowResponse.processingSteps || []
        },
        timestamp: new Date().toISOString()
      })}\n\n`;

    } catch (error) {
      console.error('❌ 响应生成器失败:', error);
      yield `data: ${JSON.stringify({
        type: 'error',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      })}\n\n`;
    }
  }

  /**
   * 后处理响应（流式API不需要）
   */
  protected async postProcessResponse(
    result: { data: ReadableStream; cacheHits?: any; optimizationApplied?: string[] },
    context: APIRequestContext
  ): Promise<ReadableStream> {
    // 记录请求完成
    console.log(`✅ 聊天请求处理完成: ${context.requestId}`);
    return result.data;
  }
}

// 导出单例实例
export const chatAPIHandler = new ChatAPIHandler();
