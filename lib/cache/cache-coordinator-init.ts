/**
 * Cache Coordinator Initialization - 缓存协调器初始化
 * 
 * 自动注册所有缓存层并启动协调器
 */

import { cacheCoordinator } from './cache-coordinator';
import { CacheAdapterFactory } from './adapters';
import { configHotReloadService } from '@/lib/config/config-hot-reload';
import { getSelfMirrorConfig } from '@/lib/config/config-layers/selfmirror-config';

/**
 * 初始化缓存协调器
 */
export function initializeCacheCoordinator(): void {
  console.log('🔄 初始化缓存协调器...');

  try {
    // 获取配置
    const config = getSelfMirrorConfig();
    
    // 注册所有缓存层适配器
    const adapters = CacheAdapterFactory.createAllAdapters();
    adapters.forEach(adapter => {
      cacheCoordinator.registerCacheLayer(adapter);
    });

    // 设置配置变更监听
    setupConfigListeners();

    // 设置缓存协调器事件监听
    setupCacheCoordinatorListeners();

    console.log('✅ 缓存协调器初始化完成');
    console.log(`📊 已注册 ${adapters.length} 个缓存层`);

  } catch (error) {
    console.error('❌ 缓存协调器初始化失败:', error);
    throw error;
  }
}

/**
 * 设置配置变更监听器
 */
function setupConfigListeners(): void {
  // 监听缓存配置变更
  configHotReloadService.on('cacheConfigChanged', ({ key, newValue }) => {
    console.log(`🔄 缓存配置变更: ${key} = ${JSON.stringify(newValue)}`);
    
    // 根据配置变更调整缓存协调器行为
    if (key === 'ai.cache.enabled') {
      handleCacheToggle(newValue);
    } else if (key.startsWith('dualCore.retrievalOptimizer.')) {
      handleRetrievalOptimizerConfigChange(key, newValue);
    }
  });

  // 监听缓存切换事件
  configHotReloadService.on('cacheToggled', ({ enabled }) => {
    handleCacheToggle(enabled);
  });
}

/**
 * 设置缓存协调器事件监听器
 */
function setupCacheCoordinatorListeners(): void {
  // 监听缓存设置事件
  cacheCoordinator.on('cacheSet', (event) => {
    console.log(`💾 缓存协调设置: ${event.key} | 成功层: ${event.successLayers.length} | 失败层: ${event.failedLayers.length}`);
  });

  // 监听缓存删除事件
  cacheCoordinator.on('cacheDeleted', (event) => {
    console.log(`🗑️ 缓存协调删除: ${event.key} | 删除层: ${event.deletedLayers.length} | 失败层: ${event.failedLayers.length}`);
  });

  // 监听一致性检查事件
  cacheCoordinator.on('consistencyCheck', (result) => {
    console.log(`🔍 缓存一致性检查: ${result.overallHealth} | 健康层: ${result.healthyLayers}/${result.totalLayers}`);
    
    if (result.overallHealth === 'critical') {
      console.error('🚨 缓存系统健康状态严重，需要立即处理');
    } else if (result.overallHealth === 'warning') {
      console.warn('⚠️ 缓存系统健康状态警告，建议检查');
    }
  });

  // 监听性能更新事件
  cacheCoordinator.on('performanceUpdated', (metrics) => {
    const hitRate = (metrics.overallHitRate * 100).toFixed(1);
    console.log(`📊 缓存性能更新: 总命中率 ${hitRate}% | 总请求 ${metrics.totalRequests}`);
  });

  // 监听层恢复事件
  cacheCoordinator.on('layerRecovered', (event) => {
    console.log(`🔄 缓存层已恢复: ${event.layerType}`);
  });

  // 监听恢复失败事件
  cacheCoordinator.on('recoveryFailed', (event) => {
    console.error(`❌ 缓存层恢复失败: ${event.layerType}`, event.error);
  });
}

/**
 * 处理缓存开关切换
 */
function handleCacheToggle(enabled: boolean): void {
  console.log(`🔄 缓存系统${enabled ? '启用' : '禁用'}`);
  
  // 这里可以根据需要启用/禁用特定的缓存层
  // 目前保持所有层的状态不变，由各层自己处理启用/禁用逻辑
}

/**
 * 处理检索优化器配置变更
 */
function handleRetrievalOptimizerConfigChange(key: string, newValue: any): void {
  console.log(`🔄 检索优化器配置变更: ${key} = ${JSON.stringify(newValue)}`);
  
  // 触发检索优化器重新配置
  // 这里可以添加具体的重新配置逻辑
}

/**
 * 获取缓存协调器状态
 */
export function getCacheCoordinatorStatus(): {
  initialized: boolean;
  registeredLayers: number;
  performanceMetrics: any;
  lastConsistencyCheck?: any;
} {
  const layers = cacheCoordinator.getRegisteredLayers();
  const metrics = cacheCoordinator.getPerformanceMetrics();
  
  return {
    initialized: layers.length > 0,
    registeredLayers: layers.length,
    performanceMetrics: metrics,
    lastConsistencyCheck: null // 可以添加最后一次检查结果的存储
  };
}

/**
 * 手动触发缓存协调器操作
 */
export async function triggerCacheCoordinatorOperations(): Promise<{
  consistencyCheck: any;
  performanceMetrics: any;
}> {
  console.log('🔄 手动触发缓存协调器操作...');
  
  // 触发一致性检查
  const consistencyCheck = await cacheCoordinator.triggerConsistencyCheck();
  
  // 触发性能指标更新
  cacheCoordinator.triggerPerformanceUpdate();
  const performanceMetrics = cacheCoordinator.getPerformanceMetrics();
  
  return {
    consistencyCheck,
    performanceMetrics
  };
}

/**
 * 测试缓存协调功能
 */
export async function testCacheCoordination(): Promise<{
  setTest: any;
  getTest: any;
  deleteTest: any;
}> {
  console.log('🧪 测试缓存协调功能...');
  
  const testKey = `test-coordination-${Date.now()}`;
  const testValue = { message: '缓存协调测试', timestamp: new Date().toISOString() };
  
  try {
    // 测试协调设置
    const setResult = await cacheCoordinator.coordinatedSet(testKey, testValue, {
      ttl: 60000, // 1分钟
      syncStrategy: 'all'
    });
    
    // 测试协调获取
    const getResult = await cacheCoordinator.coordinatedGet(testKey);
    
    // 测试协调删除
    const deleteResult = await cacheCoordinator.coordinatedDelete(testKey);
    
    return {
      setTest: setResult,
      getTest: getResult,
      deleteTest: deleteResult
    };
    
  } catch (error) {
    console.error('❌ 缓存协调测试失败:', error);
    throw error;
  }
}

/**
 * 销毁缓存协调器
 */
export function destroyCacheCoordinator(): void {
  console.log('🔄 销毁缓存协调器...');
  
  // 移除配置监听器
  configHotReloadService.removeAllListeners('cacheConfigChanged');
  configHotReloadService.removeAllListeners('cacheToggled');
  
  // 销毁缓存协调器
  cacheCoordinator.destroy();
  
  console.log('✅ 缓存协调器已销毁');
}

// 自动初始化
initializeCacheCoordinator();
