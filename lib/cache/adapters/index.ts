/**
 * Cache Layer Adapters - 缓存层适配器
 * 
 * 为现有缓存系统提供统一接口适配器
 */

import { CacheLayer, CacheLayerType } from '../cache-coordinator';
import { aiProviderCacheLayer } from '@/lib/optimization/ai-provider-cache-layer';
import { retrievalResultOptimizer } from '@/lib/services/dual-core/retrieval-result-optimizer';

/**
 * AI提供商缓存层适配器
 */
export class AIProviderCacheAdapter implements CacheLayer {
  readonly type = CacheLayerType.AI_PROVIDER;
  readonly name = 'AI Provider Cache';
  readonly enabled = true;

  async get(key: string): Promise<any> {
    return aiProviderCacheLayer.get(key);
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    // AI提供商缓存层有自己的设置逻辑，这里提供简化接口
    await aiProviderCacheLayer.set(key, value, {
      model: 'unknown',
      promptHash: key.substring(0, 8),
      processingTime: 0
    }, ttl);
  }

  async delete(key: string): Promise<boolean> {
    // AI提供商缓存层没有直接的删除方法，返回false
    return false;
  }

  async clear(): Promise<void> {
    aiProviderCacheLayer.clear();
  }

  getStats(): any {
    return aiProviderCacheLayer.getCacheStats();
  }

  healthCheck(): boolean {
    const stats = this.getStats();
    return stats.totalItems >= 0 && stats.memoryUsageMB < 200; // 简单健康检查
  }
}

/**
 * 检索结果优化器适配器
 */
export class RetrievalOptimizerAdapter implements CacheLayer {
  readonly type = CacheLayerType.RETRIEVAL_OPTIMIZER;
  readonly name = 'Retrieval Result Optimizer';
  readonly enabled = true;

  async get(key: string): Promise<any> {
    return retrievalResultOptimizer.getOptimizationItem(key);
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    // 检索结果优化器通过optimizeRetrievalResults方法设置
    // 这里提供简化接口
    if (Array.isArray(value)) {
      await retrievalResultOptimizer.optimizeRetrievalResults(value);
    }
  }

  async delete(key: string): Promise<boolean> {
    // 检索结果优化器没有直接的删除方法
    return false;
  }

  async clear(): Promise<void> {
    retrievalResultOptimizer.destroy();
    // 重新初始化
    await retrievalResultOptimizer.initialize();
  }

  getStats(): any {
    return retrievalResultOptimizer.getOptimizationStats();
  }

  healthCheck(): boolean {
    const stats = this.getStats();
    return stats.totalItems >= 0 && stats.hitRate >= 0;
  }
}

/**
 * 上下文缓存适配器（模拟实现）
 */
export class ContextCacheAdapter implements CacheLayer {
  readonly type = CacheLayerType.CONTEXT;
  readonly name = 'Context Cache';
  readonly enabled = true;

  private cache = new Map<string, { value: any; timestamp: number; ttl: number }>();
  private stats = {
    totalRequests: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    averageResponseTime: 0,
    errorCount: 0
  };

  async get(key: string): Promise<any> {
    this.stats.totalRequests++;
    
    const item = this.cache.get(key);
    if (item && Date.now() - item.timestamp < item.ttl) {
      this.stats.hitCount++;
      this.updateHitRate();
      return item.value;
    } else {
      this.stats.missCount++;
      this.updateHitRate();
      if (item) {
        this.cache.delete(key); // 删除过期项
      }
      return null;
    }
  }

  async set(key: string, value: any, ttl: number = 3600000): Promise<void> {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });
  }

  async delete(key: string): Promise<boolean> {
    return this.cache.delete(key);
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.resetStats();
  }

  getStats(): any {
    return {
      totalItems: this.cache.size,
      ...this.stats
    };
  }

  healthCheck(): boolean {
    return this.cache.size >= 0 && this.stats.errorCount < 10;
  }

  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 ? 
      this.stats.hitCount / this.stats.totalRequests : 0;
  }

  private resetStats(): void {
    this.stats = {
      totalRequests: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      averageResponseTime: 0,
      errorCount: 0
    };
  }
}

/**
 * 向量检索缓存适配器（模拟实现）
 */
export class VectorRetrievalCacheAdapter implements CacheLayer {
  readonly type = CacheLayerType.VECTOR_RETRIEVAL;
  readonly name = 'Vector Retrieval Cache';
  readonly enabled = true;

  private cache = new Map<string, { value: any; timestamp: number; ttl: number }>();
  private stats = {
    totalRequests: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    averageResponseTime: 0,
    errorCount: 0
  };

  async get(key: string): Promise<any> {
    this.stats.totalRequests++;
    
    const item = this.cache.get(key);
    if (item && Date.now() - item.timestamp < item.ttl) {
      this.stats.hitCount++;
      this.updateHitRate();
      return item.value;
    } else {
      this.stats.missCount++;
      this.updateHitRate();
      if (item) {
        this.cache.delete(key); // 删除过期项
      }
      return null;
    }
  }

  async set(key: string, value: any, ttl: number = 1800000): Promise<void> { // 30分钟默认TTL
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });
  }

  async delete(key: string): Promise<boolean> {
    return this.cache.delete(key);
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.resetStats();
  }

  getStats(): any {
    return {
      totalItems: this.cache.size,
      ...this.stats
    };
  }

  healthCheck(): boolean {
    return this.cache.size >= 0 && this.stats.errorCount < 5;
  }

  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 ? 
      this.stats.hitCount / this.stats.totalRequests : 0;
  }

  private resetStats(): void {
    this.stats = {
      totalRequests: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      averageResponseTime: 0,
      errorCount: 0
    };
  }
}

/**
 * 缓存适配器工厂
 */
export class CacheAdapterFactory {
  /**
   * 创建所有缓存层适配器
   */
  static createAllAdapters(): CacheLayer[] {
    return [
      new AIProviderCacheAdapter(),
      new RetrievalOptimizerAdapter(),
      new ContextCacheAdapter(),
      new VectorRetrievalCacheAdapter()
    ];
  }

  /**
   * 创建特定类型的适配器
   */
  static createAdapter(type: CacheLayerType): CacheLayer | null {
    switch (type) {
      case CacheLayerType.AI_PROVIDER:
        return new AIProviderCacheAdapter();
      case CacheLayerType.RETRIEVAL_OPTIMIZER:
        return new RetrievalOptimizerAdapter();
      case CacheLayerType.CONTEXT:
        return new ContextCacheAdapter();
      case CacheLayerType.VECTOR_RETRIEVAL:
        return new VectorRetrievalCacheAdapter();
      default:
        return null;
    }
  }

  /**
   * 获取适配器信息
   */
  static getAdapterInfo(): Array<{
    type: CacheLayerType;
    name: string;
    description: string;
    features: string[];
  }> {
    return [
      {
        type: CacheLayerType.AI_PROVIDER,
        name: 'AI Provider Cache',
        description: 'AI提供商响应缓存，提高AI调用性能',
        features: ['智能缓存键生成', 'LRU淘汰策略', '内存使用控制', '性能监控']
      },
      {
        type: CacheLayerType.RETRIEVAL_OPTIMIZER,
        name: 'Retrieval Result Optimizer',
        description: '检索结果优化器，提供历史加权和噪声抑制',
        features: ['历史加权排序', '噪声抑制', '质量阈值过滤', '实时优化']
      },
      {
        type: CacheLayerType.CONTEXT,
        name: 'Context Cache',
        description: '上下文缓存，存储用户会话和上下文信息',
        features: ['会话管理', 'TTL控制', '内存缓存', '快速访问']
      },
      {
        type: CacheLayerType.VECTOR_RETRIEVAL,
        name: 'Vector Retrieval Cache',
        description: '向量检索缓存，缓存向量搜索结果',
        features: ['向量搜索缓存', '相似度缓存', '检索结果缓存', '性能优化']
      }
    ];
  }
}

// 导出适配器实例
export const aiProviderCacheAdapter = new AIProviderCacheAdapter();
export const retrievalOptimizerAdapter = new RetrievalOptimizerAdapter();
export const contextCacheAdapter = new ContextCacheAdapter();
export const vectorRetrievalCacheAdapter = new VectorRetrievalCacheAdapter();
