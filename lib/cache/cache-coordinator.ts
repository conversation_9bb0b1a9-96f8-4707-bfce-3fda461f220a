/**
 * Cache Coordinator - 缓存协调器
 * 
 * 核心功能：
 * - 协调多层级缓存的一致性
 * - 缓存失效策略和同步机制
 * - 缓存性能监控和优化
 * - 缓存健康检查和自动恢复
 */

import { EventEmitter } from 'events';
// 移除错误处理导入，使用内部错误处理

/**
 * 缓存层类型枚举
 */
export enum CacheLayerType {
  AI_PROVIDER = 'AI_PROVIDER',           // AI提供商缓存
  CONTEXT = 'CONTEXT',                   // 上下文缓存
  VECTOR_RETRIEVAL = 'VECTOR_RETRIEVAL', // 向量检索缓存
  RETRIEVAL_OPTIMIZER = 'RETRIEVAL_OPTIMIZER', // 检索结果优化器
  API_RESPONSE = 'API_RESPONSE'          // API响应缓存（已移除，保留枚举用于兼容）
}

/**
 * 缓存层接口
 */
export interface CacheLayer {
  type: CacheLayerType;
  name: string;
  enabled: boolean;
  
  // 基础缓存操作
  get(key: string): Promise<any> | any;
  set(key: string, value: any, ttl?: number): Promise<void> | void;
  delete(key: string): Promise<boolean> | boolean;
  clear(): Promise<void> | void;
  
  // 统计信息
  getStats(): any;
  
  // 健康检查
  healthCheck(): Promise<boolean> | boolean;
}

/**
 * 缓存协调配置接口
 */
export interface CacheCoordinatorConfig {
  enableConsistencyCheck: boolean;
  consistencyCheckInterval: number;
  enablePerformanceMonitoring: boolean;
  performanceCheckInterval: number;
  enableAutoRecovery: boolean;
  maxInconsistencyThreshold: number;
  cacheInvalidationStrategy: 'immediate' | 'lazy' | 'scheduled';
  globalTTL: number;
}

/**
 * 缓存一致性检查结果接口
 */
export interface ConsistencyCheckResult {
  timestamp: Date;
  totalLayers: number;
  healthyLayers: number;
  inconsistentLayers: string[];
  performanceIssues: string[];
  recommendations: string[];
  overallHealth: 'healthy' | 'warning' | 'critical';
}

/**
 * 缓存性能指标接口
 */
export interface CachePerformanceMetrics {
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  overallHitRate: number;
  layerMetrics: Record<CacheLayerType, {
    requests: number;
    hits: number;
    misses: number;
    hitRate: number;
    averageResponseTime: number;
    errorCount: number;
  }>;
  lastUpdated: Date;
}

/**
 * 缓存协调器实现
 */
export class CacheCoordinator extends EventEmitter {
  private layers: Map<CacheLayerType, CacheLayer> = new Map();
  private config: CacheCoordinatorConfig;
  private performanceMetrics: CachePerformanceMetrics;
  private consistencyTimer?: NodeJS.Timeout;
  private performanceTimer?: NodeJS.Timeout;
  private isInitialized = false;

  constructor(config?: Partial<CacheCoordinatorConfig>) {
    super();
    
    this.config = {
      enableConsistencyCheck: true,
      consistencyCheckInterval: 300000, // 5分钟
      enablePerformanceMonitoring: true,
      performanceCheckInterval: 60000, // 1分钟
      enableAutoRecovery: true,
      maxInconsistencyThreshold: 3,
      cacheInvalidationStrategy: 'immediate',
      globalTTL: 3600000, // 1小时
      ...config
    };

    this.performanceMetrics = this.initializePerformanceMetrics();
    
    console.log('🔄 缓存协调器已初始化');
  }

  /**
   * 初始化性能指标
   */
  private initializePerformanceMetrics(): CachePerformanceMetrics {
    const layerMetrics: Record<CacheLayerType, any> = {} as any;
    
    Object.values(CacheLayerType).forEach(type => {
      layerMetrics[type] = {
        requests: 0,
        hits: 0,
        misses: 0,
        hitRate: 0,
        averageResponseTime: 0,
        errorCount: 0
      };
    });

    return {
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0,
      overallHitRate: 0,
      layerMetrics,
      lastUpdated: new Date()
    };
  }

  /**
   * 注册缓存层
   */
  registerCacheLayer(layer: CacheLayer): void {
    this.layers.set(layer.type, layer);
    console.log(`📝 已注册缓存层: ${layer.name} [${layer.type}]`);
    
    // 如果是第一次注册缓存层，启动监控
    if (this.layers.size === 1 && !this.isInitialized) {
      this.startMonitoring();
    }
  }

  /**
   * 注销缓存层
   */
  unregisterCacheLayer(type: CacheLayerType): boolean {
    const removed = this.layers.delete(type);
    if (removed) {
      console.log(`🗑️ 已注销缓存层: ${type}`);
    }
    return removed;
  }

  /**
   * 启动监控
   */
  private startMonitoring(): void {
    if (this.isInitialized) return;

    // 启动一致性检查
    if (this.config.enableConsistencyCheck) {
      this.consistencyTimer = setInterval(() => {
        this.performConsistencyCheck();
      }, this.config.consistencyCheckInterval);
    }

    // 启动性能监控
    if (this.config.enablePerformanceMonitoring) {
      this.performanceTimer = setInterval(() => {
        this.updatePerformanceMetrics();
      }, this.config.performanceCheckInterval);
    }

    this.isInitialized = true;
    console.log('👁️ 缓存协调器监控已启动');
  }

  /**
   * 协调缓存获取
   */
  async coordinatedGet(key: string, preferredLayers?: CacheLayerType[]): Promise<{
    value: any;
    source: CacheLayerType | null;
    hitLayers: CacheLayerType[];
    missLayers: CacheLayerType[];
  }> {
    try {
        const startTime = Date.now();
        const hitLayers: CacheLayerType[] = [];
        const missLayers: CacheLayerType[] = [];
        let value: any = null;
        let source: CacheLayerType | null = null;

        // 确定检查顺序
        const layersToCheck = preferredLayers || Array.from(this.layers.keys());
        
        // 按优先级检查各层缓存
        for (const layerType of layersToCheck) {
          const layer = this.layers.get(layerType);
          if (!layer || !layer.enabled) continue;

          try {
            const layerValue = await layer.get(key);
            
            if (layerValue !== null && layerValue !== undefined) {
              value = layerValue;
              source = layerType;
              hitLayers.push(layerType);
              
              // 更新性能指标
              this.updateLayerMetrics(layerType, 'hit', Date.now() - startTime);
              
              // 如果在较低优先级层找到，同步到较高优先级层
              await this.syncToHigherPriorityLayers(key, value, layerType, layersToCheck);
              
              break;
            } else {
              missLayers.push(layerType);
              this.updateLayerMetrics(layerType, 'miss', Date.now() - startTime);
            }
          } catch (error) {
            console.error(`❌ 缓存层 ${layerType} 获取失败:`, error);
            this.updateLayerMetrics(layerType, 'error', Date.now() - startTime);
            missLayers.push(layerType);
          }
        }

        console.log(`🔍 协调缓存获取: ${key} | 来源: ${source} | 命中: [${hitLayers.join(',')}] | 未命中: [${missLayers.join(',')}]`);

        return { value, source, hitLayers, missLayers };
    } catch (error) {
      console.error('❌ 协调缓存获取失败:', error);
      return { value: null, source: null, hitLayers: [], missLayers: [] };
    }
  }

  /**
   * 协调缓存设置
   */
  async coordinatedSet(
    key: string,
    value: any,
    options?: {
      ttl?: number;
      targetLayers?: CacheLayerType[];
      syncStrategy?: 'all' | 'selective' | 'primary';
    }
  ): Promise<{
    success: boolean;
    successLayers: CacheLayerType[];
    failedLayers: CacheLayerType[];
  }> {
    try {
        const successLayers: CacheLayerType[] = [];
        const failedLayers: CacheLayerType[] = [];
        const ttl = options?.ttl || this.config.globalTTL;

        // 确定目标层
        const targetLayers = options?.targetLayers || Array.from(this.layers.keys());
        
        // 根据同步策略设置缓存
        const layersToSet = this.determineSyncLayers(targetLayers, options?.syncStrategy);

        // 并行设置到各层
        const setPromises = layersToSet.map(async (layerType) => {
          const layer = this.layers.get(layerType);
          if (!layer || !layer.enabled) return;

          try {
            await layer.set(key, value, ttl);
            successLayers.push(layerType);
            console.log(`💾 缓存已设置到 ${layerType}: ${key}`);
          } catch (error) {
            console.error(`❌ 缓存设置失败 ${layerType}:`, error);
            failedLayers.push(layerType);
          }
        });

        await Promise.allSettled(setPromises);

        const success = successLayers.length > 0;
        
        // 触发缓存设置事件
        this.emit('cacheSet', {
          key,
          value,
          successLayers,
          failedLayers,
          ttl
        });

        console.log(`💾 协调缓存设置: ${key} | 成功: [${successLayers.join(',')}] | 失败: [${failedLayers.join(',')}]`);

        return { success, successLayers, failedLayers };
    } catch (error) {
      console.error('❌ 协调缓存设置失败:', error);
      return { success: false, successLayers: [], failedLayers: [] };
    }
  }

  /**
   * 协调缓存删除
   */
  async coordinatedDelete(key: string, targetLayers?: CacheLayerType[]): Promise<{
    success: boolean;
    deletedLayers: CacheLayerType[];
    failedLayers: CacheLayerType[];
  }> {
    try {
        const deletedLayers: CacheLayerType[] = [];
        const failedLayers: CacheLayerType[] = [];

        const layersToDelete = targetLayers || Array.from(this.layers.keys());

        // 并行删除各层缓存
        const deletePromises = layersToDelete.map(async (layerType) => {
          const layer = this.layers.get(layerType);
          if (!layer || !layer.enabled) return;

          try {
            const deleted = await layer.delete(key);
            if (deleted) {
              deletedLayers.push(layerType);
              console.log(`🗑️ 缓存已从 ${layerType} 删除: ${key}`);
            }
          } catch (error) {
            console.error(`❌ 缓存删除失败 ${layerType}:`, error);
            failedLayers.push(layerType);
          }
        });

        await Promise.allSettled(deletePromises);

        const success = deletedLayers.length > 0;
        
        // 触发缓存删除事件
        this.emit('cacheDeleted', {
          key,
          deletedLayers,
          failedLayers
        });

        console.log(`🗑️ 协调缓存删除: ${key} | 删除: [${deletedLayers.join(',')}] | 失败: [${failedLayers.join(',')}]`);

        return { success, deletedLayers, failedLayers };
    } catch (error) {
      console.error('❌ 协调缓存删除失败:', error);
      return { success: false, deletedLayers: [], failedLayers: [] };
    }
  }

  /**
   * 同步到高优先级层
   */
  private async syncToHigherPriorityLayers(
    key: string,
    value: any,
    sourceLayer: CacheLayerType,
    allLayers: CacheLayerType[]
  ): Promise<void> {
    const sourceIndex = allLayers.indexOf(sourceLayer);
    if (sourceIndex <= 0) return; // 已经是最高优先级

    const higherPriorityLayers = allLayers.slice(0, sourceIndex);
    
    for (const layerType of higherPriorityLayers) {
      const layer = this.layers.get(layerType);
      if (layer && layer.enabled) {
        try {
          await layer.set(key, value, this.config.globalTTL);
          console.log(`🔄 缓存已同步到高优先级层 ${layerType}: ${key}`);
        } catch (error) {
          console.error(`❌ 缓存同步失败 ${layerType}:`, error);
        }
      }
    }
  }

  /**
   * 确定同步层
   */
  private determineSyncLayers(
    targetLayers: CacheLayerType[],
    strategy?: 'all' | 'selective' | 'primary'
  ): CacheLayerType[] {
    switch (strategy) {
      case 'primary':
        // 只同步到主要层（AI提供商缓存）
        return targetLayers.filter(layer => layer === CacheLayerType.AI_PROVIDER);
        
      case 'selective':
        // 选择性同步（排除API响应缓存）
        return targetLayers.filter(layer => layer !== CacheLayerType.API_RESPONSE);
        
      case 'all':
      default:
        return targetLayers;
    }
  }

  /**
   * 执行一致性检查
   */
  private async performConsistencyCheck(): Promise<ConsistencyCheckResult> {
    const startTime = Date.now();
    const healthyLayers: string[] = [];
    const inconsistentLayers: string[] = [];
    const performanceIssues: string[] = [];

    console.log('🔍 开始缓存一致性检查...');

    // 检查各层健康状态
    for (const [type, layer] of Array.from(this.layers.entries())) {
      try {
        const isHealthy = await layer.healthCheck();
        if (isHealthy) {
          healthyLayers.push(type);
        } else {
          inconsistentLayers.push(type);
        }

        // 检查性能指标
        const stats = layer.getStats();
        if (stats.hitRate < 0.3) {
          performanceIssues.push(`${type}: 命中率过低 (${(stats.hitRate * 100).toFixed(1)}%)`);
        }
        if (stats.averageResponseTime > 1000) {
          performanceIssues.push(`${type}: 响应时间过长 (${stats.averageResponseTime}ms)`);
        }
      } catch (error) {
        console.error(`❌ 缓存层健康检查失败 ${type}:`, error);
        inconsistentLayers.push(type);
      }
    }

    const result: ConsistencyCheckResult = {
      timestamp: new Date(),
      totalLayers: this.layers.size,
      healthyLayers: healthyLayers.length,
      inconsistentLayers,
      performanceIssues,
      recommendations: this.generateRecommendations(inconsistentLayers, performanceIssues),
      overallHealth: this.determineOverallHealth(inconsistentLayers.length, performanceIssues.length)
    };

    const checkTime = Date.now() - startTime;
    console.log(`✅ 缓存一致性检查完成 (${checkTime}ms): ${result.overallHealth}`);

    // 触发一致性检查事件
    this.emit('consistencyCheck', result);

    // 如果启用自动恢复且有问题，尝试恢复
    if (this.config.enableAutoRecovery && inconsistentLayers.length > 0) {
      await this.attemptAutoRecovery(inconsistentLayers);
    }

    return result;
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    let totalRequests = 0;
    let totalHits = 0;
    let totalMisses = 0;

    // 收集各层性能指标
    for (const [type, layer] of Array.from(this.layers.entries())) {
      try {
        const stats = layer.getStats();
        const layerMetric = this.performanceMetrics.layerMetrics[type];
        
        layerMetric.requests = stats.totalRequests || 0;
        layerMetric.hits = stats.hitCount || 0;
        layerMetric.misses = stats.missCount || 0;
        layerMetric.hitRate = stats.hitRate || 0;
        layerMetric.averageResponseTime = stats.averageResponseTime || 0;
        layerMetric.errorCount = stats.errorCount || 0;

        totalRequests += layerMetric.requests;
        totalHits += layerMetric.hits;
        totalMisses += layerMetric.misses;
      } catch (error) {
        console.error(`❌ 获取缓存层性能指标失败 ${type}:`, error);
      }
    }

    // 更新总体指标
    this.performanceMetrics.totalRequests = totalRequests;
    this.performanceMetrics.totalHits = totalHits;
    this.performanceMetrics.totalMisses = totalMisses;
    this.performanceMetrics.overallHitRate = totalRequests > 0 ? totalHits / totalRequests : 0;
    this.performanceMetrics.lastUpdated = new Date();

    // 触发性能更新事件
    this.emit('performanceUpdated', this.performanceMetrics);
  }

  /**
   * 更新层级指标
   */
  private updateLayerMetrics(layerType: CacheLayerType, operation: 'hit' | 'miss' | 'error', responseTime: number): void {
    const metric = this.performanceMetrics.layerMetrics[layerType];
    
    metric.requests++;
    
    switch (operation) {
      case 'hit':
        metric.hits++;
        break;
      case 'miss':
        metric.misses++;
        break;
      case 'error':
        metric.errorCount++;
        break;
    }
    
    metric.hitRate = metric.requests > 0 ? metric.hits / metric.requests : 0;
    
    // 更新平均响应时间（简单移动平均）
    metric.averageResponseTime = (metric.averageResponseTime + responseTime) / 2;
  }

  /**
   * 生成建议
   */
  private generateRecommendations(inconsistentLayers: string[], performanceIssues: string[]): string[] {
    const recommendations: string[] = [];

    if (inconsistentLayers.length > 0) {
      recommendations.push(`发现 ${inconsistentLayers.length} 个不一致的缓存层，建议检查: ${inconsistentLayers.join(', ')}`);
    }

    if (performanceIssues.length > 0) {
      recommendations.push('发现性能问题，建议优化缓存配置或增加缓存容量');
    }

    if (this.performanceMetrics.overallHitRate < 0.5) {
      recommendations.push('整体缓存命中率较低，建议检查缓存策略和TTL设置');
    }

    if (recommendations.length === 0) {
      recommendations.push('缓存系统运行正常，无需特别关注');
    }

    return recommendations;
  }

  /**
   * 确定整体健康状态
   */
  private determineOverallHealth(inconsistentCount: number, performanceIssueCount: number): 'healthy' | 'warning' | 'critical' {
    if (inconsistentCount >= this.config.maxInconsistencyThreshold) {
      return 'critical';
    } else if (inconsistentCount > 0 || performanceIssueCount > 2) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }

  /**
   * 尝试自动恢复
   */
  private async attemptAutoRecovery(inconsistentLayers: string[]): Promise<void> {
    console.log(`🔄 尝试自动恢复不一致的缓存层: ${inconsistentLayers.join(', ')}`);

    for (const layerType of inconsistentLayers) {
      const layer = this.layers.get(layerType as CacheLayerType);
      if (layer) {
        try {
          // 尝试清空并重新初始化缓存层
          await layer.clear();
          console.log(`🔄 已清空缓存层: ${layerType}`);
          
          // 触发恢复事件
          this.emit('layerRecovered', { layerType, timestamp: new Date() });
        } catch (error) {
          console.error(`❌ 自动恢复失败 ${layerType}:`, error);
          this.emit('recoveryFailed', { layerType, error, timestamp: new Date() });
        }
      }
    }
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): CachePerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 获取注册的缓存层
   */
  getRegisteredLayers(): Array<{ type: CacheLayerType; name: string; enabled: boolean }> {
    return Array.from(this.layers.values()).map(layer => ({
      type: layer.type,
      name: layer.name,
      enabled: layer.enabled
    }));
  }

  /**
   * 手动触发一致性检查
   */
  async triggerConsistencyCheck(): Promise<ConsistencyCheckResult> {
    return this.performConsistencyCheck();
  }

  /**
   * 手动触发性能指标更新
   */
  triggerPerformanceUpdate(): void {
    this.updatePerformanceMetrics();
  }

  /**
   * 销毁缓存协调器
   */
  destroy(): void {
    // 停止定时器
    if (this.consistencyTimer) {
      clearInterval(this.consistencyTimer);
    }
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
    }

    // 清理缓存层
    this.layers.clear();

    // 清理事件监听器
    this.removeAllListeners();

    this.isInitialized = false;
    console.log('🔄 缓存协调器已销毁');
  }
}

// 导出单例实例
export const cacheCoordinator = new CacheCoordinator();
