/**
 * Performance Decorators - 性能监控装饰器
 * 
 * 提供便捷的性能监控装饰器和工具函数
 */

import { performanceMonitor, MetricType } from './performance-monitor';

/**
 * 性能监控装饰器选项
 */
interface MonitorOptions {
  component?: string;
  metricType?: MetricType;
  enableMemoryTracking?: boolean;
  enableErrorTracking?: boolean;
  threshold?: number;
  metadata?: Record<string, any>;
}

/**
 * 方法性能监控装饰器
 */
export function MonitorPerformance(options: MonitorOptions = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const component = options.component || target.constructor.name;
    const metricType = options.metricType || MetricType.RESPONSE_TIME;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const startMemory = options.enableMemoryTracking ? process.memoryUsage() : null;
      
      try {
        const result = await originalMethod.apply(this, args);
        
        // 记录成功的性能指标
        const duration = Date.now() - startTime;
        performanceMonitor.recordMetric(
          metricType,
          duration,
          component,
          propertyKey,
          {
            success: true,
            argumentsCount: args.length,
            ...options.metadata
          }
        );

        // 记录内存使用变化
        if (options.enableMemoryTracking && startMemory) {
          const endMemory = process.memoryUsage();
          const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
          performanceMonitor.recordMetric(
            MetricType.MEMORY_USAGE,
            memoryDelta / 1024 / 1024, // 转换为MB
            component,
            `${propertyKey}_memory`,
            { operation: 'memory_delta' }
          );
        }

        // 检查阈值警告
        if (options.threshold && duration > options.threshold) {
          console.warn(`⚠️ 性能警告: ${component}.${propertyKey} 执行时间 ${duration}ms 超过阈值 ${options.threshold}ms`);
        }

        return result;

      } catch (error) {
        // 记录错误的性能指标
        const duration = Date.now() - startTime;
        performanceMonitor.recordMetric(
          metricType,
          duration,
          component,
          propertyKey,
          {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            argumentsCount: args.length,
            ...options.metadata
          }
        );

        // 记录错误率
        if (options.enableErrorTracking) {
          performanceMonitor.recordMetric(
            MetricType.ERROR_RATE,
            1,
            component,
            `${propertyKey}_error`,
            { errorType: error instanceof Error ? error.constructor.name : 'Unknown' }
          );
        }

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * API端点性能监控装饰器
 */
export function MonitorAPI(endpoint: string, options: Omit<MonitorOptions, 'component' | 'metricType'> = {}) {
  return MonitorPerformance({
    component: 'API',
    metricType: MetricType.RESPONSE_TIME,
    enableErrorTracking: true,
    ...options,
    metadata: {
      endpoint,
      ...options.metadata
    }
  });
}

/**
 * AI处理性能监控装饰器
 */
export function MonitorAI(provider: string, options: Omit<MonitorOptions, 'component' | 'metricType'> = {}) {
  return MonitorPerformance({
    component: 'AI',
    metricType: MetricType.AI_PROCESSING_TIME,
    enableMemoryTracking: true,
    enableErrorTracking: true,
    threshold: 10000, // 10秒阈值
    ...options,
    metadata: {
      provider,
      ...options.metadata
    }
  });
}

/**
 * 缓存操作性能监控装饰器
 */
export function MonitorCache(cacheType: string, options: Omit<MonitorOptions, 'component' | 'metricType'> = {}) {
  return MonitorPerformance({
    component: 'Cache',
    metricType: MetricType.RESPONSE_TIME,
    enableErrorTracking: true,
    threshold: 100, // 100ms阈值
    ...options,
    metadata: {
      cacheType,
      ...options.metadata
    }
  });
}

/**
 * 数据库查询性能监控装饰器
 */
export function MonitorDatabase(queryType: string, options: Omit<MonitorOptions, 'component' | 'metricType'> = {}) {
  return MonitorPerformance({
    component: 'Database',
    metricType: MetricType.DATABASE_QUERY_TIME,
    enableErrorTracking: true,
    threshold: 1000, // 1秒阈值
    ...options,
    metadata: {
      queryType,
      ...options.metadata
    }
  });
}

/**
 * 性能监控工具类
 */
export class PerformanceUtils {
  /**
   * 测量函数执行时间
   */
  static async measureTime<T>(
    fn: () => Promise<T> | T,
    component: string,
    operation: string,
    metricType: MetricType = MetricType.RESPONSE_TIME
  ): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      performanceMonitor.recordMetric(
        metricType,
        duration,
        component,
        operation,
        { success: true }
      );
      
      return { result, duration };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      performanceMonitor.recordMetric(
        metricType,
        duration,
        component,
        operation,
        { 
          success: false,
          error: error instanceof Error ? error.message : String(error)
        }
      );
      
      throw error;
    }
  }

  /**
   * 测量内存使用
   */
  static async measureMemory<T>(
    fn: () => Promise<T> | T,
    component: string,
    operation: string
  ): Promise<{ result: T; memoryDelta: number }> {
    const startMemory = process.memoryUsage();
    
    const result = await fn();
    
    const endMemory = process.memoryUsage();
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
    
    performanceMonitor.recordMetric(
      MetricType.MEMORY_USAGE,
      memoryDelta / 1024 / 1024, // 转换为MB
      component,
      operation,
      { 
        startHeapUsed: startMemory.heapUsed,
        endHeapUsed: endMemory.heapUsed
      }
    );
    
    return { result, memoryDelta };
  }

  /**
   * 批量性能测试
   */
  static async batchPerformanceTest<T>(
    fn: () => Promise<T> | T,
    iterations: number,
    component: string,
    operation: string
  ): Promise<{
    results: T[];
    averageTime: number;
    minTime: number;
    maxTime: number;
    totalTime: number;
  }> {
    const results: T[] = [];
    const times: number[] = [];
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      const { result, duration } = await this.measureTime(fn, component, `${operation}_batch_${i}`);
      results.push(result);
      times.push(duration);
    }
    
    const totalTime = Date.now() - startTime;
    const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    // 记录批量测试结果
    performanceMonitor.recordMetric(
      MetricType.RESPONSE_TIME,
      averageTime,
      component,
      `${operation}_batch_average`,
      {
        iterations,
        minTime,
        maxTime,
        totalTime
      }
    );
    
    return {
      results,
      averageTime,
      minTime,
      maxTime,
      totalTime
    };
  }

  /**
   * 并发性能测试
   */
  static async concurrentPerformanceTest<T>(
    fn: () => Promise<T> | T,
    concurrency: number,
    component: string,
    operation: string
  ): Promise<{
    results: T[];
    averageTime: number;
    totalTime: number;
    successCount: number;
    errorCount: number;
  }> {
    const startTime = Date.now();
    const promises: Promise<{ result: T; duration: number; success: boolean }>[] = [];
    
    for (let i = 0; i < concurrency; i++) {
      promises.push(
        this.measureTime(fn, component, `${operation}_concurrent_${i}`)
          .then(({ result, duration }) => ({ result, duration, success: true }))
          .catch(error => ({ result: null as any, duration: 0, success: false, error }))
      );
    }
    
    const outcomes = await Promise.all(promises);
    const totalTime = Date.now() - startTime;
    
    const successfulOutcomes = outcomes.filter(o => o.success);
    const results = successfulOutcomes.map(o => o.result);
    const times = successfulOutcomes.map(o => o.duration);
    const averageTime = times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0;
    
    // 记录并发测试结果
    performanceMonitor.recordMetric(
      MetricType.THROUGHPUT,
      (successfulOutcomes.length / totalTime) * 1000, // 每秒成功请求数
      component,
      `${operation}_concurrent`,
      {
        concurrency,
        successCount: successfulOutcomes.length,
        errorCount: outcomes.length - successfulOutcomes.length,
        totalTime,
        averageTime
      }
    );
    
    return {
      results,
      averageTime,
      totalTime,
      successCount: successfulOutcomes.length,
      errorCount: outcomes.length - successfulOutcomes.length
    };
  }

  /**
   * 记录缓存命中率
   */
  static recordCacheHit(component: string, cacheType: string, hit: boolean): void {
    performanceMonitor.recordMetric(
      MetricType.CACHE_HIT_RATE,
      hit ? 100 : 0,
      component,
      `${cacheType}_cache`,
      { hit, cacheType }
    );
  }

  /**
   * 记录吞吐量
   */
  static recordThroughput(component: string, operation: string, requestsPerSecond: number): void {
    performanceMonitor.recordMetric(
      MetricType.THROUGHPUT,
      requestsPerSecond,
      component,
      operation,
      { unit: 'req/s' }
    );
  }

  /**
   * 记录并发用户数
   */
  static recordConcurrentUsers(component: string, userCount: number): void {
    performanceMonitor.recordMetric(
      MetricType.CONCURRENT_USERS,
      userCount,
      component,
      'concurrent_users',
      { timestamp: new Date().toISOString() }
    );
  }

  /**
   * 记录错误率
   */
  static recordErrorRate(component: string, operation: string, errorRate: number): void {
    performanceMonitor.recordMetric(
      MetricType.ERROR_RATE,
      errorRate,
      component,
      operation,
      { unit: '%' }
    );
  }

  /**
   * 创建性能基准测试
   */
  static async createBenchmark<T>(
    name: string,
    fn: () => Promise<T> | T,
    options: {
      iterations?: number;
      warmupIterations?: number;
      concurrency?: number;
      component?: string;
    } = {}
  ): Promise<{
    name: string;
    iterations: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    throughput: number;
    memoryUsage: number;
  }> {
    const {
      iterations = 100,
      warmupIterations = 10,
      concurrency = 1,
      component = 'Benchmark'
    } = options;

    console.log(`🏃 开始性能基准测试: ${name}`);

    // 预热
    for (let i = 0; i < warmupIterations; i++) {
      await fn();
    }

    // 执行基准测试
    let results;
    if (concurrency > 1) {
      results = await this.concurrentPerformanceTest(fn, concurrency, component, name);
    } else {
      results = await this.batchPerformanceTest(fn, iterations, component, name);
    }

    // 测量内存使用
    const { memoryDelta } = await this.measureMemory(fn, component, `${name}_memory`);

    const throughput = concurrency > 1 
      ? (results.successCount || results.results.length) / (results.totalTime / 1000)
      : results.results.length / (results.totalTime / 1000);

    const benchmark = {
      name,
      iterations: results.results.length,
      averageTime: results.averageTime,
      minTime: 'minTime' in results ? results.minTime : 0,
      maxTime: 'maxTime' in results ? results.maxTime : 0,
      throughput,
      memoryUsage: memoryDelta / 1024 / 1024 // MB
    };

    console.log(`✅ 基准测试完成: ${name}`, benchmark);
    return benchmark;
  }
}

// 导出便捷函数
export const measureTime = PerformanceUtils.measureTime;
export const measureMemory = PerformanceUtils.measureMemory;
export const recordCacheHit = PerformanceUtils.recordCacheHit;
export const recordThroughput = PerformanceUtils.recordThroughput;
export const createBenchmark = PerformanceUtils.createBenchmark;
