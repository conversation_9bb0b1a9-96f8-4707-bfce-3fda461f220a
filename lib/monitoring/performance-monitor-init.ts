/**
 * Performance Monitor Initialization - 性能监控初始化
 * 
 * 自动启动性能监控并设置事件监听
 */

import { performanceMonitor } from './performance-monitor';
import { configHotReloadService } from '@/lib/config/config-hot-reload';

/**
 * 初始化性能监控
 */
export function initializePerformanceMonitoring(): void {
  console.log('📊 初始化性能监控系统...');

  try {
    // 启动性能监控
    performanceMonitor.startMonitoring();

    // 设置性能监控事件监听
    setupPerformanceEventListeners();

    // 设置配置变更监听
    setupConfigListeners();

    console.log('✅ 性能监控系统初始化完成');

  } catch (error) {
    console.error('❌ 性能监控系统初始化失败:', error);
    throw error;
  }
}

/**
 * 设置性能监控事件监听器
 */
function setupPerformanceEventListeners(): void {
  // 监听指标记录事件
  performanceMonitor.on('metricRecorded', (metric) => {
    // 可以在这里添加自定义的指标处理逻辑
    if (metric.value > 5000 && metric.type === 'RESPONSE_TIME') {
      console.warn(`⚠️ 响应时间警告: ${metric.component}.${metric.operation} = ${metric.value}ms`);
    }
  });

  // 监听警报创建事件
  performanceMonitor.on('alertCreated', (alert) => {
    console.log(`🚨 性能警报: [${alert.level.toUpperCase()}] ${alert.message}`);
    
    // 可以在这里集成外部告警系统
    if (alert.level === 'critical') {
      // 发送紧急通知
      console.error(`🚨 严重性能警报: ${alert.message}`);
    }
  });

  // 监听警报解决事件
  performanceMonitor.on('alertResolved', (alert) => {
    console.log(`✅ 性能警报已解决: ${alert.id}`);
  });

  // 监听报告生成事件
  performanceMonitor.on('reportGenerated', (report) => {
    console.log(`📋 性能报告已生成: ${report.id} | 健康状态: ${report.summary.overallHealth}`);
  });

  // 监听监控启动事件
  performanceMonitor.on('monitoringStarted', () => {
    console.log('🚀 性能监控已启动');
  });

  // 监听监控停止事件
  performanceMonitor.on('monitoringStopped', () => {
    console.log('⏹️ 性能监控已停止');
  });

  // 监听内存优化事件
  performanceMonitor.on('memoryOptimized', (event) => {
    console.log(`🧹 内存优化已执行: ${event.action}`);
  });

  // 监听缓存优化事件
  performanceMonitor.on('cacheOptimized', (event) => {
    console.log(`🔄 缓存优化已执行: ${event.action}`);
  });

  // 监听响应时间优化事件
  performanceMonitor.on('responseTimeOptimized', (event) => {
    console.log(`⚡ 响应时间优化已执行: ${event.action}`);
  });
}

/**
 * 设置配置变更监听器
 */
function setupConfigListeners(): void {
  // 监听性能监控配置变更
  configHotReloadService.on('performanceConfigChanged', ({ key, newValue }) => {
    console.log(`📊 性能监控配置变更: ${key} = ${JSON.stringify(newValue)}`);
    
    // 根据配置变更调整监控行为
    if (key === 'performance.monitoring.enabled') {
      if (newValue) {
        performanceMonitor.startMonitoring();
      } else {
        performanceMonitor.stopMonitoring();
      }
    }
  });
}

/**
 * 获取性能监控状态
 */
export function getPerformanceMonitoringStatus(): {
  initialized: boolean;
  monitoring: boolean;
  metricsCount: number;
  activeAlertsCount: number;
} {
  const status = performanceMonitor.getMonitoringStatus();
  
  return {
    initialized: true,
    monitoring: status.isMonitoring,
    metricsCount: status.metricsCount,
    activeAlertsCount: status.activeAlertsCount
  };
}

/**
 * 手动触发性能监控操作
 */
export async function triggerPerformanceMonitoringOperations(): Promise<{
  systemMetrics: any;
  performanceReport: any;
}> {
  console.log('📊 手动触发性能监控操作...');
  
  // 收集系统指标
  const memoryUsage = process.memoryUsage();
  const systemMetrics = {
    memoryUsage: {
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      usagePercent: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100 * 100) / 100
    },
    uptime: Math.round(process.uptime()),
    processId: process.pid
  };

  // 生成性能报告
  const stats = performanceMonitor.getPerformanceStats();
  const alerts = performanceMonitor.getActiveAlerts();
  
  const performanceReport = {
    timestamp: new Date().toISOString(),
    totalMetrics: stats.size,
    activeAlerts: alerts.length,
    criticalAlerts: alerts.filter(a => a.level === 'critical').length,
    warningAlerts: alerts.filter(a => a.level === 'warning').length
  };
  
  return {
    systemMetrics,
    performanceReport
  };
}

/**
 * 测试性能监控功能
 */
export async function testPerformanceMonitoring(): Promise<{
  metricTest: any;
  alertTest: any;
  optimizationTest: any;
}> {
  console.log('🧪 测试性能监控功能...');
  
  try {
    // 测试指标记录
    const testMetricValue = Math.random() * 1000 + 500;
    performanceMonitor.recordMetric(
      'RESPONSE_TIME' as any,
      testMetricValue,
      'TestComponent',
      'test_operation',
      { test: true }
    );
    
    const metricTest = {
      success: true,
      metricValue: testMetricValue,
      component: 'TestComponent',
      operation: 'test_operation'
    };

    // 测试警报创建（通过记录高值指标）
    performanceMonitor.recordMetric(
      'RESPONSE_TIME' as any,
      6000, // 超过阈值的值
      'TestComponent',
      'alert_test',
      { test: true, alertTest: true }
    );
    
    const alertTest = {
      success: true,
      triggerValue: 6000,
      expectedAlert: 'critical'
    };

    // 测试优化功能
    const optimizationTest = {
      success: true,
      memoryOptimization: global.gc ? 'available' : 'not_available',
      timestamp: new Date().toISOString()
    };

    return {
      metricTest,
      alertTest,
      optimizationTest
    };
    
  } catch (error) {
    console.error('❌ 性能监控测试失败:', error);
    throw error;
  }
}

/**
 * 销毁性能监控
 */
export function destroyPerformanceMonitoring(): void {
  console.log('📊 销毁性能监控系统...');
  
  // 移除配置监听器
  configHotReloadService.removeAllListeners('performanceConfigChanged');
  
  // 销毁性能监控器
  performanceMonitor.destroy();
  
  console.log('✅ 性能监控系统已销毁');
}

// 自动初始化
initializePerformanceMonitoring();
