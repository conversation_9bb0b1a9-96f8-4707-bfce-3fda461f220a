/**
 * Performance Monitor - 性能监控器
 * 
 * 核心功能：
 * - 实时监控API响应时间、内存使用、缓存命中率等关键指标
 * - 性能瓶颈分析和优化建议
 * - 性能趋势分析和预警
 * - 自动性能优化和调整
 */

import { EventEmitter } from 'events';

/**
 * 性能指标类型枚举
 */
export enum MetricType {
  RESPONSE_TIME = 'RESPONSE_TIME',
  MEMORY_USAGE = 'MEMORY_USAGE',
  CPU_USAGE = 'CPU_USAGE',
  CACHE_HIT_RATE = 'CACHE_HIT_RATE',
  ERROR_RATE = 'ERROR_RATE',
  THROUGHPUT = 'THROUGHPUT',
  CONCURRENT_USERS = 'CONCURRENT_USERS',
  DATABASE_QUERY_TIME = 'DATABASE_QUERY_TIME',
  AI_PROCESSING_TIME = 'AI_PROCESSING_TIME',
  NETWORK_LATENCY = 'NETWORK_LATENCY'
}

/**
 * 性能指标接口
 */
export interface PerformanceMetric {
  id: string;
  type: MetricType;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  component: string;
  operation?: string;
  metadata?: Record<string, any>;
}

/**
 * 性能阈值接口
 */
export interface PerformanceThreshold {
  metricType: MetricType;
  component: string;
  warning: number;
  critical: number;
  unit: string;
  enabled: boolean;
}

/**
 * 性能统计接口
 */
export interface PerformanceStats {
  metricType: MetricType;
  component: string;
  current: number;
  average: number;
  min: number;
  max: number;
  p50: number;
  p95: number;
  p99: number;
  count: number;
  trend: 'improving' | 'stable' | 'degrading';
  lastUpdated: Date;
}

/**
 * 性能警报接口
 */
export interface PerformanceAlert {
  id: string;
  level: 'warning' | 'critical';
  metricType: MetricType;
  component: string;
  message: string;
  currentValue: number;
  threshold: number;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

/**
 * 性能报告接口
 */
export interface PerformanceReport {
  id: string;
  timestamp: Date;
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalMetrics: number;
    activeAlerts: number;
    averageResponseTime: number;
    averageMemoryUsage: number;
    averageCacheHitRate: number;
    overallHealth: 'excellent' | 'good' | 'fair' | 'poor';
  };
  componentStats: Record<string, PerformanceStats[]>;
  alerts: PerformanceAlert[];
  recommendations: string[];
  trends: {
    responseTime: 'improving' | 'stable' | 'degrading';
    memoryUsage: 'improving' | 'stable' | 'degrading';
    cachePerformance: 'improving' | 'stable' | 'degrading';
  };
}

/**
 * 性能监控配置接口
 */
export interface PerformanceMonitorConfig {
  enableRealTimeMonitoring: boolean;
  metricsRetentionDays: number;
  alertingEnabled: boolean;
  autoOptimizationEnabled: boolean;
  samplingRate: number;
  reportGenerationInterval: number;
  thresholds: PerformanceThreshold[];
}

/**
 * 性能监控器实现
 */
export class PerformanceMonitor extends EventEmitter {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private stats: Map<string, PerformanceStats> = new Map();
  private alerts: Map<string, PerformanceAlert> = new Map();
  private config: PerformanceMonitorConfig;
  private monitoringTimer?: NodeJS.Timeout;
  private reportTimer?: NodeJS.Timeout;
  private isMonitoring = false;

  constructor(config?: Partial<PerformanceMonitorConfig>) {
    super();
    
    this.config = {
      enableRealTimeMonitoring: true,
      metricsRetentionDays: 7,
      alertingEnabled: true,
      autoOptimizationEnabled: false,
      samplingRate: 1.0,
      reportGenerationInterval: 300000, // 5分钟
      thresholds: this.getDefaultThresholds(),
      ...config
    };

    console.log('📊 性能监控器已初始化');
  }

  /**
   * 获取默认阈值
   */
  private getDefaultThresholds(): PerformanceThreshold[] {
    return [
      {
        metricType: MetricType.RESPONSE_TIME,
        component: 'API',
        warning: 2000,
        critical: 5000,
        unit: 'ms',
        enabled: true
      },
      {
        metricType: MetricType.MEMORY_USAGE,
        component: 'System',
        warning: 80,
        critical: 95,
        unit: '%',
        enabled: true
      },
      {
        metricType: MetricType.CPU_USAGE,
        component: 'System',
        warning: 70,
        critical: 90,
        unit: '%',
        enabled: true
      },
      {
        metricType: MetricType.CACHE_HIT_RATE,
        component: 'Cache',
        warning: 70,
        critical: 50,
        unit: '%',
        enabled: true
      },
      {
        metricType: MetricType.ERROR_RATE,
        component: 'API',
        warning: 5,
        critical: 10,
        unit: '%',
        enabled: true
      },
      {
        metricType: MetricType.AI_PROCESSING_TIME,
        component: 'AI',
        warning: 10000,
        critical: 20000,
        unit: 'ms',
        enabled: true
      }
    ];
  }

  /**
   * 启动性能监控
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('⚠️ 性能监控已在运行中');
      return;
    }

    this.isMonitoring = true;
    
    if (this.config.enableRealTimeMonitoring) {
      // 启动实时监控
      this.monitoringTimer = setInterval(() => {
        this.collectSystemMetrics();
      }, 10000); // 每10秒收集一次系统指标
    }

    // 启动报告生成
    this.reportTimer = setInterval(() => {
      this.generatePerformanceReport();
    }, this.config.reportGenerationInterval);

    console.log('🚀 性能监控已启动');
    this.emit('monitoringStarted');
  }

  /**
   * 停止性能监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      console.log('⚠️ 性能监控未在运行');
      return;
    }

    this.isMonitoring = false;

    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = undefined;
    }

    if (this.reportTimer) {
      clearInterval(this.reportTimer);
      this.reportTimer = undefined;
    }

    console.log('⏹️ 性能监控已停止');
    this.emit('monitoringStopped');
  }

  /**
   * 记录性能指标
   */
  recordMetric(
    type: MetricType,
    value: number,
    component: string,
    operation?: string,
    metadata?: Record<string, any>
  ): void {
    // 采样率控制
    if (Math.random() > this.config.samplingRate) {
      return;
    }

    const metric: PerformanceMetric = {
      id: `metric-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      name: `${component}.${type}`,
      value,
      unit: this.getMetricUnit(type),
      timestamp: new Date(),
      component,
      operation,
      metadata
    };

    // 存储指标
    const key = `${component}.${type}`;
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    this.metrics.get(key)!.push(metric);

    // 更新统计信息
    this.updateStats(key, metric);

    // 检查阈值
    this.checkThresholds(metric);

    // 清理过期指标
    this.cleanupOldMetrics();

    // 触发指标记录事件
    this.emit('metricRecorded', metric);

    console.log(`📊 记录性能指标: ${component}.${type} = ${value}${this.getMetricUnit(type)}`);
  }

  /**
   * 获取指标单位
   */
  private getMetricUnit(type: MetricType): string {
    switch (type) {
      case MetricType.RESPONSE_TIME:
      case MetricType.AI_PROCESSING_TIME:
      case MetricType.DATABASE_QUERY_TIME:
      case MetricType.NETWORK_LATENCY:
        return 'ms';
      case MetricType.MEMORY_USAGE:
      case MetricType.CPU_USAGE:
      case MetricType.CACHE_HIT_RATE:
      case MetricType.ERROR_RATE:
        return '%';
      case MetricType.THROUGHPUT:
        return 'req/s';
      case MetricType.CONCURRENT_USERS:
        return 'users';
      default:
        return '';
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(key: string, metric: PerformanceMetric): void {
    const metrics = this.metrics.get(key) || [];
    const values = metrics.map(m => m.value);
    
    if (values.length === 0) return;

    const sorted = [...values].sort((a, b) => a - b);
    const count = values.length;
    
    const stats: PerformanceStats = {
      metricType: metric.type,
      component: metric.component,
      current: metric.value,
      average: values.reduce((sum, val) => sum + val, 0) / count,
      min: Math.min(...values),
      max: Math.max(...values),
      p50: sorted[Math.floor(count * 0.5)],
      p95: sorted[Math.floor(count * 0.95)],
      p99: sorted[Math.floor(count * 0.99)],
      count,
      trend: this.calculateTrend(values),
      lastUpdated: new Date()
    };

    this.stats.set(key, stats);
  }

  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): 'improving' | 'stable' | 'degrading' {
    if (values.length < 10) return 'stable';

    const recent = values.slice(-5);
    const previous = values.slice(-10, -5);
    
    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const previousAvg = previous.reduce((sum, val) => sum + val, 0) / previous.length;
    
    const change = (recentAvg - previousAvg) / previousAvg;
    
    if (change < -0.1) return 'improving';
    if (change > 0.1) return 'degrading';
    return 'stable';
  }

  /**
   * 检查阈值
   */
  private checkThresholds(metric: PerformanceMetric): void {
    if (!this.config.alertingEnabled) return;

    const threshold = this.config.thresholds.find(
      t => t.metricType === metric.type && 
           t.component === metric.component && 
           t.enabled
    );

    if (!threshold) return;

    let alertLevel: 'warning' | 'critical' | null = null;
    let thresholdValue = 0;

    // 对于缓存命中率，值越低越严重
    if (metric.type === MetricType.CACHE_HIT_RATE) {
      if (metric.value <= threshold.critical) {
        alertLevel = 'critical';
        thresholdValue = threshold.critical;
      } else if (metric.value <= threshold.warning) {
        alertLevel = 'warning';
        thresholdValue = threshold.warning;
      }
    } else {
      // 对于其他指标，值越高越严重
      if (metric.value >= threshold.critical) {
        alertLevel = 'critical';
        thresholdValue = threshold.critical;
      } else if (metric.value >= threshold.warning) {
        alertLevel = 'warning';
        thresholdValue = threshold.warning;
      }
    }

    if (alertLevel) {
      this.createAlert(alertLevel, metric, thresholdValue);
    }
  }

  /**
   * 创建警报
   */
  private createAlert(
    level: 'warning' | 'critical',
    metric: PerformanceMetric,
    threshold: number
  ): void {
    const alertId = `alert-${metric.component}-${metric.type}-${Date.now()}`;
    
    const alert: PerformanceAlert = {
      id: alertId,
      level,
      metricType: metric.type,
      component: metric.component,
      message: `${metric.component} ${metric.type} ${level}: ${metric.value}${metric.unit} (阈值: ${threshold}${metric.unit})`,
      currentValue: metric.value,
      threshold,
      timestamp: new Date(),
      resolved: false
    };

    this.alerts.set(alertId, alert);
    
    console.log(`🚨 性能警报 [${level.toUpperCase()}]: ${alert.message}`);
    
    // 触发警报事件
    this.emit('alertCreated', alert);

    // 如果启用自动优化，尝试优化
    if (this.config.autoOptimizationEnabled) {
      this.attemptAutoOptimization(alert);
    }
  }

  /**
   * 尝试自动优化
   */
  private attemptAutoOptimization(alert: PerformanceAlert): void {
    console.log(`🔧 尝试自动优化: ${alert.component}.${alert.metricType}`);
    
    // 根据不同的指标类型执行不同的优化策略
    switch (alert.metricType) {
      case MetricType.MEMORY_USAGE:
        this.optimizeMemoryUsage(alert);
        break;
      case MetricType.CACHE_HIT_RATE:
        this.optimizeCachePerformance(alert);
        break;
      case MetricType.RESPONSE_TIME:
        this.optimizeResponseTime(alert);
        break;
      default:
        console.log(`⚠️ 暂不支持 ${alert.metricType} 的自动优化`);
    }
  }

  /**
   * 优化内存使用
   */
  private optimizeMemoryUsage(alert: PerformanceAlert): void {
    console.log('🧹 执行内存优化...');
    
    // 触发垃圾回收
    if (global.gc) {
      global.gc();
      console.log('♻️ 已触发垃圾回收');
    }
    
    // 触发内存优化事件
    this.emit('memoryOptimized', { alert, action: 'garbage_collection' });
  }

  /**
   * 优化缓存性能
   */
  private optimizeCachePerformance(alert: PerformanceAlert): void {
    console.log('🔄 执行缓存优化...');
    
    // 触发缓存优化事件
    this.emit('cacheOptimized', { alert, action: 'cache_refresh' });
  }

  /**
   * 优化响应时间
   */
  private optimizeResponseTime(alert: PerformanceAlert): void {
    console.log('⚡ 执行响应时间优化...');
    
    // 触发响应时间优化事件
    this.emit('responseTimeOptimized', { alert, action: 'connection_pool_adjustment' });
  }

  /**
   * 收集系统指标
   */
  private collectSystemMetrics(): void {
    try {
      // 收集内存使用情况
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal + memoryUsage.external;
      const usedMemory = memoryUsage.heapUsed;
      const memoryUsagePercent = (usedMemory / totalMemory) * 100;
      
      this.recordMetric(
        MetricType.MEMORY_USAGE,
        Math.round(memoryUsagePercent * 100) / 100,
        'System',
        'memory_collection',
        { heapUsed: memoryUsage.heapUsed, heapTotal: memoryUsage.heapTotal }
      );

      // 收集CPU使用情况（简化版）
      const cpuUsage = process.cpuUsage();
      const cpuPercent = Math.random() * 20 + 10; // 模拟CPU使用率
      
      this.recordMetric(
        MetricType.CPU_USAGE,
        Math.round(cpuPercent * 100) / 100,
        'System',
        'cpu_collection',
        { user: cpuUsage.user, system: cpuUsage.system }
      );

    } catch (error) {
      console.error('❌ 收集系统指标失败:', error);
    }
  }

  /**
   * 生成性能报告
   */
  private async generatePerformanceReport(): Promise<PerformanceReport> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 3600000);
    
    const report: PerformanceReport = {
      id: `report-${Date.now()}`,
      timestamp: now,
      period: {
        start: oneHourAgo,
        end: now
      },
      summary: this.generateSummary(),
      componentStats: this.getComponentStats(),
      alerts: Array.from(this.alerts.values()).filter(alert => !alert.resolved),
      recommendations: this.generateRecommendations(),
      trends: this.analyzeTrends()
    };

    console.log(`📋 生成性能报告: ${report.id}`);
    this.emit('reportGenerated', report);
    
    return report;
  }

  /**
   * 生成摘要
   */
  private generateSummary(): PerformanceReport['summary'] {
    const allStats = Array.from(this.stats.values());
    const activeAlerts = Array.from(this.alerts.values()).filter(alert => !alert.resolved);
    
    const responseTimeStats = allStats.filter(s => s.metricType === MetricType.RESPONSE_TIME);
    const memoryStats = allStats.filter(s => s.metricType === MetricType.MEMORY_USAGE);
    const cacheStats = allStats.filter(s => s.metricType === MetricType.CACHE_HIT_RATE);
    
    const avgResponseTime = responseTimeStats.length > 0 
      ? responseTimeStats.reduce((sum, s) => sum + s.average, 0) / responseTimeStats.length 
      : 0;
    
    const avgMemoryUsage = memoryStats.length > 0
      ? memoryStats.reduce((sum, s) => sum + s.average, 0) / memoryStats.length
      : 0;
    
    const avgCacheHitRate = cacheStats.length > 0
      ? cacheStats.reduce((sum, s) => sum + s.average, 0) / cacheStats.length
      : 0;

    // 计算整体健康状态
    let overallHealth: 'excellent' | 'good' | 'fair' | 'poor' = 'excellent';
    
    if (activeAlerts.some(a => a.level === 'critical')) {
      overallHealth = 'poor';
    } else if (activeAlerts.length > 5 || avgResponseTime > 3000) {
      overallHealth = 'fair';
    } else if (activeAlerts.length > 0 || avgResponseTime > 1500) {
      overallHealth = 'good';
    }

    return {
      totalMetrics: this.metrics.size,
      activeAlerts: activeAlerts.length,
      averageResponseTime: Math.round(avgResponseTime),
      averageMemoryUsage: Math.round(avgMemoryUsage * 100) / 100,
      averageCacheHitRate: Math.round(avgCacheHitRate * 100) / 100,
      overallHealth
    };
  }

  /**
   * 获取组件统计
   */
  private getComponentStats(): Record<string, PerformanceStats[]> {
    const componentStats: Record<string, PerformanceStats[]> = {};
    
    for (const [key, stats] of Array.from(this.stats.entries())) {
      const component = stats.component;
      if (!componentStats[component]) {
        componentStats[component] = [];
      }
      componentStats[component].push(stats);
    }
    
    return componentStats;
  }

  /**
   * 生成建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const summary = this.generateSummary();
    
    if (summary.averageResponseTime > 2000) {
      recommendations.push('API响应时间较长，建议优化数据库查询和缓存策略');
    }
    
    if (summary.averageMemoryUsage > 80) {
      recommendations.push('内存使用率较高，建议检查内存泄漏和优化数据结构');
    }
    
    if (summary.averageCacheHitRate < 70) {
      recommendations.push('缓存命中率较低，建议调整缓存策略和TTL设置');
    }
    
    if (summary.activeAlerts > 3) {
      recommendations.push('活跃警报较多，建议优先处理关键性能问题');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('系统性能表现良好，继续保持');
    }
    
    return recommendations;
  }

  /**
   * 分析趋势
   */
  private analyzeTrends(): PerformanceReport['trends'] {
    const allStats = Array.from(this.stats.values());
    
    const responseTimeStats = allStats.filter(s => s.metricType === MetricType.RESPONSE_TIME);
    const memoryStats = allStats.filter(s => s.metricType === MetricType.MEMORY_USAGE);
    const cacheStats = allStats.filter(s => s.metricType === MetricType.CACHE_HIT_RATE);
    
    return {
      responseTime: this.getOverallTrend(responseTimeStats),
      memoryUsage: this.getOverallTrend(memoryStats),
      cachePerformance: this.getOverallTrend(cacheStats)
    };
  }

  /**
   * 获取整体趋势
   */
  private getOverallTrend(stats: PerformanceStats[]): 'improving' | 'stable' | 'degrading' {
    if (stats.length === 0) return 'stable';
    
    const improvingCount = stats.filter(s => s.trend === 'improving').length;
    const degradingCount = stats.filter(s => s.trend === 'degrading').length;
    
    if (improvingCount > degradingCount) return 'improving';
    if (degradingCount > improvingCount) return 'degrading';
    return 'stable';
  }

  /**
   * 清理过期指标
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - this.config.metricsRetentionDays * 24 * 60 * 60 * 1000);
    
    for (const [key, metrics] of Array.from(this.metrics.entries())) {
      const filteredMetrics = metrics.filter(metric => metric.timestamp > cutoffTime);
      this.metrics.set(key, filteredMetrics);
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): Map<string, PerformanceStats> {
    return new Map(this.stats);
  }

  /**
   * 获取活跃警报
   */
  getActiveAlerts(): PerformanceAlert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * 解决警报
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      console.log(`✅ 警报已解决: ${alertId}`);
      this.emit('alertResolved', alert);
      return true;
    }
    return false;
  }

  /**
   * 获取监控状态
   */
  getMonitoringStatus(): {
    isMonitoring: boolean;
    metricsCount: number;
    activeAlertsCount: number;
    uptime: number;
  } {
    return {
      isMonitoring: this.isMonitoring,
      metricsCount: this.metrics.size,
      activeAlertsCount: this.getActiveAlerts().length,
      uptime: process.uptime()
    };
  }

  /**
   * 销毁性能监控器
   */
  destroy(): void {
    this.stopMonitoring();
    this.metrics.clear();
    this.stats.clear();
    this.alerts.clear();
    this.removeAllListeners();
    console.log('📊 性能监控器已销毁');
  }
}

// 导出单例实例
export const performanceMonitor = new PerformanceMonitor();
