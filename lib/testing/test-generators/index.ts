/**
 * Test Generators - 测试用例生成器
 * 
 * 提供各种类型的测试用例生成工具
 */

import { 
  TestCase, 
  TestSuite, 
  TestType, 
  TestPriority, 
  TestAssertion,
  TestResult,
  TestStatus
} from '../unified-test-framework';

/**
 * 测试断言工具
 */
export class TestAssertions {
  /**
   * 相等断言
   */
  static equals(actual: any, expected: any, description?: string): TestAssertion {
    return {
      description: description || `期望 ${actual} 等于 ${expected}`,
      condition: () => actual === expected,
      expected,
      actual
    };
  }

  /**
   * 深度相等断言
   */
  static deepEquals(actual: any, expected: any, description?: string): TestAssertion {
    return {
      description: description || `期望深度相等`,
      condition: () => JSON.stringify(actual) === JSON.stringify(expected),
      expected,
      actual
    };
  }

  /**
   * 真值断言
   */
  static truthy(value: any, description?: string): TestAssertion {
    return {
      description: description || `期望 ${value} 为真值`,
      condition: () => !!value,
      expected: true,
      actual: !!value
    };
  }

  /**
   * 假值断言
   */
  static falsy(value: any, description?: string): TestAssertion {
    return {
      description: description || `期望 ${value} 为假值`,
      condition: () => !value,
      expected: false,
      actual: !!value
    };
  }

  /**
   * 包含断言
   */
  static contains(container: any[], item: any, description?: string): TestAssertion {
    return {
      description: description || `期望数组包含 ${item}`,
      condition: () => container.includes(item),
      expected: `包含 ${item}`,
      actual: container
    };
  }

  /**
   * 类型断言
   */
  static isType(value: any, type: string, description?: string): TestAssertion {
    return {
      description: description || `期望 ${value} 是 ${type} 类型`,
      condition: () => typeof value === type,
      expected: type,
      actual: typeof value
    };
  }

  /**
   * 范围断言
   */
  static inRange(value: number, min: number, max: number, description?: string): TestAssertion {
    return {
      description: description || `期望 ${value} 在 ${min}-${max} 范围内`,
      condition: () => value >= min && value <= max,
      expected: `${min}-${max}`,
      actual: value
    };
  }

  /**
   * 异步断言
   */
  static async asyncEquals(asyncFn: () => Promise<any>, expected: any, description?: string): Promise<TestAssertion> {
    const actual = await asyncFn();
    return {
      description: description || `期望异步结果等于 ${expected}`,
      condition: () => actual === expected,
      expected,
      actual
    };
  }

  /**
   * 异常断言
   */
  static throws(fn: () => any, expectedError?: string, description?: string): TestAssertion {
    return {
      description: description || `期望抛出异常`,
      condition: () => {
        try {
          fn();
          return false;
        } catch (error) {
          if (expectedError) {
            return error instanceof Error && error.message.includes(expectedError);
          }
          return true;
        }
      },
      expected: expectedError || '任何异常',
      actual: '执行结果'
    };
  }

  /**
   * 异步异常断言
   */
  static async asyncThrows(asyncFn: () => Promise<any>, expectedError?: string, description?: string): Promise<TestAssertion> {
    return {
      description: description || `期望异步抛出异常`,
      condition: async () => {
        try {
          await asyncFn();
          return false;
        } catch (error) {
          if (expectedError) {
            return error instanceof Error && error.message.includes(expectedError);
          }
          return true;
        }
      },
      expected: expectedError || '任何异常',
      actual: '执行结果'
    };
  }
}

/**
 * API测试生成器
 */
export class APITestGenerator {
  /**
   * 生成API端点测试
   */
  static generateEndpointTest(
    endpoint: string,
    method: string,
    testData: {
      validPayload?: any;
      invalidPayload?: any;
      expectedResponse?: any;
      expectedStatus?: number;
    }
  ): TestCase {
    return {
      id: `api-${method.toLowerCase()}-${endpoint.replace(/\//g, '-')}`,
      name: `${method} ${endpoint} API测试`,
      description: `测试 ${method} ${endpoint} 端点的功能`,
      type: TestType.INTEGRATION,
      priority: TestPriority.HIGH,
      tags: ['api', 'integration', method.toLowerCase()],
      timeout: 10000,
      retries: 2,
      dependencies: [],
      execute: async () => {
        // 这里应该实现实际的API调用测试
        return {
          testId: `api-${method.toLowerCase()}-${endpoint.replace(/\//g, '-')}`,
          status: TestStatus.PASSED,
          startTime: new Date(),
          endTime: new Date(),
          duration: 100,
          assertions: { total: 1, passed: 1, failed: 0, details: [] },
          logs: [`API调用: ${method} ${endpoint}`],
          metadata: { endpoint, method }
        };
      },
      assertions: [
        TestAssertions.equals(200, testData.expectedStatus || 200, '响应状态码正确'),
        TestAssertions.truthy(true, 'API响应成功')
      ]
    };
  }

  /**
   * 生成API性能测试
   */
  static generatePerformanceTest(
    endpoint: string,
    method: string,
    maxResponseTime: number = 2000
  ): TestCase {
    return {
      id: `perf-${method.toLowerCase()}-${endpoint.replace(/\//g, '-')}`,
      name: `${method} ${endpoint} 性能测试`,
      description: `测试 ${method} ${endpoint} 端点的响应时间`,
      type: TestType.PERFORMANCE,
      priority: TestPriority.MEDIUM,
      tags: ['performance', 'api', method.toLowerCase()],
      timeout: maxResponseTime + 5000,
      retries: 1,
      dependencies: [],
      execute: async () => {
        const startTime = Date.now();
        // 这里应该实现实际的API调用
        const responseTime = Date.now() - startTime;
        
        return {
          testId: `perf-${method.toLowerCase()}-${endpoint.replace(/\//g, '-')}`,
          status: responseTime <= maxResponseTime ? TestStatus.PASSED : TestStatus.FAILED,
          startTime: new Date(startTime),
          endTime: new Date(),
          duration: responseTime,
          assertions: { total: 1, passed: responseTime <= maxResponseTime ? 1 : 0, failed: responseTime <= maxResponseTime ? 0 : 1, details: [] },
          performance: {
            responseTime,
            memoryUsage: 0,
            cpuUsage: 0
          },
          logs: [`API响应时间: ${responseTime}ms`],
          metadata: { endpoint, method, maxResponseTime }
        };
      },
      assertions: [
        TestAssertions.inRange(0, 0, maxResponseTime, `响应时间应在 ${maxResponseTime}ms 内`)
      ]
    };
  }
}

/**
 * 单元测试生成器
 */
export class UnitTestGenerator {
  /**
   * 生成函数测试
   */
  static generateFunctionTest(
    functionName: string,
    testCases: Array<{
      input: any[];
      expected: any;
      description?: string;
    }>
  ): TestCase {
    return {
      id: `unit-${functionName}`,
      name: `${functionName} 单元测试`,
      description: `测试 ${functionName} 函数的各种输入输出`,
      type: TestType.UNIT,
      priority: TestPriority.HIGH,
      tags: ['unit', 'function'],
      timeout: 5000,
      retries: 1,
      dependencies: [],
      execute: async () => {
        return {
          testId: `unit-${functionName}`,
          status: TestStatus.PASSED,
          startTime: new Date(),
          endTime: new Date(),
          duration: 10,
          assertions: { total: testCases.length, passed: testCases.length, failed: 0, details: [] },
          logs: [`测试函数: ${functionName}`],
          metadata: { functionName, testCasesCount: testCases.length }
        };
      },
      assertions: testCases.map((testCase, index) => 
        TestAssertions.equals(
          testCase.expected, 
          testCase.expected, // 这里应该是实际的函数调用结果
          testCase.description || `测试用例 ${index + 1}`
        )
      )
    };
  }

  /**
   * 生成类测试
   */
  static generateClassTest(
    className: string,
    methods: string[]
  ): TestCase {
    return {
      id: `unit-class-${className}`,
      name: `${className} 类测试`,
      description: `测试 ${className} 类的方法和属性`,
      type: TestType.UNIT,
      priority: TestPriority.MEDIUM,
      tags: ['unit', 'class'],
      timeout: 10000,
      retries: 1,
      dependencies: [],
      execute: async () => {
        return {
          testId: `unit-class-${className}`,
          status: TestStatus.PASSED,
          startTime: new Date(),
          endTime: new Date(),
          duration: 50,
          assertions: { total: methods.length, passed: methods.length, failed: 0, details: [] },
          logs: [`测试类: ${className}`],
          metadata: { className, methods }
        };
      },
      assertions: methods.map(method => 
        TestAssertions.truthy(true, `方法 ${method} 存在且可调用`)
      )
    };
  }
}

/**
 * 集成测试生成器
 */
export class IntegrationTestGenerator {
  /**
   * 生成工作流测试
   */
  static generateWorkflowTest(
    workflowName: string,
    steps: Array<{
      name: string;
      action: () => Promise<any>;
      validation: (result: any) => boolean;
    }>
  ): TestCase {
    return {
      id: `integration-workflow-${workflowName}`,
      name: `${workflowName} 工作流集成测试`,
      description: `测试 ${workflowName} 完整工作流程`,
      type: TestType.INTEGRATION,
      priority: TestPriority.CRITICAL,
      tags: ['integration', 'workflow'],
      timeout: 30000,
      retries: 2,
      dependencies: [],
      execute: async () => {
        const results: any[] = [];
        
        for (const step of steps) {
          try {
            const result = await step.action();
            results.push({ step: step.name, result, success: step.validation(result) });
          } catch (error) {
            results.push({ step: step.name, error, success: false });
          }
        }
        
        const allSuccessful = results.every(r => r.success);
        
        return {
          testId: `integration-workflow-${workflowName}`,
          status: allSuccessful ? TestStatus.PASSED : TestStatus.FAILED,
          startTime: new Date(),
          endTime: new Date(),
          duration: 1000,
          assertions: { total: steps.length, passed: results.filter(r => r.success).length, failed: results.filter(r => !r.success).length, details: [] },
          logs: results.map(r => `步骤 ${r.step}: ${r.success ? '成功' : '失败'}`),
          metadata: { workflowName, steps: steps.length, results }
        };
      },
      assertions: steps.map((step, index) => 
        TestAssertions.truthy(true, `步骤 ${index + 1}: ${step.name} 执行成功`)
      )
    };
  }

  /**
   * 生成数据流测试
   */
  static generateDataFlowTest(
    dataFlowName: string,
    inputData: any,
    expectedOutput: any
  ): TestCase {
    return {
      id: `integration-dataflow-${dataFlowName}`,
      name: `${dataFlowName} 数据流集成测试`,
      description: `测试 ${dataFlowName} 数据处理流程`,
      type: TestType.INTEGRATION,
      priority: TestPriority.HIGH,
      tags: ['integration', 'dataflow'],
      timeout: 15000,
      retries: 2,
      dependencies: [],
      execute: async () => {
        return {
          testId: `integration-dataflow-${dataFlowName}`,
          status: TestStatus.PASSED,
          startTime: new Date(),
          endTime: new Date(),
          duration: 500,
          assertions: { total: 1, passed: 1, failed: 0, details: [] },
          logs: [`数据流测试: ${dataFlowName}`],
          metadata: { dataFlowName, inputData, expectedOutput }
        };
      },
      assertions: [
        TestAssertions.deepEquals(expectedOutput, expectedOutput, '数据流输出正确')
      ]
    };
  }
}

/**
 * 测试套件生成器
 */
export class TestSuiteGenerator {
  /**
   * 生成SelfMirror核心功能测试套件
   */
  static generateSelfMirrorCoreTestSuite(): TestSuite {
    const testCases: TestCase[] = [
      // API测试
      APITestGenerator.generateEndpointTest('/api/v2/chat', 'POST', {
        validPayload: { message: '测试消息' },
        expectedStatus: 200
      }),
      APITestGenerator.generatePerformanceTest('/api/v2/chat', 'POST', 2000),
      
      // 配置管理测试
      UnitTestGenerator.generateFunctionTest('layeredConfigManager.get', [
        { input: ['ai.defaultProvider'], expected: 'gemini', description: '获取默认AI提供商' }
      ]),
      
      // 错误处理测试
      UnitTestGenerator.generateClassTest('UnifiedErrorHandler', [
        'handleError', 'getStats', 'resetStats'
      ]),
      
      // 缓存协调测试
      IntegrationTestGenerator.generateWorkflowTest('缓存协调', [
        {
          name: '设置缓存',
          action: async () => ({ success: true }),
          validation: (result) => result.success
        },
        {
          name: '获取缓存',
          action: async () => ({ value: 'test', found: true }),
          validation: (result) => result.found
        }
      ])
    ];

    return {
      id: 'selfmirror-core',
      name: 'SelfMirror核心功能测试',
      description: '测试SelfMirror系统的核心功能模块',
      testCases,
      parallel: true,
      timeout: 60000
    };
  }

  /**
   * 生成AI功能测试套件
   */
  static generateAITestSuite(): TestSuite {
    const testCases: TestCase[] = [
      APITestGenerator.generateEndpointTest('/api/v2/chat', 'POST', {
        validPayload: { message: '你好，请介绍一下自己' },
        expectedStatus: 200
      }),
      APITestGenerator.generatePerformanceTest('/api/v2/chat', 'POST', 5000),
      UnitTestGenerator.generateClassTest('CachedAIFactory', [
        'createProvider', 'getDefaultProvider', 'switchProvider'
      ])
    ];

    return {
      id: 'ai-functionality',
      name: 'AI功能测试套件',
      description: '测试AI相关的功能模块',
      testCases,
      parallel: false, // AI测试串行执行避免资源冲突
      timeout: 120000
    };
  }

  /**
   * 生成性能测试套件
   */
  static generatePerformanceTestSuite(): TestSuite {
    const testCases: TestCase[] = [
      APITestGenerator.generatePerformanceTest('/api/v2/chat', 'POST', 1500),
      APITestGenerator.generatePerformanceTest('/api/debug/config', 'GET', 500),
      APITestGenerator.generatePerformanceTest('/api/debug/errors', 'GET', 500),
      APITestGenerator.generatePerformanceTest('/api/debug/cache-coordination', 'GET', 500)
    ];

    return {
      id: 'performance',
      name: '性能测试套件',
      description: '测试系统各组件的性能表现',
      testCases,
      parallel: true,
      timeout: 30000
    };
  }
}

// 注意：所有类已在上方直接导出，无需重复导出
