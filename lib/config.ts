// 应用配置常量
export const APP_CONFIG = {
  // Gemini API 配置
  GEMINI_MODEL: "gemini-2.5-flash-preview-05-20", // 支持thinking的最新预览版本
  GEMINI_API_KEY: "AIzaSyDHdlBWyQXDBbQVBcjr6zvNayUgkZd6N1w", // 直接硬编码用于本地开发
  
  // 代理配置
  PROXY_URL: "http://127.0.0.1:7897",
  
  // API 配置
  MAX_DURATION: 30,
  
  // 调试配置
  ENABLE_DEBUG_LOGS: process.env.NODE_ENV === 'development',
} as const;

// API配置导出（为了兼容性）
export const API_CONFIG = {
  apiKey: APP_CONFIG.GEMINI_API_KEY,
  modelName: APP_CONFIG.GEMINI_MODEL,
  proxyUrl: APP_CONFIG.PROXY_URL
};

// 工具函数：条件性日志输出
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const debugLog = (message: string, ...args: any[]) => {
  if (APP_CONFIG.ENABLE_DEBUG_LOGS) {
    console.log(message, ...args);
  }
}; 