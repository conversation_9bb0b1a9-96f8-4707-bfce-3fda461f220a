// 新的五文件记忆系统管理器
import { promises as fs } from 'fs';
import path from 'path';

const MEMORY_DIR = path.join(process.cwd(), 'memory');
const PROMPTS_DIR = path.join(MEMORY_DIR, 'prompts');

// 确保记忆目录存在
async function ensureMemoryDir() {
  try {
    await fs.access(MEMORY_DIR);
  } catch {
    await fs.mkdir(MEMORY_DIR, { recursive: true });
  }
}

// 五文件系统的文件路径
export const MEMORY_FILES = {
  XIAO_JING_PERSONA: '小镜人设提示词.md',
  USER_PROFILE: '用户画像.md',
  MENTAL_ELEMENTS: '心智要素结构.md',
  KEY_EVENTS: '关键事件.md',
  DAILY_INSIGHT_COLD: '每日洞察归档.md',
  DAILY_INSIGHT_HOT: '每日洞察今天.md',
  DIALOGUE_HISTORY: '对话历史.md'
} as const;

// 提示词文件路径
export const PROMPT_FILES = {
  SYSTEM_PROMPT: '系统提示词.md',
  DAILY_CHAT_PROMPT: '日常对话模式提示词.md',
  DAILY_INSIGHT_PROMPT: '每日洞察模式提示词.md',
  DEEP_REFINEMENT_PROMPT: '深度精炼模式提示词.md'
} as const;

// 读取记忆文件
export async function readMemoryFile(filename: string): Promise<string> {
  await ensureMemoryDir();
  const filepath = path.join(MEMORY_DIR, filename);
  
  try {
    return await fs.readFile(filepath, 'utf-8');
  } catch {
    // 文件不存在返回空字符串
    return '';
  }
}

// 读取提示词文件
export async function readPromptFile(filename: string): Promise<string> {
  await ensureMemoryDir();
  const filepath = path.join(PROMPTS_DIR, filename);
  
  try {
    return await fs.readFile(filepath, 'utf-8');
  } catch {
    return '';
  }
}

// 写入记忆文件
export async function writeMemoryFile(filename: string, content: string): Promise<void> {
  await ensureMemoryDir();
  const filepath = path.join(MEMORY_DIR, filename);
  await fs.writeFile(filepath, content, 'utf-8');
}

// 写入提示词文件
export async function writePromptFile(filename: string, content: string): Promise<void> {
  await ensureMemoryDir();
  // 确保提示词目录存在
  try {
    await fs.access(PROMPTS_DIR);
  } catch {
    await fs.mkdir(PROMPTS_DIR, { recursive: true });
  }

  const filepath = path.join(PROMPTS_DIR, filename);
  await fs.writeFile(filepath, content, 'utf-8');
}

// 追加内容到记忆文件
export async function appendToMemoryFile(filename: string, content: string): Promise<void> {
  await ensureMemoryDir();
  const filepath = path.join(MEMORY_DIR, filename);
  
  // 添加时间戳和分隔线
  const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
  const contentWithTimestamp = `\n\n---\n\n## ${timestamp}\n\n${content}`;
  
  await fs.appendFile(filepath, contentWithTimestamp, 'utf-8');
}

// 加载完整的记忆上下文
export async function loadMemoryContext(): Promise<{
  xiaoJingPersona: string;
  userProfile: string;
  mentalElements: string;
  keyEvents: string;
  dailyInsightCold: string;
  dailyInsightHot: string;
  dialogueHistory: string;
}> {
  const [
    xiaoJingPersona,
    userProfile,
    mentalElements,
    keyEvents,
    dailyInsightCold,
    dailyInsightHot,
    dialogueHistory
  ] = await Promise.all([
    readMemoryFile(MEMORY_FILES.XIAO_JING_PERSONA),
    readMemoryFile(MEMORY_FILES.USER_PROFILE),
    readMemoryFile(MEMORY_FILES.MENTAL_ELEMENTS),
    readMemoryFile(MEMORY_FILES.KEY_EVENTS),
    readMemoryFile(MEMORY_FILES.DAILY_INSIGHT_COLD),
    readMemoryFile(MEMORY_FILES.DAILY_INSIGHT_HOT),
    readMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY)
  ]);
  
  return {
    xiaoJingPersona,
    userProfile,
    mentalElements,
    keyEvents,
    dailyInsightCold,
    dailyInsightHot,
    dialogueHistory
  };
}

// 加载提示词上下文
export async function loadPromptContext(): Promise<{
  systemPrompt: string;
  dailyChatPrompt: string;
  dailyInsightPrompt: string;
  deepRefinementPrompt: string;
}> {
  const [
    systemPrompt,
    dailyChatPrompt,
    dailyInsightPrompt,
    deepRefinementPrompt
  ] = await Promise.all([
    readPromptFile(PROMPT_FILES.SYSTEM_PROMPT),
    readPromptFile(PROMPT_FILES.DAILY_CHAT_PROMPT),
    readPromptFile(PROMPT_FILES.DAILY_INSIGHT_PROMPT),
    readPromptFile(PROMPT_FILES.DEEP_REFINEMENT_PROMPT)
  ]);
  
  return {
    systemPrompt,
    dailyChatPrompt,
    dailyInsightPrompt,
    deepRefinementPrompt
  };
}

// 保存对话到历史记录
export async function saveConversationToHistory(userMessage: string, assistantMessage: string): Promise<void> {
  const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
  const conversationEntry = `
### ${timestamp}

**用户**: ${userMessage}

**小镜**: ${assistantMessage}

---`;
  
  await appendToMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY, conversationEntry);
}

// 获取最近的对话历史
export async function getRecentDialogueHistory(rounds: number = 6): Promise<string> {
  const dialogueHistory = await readMemoryFile(MEMORY_FILES.DIALOGUE_HISTORY);

  if (!dialogueHistory.trim()) {
    return '';
  }

  // 精确提取最近N轮对话
  const conversationBlocks = extractConversationBlocks(dialogueHistory);
  const recentBlocks = conversationBlocks.slice(-rounds);

  console.log(`📚 获取最近 ${rounds} 轮对话，实际获取 ${recentBlocks.length} 轮`);
  return recentBlocks.join('\n\n');
}

// 提取对话块的辅助函数
function extractConversationBlocks(dialogueHistory: string): string[] {
  const blocks: string[] = [];
  const lines = dialogueHistory.split('\n');
  let currentBlock = '';
  let inBlock = false;

  for (const line of lines) {
    // 检测对话块开始（时间戳行）
    if (line.match(/^### \d{4}\/\d{1,2}\/\d{1,2} \d{1,2}:\d{1,2}:\d{1,2}$/)) {
      // 如果已经在处理一个块，先保存它
      if (inBlock && currentBlock.trim()) {
        blocks.push(currentBlock.trim());
      }
      // 开始新的块
      currentBlock = line;
      inBlock = true;
    } else if (inBlock) {
      currentBlock += '\n' + line;

      // 检测块结束（分隔线）
      if (line.trim() === '---') {
        blocks.push(currentBlock.trim());
        currentBlock = '';
        inBlock = false;
      }
    }
  }

  // 处理最后一个块（如果没有以---结尾）
  if (inBlock && currentBlock.trim()) {
    blocks.push(currentBlock.trim());
  }

  return blocks;
}

// 意义RAG检索（暂时禁用RAG系统，使用关键词匹配）
export async function meaningRAGSearch(query: string, sourceFile: string): Promise<string> {
  // 暂时禁用RAG系统以避免WebAssembly问题
  // TODO: 在解决WebAssembly配置后重新启用
  console.log('🔍 使用关键词匹配进行检索 (RAG系统暂时禁用)');

  // 使用简单的关键词匹配实现
  const content = await readMemoryFile(sourceFile);

  if (!content.trim()) {
    console.log('📄 源文件为空，跳过检索');
    return '';
  }

  const keywords = query.toLowerCase().split(/\s+/).filter(k => k.length > 1);
  const lines = content.split('\n');

  // 改进：添加时间窗口过滤（如果是对话历史文件）
  let filteredLines = lines;
  if (sourceFile === MEMORY_FILES.DIALOGUE_HISTORY) {
    // 只检索最近7天的对话
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    filteredLines = lines.filter(line => {
      const timeMatch = line.match(/### (\d{4}\/\d{1,2}\/\d{1,2} \d{1,2}:\d{1,2}:\d{1,2})/);
      if (timeMatch) {
        const lineDate = new Date(timeMatch[1]);
        return lineDate >= sevenDaysAgo;
      }
      return true; // 保留没有时间戳的行
    });

    console.log(`⏰ 时间窗口过滤: ${lines.length} -> ${filteredLines.length} 行`);
  }

  const relevantLines = filteredLines.filter(line =>
    keywords.some(keyword => line.toLowerCase().includes(keyword))
  );

  console.log(`🔍 关键词匹配结果: ${relevantLines.length} 个相关行`);
  return relevantLines.slice(0, 10).join('\n'); // 返回前10个相关行
}
