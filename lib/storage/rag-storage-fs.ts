// RAG系统文件系统存储管理器 - 用于Node.js环境

import { promises as fs } from 'fs';
import path from 'path';
import { ChunkPair, ChunkMetadata, StorageError } from '@/types/rag';
import { RAG_CONFIG } from '@/lib/config/rag-config';

class RAGFileSystemStorage {
  private storageDir: string;
  private chunksFile: string;
  private vectorsFile: string;
  private metadataFile: string;
  private initialized = false;

  constructor() {
    this.storageDir = path.join(process.cwd(), 'data', 'rag');
    this.chunksFile = path.join(this.storageDir, 'chunks.json');
    this.vectorsFile = path.join(this.storageDir, 'vectors.json');
    this.metadataFile = path.join(this.storageDir, 'metadata.json');
  }

  /**
   * 初始化文件系统存储
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 确保存储目录存在
      await fs.mkdir(this.storageDir, { recursive: true });

      // 初始化存储文件（如果不存在）
      await this.ensureFileExists(this.chunksFile, '{}');
      await this.ensureFileExists(this.vectorsFile, '{}');
      await this.ensureFileExists(this.metadataFile, '{}');

      this.initialized = true;
      console.log('✅ RAG文件系统存储初始化成功');
    } catch (error) {
      throw new StorageError('RAG文件系统存储初始化失败', error);
    }
  }

  /**
   * 确保文件存在，如果不存在则创建
   */
  private async ensureFileExists(filePath: string, defaultContent: string): Promise<void> {
    try {
      await fs.access(filePath);
    } catch {
      await fs.writeFile(filePath, defaultContent, 'utf-8');
    }
  }

  /**
   * 读取JSON文件
   */
  private async readJsonFile<T>(filePath: string): Promise<T> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      console.error(`读取文件失败: ${filePath}`, error);
      return {} as T;
    }
  }

  /**
   * 写入JSON文件
   */
  private async writeJsonFile<T>(filePath: string, data: T): Promise<void> {
    try {
      const content = JSON.stringify(data, null, 2);
      await fs.writeFile(filePath, content, 'utf-8');
    } catch (error) {
      throw new StorageError(`写入文件失败: ${filePath}`, error);
    }
  }

  /**
   * 保存块对到文件系统
   */
  async saveChunkPair(chunkPair: ChunkPair): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // 读取现有数据
      const chunks = await this.readJsonFile<Record<string, any>>(this.chunksFile);
      const vectors = await this.readJsonFile<Record<string, any>>(this.vectorsFile);

      // 保存chunk数据（不包含vector）
      chunks[chunkPair.id] = {
        id: chunkPair.id,
        parentChunk: chunkPair.parentChunk,
        childChunk: chunkPair.childChunk,
        sourceDocumentId: chunkPair.sourceDocumentId,
        sourceDocumentType: chunkPair.sourceDocumentType,
        metadata: chunkPair.metadata,
        createdAt: chunkPair.createdAt,
        updatedAt: chunkPair.updatedAt
      };

      // 保存vector数据
      vectors[chunkPair.id] = {
        chunkId: chunkPair.id,
        vector: chunkPair.vector,
        dimensions: chunkPair.vector.length
      };

      // 写入文件
      await Promise.all([
        this.writeJsonFile(this.chunksFile, chunks),
        this.writeJsonFile(this.vectorsFile, vectors)
      ]);

      console.log(`💾 块对 ${chunkPair.id} 保存成功 (文件系统)`);
    } catch (error) {
      throw new StorageError('保存块对失败', error);
    }
  }

  /**
   * 批量保存块对
   */
  async saveChunkPairs(chunkPairs: ChunkPair[]): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // 读取现有数据
      const chunks = await this.readJsonFile<Record<string, any>>(this.chunksFile);
      const vectors = await this.readJsonFile<Record<string, any>>(this.vectorsFile);

      // 批量添加数据
      for (const chunkPair of chunkPairs) {
        chunks[chunkPair.id] = {
          id: chunkPair.id,
          parentChunk: chunkPair.parentChunk,
          childChunk: chunkPair.childChunk,
          sourceDocumentId: chunkPair.sourceDocumentId,
          sourceDocumentType: chunkPair.sourceDocumentType,
          metadata: chunkPair.metadata,
          createdAt: chunkPair.createdAt,
          updatedAt: chunkPair.updatedAt
        };

        vectors[chunkPair.id] = {
          chunkId: chunkPair.id,
          vector: chunkPair.vector,
          dimensions: chunkPair.vector.length
        };
      }

      // 写入文件
      await Promise.all([
        this.writeJsonFile(this.chunksFile, chunks),
        this.writeJsonFile(this.vectorsFile, vectors)
      ]);

      console.log(`💾 批量保存 ${chunkPairs.length} 个块对成功 (文件系统)`);
    } catch (error) {
      throw new StorageError('批量保存块对失败', error);
    }
  }

  /**
   * 根据ID获取块对
   */
  async getChunkPair(id: string): Promise<ChunkPair | null> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const chunks = await this.readJsonFile<Record<string, any>>(this.chunksFile);
      const vectors = await this.readJsonFile<Record<string, any>>(this.vectorsFile);

      const chunkData = chunks[id];
      const vectorData = vectors[id];

      if (!chunkData || !vectorData) {
        return null;
      }

      return {
        ...chunkData,
        vector: vectorData.vector
      };
    } catch (error) {
      throw new StorageError('获取块对失败', error);
    }
  }

  /**
   * 获取所有向量数据（用于向量搜索）
   */
  async getAllVectors(): Promise<Array<{ chunkId: string; vector: number[] }>> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const vectors = await this.readJsonFile<Record<string, any>>(this.vectorsFile);
      return Object.values(vectors).map(v => ({ 
        chunkId: v.chunkId, 
        vector: v.vector 
      }));
    } catch (error) {
      throw new StorageError('获取向量数据失败', error);
    }
  }

  /**
   * 根据文档ID删除所有相关块对
   */
  async deleteByDocumentId(documentId: string): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const chunks = await this.readJsonFile<Record<string, any>>(this.chunksFile);
      const vectors = await this.readJsonFile<Record<string, any>>(this.vectorsFile);

      // 找到要删除的块对
      const chunksToDelete = Object.values(chunks).filter(
        (chunk: any) => chunk.sourceDocumentId === documentId
      );

      // 删除相关数据
      for (const chunk of chunksToDelete) {
        delete chunks[chunk.id];
        delete vectors[chunk.id];
      }

      // 写入文件
      await Promise.all([
        this.writeJsonFile(this.chunksFile, chunks),
        this.writeJsonFile(this.vectorsFile, vectors)
      ]);

      console.log(`🗑️ 删除文档 ${documentId} 的 ${chunksToDelete.length} 个块对 (文件系统)`);
    } catch (error) {
      throw new StorageError('删除块对失败', error);
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalChunks: number;
    totalVectors: number;
    storageSize: number;
    documentTypes: Record<string, number>;
  }> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const chunks = await this.readJsonFile<Record<string, any>>(this.chunksFile);
      const vectors = await this.readJsonFile<Record<string, any>>(this.vectorsFile);

      const chunkArray = Object.values(chunks);
      const vectorArray = Object.values(vectors);

      const documentTypes: Record<string, number> = {};
      chunkArray.forEach((chunk: any) => {
        documentTypes[chunk.sourceDocumentType] = (documentTypes[chunk.sourceDocumentType] || 0) + 1;
      });

      // 计算文件大小
      const [chunksStats, vectorsStats] = await Promise.all([
        fs.stat(this.chunksFile).catch(() => ({ size: 0 })),
        fs.stat(this.vectorsFile).catch(() => ({ size: 0 }))
      ]);

      const storageSize = (chunksStats.size + vectorsStats.size) / 1024 / 1024; // MB

      return {
        totalChunks: chunkArray.length,
        totalVectors: vectorArray.length,
        storageSize,
        documentTypes
      };
    } catch (error) {
      throw new StorageError('获取存储统计失败', error);
    }
  }

  /**
   * 清空所有数据
   */
  async clearAll(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      await Promise.all([
        this.writeJsonFile(this.chunksFile, {}),
        this.writeJsonFile(this.vectorsFile, {}),
        this.writeJsonFile(this.metadataFile, {})
      ]);

      console.log('🗑️ RAG文件系统存储已清空');
    } catch (error) {
      throw new StorageError('清空存储失败', error);
    }
  }
}

// 导出单例
export const ragFileSystemStorage = new RAGFileSystemStorage();
