/**
 * 存储工厂类
 * 负责创建和管理不同类型的存储实例
 */

import path from 'path';
import {
  IMemoryStorage,
  StorageError,
  StorageErrorCodes,
  FileSystemStorageConfig,
  IndexedDBStorageConfig,
  MemoryStorageConfig
} from './interfaces/IMemoryStorage';

import { FileSystemStorage } from './implementations/FileSystemStorage';
import { IndexedDBStorage } from './implementations/IndexedDBStorage';
import { MemoryStorage } from './implementations/MemoryStorage';

// ==================== 配置类型 ====================

export interface StorageConfig {
  provider: 'filesystem' | 'indexeddb' | 'memory';
  config: FileSystemStorageConfig | IndexedDBStorageConfig | MemoryStorageConfig;
}

export interface StorageFactoryOptions {
  /** 是否启用实例缓存 */
  enableCache?: boolean;
  
  /** 自动检测环境配置 */
  autoDetect?: boolean;
  
  /** 默认存储提供者 */
  defaultProvider?: 'filesystem' | 'indexeddb' | 'memory';
  
  /** 降级策略 */
  fallbackStrategy?: 'memory' | 'throw' | 'auto';
}

// ==================== 存储工厂类 ====================

export class StorageFactory {
  private static instances: Map<string, IMemoryStorage> = new Map();
  private static options: StorageFactoryOptions = {
    enableCache: true,
    autoDetect: true,
    defaultProvider: 'filesystem',
    fallbackStrategy: 'auto'
  };

  /**
   * 配置工厂选项
   */
  static configure(options: Partial<StorageFactoryOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 创建存储实例
   */
  static async create(config: StorageConfig): Promise<IMemoryStorage> {
    const key = this.getConfigKey(config);

    // 检查缓存
    if (this.options.enableCache && this.instances.has(key)) {
      const instance = this.instances.get(key)!;
      console.log(`♻️ Reusing cached storage instance: ${config.provider}`);
      return instance;
    }

    let storage: IMemoryStorage;

    try {
      storage = await this.createStorageInstance(config);
      await storage.initialize();

      // 缓存实例
      if (this.options.enableCache) {
        this.instances.set(key, storage);
      }

      console.log(`✅ Created storage instance: ${config.provider}`);
      return storage;
    } catch (error) {
      console.error(`❌ Failed to create storage: ${config.provider}`, error);
      
      // 应用降级策略
      if (this.options.fallbackStrategy !== 'throw') {
        return this.createFallbackStorage(config, error instanceof Error ? error : new Error(String(error)));
      }
      
      throw error;
    }
  }

  /**
   * 从环境自动检测并创建存储实例
   */
  static async createFromEnvironment(): Promise<IMemoryStorage> {
    const config = this.detectEnvironmentConfig();
    return this.create(config);
  }

  /**
   * 创建特定类型的存储实例
   */
  static async createFileSystemStorage(
    basePath?: string,
    options?: Partial<FileSystemStorageConfig>
  ): Promise<IMemoryStorage> {
    const config: StorageConfig = {
      provider: 'filesystem',
      config: {
        provider: 'filesystem',
        basePath: basePath || path.join(process.cwd(), 'memory'),
        encoding: 'utf-8',
        createDirs: true,
        ...options
      }
    };

    return this.create(config);
  }

  static async createIndexedDBStorage(
    dbName?: string,
    options?: Partial<IndexedDBStorageConfig>
  ): Promise<IMemoryStorage> {
    const config: StorageConfig = {
      provider: 'indexeddb',
      config: {
        provider: 'indexeddb',
        dbName: dbName || 'SelfMirrorDB',
        version: 1,
        storeName: 'storage',
        ...options
      }
    };

    return this.create(config);
  }

  static async createMemoryStorage(
    options?: Partial<MemoryStorageConfig>
  ): Promise<IMemoryStorage> {
    const config: StorageConfig = {
      provider: 'memory',
      config: {
        provider: 'memory',
        maxSize: 50 * 1024 * 1024, // 50MB
        persistent: false,
        ...options
      }
    };

    return this.create(config);
  }

  /**
   * 获取所有缓存的实例
   */
  static getCachedInstances(): Map<string, IMemoryStorage> {
    return new Map(this.instances);
  }

  /**
   * 清理指定实例
   */
  static async closeInstance(configKey: string): Promise<void> {
    const instance = this.instances.get(configKey);
    if (instance) {
      await instance.close();
      this.instances.delete(configKey);
      console.log(`📦 Closed storage instance: ${configKey}`);
    }
  }

  /**
   * 清理所有实例
   */
  static async closeAllInstances(): Promise<void> {
    const closePromises = Array.from(this.instances.entries()).map(
      async ([key, instance]) => {
        try {
          await instance.close();
          console.log(`📦 Closed storage instance: ${key}`);
        } catch (error) {
          console.error(`❌ Failed to close storage instance: ${key}`, error);
        }
      }
    );

    await Promise.all(closePromises);
    this.instances.clear();
    console.log('📦 All storage instances closed');
  }

  /**
   * 健康检查
   */
  static async healthCheck(storage: IMemoryStorage): Promise<boolean> {
    try {
      // 执行基本的读写测试
      const testKey = '__health_check__';
      const testContent = 'health_check_' + Date.now();

      await storage.write(testKey, testContent);
      const readContent = await storage.read(testKey);
      await storage.delete(testKey);

      return readContent === testContent;
    } catch (error) {
      console.error('Storage health check failed:', error);
      return false;
    }
  }

  // ==================== 私有方法 ====================

  private static async createStorageInstance(config: StorageConfig): Promise<IMemoryStorage> {
    switch (config.provider) {
      case 'filesystem':
        return new FileSystemStorage(config.config as FileSystemStorageConfig);
      
      case 'indexeddb':
        return new IndexedDBStorage(config.config as IndexedDBStorageConfig);
      
      case 'memory':
        return new MemoryStorage(config.config as MemoryStorageConfig);
      
      default:
        throw new StorageError(
          `Unsupported storage provider: ${(config as any).provider}`,
          StorageErrorCodes.INITIALIZATION_FAILED
        );
    }
  }

  private static async createFallbackStorage(
    originalConfig: StorageConfig,
    originalError: Error
  ): Promise<IMemoryStorage> {
    console.warn(`⚠️ Creating fallback storage due to error:`, originalError.message);

    let fallbackConfig: StorageConfig;

    if (this.options.fallbackStrategy === 'memory') {
      fallbackConfig = {
        provider: 'memory',
        config: {
          provider: 'memory',
          maxSize: 10 * 1024 * 1024, // 10MB for fallback
          persistent: false
        }
      };
    } else {
      // 自动降级策略
      if (originalConfig.provider === 'filesystem') {
        // 文件系统失败，尝试 IndexedDB
        fallbackConfig = {
          provider: 'indexeddb',
          config: {
            provider: 'indexeddb',
            dbName: 'SelfMirrorDB_Fallback',
            version: 1
          }
        };
      } else {
        // 其他情况降级到内存存储
        fallbackConfig = {
          provider: 'memory',
          config: {
            provider: 'memory',
            maxSize: 10 * 1024 * 1024,
            persistent: false
          }
        };
      }
    }

    try {
      const fallbackStorage = await this.createStorageInstance(fallbackConfig);
      await fallbackStorage.initialize();
      
      console.log(`✅ Fallback storage created: ${fallbackConfig.provider}`);
      return fallbackStorage;
    } catch (fallbackError) {
      console.error(`❌ Fallback storage also failed:`, fallbackError);
      throw new StorageError(
        `Both primary and fallback storage failed. Primary: ${originalError.message}, Fallback: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`,
        StorageErrorCodes.INITIALIZATION_FAILED
      );
    }
  }

  private static detectEnvironmentConfig(): StorageConfig {
    // 检测运行环境
    if (typeof window !== 'undefined') {
      // 浏览器环境
      if (typeof indexedDB !== 'undefined') {
        console.log('🌐 Detected browser environment, using IndexedDB');
        return {
          provider: 'indexeddb',
          config: {
            provider: 'indexeddb',
            dbName: 'SelfMirrorDB',
            version: 1,
            storeName: 'storage'
          }
        };
      } else {
        console.log('🌐 Detected browser environment without IndexedDB, using memory storage');
        return {
          provider: 'memory',
          config: {
            provider: 'memory',
            maxSize: 10 * 1024 * 1024,
            persistent: true // 尝试使用 localStorage 持久化
          }
        };
      }
    } else {
      // Node.js 环境
      console.log('🖥️ Detected Node.js environment, using filesystem storage');
      return {
        provider: 'filesystem',
        config: {
          provider: 'filesystem',
          basePath: path.join(process.cwd(), 'memory'),
          encoding: 'utf-8',
          createDirs: true
        }
      };
    }
  }

  private static getConfigKey(config: StorageConfig): string {
    // 生成配置的唯一键
    const configStr = JSON.stringify(config, Object.keys(config).sort());
    return Buffer.from(configStr).toString('base64').slice(0, 16);
  }
}

// ==================== 便捷函数 ====================

/**
 * 创建默认存储实例
 */
export async function createDefaultStorage(): Promise<IMemoryStorage> {
  return StorageFactory.createFromEnvironment();
}

/**
 * 创建文件系统存储
 */
export async function createFileSystemStorage(
  basePath?: string,
  options?: Partial<FileSystemStorageConfig>
): Promise<IMemoryStorage> {
  return StorageFactory.createFileSystemStorage(basePath, options);
}

/**
 * 创建 IndexedDB 存储
 */
export async function createIndexedDBStorage(
  dbName?: string,
  options?: Partial<IndexedDBStorageConfig>
): Promise<IMemoryStorage> {
  return StorageFactory.createIndexedDBStorage(dbName, options);
}

/**
 * 创建内存存储
 */
export async function createMemoryStorage(
  options?: Partial<MemoryStorageConfig>
): Promise<IMemoryStorage> {
  return StorageFactory.createMemoryStorage(options);
}

// ==================== 导出 ====================

export default StorageFactory;
