// RAG存储环境适配器 - 自动选择合适的存储方案

import { ChunkPair, ChunkMetadata, StorageError } from '@/types/rag';

// 定义存储接口
interface RAGStorageInterface {
  initialize(): Promise<void>;
  saveChunkPair(chunkPair: ChunkPair): Promise<void>;
  saveChunkPairs(chunkPairs: ChunkPair[]): Promise<void>;
  getChunkPair(id: string): Promise<ChunkPair | null>;
  getAllVectors(): Promise<Array<{ chunkId: string; vector: number[] }>>;
  deleteByDocumentId(documentId: string): Promise<void>;
  getStorageStats(): Promise<{
    totalChunks: number;
    totalVectors: number;
    storageSize: number;
    documentTypes: Record<string, number>;
  }>;
  clearAll(): Promise<void>;
}

class RAGStorageAdapter implements RAGStorageInterface {
  private storage: RAGStorageInterface | null = null;
  private storageType: 'indexeddb' | 'filesystem' | null = null;

  /**
   * 初始化存储适配器
   */
  async initialize(): Promise<void> {
    if (this.storage) {
      return; // 已经初始化
    }

    // 检测运行环境
    if (typeof window !== 'undefined' && typeof indexedDB !== 'undefined') {
      // 浏览器环境，使用IndexedDB
      console.log('🌐 检测到浏览器环境，使用IndexedDB存储');
      this.storageType = 'indexeddb';
      
      try {
        const { ragStorage } = await import('./rag-storage');
        this.storage = ragStorage;
        await this.storage.initialize();
      } catch (error) {
        console.error('❌ IndexedDB初始化失败，降级到内存存储:', error);
        await this.initializeMemoryStorage();
      }
    } else {
      // Node.js环境，使用文件系统
      console.log('🖥️ 检测到Node.js环境，使用文件系统存储');
      this.storageType = 'filesystem';
      
      try {
        const { ragFileSystemStorage } = await import('./rag-storage-fs');
        this.storage = ragFileSystemStorage;
        await this.storage.initialize();
      } catch (error) {
        console.error('❌ 文件系统存储初始化失败，降级到内存存储:', error);
        await this.initializeMemoryStorage();
      }
    }
  }

  /**
   * 初始化内存存储（降级方案）
   */
  private async initializeMemoryStorage(): Promise<void> {
    console.log('💾 使用内存存储作为降级方案');
    this.storageType = 'filesystem'; // 标记为文件系统类型以避免重复初始化
    this.storage = new MemoryRAGStorage();
    await this.storage.initialize();
  }

  /**
   * 确保存储已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.storage) {
      await this.initialize();
    }
  }

  /**
   * 保存块对
   */
  async saveChunkPair(chunkPair: ChunkPair): Promise<void> {
    await this.ensureInitialized();
    return this.storage!.saveChunkPair(chunkPair);
  }

  /**
   * 批量保存块对
   */
  async saveChunkPairs(chunkPairs: ChunkPair[]): Promise<void> {
    await this.ensureInitialized();
    return this.storage!.saveChunkPairs(chunkPairs);
  }

  /**
   * 根据ID获取块对
   */
  async getChunkPair(id: string): Promise<ChunkPair | null> {
    await this.ensureInitialized();
    return this.storage!.getChunkPair(id);
  }

  /**
   * 获取所有向量数据
   */
  async getAllVectors(): Promise<Array<{ chunkId: string; vector: number[] }>> {
    await this.ensureInitialized();
    return this.storage!.getAllVectors();
  }

  /**
   * 根据文档ID删除块对
   */
  async deleteByDocumentId(documentId: string): Promise<void> {
    await this.ensureInitialized();
    return this.storage!.deleteByDocumentId(documentId);
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalChunks: number;
    totalVectors: number;
    storageSize: number;
    documentTypes: Record<string, number>;
  }> {
    await this.ensureInitialized();
    const stats = await this.storage!.getStorageStats();
    
    // 添加存储类型信息
    console.log(`📊 存储统计 (${this.storageType}):`, {
      ...stats,
      storageType: this.storageType
    });
    
    return stats;
  }

  /**
   * 清空所有数据
   */
  async clearAll(): Promise<void> {
    await this.ensureInitialized();
    return this.storage!.clearAll();
  }

  /**
   * 获取当前存储类型
   */
  getStorageType(): string {
    return this.storageType || 'unknown';
  }
}

/**
 * 内存存储实现（降级方案）
 */
class MemoryRAGStorage implements RAGStorageInterface {
  private chunks: Map<string, ChunkPair> = new Map();
  private initialized = false;

  async initialize(): Promise<void> {
    this.initialized = true;
    console.log('✅ 内存RAG存储初始化成功');
  }

  async saveChunkPair(chunkPair: ChunkPair): Promise<void> {
    this.chunks.set(chunkPair.id, chunkPair);
    console.log(`💾 块对 ${chunkPair.id} 保存成功 (内存)`);
  }

  async saveChunkPairs(chunkPairs: ChunkPair[]): Promise<void> {
    for (const chunkPair of chunkPairs) {
      this.chunks.set(chunkPair.id, chunkPair);
    }
    console.log(`💾 批量保存 ${chunkPairs.length} 个块对成功 (内存)`);
  }

  async getChunkPair(id: string): Promise<ChunkPair | null> {
    return this.chunks.get(id) || null;
  }

  async getAllVectors(): Promise<Array<{ chunkId: string; vector: number[] }>> {
    return Array.from(this.chunks.values()).map(chunk => ({
      chunkId: chunk.id,
      vector: chunk.vector
    }));
  }

  async deleteByDocumentId(documentId: string): Promise<void> {
    const toDelete: string[] = [];
    for (const [id, chunk] of this.chunks) {
      if (chunk.sourceDocumentId === documentId) {
        toDelete.push(id);
      }
    }
    
    for (const id of toDelete) {
      this.chunks.delete(id);
    }
    
    console.log(`🗑️ 删除文档 ${documentId} 的 ${toDelete.length} 个块对 (内存)`);
  }

  async getStorageStats(): Promise<{
    totalChunks: number;
    totalVectors: number;
    storageSize: number;
    documentTypes: Record<string, number>;
  }> {
    const chunks = Array.from(this.chunks.values());
    const documentTypes: Record<string, number> = {};
    
    chunks.forEach(chunk => {
      documentTypes[chunk.sourceDocumentType] = (documentTypes[chunk.sourceDocumentType] || 0) + 1;
    });

    // 粗略估算内存使用
    const storageSize = JSON.stringify(chunks).length / 1024 / 1024; // MB

    return {
      totalChunks: chunks.length,
      totalVectors: chunks.length,
      storageSize,
      documentTypes
    };
  }

  async clearAll(): Promise<void> {
    this.chunks.clear();
    console.log('🗑️ 内存RAG存储已清空');
  }
}

// 导出单例适配器
export const ragStorageAdapter = new RAGStorageAdapter();
