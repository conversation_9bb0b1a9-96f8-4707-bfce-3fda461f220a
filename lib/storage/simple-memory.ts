// 极简的Markdown记忆系统
import { promises as fs } from 'fs';
import path from 'path';

const MEMORY_DIR = path.join(process.cwd(), 'memory');

// 确保记忆目录存在
async function ensureMemoryDir() {
  try {
    await fs.access(MEMORY_DIR);
  } catch {
    await fs.mkdir(MEMORY_DIR, { recursive: true });
  }
}

// 读取markdown文件
export async function readMarkdownFile(filename: string): Promise<string> {
  await ensureMemoryDir();
  const filepath = path.join(MEMORY_DIR, filename);
  
  try {
    return await fs.readFile(filepath, 'utf-8');
  } catch {
    // 文件不存在返回空字符串
    return '';
  }
}

// 追加内容到markdown文件
export async function appendToMarkdownFile(filename: string, content: string): Promise<void> {
  await ensureMemoryDir();
  const filepath = path.join(MEMORY_DIR, filename);
  
  // 添加时间戳和分隔线
  const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
  const contentWithTimestamp = `\n\n---\n\n## ${timestamp}\n\n${content}`;
  
  await fs.appendFile(filepath, contentWithTimestamp, 'utf-8');
}

// 读取所有记忆文件用于上下文
export async function loadMemoryContext(): Promise<{
  userProfile: string;
  keyEvents: string;
  dailyInsights: string;
  dailyInsightsToday: string;
}> {
  const [userProfile, keyEvents, dailyInsightsArchive, dailyInsightsToday] = await Promise.all([
    readMarkdownFile('用户画像.md'),
    readMarkdownFile('关键事件.md'),
    readMarkdownFile('每日洞察归档.md'),
    readMarkdownFile('每日洞察今天.md')
  ]);
  
  // 合并归档和今天的洞察（归档在前，今天在后）
  const dailyInsights = dailyInsightsArchive;
  
  return { userProfile, keyEvents, dailyInsights, dailyInsightsToday };
}

// 归档今天的洞察到历史文件
export async function archiveTodayInsights(): Promise<void> {
  await ensureMemoryDir();
  
  const todayFile = path.join(MEMORY_DIR, '每日洞察今天.md');
  const archiveFile = path.join(MEMORY_DIR, '每日洞察归档.md');
  
  try {
    // 读取今天的内容
    const todayContent = await fs.readFile(todayFile, 'utf-8');
    
    if (todayContent.trim() && !todayContent.includes('*这里记录今天的对话洞察*')) {
      // 追加到归档文件
      const archiveHeader = `\n\n---\n\n## 归档于 ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n\n`;
      await fs.appendFile(archiveFile, archiveHeader + todayContent, 'utf-8');
      
      // 清空今天的文件
      await fs.writeFile(todayFile, '# 每日洞察（今天）\n\n*这里记录今天的对话洞察*', 'utf-8');
    }
  } catch (error) {
    console.error('归档失败:', error);
    throw error;
  }
}

// 保存对话到历史记录
export async function saveConversationToHistory(userMessage: string, assistantMessage: string): Promise<void> {
  await ensureMemoryDir();
  const filepath = path.join(MEMORY_DIR, '对话历史.md');
  
  const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
  const conversationEntry = `
### ${timestamp}

**用户**: ${userMessage}

**助手**: ${assistantMessage}

---`;
  
  await fs.appendFile(filepath, conversationEntry, 'utf-8');
} 