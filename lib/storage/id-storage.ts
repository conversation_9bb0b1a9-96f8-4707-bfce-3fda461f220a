/**
 * ID存储管理器
 * 负责全局ID注册表的持久化存储和恢复
 */

import { promises as fs } from 'fs';
import { join } from 'path';
import { GlobalIdMetadata, DailyIdCounter } from '@/lib/services/vector-database/interfaces';

export interface IdStorageData {
  idRegistry: Record<string, GlobalIdMetadata>;
  dailyCounters: Record<string, DailyIdCounter>;
  metadata: {
    version: string;
    lastSaved: string;
    totalIds: number;
  };
}

export interface IdStorageConfig {
  storageDir: string;
  fileName: string;
  backupEnabled: boolean;
  maxBackups: number;
  autoSaveInterval: number; // 毫秒
}

/**
 * ID存储管理器实现
 * 支持文件系统持久化、自动备份、数据恢复等功能
 */
export class IdStorageManager {
  private config: IdStorageConfig;
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private lastSaveTime: Date = new Date();
  private isDirty: boolean = false;

  constructor(config?: Partial<IdStorageConfig>) {
    this.config = {
      storageDir: join(process.cwd(), 'memory'),
      fileName: 'id-registry.json',
      backupEnabled: true,
      maxBackups: 5,
      autoSaveInterval: 30000, // 30秒
      ...config
    };
  }

  /**
   * 初始化存储管理器
   */
  async initialize(): Promise<void> {
    try {
      // 确保存储目录存在
      await this.ensureStorageDirectory();
      
      // 启动自动保存
      this.startAutoSave();
      
      console.log(`📁 ID存储管理器初始化完成: ${this.getStorageFilePath()}`);
    } catch (error) {
      console.error('❌ ID存储管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 保存ID注册表数据
   */
  async saveIdRegistry(
    idRegistry: Map<string, GlobalIdMetadata>,
    dailyCounters: Map<string, DailyIdCounter>
  ): Promise<void> {
    try {
      const data: IdStorageData = {
        idRegistry: Object.fromEntries(idRegistry),
        dailyCounters: Object.fromEntries(dailyCounters),
        metadata: {
          version: '1.0.0',
          lastSaved: new Date().toISOString(),
          totalIds: idRegistry.size
        }
      };

      const filePath = this.getStorageFilePath();
      
      // 创建备份（如果启用）
      if (this.config.backupEnabled) {
        await this.createBackup();
      }

      // 保存数据
      await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
      
      this.lastSaveTime = new Date();
      this.isDirty = false;
      
      console.log(`💾 ID注册表已保存: ${idRegistry.size} 个ID, ${dailyCounters.size} 个日计数器`);
    } catch (error) {
      console.error('❌ 保存ID注册表失败:', error);
      throw error;
    }
  }

  /**
   * 加载ID注册表数据
   */
  async loadIdRegistry(): Promise<{
    idRegistry: Map<string, GlobalIdMetadata>;
    dailyCounters: Map<string, DailyIdCounter>;
  }> {
    try {
      const filePath = this.getStorageFilePath();
      
      // 检查文件是否存在
      try {
        await fs.access(filePath);
      } catch {
        console.log('📝 ID注册表文件不存在，创建新的注册表');
        return {
          idRegistry: new Map(),
          dailyCounters: new Map()
        };
      }

      // 读取文件
      const content = await fs.readFile(filePath, 'utf8');
      const data: IdStorageData = JSON.parse(content);

      // 验证数据格式
      this.validateStorageData(data);

      // 转换为Map格式
      const idRegistry = new Map(Object.entries(data.idRegistry));
      const dailyCounters = new Map(Object.entries(data.dailyCounters));

      // 清理过期数据
      await this.cleanupExpiredData(idRegistry, dailyCounters);

      console.log(`📥 ID注册表已加载: ${idRegistry.size} 个ID, ${dailyCounters.size} 个日计数器`);
      console.log(`📊 数据版本: ${data.metadata.version}, 最后保存: ${data.metadata.lastSaved}`);

      return { idRegistry, dailyCounters };
    } catch (error) {
      console.error('❌ 加载ID注册表失败:', error);
      
      // 尝试从备份恢复
      if (this.config.backupEnabled) {
        console.log('🔄 尝试从备份恢复...');
        return await this.restoreFromBackup();
      }
      
      throw error;
    }
  }

  /**
   * 标记数据为脏数据（需要保存）
   */
  markDirty(): void {
    this.isDirty = true;
  }

  /**
   * 检查是否有未保存的数据
   */
  isDirtyData(): boolean {
    return this.isDirty;
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    fileSize: number;
    lastModified: Date;
    backupCount: number;
    autoSaveEnabled: boolean;
    lastSaveTime: Date;
  }> {
    try {
      const filePath = this.getStorageFilePath();
      const stats = await fs.stat(filePath);
      const backupCount = await this.getBackupCount();

      return {
        fileSize: stats.size,
        lastModified: stats.mtime,
        backupCount,
        autoSaveEnabled: this.autoSaveTimer !== null,
        lastSaveTime: this.lastSaveTime
      };
    } catch (error) {
      return {
        fileSize: 0,
        lastModified: new Date(0),
        backupCount: 0,
        autoSaveEnabled: this.autoSaveTimer !== null,
        lastSaveTime: this.lastSaveTime
      };
    }
  }

  /**
   * 手动触发保存
   */
  async forceSave(
    idRegistry: Map<string, GlobalIdMetadata>,
    dailyCounters: Map<string, DailyIdCounter>
  ): Promise<void> {
    await this.saveIdRegistry(idRegistry, dailyCounters);
  }

  /**
   * 停止自动保存
   */
  stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
      console.log('⏹️ ID存储自动保存已停止');
    }
  }

  /**
   * 销毁存储管理器
   */
  async destroy(): Promise<void> {
    this.stopAutoSave();
    console.log('🗑️ ID存储管理器已销毁');
  }

  // 私有方法

  private getStorageFilePath(): string {
    return join(this.config.storageDir, this.config.fileName);
  }

  private getBackupFilePath(index: number): string {
    const baseName = this.config.fileName.replace('.json', '');
    return join(this.config.storageDir, `${baseName}.backup.${index}.json`);
  }

  private async ensureStorageDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.config.storageDir, { recursive: true });
    } catch (error) {
      console.error('❌ 创建存储目录失败:', error);
      throw error;
    }
  }

  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    this.autoSaveTimer = setInterval(() => {
      if (this.isDirty) {
        console.log('⏰ 自动保存触发（数据已变更）');
        // 注意：这里需要外部提供数据，实际实现中会通过回调或事件机制
      }
    }, this.config.autoSaveInterval);

    console.log(`⏰ ID存储自动保存已启动: ${this.config.autoSaveInterval}ms 间隔`);
  }

  private async createBackup(): Promise<void> {
    try {
      const sourceFile = this.getStorageFilePath();
      
      // 检查源文件是否存在
      try {
        await fs.access(sourceFile);
      } catch {
        return; // 源文件不存在，无需备份
      }

      // 轮转备份文件
      for (let i = this.config.maxBackups - 1; i > 0; i--) {
        const currentBackup = this.getBackupFilePath(i);
        const nextBackup = this.getBackupFilePath(i + 1);
        
        try {
          await fs.access(currentBackup);
          await fs.rename(currentBackup, nextBackup);
        } catch {
          // 备份文件不存在，跳过
        }
      }

      // 创建新的备份
      const newBackup = this.getBackupFilePath(1);
      await fs.copyFile(sourceFile, newBackup);
      
      console.log(`📋 已创建备份: ${newBackup}`);
    } catch (error) {
      console.error('❌ 创建备份失败:', error);
      // 备份失败不应该阻止主要操作
    }
  }

  private async getBackupCount(): Promise<number> {
    let count = 0;
    for (let i = 1; i <= this.config.maxBackups; i++) {
      try {
        await fs.access(this.getBackupFilePath(i));
        count++;
      } catch {
        break;
      }
    }
    return count;
  }

  private async restoreFromBackup(): Promise<{
    idRegistry: Map<string, GlobalIdMetadata>;
    dailyCounters: Map<string, DailyIdCounter>;
  }> {
    for (let i = 1; i <= this.config.maxBackups; i++) {
      try {
        const backupFile = this.getBackupFilePath(i);
        await fs.access(backupFile);
        
        const content = await fs.readFile(backupFile, 'utf8');
        const data: IdStorageData = JSON.parse(content);
        
        this.validateStorageData(data);
        
        const idRegistry = new Map(Object.entries(data.idRegistry));
        const dailyCounters = new Map(Object.entries(data.dailyCounters));
        
        console.log(`✅ 从备份 ${i} 恢复成功: ${idRegistry.size} 个ID`);
        return { idRegistry, dailyCounters };
      } catch (error) {
        console.warn(`⚠️ 备份 ${i} 恢复失败:`, error);
        continue;
      }
    }
    
    throw new Error('所有备份文件都无法恢复');
  }

  private validateStorageData(data: IdStorageData): void {
    if (!data || typeof data !== 'object') {
      throw new Error('无效的存储数据格式');
    }
    
    if (!data.idRegistry || typeof data.idRegistry !== 'object') {
      throw new Error('缺少或无效的ID注册表数据');
    }
    
    if (!data.dailyCounters || typeof data.dailyCounters !== 'object') {
      throw new Error('缺少或无效的日计数器数据');
    }
    
    if (!data.metadata || !data.metadata.version) {
      throw new Error('缺少或无效的元数据');
    }
  }

  private async cleanupExpiredData(
    idRegistry: Map<string, GlobalIdMetadata>,
    dailyCounters: Map<string, DailyIdCounter>
  ): Promise<void> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const cutoffDate = thirtyDaysAgo.toISOString().slice(0, 10).replace(/-/g, '');
    
    let cleanedIds = 0;
    let cleanedCounters = 0;
    
    // 清理过期的日计数器
    for (const [date, counter] of dailyCounters.entries()) {
      if (date < cutoffDate) {
        dailyCounters.delete(date);
        cleanedCounters++;
      }
    }
    
    // 清理过期的ID（可选，根据业务需求）
    // 这里暂时保留所有ID，只清理计数器
    
    if (cleanedIds > 0 || cleanedCounters > 0) {
      console.log(`🧹 清理了 ${cutoffDate} 之前的过期数据: ${cleanedIds} 个ID, ${cleanedCounters} 个计数器`);
    }
  }
}

// 导出默认实例
export const idStorageManager = new IdStorageManager();
