/**
 * 遗留存储适配器
 * 提供向后兼容性，使现有代码可以无缝使用新的存储抽象层
 */

import { IMemoryStorage } from '../interfaces/IMemoryStorage';
import { StorageFactory } from '../StorageFactory';

/**
 * 遗留存储适配器类
 * 保持与现有 simple-memory.ts 和 memory-manager.ts 的接口兼容
 */
export class LegacyStorageAdapter {
  private storage: IMemoryStorage | null = null;
  private initialized = false;

  constructor(private storageInstance?: IMemoryStorage) {
    if (storageInstance) {
      this.storage = storageInstance;
      this.initialized = true;
    }
  }

  /**
   * 初始化存储（如果未提供实例）
   */
  private async ensureStorage(): Promise<IMemoryStorage> {
    if (!this.storage) {
      this.storage = await StorageFactory.createFromEnvironment();
      this.initialized = true;
    }
    return this.storage;
  }

  // ==================== 兼容 simple-memory.ts 接口 ====================

  /**
   * 读取 Markdown 文件
   * @param filename 文件名
   * @returns 文件内容
   */
  async readMarkdownFile(filename: string): Promise<string> {
    const storage = await this.ensureStorage();
    try {
      return await storage.read(filename);
    } catch (error) {
      // 保持原有行为：文件不存在返回空字符串
      if (error instanceof Error && 'code' in error && (error as any).code === 'KEY_NOT_FOUND') {
        return '';
      }
      throw error;
    }
  }

  /**
   * 追加内容到 Markdown 文件
   * @param filename 文件名
   * @param content 要追加的内容
   */
  async appendToMarkdownFile(filename: string, content: string): Promise<void> {
    const storage = await this.ensureStorage();
    
    // 读取现有内容
    let existingContent = '';
    try {
      existingContent = await storage.read(filename);
    } catch (error) {
      // 文件不存在，从空内容开始
      if (!(error instanceof Error && 'code' in error && (error as any).code === 'KEY_NOT_FOUND')) {
        throw error;
      }
    }

    // 添加时间戳和分隔线（保持原有格式）
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    const contentWithTimestamp = `\n\n---\n\n## ${timestamp}\n\n${content}`;
    
    const newContent = existingContent + contentWithTimestamp;
    await storage.write(filename, newContent);
  }

  /**
   * 加载记忆上下文
   * @returns 记忆上下文对象
   */
  async loadMemoryContext(): Promise<{
    userProfile: string;
    keyEvents: string;
    dailyInsights: string;
    dailyInsightsToday: string;
  }> {
    const storage = await this.ensureStorage();

    const [userProfile, keyEvents, dailyInsightsArchive, dailyInsightsToday] = await Promise.all([
      this.readMarkdownFile('用户画像.md'),
      this.readMarkdownFile('关键事件.md'),
      this.readMarkdownFile('每日洞察归档.md'),
      this.readMarkdownFile('每日洞察今天.md')
    ]);

    // 合并归档和今天的洞察（归档在前，今天在后）
    const dailyInsights = dailyInsightsArchive;

    return { userProfile, keyEvents, dailyInsights, dailyInsightsToday };
  }

  /**
   * 归档今天的洞察到历史文件
   */
  async archiveTodayInsights(): Promise<void> {
    const storage = await this.ensureStorage();

    try {
      // 读取今天的内容
      const todayContent = await storage.read('每日洞察今天.md');

      if (todayContent.trim() && !todayContent.includes('*这里记录今天的对话洞察*')) {
        // 读取归档文件
        let archiveContent = '';
        try {
          archiveContent = await storage.read('每日洞察归档.md');
        } catch (error) {
          if (!(error instanceof Error && 'code' in error && (error as any).code === 'KEY_NOT_FOUND')) {
            throw error;
          }
        }

        // 追加到归档文件
        const archiveHeader = `\n\n---\n\n## 归档于 ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n\n`;
        const newArchiveContent = archiveContent + archiveHeader + todayContent;
        await storage.write('每日洞察归档.md', newArchiveContent);

        // 清空今天的文件
        await storage.write('每日洞察今天.md', '# 每日洞察（今天）\n\n*这里记录今天的对话洞察*');
      }
    } catch (error) {
      console.error('归档失败:', error);
      throw error;
    }
  }

  /**
   * 保存对话到历史记录
   * @param userMessage 用户消息
   * @param assistantMessage 助手消息
   */
  async saveConversationToHistory(userMessage: string, assistantMessage: string): Promise<void> {
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    const conversationEntry = `
### ${timestamp}

**用户**: ${userMessage}

**助手**: ${assistantMessage}

---`;

    await this.appendToMarkdownFile('对话历史.md', conversationEntry);
  }

  // ==================== 兼容 memory-manager.ts 接口 ====================

  /**
   * 读取记忆文件
   * @param filename 文件名
   * @returns 文件内容
   */
  async readMemoryFile(filename: string): Promise<string> {
    return this.readMarkdownFile(filename);
  }

  /**
   * 写入记忆文件
   * @param filename 文件名
   * @param content 文件内容
   */
  async writeMemoryFile(filename: string, content: string): Promise<void> {
    const storage = await this.ensureStorage();
    await storage.write(filename, content);
  }

  /**
   * 读取提示词文件
   * @param filename 文件名
   * @returns 文件内容
   */
  async readPromptFile(filename: string): Promise<string> {
    const storage = await this.ensureStorage();
    const promptPath = `prompts/${filename}`;
    try {
      return await storage.read(promptPath);
    } catch (error) {
      if (error instanceof Error && 'code' in error && (error as any).code === 'KEY_NOT_FOUND') {
        return '';
      }
      throw error;
    }
  }

  /**
   * 写入提示词文件
   * @param filename 文件名
   * @param content 文件内容
   */
  async writePromptFile(filename: string, content: string): Promise<void> {
    const storage = await this.ensureStorage();
    const promptPath = `prompts/${filename}`;
    await storage.write(promptPath, content);
  }

  /**
   * 加载完整的记忆上下文（五文件系统）
   */
  async loadFullMemoryContext(): Promise<{
    xiaoJingPersona: string;
    userProfile: string;
    mentalElements: string;
    keyEvents: string;
    dailyInsightCold: string;
    dailyInsightHot: string;
    dialogueHistory: string;
  }> {
    const [
      xiaoJingPersona,
      userProfile,
      mentalElements,
      keyEvents,
      dailyInsightCold,
      dailyInsightHot,
      dialogueHistory
    ] = await Promise.all([
      this.readMemoryFile('小镜人设提示词.md'),
      this.readMemoryFile('用户画像.md'),
      this.readMemoryFile('心智要素结构.md'),
      this.readMemoryFile('关键事件.md'),
      this.readMemoryFile('每日洞察归档.md'),
      this.readMemoryFile('每日洞察今天.md'),
      this.readMemoryFile('对话历史.md')
    ]);

    return {
      xiaoJingPersona,
      userProfile,
      mentalElements,
      keyEvents,
      dailyInsightCold,
      dailyInsightHot,
      dialogueHistory
    };
  }

  /**
   * 加载提示词上下文
   */
  async loadPromptContext(): Promise<{
    systemPrompt: string;
    dailyChatPrompt: string;
    dailyInsightPrompt: string;
    deepRefinementPrompt: string;
  }> {
    const [
      systemPrompt,
      dailyChatPrompt,
      dailyInsightPrompt,
      deepRefinementPrompt
    ] = await Promise.all([
      this.readPromptFile('系统提示词.md'),
      this.readPromptFile('日常对话模式提示词.md'),
      this.readPromptFile('每日洞察模式提示词.md'),
      this.readPromptFile('深度精炼模式提示词.md')
    ]);

    return {
      systemPrompt,
      dailyChatPrompt,
      dailyInsightPrompt,
      deepRefinementPrompt
    };
  }

  // ==================== 新增便捷方法 ====================

  /**
   * 获取底层存储实例
   */
  async getStorageInstance(): Promise<IMemoryStorage> {
    return this.ensureStorage();
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats() {
    const storage = await this.ensureStorage();
    return storage.getStats();
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(filename: string): Promise<boolean> {
    const storage = await this.ensureStorage();
    return storage.exists(filename);
  }

  /**
   * 列出所有文件
   */
  async listFiles(): Promise<string[]> {
    const storage = await this.ensureStorage();
    return storage.list();
  }

  /**
   * 搜索文件
   */
  async searchFiles(pattern: string): Promise<string[]> {
    const storage = await this.ensureStorage();
    return storage.search(pattern);
  }

  /**
   * 批量读取文件
   */
  async readMultipleFiles(filenames: string[]): Promise<Record<string, string>> {
    const storage = await this.ensureStorage();
    return storage.readBatch(filenames);
  }

  /**
   * 批量写入文件
   */
  async writeMultipleFiles(files: Record<string, string>): Promise<void> {
    const storage = await this.ensureStorage();
    return storage.writeBatch(files);
  }

  /**
   * 关闭存储
   */
  async close(): Promise<void> {
    if (this.storage) {
      await this.storage.close();
      this.storage = null;
      this.initialized = false;
    }
  }
}

// ==================== 全局实例 ====================

let globalAdapter: LegacyStorageAdapter | null = null;

/**
 * 获取全局存储适配器实例
 */
export async function getGlobalStorageAdapter(): Promise<LegacyStorageAdapter> {
  if (!globalAdapter) {
    globalAdapter = new LegacyStorageAdapter();
  }
  return globalAdapter;
}

/**
 * 设置全局存储适配器实例
 */
export function setGlobalStorageAdapter(adapter: LegacyStorageAdapter): void {
  globalAdapter = adapter;
}

// ==================== 兼容性导出 ====================

/**
 * 兼容性函数：读取 Markdown 文件
 */
export async function readMarkdownFile(filename: string): Promise<string> {
  const adapter = await getGlobalStorageAdapter();
  return adapter.readMarkdownFile(filename);
}

/**
 * 兼容性函数：追加到 Markdown 文件
 */
export async function appendToMarkdownFile(filename: string, content: string): Promise<void> {
  const adapter = await getGlobalStorageAdapter();
  return adapter.appendToMarkdownFile(filename, content);
}

/**
 * 兼容性函数：加载记忆上下文
 */
export async function loadMemoryContext(): Promise<{
  userProfile: string;
  keyEvents: string;
  dailyInsights: string;
  dailyInsightsToday: string;
}> {
  const adapter = await getGlobalStorageAdapter();
  return adapter.loadMemoryContext();
}

/**
 * 兼容性函数：归档今天的洞察
 */
export async function archiveTodayInsights(): Promise<void> {
  const adapter = await getGlobalStorageAdapter();
  return adapter.archiveTodayInsights();
}

/**
 * 兼容性函数：保存对话到历史记录
 */
export async function saveConversationToHistory(userMessage: string, assistantMessage: string): Promise<void> {
  const adapter = await getGlobalStorageAdapter();
  return adapter.saveConversationToHistory(userMessage, assistantMessage);
}

/**
 * 兼容性函数：读取记忆文件
 */
export async function readMemoryFile(filename: string): Promise<string> {
  const adapter = await getGlobalStorageAdapter();
  return adapter.readMemoryFile(filename);
}

/**
 * 兼容性函数：写入记忆文件
 */
export async function writeMemoryFile(filename: string, content: string): Promise<void> {
  const adapter = await getGlobalStorageAdapter();
  return adapter.writeMemoryFile(filename, content);
}

export default LegacyStorageAdapter;
