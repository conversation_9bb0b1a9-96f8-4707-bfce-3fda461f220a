/**
 * 内存存储实现
 * 基于内存的存储后端，支持可选的持久化
 */

import {
  IMemoryStorage,
  StorageMetadata,
  StorageStats,
  StorageError,
  StorageErrorCodes,
  MemoryStorageConfig
} from '../interfaces/IMemoryStorage';

interface MemoryRecord {
  content: string;
  metadata: StorageMetadata;
}

export class MemoryStorage implements IMemoryStorage {
  private config: MemoryStorageConfig;
  private data: Map<string, MemoryRecord> = new Map();
  private initialized = false;
  private currentSize = 0;

  constructor(config: MemoryStorageConfig) {
    this.config = {
      maxSize: 50 * 1024 * 1024, // 50MB 默认
      persistent: false,
      lruSize: 1000,
      ...config
    };
  }

  // ==================== 生命周期 ====================

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 如果启用持久化，尝试从 localStorage 加载数据
      if (this.config.persistent && typeof localStorage !== 'undefined') {
        await this.loadFromPersistentStorage();
      }

      this.initialized = true;
      console.log(`✅ MemoryStorage initialized (max: ${this.formatSize(this.config.maxSize!)})`);
    } catch (error) {
      throw new StorageError(
        `Failed to initialize MemoryStorage: ${error instanceof Error ? error.message : String(error)}`,
        StorageErrorCodes.INITIALIZATION_FAILED,
        error instanceof Error ? error : undefined
      );
    }
  }

  async close(): Promise<void> {
    // 如果启用持久化，保存数据到 localStorage
    if (this.config.persistent && typeof localStorage !== 'undefined') {
      await this.saveToPersistentStorage();
    }

    this.data.clear();
    this.currentSize = 0;
    this.initialized = false;
    console.log('📦 MemoryStorage closed');
  }

  // ==================== 基础操作 ====================

  async read(key: string): Promise<string> {
    this.ensureInitialized();
    this.validateKey(key);

    const record = this.data.get(key);
    if (!record) {
      throw new StorageError(
        `Key not found: ${key}`,
        StorageErrorCodes.KEY_NOT_FOUND
      );
    }

    // 更新访问时间
    record.metadata.custom = {
      ...record.metadata.custom,
      lastAccessed: new Date()
    };

    return record.content;
  }

  async write(key: string, content: string): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    const contentSize = new Blob([content]).size;
    
    // 检查是否超出最大存储限制
    const existingRecord = this.data.get(key);
    const existingSize = existingRecord ? existingRecord.metadata.size : 0;
    const newTotalSize = this.currentSize - existingSize + contentSize;

    if (newTotalSize > this.config.maxSize!) {
      // 尝试清理空间
      await this.cleanup();
      
      // 重新检查
      const currentTotalSize = this.currentSize - existingSize + contentSize;
      if (currentTotalSize > this.config.maxSize!) {
        throw new StorageError(
          `Storage quota exceeded. Required: ${this.formatSize(contentSize)}, Available: ${this.formatSize(this.config.maxSize! - this.currentSize)}`,
          StorageErrorCodes.QUOTA_EXCEEDED
        );
      }
    }

    const now = new Date();
    const metadata: StorageMetadata = {
      size: contentSize,
      createdAt: existingRecord?.metadata.createdAt || now,
      updatedAt: now,
      version: (existingRecord?.metadata.version || 0) + 1,
      tags: existingRecord?.metadata.tags || [],
      checksum: this.calculateChecksum(content),
      contentType: this.detectContentType(key),
      custom: existingRecord?.metadata.custom || {}
    };

    const record: MemoryRecord = { content, metadata };
    this.data.set(key, record);
    
    // 更新总大小
    this.currentSize = this.currentSize - existingSize + contentSize;

    console.log(`💾 Written key: ${key} (${this.formatSize(contentSize)})`);
  }

  async update(key: string, content: string): Promise<void> {
    // 检查键是否存在
    if (!(await this.exists(key))) {
      throw new StorageError(
        `Key not found for update: ${key}`,
        StorageErrorCodes.KEY_NOT_FOUND
      );
    }

    await this.write(key, content);
  }

  async delete(key: string): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    const record = this.data.get(key);
    if (!record) {
      throw new StorageError(
        `Key not found: ${key}`,
        StorageErrorCodes.KEY_NOT_FOUND
      );
    }

    this.data.delete(key);
    this.currentSize -= record.metadata.size;

    console.log(`🗑️ Deleted key: ${key}`);
  }

  // ==================== 批量操作 ====================

  async readBatch(keys: string[]): Promise<Record<string, string>> {
    const results: Record<string, string> = {};

    for (const key of keys) {
      try {
        results[key] = await this.read(key);
      } catch (error) {
        // 忽略不存在的键
        if (!(error instanceof Error && 'code' in error && (error as any).code === StorageErrorCodes.KEY_NOT_FOUND)) {
          throw error;
        }
      }
    }

    return results;
  }

  async writeBatch(entries: Record<string, string>): Promise<void> {
    // 计算总大小
    let totalSize = 0;
    for (const content of Object.values(entries)) {
      totalSize += new Blob([content]).size;
    }

    // 检查配额
    if (this.currentSize + totalSize > this.config.maxSize!) {
      await this.cleanup();
      
      if (this.currentSize + totalSize > this.config.maxSize!) {
        throw new StorageError(
          `Batch write would exceed storage quota`,
          StorageErrorCodes.QUOTA_EXCEEDED
        );
      }
    }

    // 批量写入
    for (const [key, content] of Object.entries(entries)) {
      await this.write(key, content);
    }

    console.log(`💾 Batch written ${Object.keys(entries).length} keys`);
  }

  // ==================== 查询操作 ====================

  async list(): Promise<string[]> {
    this.ensureInitialized();
    return Array.from(this.data.keys());
  }

  async exists(key: string): Promise<boolean> {
    this.ensureInitialized();
    this.validateKey(key);
    return this.data.has(key);
  }

  async search(pattern: string): Promise<string[]> {
    const allKeys = await this.list();
    const regex = this.patternToRegex(pattern);
    return allKeys.filter(key => regex.test(key));
  }

  // ==================== 元数据操作 ====================

  async getMetadata(key: string): Promise<StorageMetadata> {
    this.ensureInitialized();
    this.validateKey(key);

    const record = this.data.get(key);
    if (!record) {
      throw new StorageError(
        `Key not found: ${key}`,
        StorageErrorCodes.KEY_NOT_FOUND
      );
    }

    return { ...record.metadata };
  }

  async setMetadata(key: string, metadata: Partial<StorageMetadata>): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    const record = this.data.get(key);
    if (!record) {
      throw new StorageError(
        `Key not found: ${key}`,
        StorageErrorCodes.KEY_NOT_FOUND
      );
    }

    record.metadata = { ...record.metadata, ...metadata };
  }

  // ==================== 统计信息 ====================

  async getStats(): Promise<StorageStats> {
    this.ensureInitialized();

    let lastAccessed = new Date(0);

    for (const record of this.data.values()) {
      const accessTime = record.metadata.custom?.lastAccessed || record.metadata.updatedAt;
      if (accessTime > lastAccessed) {
        lastAccessed = accessTime;
      }
    }

    const usagePercentage = (this.currentSize / this.config.maxSize!) * 100;

    return {
      totalKeys: this.data.size,
      totalSize: this.currentSize,
      lastAccessed,
      storageType: 'memory',
      provider: 'MemoryStorage',
      availableSpace: this.config.maxSize! - this.currentSize,
      usagePercentage
    };
  }

  // ==================== 私有方法 ====================

  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new StorageError(
        'Storage not initialized',
        StorageErrorCodes.INITIALIZATION_FAILED
      );
    }
  }

  private validateKey(key: string): void {
    if (!key || typeof key !== 'string') {
      throw new StorageError(
        'Invalid key: must be a non-empty string',
        StorageErrorCodes.INVALID_KEY
      );
    }
  }

  private calculateChecksum(content: string): string {
    // 简单的哈希实现
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }

  private detectContentType(key: string): string {
    const ext = key.split('.').pop()?.toLowerCase();
    const contentTypes: Record<string, string> = {
      'md': 'text/markdown',
      'txt': 'text/plain',
      'json': 'application/json',
      'yaml': 'text/yaml',
      'yml': 'text/yaml'
    };
    return contentTypes[ext || ''] || 'text/plain';
  }

  private patternToRegex(pattern: string): RegExp {
    const escaped = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = escaped.replace(/\\\*/g, '.*').replace(/\\\?/g, '.');
    return new RegExp(`^${regex}$`, 'i');
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  private async cleanup(): Promise<void> {
    // 简单的 LRU 清理策略
    const entries = Array.from(this.data.entries());
    
    // 按最后访问时间排序
    entries.sort((a, b) => {
      const aTime = a[1].metadata.custom?.lastAccessed || a[1].metadata.updatedAt;
      const bTime = b[1].metadata.custom?.lastAccessed || b[1].metadata.updatedAt;
      return aTime.getTime() - bTime.getTime();
    });

    // 删除最旧的 10% 数据
    const deleteCount = Math.max(1, Math.floor(entries.length * 0.1));
    
    for (let i = 0; i < deleteCount; i++) {
      const [key, record] = entries[i];
      this.data.delete(key);
      this.currentSize -= record.metadata.size;
    }

    console.log(`🧹 Cleaned up ${deleteCount} old entries`);
  }

  private async loadFromPersistentStorage(): Promise<void> {
    try {
      const storageKey = `memory_storage_${this.config.provider}`;
      const serializedData = localStorage.getItem(storageKey);
      
      if (serializedData) {
        const data = JSON.parse(serializedData);
        
        for (const [key, record] of Object.entries(data)) {
          const typedRecord = record as MemoryRecord;
          // 恢复日期对象
          typedRecord.metadata.createdAt = new Date(typedRecord.metadata.createdAt);
          typedRecord.metadata.updatedAt = new Date(typedRecord.metadata.updatedAt);
          
          this.data.set(key, typedRecord);
          this.currentSize += typedRecord.metadata.size;
        }
        
        console.log(`📥 Loaded ${this.data.size} entries from persistent storage`);
      }
    } catch (error) {
      console.warn('Failed to load from persistent storage:', error);
    }
  }

  private async saveToPersistentStorage(): Promise<void> {
    try {
      const storageKey = `memory_storage_${this.config.provider}`;
      const dataObject = Object.fromEntries(this.data);
      const serializedData = JSON.stringify(dataObject);
      
      localStorage.setItem(storageKey, serializedData);
      console.log(`📤 Saved ${this.data.size} entries to persistent storage`);
    } catch (error) {
      console.warn('Failed to save to persistent storage:', error);
    }
  }
}
