/**
 * IndexedDB 存储实现
 * 基于浏览器 IndexedDB 的存储后端
 */

import {
  IMemoryStorage,
  StorageMetadata,
  StorageStats,
  StorageError,
  StorageErrorCodes,
  IndexedDBStorageConfig
} from '../interfaces/IMemoryStorage';

interface StorageRecord {
  key: string;
  content: string;
  metadata: StorageMetadata;
}

export class IndexedDBStorage implements IMemoryStorage {
  private config: IndexedDBStorageConfig;
  private db: IDBDatabase | null = null;
  private initialized = false;

  constructor(config: IndexedDBStorageConfig) {
    this.config = {
      storeName: 'storage',
      indexes: [],
      ...config
    };

    // 检查浏览器环境
    if (typeof window === 'undefined' || typeof indexedDB === 'undefined') {
      throw new StorageError(
        'IndexedDB is not available in this environment',
        StorageErrorCodes.INITIALIZATION_FAILED
      );
    }
  }

  // ==================== 生命周期 ====================

  async initialize(): Promise<void> {
    if (this.initialized) return;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.dbName, this.config.version);

      request.onerror = () => {
        reject(new StorageError(
          `Failed to open IndexedDB: ${request.error?.message}`,
          StorageErrorCodes.INITIALIZATION_FAILED
        ));
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.initialized = true;
        console.log(`✅ IndexedDBStorage initialized: ${this.config.dbName}`);
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        this.createObjectStores(db);
      };
    });
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
    this.initialized = false;
    console.log('📦 IndexedDBStorage closed');
  }

  // ==================== 基础操作 ====================

  async read(key: string): Promise<string> {
    this.ensureInitialized();
    this.validateKey(key);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readonly');
      const store = transaction.objectStore(this.config.storeName!);
      const request = store.get(key);

      request.onerror = () => {
        reject(new StorageError(
          `Failed to read key: ${key}`,
          StorageErrorCodes.READ_ERROR
        ));
      };

      request.onsuccess = () => {
        const record = request.result as StorageRecord;
        if (!record) {
          reject(new StorageError(
            `Key not found: ${key}`,
            StorageErrorCodes.KEY_NOT_FOUND
          ));
          return;
        }

        // 更新访问时间
        this.updateAccessTime(key);
        resolve(record.content);
      };
    });
  }

  async write(key: string, content: string): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readwrite');
      const store = transaction.objectStore(this.config.storeName!);

      const now = new Date();
      const metadata: StorageMetadata = {
        size: new Blob([content]).size,
        createdAt: now,
        updatedAt: now,
        version: 1,
        tags: [],
        checksum: this.calculateChecksum(content),
        contentType: this.detectContentType(key)
      };

      // 检查是否已存在，更新版本号
      const getRequest = store.get(key);
      getRequest.onsuccess = () => {
        const existingRecord = getRequest.result as StorageRecord;
        if (existingRecord) {
          metadata.createdAt = existingRecord.metadata.createdAt;
          metadata.version = existingRecord.metadata.version + 1;
        }

        const record: StorageRecord = { key, content, metadata };
        const putRequest = store.put(record);

        putRequest.onerror = () => {
          reject(new StorageError(
            `Failed to write key: ${key}`,
            StorageErrorCodes.WRITE_ERROR
          ));
        };

        putRequest.onsuccess = () => {
          console.log(`💾 Written key: ${key} (${metadata.size} bytes)`);
          resolve();
        };
      };

      getRequest.onerror = () => {
        reject(new StorageError(
          `Failed to check existing key: ${key}`,
          StorageErrorCodes.READ_ERROR
        ));
      };
    });
  }

  async update(key: string, content: string): Promise<void> {
    // 检查键是否存在
    if (!(await this.exists(key))) {
      throw new StorageError(
        `Key not found for update: ${key}`,
        StorageErrorCodes.KEY_NOT_FOUND
      );
    }

    await this.write(key, content);
  }

  async delete(key: string): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readwrite');
      const store = transaction.objectStore(this.config.storeName!);
      const request = store.delete(key);

      request.onerror = () => {
        reject(new StorageError(
          `Failed to delete key: ${key}`,
          StorageErrorCodes.DELETE_ERROR
        ));
      };

      request.onsuccess = () => {
        console.log(`🗑️ Deleted key: ${key}`);
        resolve();
      };
    });
  }

  // ==================== 批量操作 ====================

  async readBatch(keys: string[]): Promise<Record<string, string>> {
    const results: Record<string, string> = {};

    for (const key of keys) {
      try {
        results[key] = await this.read(key);
      } catch (error) {
        // 忽略不存在的键
        if (!(error instanceof Error && 'code' in error && (error as any).code === StorageErrorCodes.KEY_NOT_FOUND)) {
          throw error;
        }
      }
    }

    return results;
  }

  async writeBatch(entries: Record<string, string>): Promise<void> {
    this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readwrite');
      const store = transaction.objectStore(this.config.storeName!);

      let completed = 0;
      const total = Object.keys(entries).length;

      if (total === 0) {
        resolve();
        return;
      }

      transaction.oncomplete = () => {
        console.log(`💾 Batch written ${total} keys`);
        resolve();
      };

      transaction.onerror = () => {
        reject(new StorageError(
          'Failed to write batch',
          StorageErrorCodes.WRITE_ERROR
        ));
      };

      for (const [key, content] of Object.entries(entries)) {
        const now = new Date();
        const metadata: StorageMetadata = {
          size: new Blob([content]).size,
          createdAt: now,
          updatedAt: now,
          version: 1,
          tags: [],
          checksum: this.calculateChecksum(content),
          contentType: this.detectContentType(key)
        };

        const record: StorageRecord = { key, content, metadata };
        store.put(record);
      }
    });
  }

  // ==================== 查询操作 ====================

  async list(): Promise<string[]> {
    this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readonly');
      const store = transaction.objectStore(this.config.storeName!);
      const request = store.getAllKeys();

      request.onerror = () => {
        reject(new StorageError(
          'Failed to list keys',
          StorageErrorCodes.READ_ERROR
        ));
      };

      request.onsuccess = () => {
        resolve(request.result as string[]);
      };
    });
  }

  async exists(key: string): Promise<boolean> {
    this.ensureInitialized();
    this.validateKey(key);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readonly');
      const store = transaction.objectStore(this.config.storeName!);
      const request = store.count(key);

      request.onerror = () => {
        reject(new StorageError(
          `Failed to check key existence: ${key}`,
          StorageErrorCodes.READ_ERROR
        ));
      };

      request.onsuccess = () => {
        resolve(request.result > 0);
      };
    });
  }

  async search(pattern: string): Promise<string[]> {
    const allKeys = await this.list();
    const regex = this.patternToRegex(pattern);
    return allKeys.filter(key => regex.test(key));
  }

  // ==================== 元数据操作 ====================

  async getMetadata(key: string): Promise<StorageMetadata> {
    this.ensureInitialized();
    this.validateKey(key);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readonly');
      const store = transaction.objectStore(this.config.storeName!);
      const request = store.get(key);

      request.onerror = () => {
        reject(new StorageError(
          `Failed to get metadata for key: ${key}`,
          StorageErrorCodes.READ_ERROR
        ));
      };

      request.onsuccess = () => {
        const record = request.result as StorageRecord;
        if (!record) {
          reject(new StorageError(
            `Key not found: ${key}`,
            StorageErrorCodes.KEY_NOT_FOUND
          ));
          return;
        }

        resolve(record.metadata);
      };
    });
  }

  async setMetadata(key: string, metadata: Partial<StorageMetadata>): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readwrite');
      const store = transaction.objectStore(this.config.storeName!);
      const getRequest = store.get(key);

      getRequest.onerror = () => {
        reject(new StorageError(
          `Failed to get record for metadata update: ${key}`,
          StorageErrorCodes.READ_ERROR
        ));
      };

      getRequest.onsuccess = () => {
        const record = getRequest.result as StorageRecord;
        if (!record) {
          reject(new StorageError(
            `Key not found: ${key}`,
            StorageErrorCodes.KEY_NOT_FOUND
          ));
          return;
        }

        record.metadata = { ...record.metadata, ...metadata };
        const putRequest = store.put(record);

        putRequest.onerror = () => {
          reject(new StorageError(
            `Failed to update metadata for key: ${key}`,
            StorageErrorCodes.WRITE_ERROR
          ));
        };

        putRequest.onsuccess = () => {
          resolve();
        };
      };
    });
  }

  // ==================== 统计信息 ====================

  async getStats(): Promise<StorageStats> {
    this.ensureInitialized();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.storeName!], 'readonly');
      const store = transaction.objectStore(this.config.storeName!);
      const request = store.getAll();

      request.onerror = () => {
        reject(new StorageError(
          'Failed to get storage stats',
          StorageErrorCodes.READ_ERROR
        ));
      };

      request.onsuccess = () => {
        const records = request.result as StorageRecord[];
        
        let totalSize = 0;
        let lastAccessed = new Date(0);

        for (const record of records) {
          totalSize += record.metadata.size;
          
          const accessTime = record.metadata.custom?.lastAccessed || record.metadata.updatedAt;
          if (accessTime > lastAccessed) {
            lastAccessed = accessTime;
          }
        }

        // 估算可用空间（IndexedDB 通常有配额限制）
        const estimatedQuota = 50 * 1024 * 1024; // 50MB 估算
        const usagePercentage = (totalSize / estimatedQuota) * 100;

        resolve({
          totalKeys: records.length,
          totalSize,
          lastAccessed,
          storageType: 'indexeddb',
          provider: 'IndexedDBStorage',
          availableSpace: estimatedQuota - totalSize,
          usagePercentage: Math.min(usagePercentage, 100)
        });
      };
    });
  }

  // ==================== 私有方法 ====================

  private ensureInitialized(): void {
    if (!this.initialized || !this.db) {
      throw new StorageError(
        'Storage not initialized',
        StorageErrorCodes.INITIALIZATION_FAILED
      );
    }
  }

  private validateKey(key: string): void {
    if (!key || typeof key !== 'string') {
      throw new StorageError(
        'Invalid key: must be a non-empty string',
        StorageErrorCodes.INVALID_KEY
      );
    }
  }

  private createObjectStores(db: IDBDatabase): void {
    // 创建主存储对象存储
    if (!db.objectStoreNames.contains(this.config.storeName!)) {
      const store = db.createObjectStore(this.config.storeName!, { keyPath: 'key' });
      
      // 创建索引
      for (const indexConfig of this.config.indexes || []) {
        store.createIndex(
          indexConfig.name,
          indexConfig.keyPath,
          {
            unique: indexConfig.unique || false,
            multiEntry: indexConfig.multiEntry || false
          }
        );
      }
    }
  }

  private calculateChecksum(content: string): string {
    // 简单的哈希实现（生产环境建议使用更强的哈希算法）
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  private detectContentType(key: string): string {
    const ext = key.split('.').pop()?.toLowerCase();
    const contentTypes: Record<string, string> = {
      'md': 'text/markdown',
      'txt': 'text/plain',
      'json': 'application/json',
      'yaml': 'text/yaml',
      'yml': 'text/yaml'
    };
    return contentTypes[ext || ''] || 'text/plain';
  }

  private patternToRegex(pattern: string): RegExp {
    const escaped = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = escaped.replace(/\\\*/g, '.*').replace(/\\\?/g, '.');
    return new RegExp(`^${regex}$`, 'i');
  }

  private async updateAccessTime(key: string): Promise<void> {
    try {
      await this.setMetadata(key, {
        custom: { lastAccessed: new Date() }
      });
    } catch (error) {
      // 忽略访问时间更新失败
      console.warn(`Failed to update access time for key: ${key}`, error);
    }
  }
}
