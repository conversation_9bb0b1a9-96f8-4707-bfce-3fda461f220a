/**
 * 文件系统存储实现
 * 基于 Node.js 文件系统的存储后端
 */

import { promises as fs } from 'fs';
import path from 'path';
import crypto from 'crypto';
import {
  IMemoryStorage,
  StorageMetadata,
  StorageStats,
  StorageError,
  StorageErrorCodes,
  FileSystemStorageConfig
} from '../interfaces/IMemoryStorage';

export class FileSystemStorage implements IMemoryStorage {
  private config: FileSystemStorageConfig;
  private metadataCache: Map<string, StorageMetadata> = new Map();
  private initialized = false;

  constructor(config: FileSystemStorageConfig) {
    this.config = {
      encoding: 'utf-8',
      fileMode: 0o644,
      dirMode: 0o755,
      createDirs: true,
      ...config
    };
  }

  // ==================== 生命周期 ====================

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 确保基础目录存在
      if (this.config.createDirs) {
        await this.ensureDirectory(this.config.basePath);
      }

      // 验证目录访问权限
      await fs.access(this.config.basePath, fs.constants.R_OK | fs.constants.W_OK);

      // 加载元数据缓存
      await this.loadMetadataCache();

      this.initialized = true;
      console.log(`✅ FileSystemStorage initialized at: ${this.config.basePath}`);
    } catch (error) {
      throw new StorageError(
        `Failed to initialize FileSystemStorage: ${error instanceof Error ? error.message : String(error)}`,
        StorageErrorCodes.INITIALIZATION_FAILED,
        error instanceof Error ? error : undefined
      );
    }
  }

  async close(): Promise<void> {
    // 保存元数据缓存
    await this.saveMetadataCache();
    this.metadataCache.clear();
    this.initialized = false;
    console.log('📦 FileSystemStorage closed');
  }

  // ==================== 基础操作 ====================

  async read(key: string): Promise<string> {
    this.ensureInitialized();
    this.validateKey(key);

    const filePath = this.getFilePath(key);

    try {
      const content = await fs.readFile(filePath, this.config.encoding!);
      
      // 更新访问时间
      await this.updateMetadata(key, { 
        custom: { ...this.metadataCache.get(key)?.custom, lastAccessed: new Date() }
      });

      return content;
    } catch (error) {
      if (error instanceof Error && 'code' in error && (error as any).code === 'ENOENT') {
        throw new StorageError(
          `Key not found: ${key}`,
          StorageErrorCodes.KEY_NOT_FOUND
        );
      }
      throw new StorageError(
        `Failed to read key: ${key}`,
        StorageErrorCodes.READ_ERROR,
        error instanceof Error ? error : undefined
      );
    }
  }

  async write(key: string, content: string): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    const filePath = this.getFilePath(key);
    const directory = path.dirname(filePath);

    try {
      // 确保目录存在
      await this.ensureDirectory(directory);

      // 写入文件
      await fs.writeFile(filePath, content, this.config.encoding!);

      // 设置文件权限
      if (this.config.fileMode) {
        await fs.chmod(filePath, this.config.fileMode);
      }

      // 更新元数据
      const stats = await fs.stat(filePath);
      const checksum = this.calculateChecksum(content);
      
      await this.updateMetadata(key, {
        size: stats.size,
        updatedAt: new Date(),
        checksum,
        contentType: this.detectContentType(key),
        version: (this.metadataCache.get(key)?.version || 0) + 1
      });

      console.log(`💾 Written key: ${key} (${stats.size} bytes)`);
    } catch (error) {
      throw new StorageError(
        `Failed to write key: ${key}`,
        StorageErrorCodes.WRITE_ERROR,
        error instanceof Error ? error : undefined
      );
    }
  }

  async update(key: string, content: string): Promise<void> {
    // 检查键是否存在
    if (!(await this.exists(key))) {
      throw new StorageError(
        `Key not found for update: ${key}`,
        StorageErrorCodes.KEY_NOT_FOUND
      );
    }

    await this.write(key, content);
  }

  async delete(key: string): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    const filePath = this.getFilePath(key);

    try {
      await fs.unlink(filePath);
      this.metadataCache.delete(key);
      console.log(`🗑️ Deleted key: ${key}`);
    } catch (error) {
      if (error instanceof Error && 'code' in error && (error as any).code === 'ENOENT') {
        throw new StorageError(
          `Key not found: ${key}`,
          StorageErrorCodes.KEY_NOT_FOUND
        );
      }
      throw new StorageError(
        `Failed to delete key: ${key}`,
        StorageErrorCodes.DELETE_ERROR,
        error instanceof Error ? error : undefined
      );
    }
  }

  // ==================== 批量操作 ====================

  async readBatch(keys: string[]): Promise<Record<string, string>> {
    const results: Record<string, string> = {};
    
    await Promise.all(
      keys.map(async (key) => {
        try {
          results[key] = await this.read(key);
        } catch (error) {
          // 忽略不存在的键，继续处理其他键
          if (!(error instanceof Error && 'code' in error && (error as any).code === StorageErrorCodes.KEY_NOT_FOUND)) {
            throw error;
          }
        }
      })
    );

    return results;
  }

  async writeBatch(entries: Record<string, string>): Promise<void> {
    const writePromises = Object.entries(entries).map(([key, content]) =>
      this.write(key, content)
    );

    try {
      await Promise.all(writePromises);
      console.log(`💾 Batch written ${Object.keys(entries).length} keys`);
    } catch (error) {
      throw new StorageError(
        'Failed to write batch',
        StorageErrorCodes.WRITE_ERROR,
        error instanceof Error ? error : undefined
      );
    }
  }

  // ==================== 查询操作 ====================

  async list(): Promise<string[]> {
    this.ensureInitialized();

    try {
      const keys: string[] = [];
      await this.walkDirectory(this.config.basePath, (filePath) => {
        const relativePath = path.relative(this.config.basePath, filePath);
        keys.push(relativePath);
      });
      return keys;
    } catch (error) {
      throw new StorageError(
        'Failed to list keys',
        StorageErrorCodes.READ_ERROR,
        error instanceof Error ? error : undefined
      );
    }
  }

  async exists(key: string): Promise<boolean> {
    this.ensureInitialized();
    this.validateKey(key);

    const filePath = this.getFilePath(key);

    try {
      await fs.access(filePath, fs.constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  async search(pattern: string): Promise<string[]> {
    const allKeys = await this.list();
    const regex = this.patternToRegex(pattern);
    return allKeys.filter(key => regex.test(key));
  }

  // ==================== 元数据操作 ====================

  async getMetadata(key: string): Promise<StorageMetadata> {
    this.ensureInitialized();
    this.validateKey(key);

    // 先检查缓存
    if (this.metadataCache.has(key)) {
      return this.metadataCache.get(key)!;
    }

    // 如果缓存中没有，从文件系统获取
    const filePath = this.getFilePath(key);

    try {
      const stats = await fs.stat(filePath);
      const content = await fs.readFile(filePath, this.config.encoding!);
      
      const metadata: StorageMetadata = {
        size: stats.size,
        createdAt: stats.birthtime,
        updatedAt: stats.mtime,
        version: 1,
        tags: [],
        checksum: this.calculateChecksum(content),
        contentType: this.detectContentType(key)
      };

      this.metadataCache.set(key, metadata);
      return metadata;
    } catch (error) {
      if (error instanceof Error && 'code' in error && (error as any).code === 'ENOENT') {
        throw new StorageError(
          `Key not found: ${key}`,
          StorageErrorCodes.KEY_NOT_FOUND
        );
      }
      throw error;
    }
  }

  async setMetadata(key: string, metadata: Partial<StorageMetadata>): Promise<void> {
    this.ensureInitialized();
    this.validateKey(key);

    const currentMetadata = await this.getMetadata(key);
    const updatedMetadata = { ...currentMetadata, ...metadata };
    
    this.metadataCache.set(key, updatedMetadata);
  }

  // ==================== 统计信息 ====================

  async getStats(): Promise<StorageStats> {
    this.ensureInitialized();

    try {
      const keys = await this.list();
      let totalSize = 0;
      let lastAccessed = new Date(0);

      for (const key of keys) {
        const metadata = await this.getMetadata(key);
        totalSize += metadata.size;
        
        const accessTime = metadata.custom?.lastAccessed || metadata.updatedAt;
        if (accessTime > lastAccessed) {
          lastAccessed = accessTime;
        }
      }

      // 获取可用空间（简化实现）
      const stats = await fs.stat(this.config.basePath);
      const availableSpace = -1; // 文件系统通常没有固定限制

      return {
        totalKeys: keys.length,
        totalSize,
        lastAccessed,
        storageType: 'filesystem',
        provider: 'FileSystemStorage',
        availableSpace,
        usagePercentage: 0 // 无法准确计算
      };
    } catch (error) {
      throw new StorageError(
        'Failed to get storage stats',
        StorageErrorCodes.READ_ERROR,
        error instanceof Error ? error : undefined
      );
    }
  }

  // ==================== 私有方法 ====================

  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new StorageError(
        'Storage not initialized',
        StorageErrorCodes.INITIALIZATION_FAILED
      );
    }
  }

  private validateKey(key: string): void {
    if (!key || typeof key !== 'string') {
      throw new StorageError(
        'Invalid key: must be a non-empty string',
        StorageErrorCodes.INVALID_KEY
      );
    }

    // 检查路径安全性
    if (key.includes('..') || path.isAbsolute(key)) {
      throw new StorageError(
        'Invalid key: path traversal not allowed',
        StorageErrorCodes.INVALID_KEY
      );
    }
  }

  private getFilePath(key: string): string {
    return path.join(this.config.basePath, key);
  }

  private async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { 
        recursive: true, 
        mode: this.config.dirMode 
      });
    } catch (error) {
      if (!(error instanceof Error && 'code' in error && (error as any).code === 'EEXIST')) {
        throw error;
      }
    }
  }

  private async walkDirectory(
    dirPath: string, 
    callback: (filePath: string) => void
  ): Promise<void> {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        await this.walkDirectory(fullPath, callback);
      } else if (entry.isFile()) {
        callback(fullPath);
      }
    }
  }

  private calculateChecksum(content: string): string {
    return crypto.createHash('md5').update(content, 'utf8').digest('hex');
  }

  private detectContentType(key: string): string {
    const ext = path.extname(key).toLowerCase();
    const contentTypes: Record<string, string> = {
      '.md': 'text/markdown',
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.yaml': 'text/yaml',
      '.yml': 'text/yaml'
    };
    return contentTypes[ext] || 'text/plain';
  }

  private patternToRegex(pattern: string): RegExp {
    // 简单的通配符转正则表达式
    const escaped = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = escaped.replace(/\\\*/g, '.*').replace(/\\\?/g, '.');
    return new RegExp(`^${regex}$`, 'i');
  }

  private async updateMetadata(key: string, updates: Partial<StorageMetadata>): Promise<void> {
    const current = this.metadataCache.get(key) || {
      size: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1,
      tags: []
    };

    this.metadataCache.set(key, { ...current, ...updates });
  }

  private async loadMetadataCache(): Promise<void> {
    // 实现元数据缓存加载逻辑
    // 可以从 .metadata.json 文件加载
  }

  private async saveMetadataCache(): Promise<void> {
    // 实现元数据缓存保存逻辑
    // 可以保存到 .metadata.json 文件
  }
}
