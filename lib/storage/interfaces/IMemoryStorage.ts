/**
 * SelfMirror 存储抽象层接口
 * 参考 Mem0 项目的存储设计模式，提供统一的存储接口
 */

export interface IMemoryStorage {
  // ==================== 基础操作 ====================
  
  /**
   * 读取指定键的内容
   * @param key 存储键
   * @returns 存储内容
   * @throws StorageError 当键不存在或读取失败时
   */
  read(key: string): Promise<string>;
  
  /**
   * 写入内容到指定键
   * @param key 存储键
   * @param content 要写入的内容
   * @throws StorageError 当写入失败时
   */
  write(key: string, content: string): Promise<void>;
  
  /**
   * 更新指定键的内容
   * @param key 存储键
   * @param content 新内容
   * @throws StorageError 当键不存在或更新失败时
   */
  update(key: string, content: string): Promise<void>;
  
  /**
   * 删除指定键
   * @param key 存储键
   * @throws StorageError 当删除失败时
   */
  delete(key: string): Promise<void>;
  
  // ==================== 批量操作 ====================
  
  /**
   * 批量读取多个键的内容
   * @param keys 存储键数组
   * @returns 键值对映射
   */
  readBatch(keys: string[]): Promise<Record<string, string>>;
  
  /**
   * 批量写入多个键值对
   * @param entries 键值对映射
   * @throws StorageError 当批量写入失败时
   */
  writeBatch(entries: Record<string, string>): Promise<void>;
  
  // ==================== 查询操作 ====================
  
  /**
   * 列出所有存储键
   * @returns 存储键数组
   */
  list(): Promise<string[]>;
  
  /**
   * 检查指定键是否存在
   * @param key 存储键
   * @returns 是否存在
   */
  exists(key: string): Promise<boolean>;
  
  /**
   * 根据模式搜索键
   * @param pattern 搜索模式（支持通配符）
   * @returns 匹配的键数组
   */
  search(pattern: string): Promise<string[]>;
  
  // ==================== 元数据操作 ====================
  
  /**
   * 获取指定键的元数据
   * @param key 存储键
   * @returns 元数据信息
   */
  getMetadata(key: string): Promise<StorageMetadata>;
  
  /**
   * 设置指定键的元数据
   * @param key 存储键
   * @param metadata 元数据（部分更新）
   */
  setMetadata(key: string, metadata: Partial<StorageMetadata>): Promise<void>;
  
  // ==================== 生命周期 ====================
  
  /**
   * 初始化存储系统
   * @throws StorageError 当初始化失败时
   */
  initialize(): Promise<void>;
  
  /**
   * 关闭存储系统，释放资源
   */
  close(): Promise<void>;
  
  // ==================== 统计信息 ====================
  
  /**
   * 获取存储统计信息
   * @returns 统计信息
   */
  getStats(): Promise<StorageStats>;
}

// ==================== 类型定义 ====================

/**
 * 存储元数据
 */
export interface StorageMetadata {
  /** 内容大小（字节） */
  size: number;
  
  /** 创建时间 */
  createdAt: Date;
  
  /** 最后更新时间 */
  updatedAt: Date;
  
  /** 版本号 */
  version: number;
  
  /** 标签数组 */
  tags: string[];
  
  /** 内容校验和（可选） */
  checksum?: string;
  
  /** 内容类型 */
  contentType?: string;
  
  /** 自定义属性 */
  custom?: Record<string, any>;
}

/**
 * 存储统计信息
 */
export interface StorageStats {
  /** 总键数 */
  totalKeys: number;
  
  /** 总存储大小（字节） */
  totalSize: number;
  
  /** 最后访问时间 */
  lastAccessed: Date;
  
  /** 存储类型 */
  storageType: string;
  
  /** 存储提供者 */
  provider: string;
  
  /** 可用空间（字节，-1 表示无限制） */
  availableSpace: number;
  
  /** 已使用空间百分比 */
  usagePercentage: number;
}

// ==================== 配置接口 ====================

/**
 * 存储配置基类
 */
export interface BaseStorageConfig {
  /** 存储提供者名称 */
  provider: string;
  
  /** 是否启用压缩 */
  compression?: boolean;
  
  /** 是否启用加密 */
  encryption?: boolean;
  
  /** 缓存配置 */
  cache?: CacheConfig;
  
  /** 备份配置 */
  backup?: BackupConfig;
}

/**
 * 文件系统存储配置
 */
export interface FileSystemStorageConfig extends BaseStorageConfig {
  provider: 'filesystem';
  
  /** 基础路径 */
  basePath: string;
  
  /** 文件编码 */
  encoding?: BufferEncoding;
  
  /** 文件权限 */
  fileMode?: number;
  
  /** 目录权限 */
  dirMode?: number;
  
  /** 是否创建目录 */
  createDirs?: boolean;
}

/**
 * IndexedDB 存储配置
 */
export interface IndexedDBStorageConfig extends BaseStorageConfig {
  provider: 'indexeddb';
  
  /** 数据库名称 */
  dbName: string;
  
  /** 数据库版本 */
  version: number;
  
  /** 对象存储名称 */
  storeName?: string;
  
  /** 索引配置 */
  indexes?: IndexConfig[];
}

/**
 * 内存存储配置
 */
export interface MemoryStorageConfig extends BaseStorageConfig {
  provider: 'memory';
  
  /** 最大存储大小（字节） */
  maxSize?: number;
  
  /** 是否持久化到本地存储 */
  persistent?: boolean;
  
  /** LRU 缓存大小 */
  lruSize?: number;
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 是否启用缓存 */
  enabled: boolean;
  
  /** 缓存大小 */
  size: number;
  
  /** 缓存 TTL（毫秒） */
  ttl: number;
  
  /** 缓存策略 */
  strategy: 'lru' | 'lfu' | 'fifo';
}

/**
 * 备份配置
 */
export interface BackupConfig {
  /** 是否启用自动备份 */
  enabled: boolean;
  
  /** 备份间隔（毫秒） */
  interval: number;
  
  /** 备份保留数量 */
  retention: number;
  
  /** 备份路径 */
  path?: string;
}

/**
 * IndexedDB 索引配置
 */
export interface IndexConfig {
  /** 索引名称 */
  name: string;
  
  /** 索引键路径 */
  keyPath: string | string[];
  
  /** 是否唯一 */
  unique?: boolean;
  
  /** 是否多值 */
  multiEntry?: boolean;
}

// ==================== 错误类型 ====================

/**
 * 存储错误类
 */
export class StorageError extends Error {
  public readonly code: string;
  public readonly cause?: Error;
  
  constructor(message: string, code: string = 'STORAGE_ERROR', cause?: Error) {
    super(message);
    this.name = 'StorageError';
    this.code = code;
    this.cause = cause;
    
    // 保持错误堆栈
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, StorageError);
    }
  }
}

// ==================== 常用错误代码 ====================

export const StorageErrorCodes = {
  // 通用错误
  STORAGE_ERROR: 'STORAGE_ERROR',
  INITIALIZATION_FAILED: 'INITIALIZATION_FAILED',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  
  // 操作错误
  KEY_NOT_FOUND: 'KEY_NOT_FOUND',
  KEY_ALREADY_EXISTS: 'KEY_ALREADY_EXISTS',
  READ_ERROR: 'READ_ERROR',
  WRITE_ERROR: 'WRITE_ERROR',
  DELETE_ERROR: 'DELETE_ERROR',
  
  // 权限错误
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  ACCESS_DENIED: 'ACCESS_DENIED',
  
  // 容量错误
  STORAGE_FULL: 'STORAGE_FULL',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  
  // 数据错误
  INVALID_KEY: 'INVALID_KEY',
  INVALID_DATA: 'INVALID_DATA',
  CORRUPTION_DETECTED: 'CORRUPTION_DETECTED',
  
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR'
} as const;

export type StorageErrorCode = typeof StorageErrorCodes[keyof typeof StorageErrorCodes];
