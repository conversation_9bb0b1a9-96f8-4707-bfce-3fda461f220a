// RAG系统本地存储管理器

import { ChunkPair, ChunkMetadata, StorageError } from '@/types/rag';
import { RAG_CONFIG } from '@/lib/config/rag-config';

class RAGStorage {
  private db: IDBDatabase | null = null;
  private dbName = RAG_CONFIG.storage.dbName;
  private dbVersion = RAG_CONFIG.storage.version;

  // 数据库表名
  private readonly STORES = {
    CHUNKS: 'chunks',
    VECTORS: 'vectors',
    METADATA: 'metadata'
  };

  /**
   * 初始化IndexedDB数据库
   */
  async initialize(): Promise<void> {
    if (typeof window === 'undefined') {
      throw new StorageError('RAG存储只能在浏览器环境中使用');
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new StorageError('无法打开IndexedDB数据库', request.error));
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ RAG存储数据库初始化成功');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // 创建chunks存储
        if (!db.objectStoreNames.contains(this.STORES.CHUNKS)) {
          const chunksStore = db.createObjectStore(this.STORES.CHUNKS, { keyPath: 'id' });
          chunksStore.createIndex('sourceDocumentId', 'sourceDocumentId', { unique: false });
          chunksStore.createIndex('sourceDocumentType', 'sourceDocumentType', { unique: false });
          chunksStore.createIndex('createdAt', 'createdAt', { unique: false });
        }

        // 创建vectors存储
        if (!db.objectStoreNames.contains(this.STORES.VECTORS)) {
          const vectorsStore = db.createObjectStore(this.STORES.VECTORS, { keyPath: 'chunkId' });
          vectorsStore.createIndex('dimensions', 'dimensions', { unique: false });
        }

        // 创建metadata存储
        if (!db.objectStoreNames.contains(this.STORES.METADATA)) {
          const metadataStore = db.createObjectStore(this.STORES.METADATA, { keyPath: 'key' });
        }

        console.log('📊 RAG数据库结构创建完成');
      };
    });
  }

  /**
   * 保存块对到本地存储
   */
  async saveChunkPair(chunkPair: ChunkPair): Promise<void> {
    if (!this.db) {
      throw new StorageError('数据库未初始化');
    }

    const transaction = this.db.transaction([this.STORES.CHUNKS, this.STORES.VECTORS], 'readwrite');
    
    try {
      // 保存chunk数据
      const chunksStore = transaction.objectStore(this.STORES.CHUNKS);
      await this.promisifyRequest(chunksStore.put({
        id: chunkPair.id,
        parentChunk: chunkPair.parentChunk,
        childChunk: chunkPair.childChunk,
        sourceDocumentId: chunkPair.sourceDocumentId,
        sourceDocumentType: chunkPair.sourceDocumentType,
        metadata: chunkPair.metadata,
        createdAt: chunkPair.createdAt,
        updatedAt: chunkPair.updatedAt
      }));

      // 保存vector数据
      const vectorsStore = transaction.objectStore(this.STORES.VECTORS);
      await this.promisifyRequest(vectorsStore.put({
        chunkId: chunkPair.id,
        vector: chunkPair.vector,
        dimensions: chunkPair.vector.length
      }));

      console.log(`💾 块对 ${chunkPair.id} 保存成功`);
    } catch (error) {
      throw new StorageError('保存块对失败', error);
    }
  }

  /**
   * 批量保存块对
   */
  async saveChunkPairs(chunkPairs: ChunkPair[]): Promise<void> {
    if (!this.db) {
      throw new StorageError('数据库未初始化');
    }

    const transaction = this.db.transaction([this.STORES.CHUNKS, this.STORES.VECTORS], 'readwrite');
    const chunksStore = transaction.objectStore(this.STORES.CHUNKS);
    const vectorsStore = transaction.objectStore(this.STORES.VECTORS);

    try {
      for (const chunkPair of chunkPairs) {
        // 保存chunk数据
        chunksStore.put({
          id: chunkPair.id,
          parentChunk: chunkPair.parentChunk,
          childChunk: chunkPair.childChunk,
          sourceDocumentId: chunkPair.sourceDocumentId,
          sourceDocumentType: chunkPair.sourceDocumentType,
          metadata: chunkPair.metadata,
          createdAt: chunkPair.createdAt,
          updatedAt: chunkPair.updatedAt
        });

        // 保存vector数据
        vectorsStore.put({
          chunkId: chunkPair.id,
          vector: chunkPair.vector,
          dimensions: chunkPair.vector.length
        });
      }

      await this.promisifyTransaction(transaction);
      console.log(`💾 批量保存 ${chunkPairs.length} 个块对成功`);
    } catch (error) {
      throw new StorageError('批量保存块对失败', error);
    }
  }

  /**
   * 根据ID获取块对
   */
  async getChunkPair(id: string): Promise<ChunkPair | null> {
    if (!this.db) {
      throw new StorageError('数据库未初始化');
    }

    const transaction = this.db.transaction([this.STORES.CHUNKS, this.STORES.VECTORS], 'readonly');
    
    try {
      const chunksStore = transaction.objectStore(this.STORES.CHUNKS);
      const vectorsStore = transaction.objectStore(this.STORES.VECTORS);

      const chunkData = await this.promisifyRequest(chunksStore.get(id));
      if (!chunkData) return null;

      const vectorData = await this.promisifyRequest(vectorsStore.get(id));
      if (!vectorData) return null;

      return {
        ...chunkData,
        vector: vectorData.vector
      };
    } catch (error) {
      throw new StorageError('获取块对失败', error);
    }
  }

  /**
   * 获取所有向量数据（用于向量搜索）
   */
  async getAllVectors(): Promise<Array<{ chunkId: string; vector: number[] }>> {
    if (!this.db) {
      throw new StorageError('数据库未初始化');
    }

    const transaction = this.db.transaction([this.STORES.VECTORS], 'readonly');
    const store = transaction.objectStore(this.STORES.VECTORS);

    try {
      const request = store.getAll();
      const vectors = await this.promisifyRequest(request);
      return vectors.map(v => ({ chunkId: v.chunkId, vector: v.vector }));
    } catch (error) {
      throw new StorageError('获取向量数据失败', error);
    }
  }

  /**
   * 根据文档ID删除所有相关块对
   */
  async deleteByDocumentId(documentId: string): Promise<void> {
    if (!this.db) {
      throw new StorageError('数据库未初始化');
    }

    const transaction = this.db.transaction([this.STORES.CHUNKS, this.STORES.VECTORS], 'readwrite');
    
    try {
      const chunksStore = transaction.objectStore(this.STORES.CHUNKS);
      const index = chunksStore.index('sourceDocumentId');
      const chunks = await this.promisifyRequest(index.getAll(documentId));

      const vectorsStore = transaction.objectStore(this.STORES.VECTORS);

      for (const chunk of chunks) {
        await this.promisifyRequest(chunksStore.delete(chunk.id));
        await this.promisifyRequest(vectorsStore.delete(chunk.id));
      }

      console.log(`🗑️ 删除文档 ${documentId} 的 ${chunks.length} 个块对`);
    } catch (error) {
      throw new StorageError('删除块对失败', error);
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalChunks: number;
    totalVectors: number;
    storageSize: number;
    documentTypes: Record<string, number>;
  }> {
    if (!this.db) {
      throw new StorageError('数据库未初始化');
    }

    const transaction = this.db.transaction([this.STORES.CHUNKS, this.STORES.VECTORS], 'readonly');
    
    try {
      const chunksStore = transaction.objectStore(this.STORES.CHUNKS);
      const vectorsStore = transaction.objectStore(this.STORES.VECTORS);

      const [chunks, vectors] = await Promise.all([
        this.promisifyRequest(chunksStore.getAll()),
        this.promisifyRequest(vectorsStore.getAll())
      ]);

      const documentTypes: Record<string, number> = {};
      chunks.forEach(chunk => {
        documentTypes[chunk.sourceDocumentType] = (documentTypes[chunk.sourceDocumentType] || 0) + 1;
      });

      // 粗略估算存储大小
      const storageSize = JSON.stringify({ chunks, vectors }).length / 1024 / 1024; // MB

      return {
        totalChunks: chunks.length,
        totalVectors: vectors.length,
        storageSize,
        documentTypes
      };
    } catch (error) {
      throw new StorageError('获取存储统计失败', error);
    }
  }

  /**
   * 清空所有数据
   */
  async clearAll(): Promise<void> {
    if (!this.db) {
      throw new StorageError('数据库未初始化');
    }

    const transaction = this.db.transaction([this.STORES.CHUNKS, this.STORES.VECTORS, this.STORES.METADATA], 'readwrite');
    
    try {
      await Promise.all([
        this.promisifyRequest(transaction.objectStore(this.STORES.CHUNKS).clear()),
        this.promisifyRequest(transaction.objectStore(this.STORES.VECTORS).clear()),
        this.promisifyRequest(transaction.objectStore(this.STORES.METADATA).clear())
      ]);

      console.log('🗑️ RAG存储已清空');
    } catch (error) {
      throw new StorageError('清空存储失败', error);
    }
  }

  // 辅助方法：将IDBRequest转换为Promise
  private promisifyRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // 辅助方法：将IDBTransaction转换为Promise
  private promisifyTransaction(transaction: IDBTransaction): Promise<void> {
    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve();
      transaction.onerror = () => reject(transaction.error);
    });
  }
}

// 导出单例
export const ragStorage = new RAGStorage();
