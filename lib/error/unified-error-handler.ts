/**
 * Unified Error Handler - 统一错误处理器
 * 
 * 核心功能：
 * - 标准化错误分类和处理
 * - 错误恢复策略和重试机制
 * - 错误日志记录和监控
 * - 错误上下文收集和分析
 */

import { EventEmitter } from 'events';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  // 系统错误
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  INITIALIZATION_ERROR = 'INITIALIZATION_ERROR',
  
  // API错误
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  
  // AI提供商错误
  AI_PROVIDER_ERROR = 'AI_PROVIDER_ERROR',
  AI_TIMEOUT_ERROR = 'AI_TIMEOUT_ERROR',
  AI_QUOTA_ERROR = 'AI_QUOTA_ERROR',
  AI_MODEL_ERROR = 'AI_MODEL_ERROR',
  
  // 数据处理错误
  DATA_PROCESSING_ERROR = 'DATA_PROCESSING_ERROR',
  DATA_VALIDATION_ERROR = 'DATA_VALIDATION_ERROR',
  DATA_TRANSFORMATION_ERROR = 'DATA_TRANSFORMATION_ERROR',
  
  // 缓存错误
  CACHE_ERROR = 'CACHE_ERROR',
  CACHE_MISS_ERROR = 'CACHE_MISS_ERROR',
  CACHE_CORRUPTION_ERROR = 'CACHE_CORRUPTION_ERROR',
  
  // 向量数据库错误
  VECTOR_DB_ERROR = 'VECTOR_DB_ERROR',
  VECTOR_SEARCH_ERROR = 'VECTOR_SEARCH_ERROR',
  VECTOR_INDEX_ERROR = 'VECTOR_INDEX_ERROR',
  
  // 业务逻辑错误
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  WORKFLOW_ERROR = 'WORKFLOW_ERROR',
  CONTEXT_ERROR = 'CONTEXT_ERROR',
  
  // 网络错误
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  
  // 未知错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误严重级别枚举
 */
export enum ErrorSeverity {
  LOW = 'LOW',           // 低级别：不影响核心功能
  MEDIUM = 'MEDIUM',     // 中级别：影响部分功能
  HIGH = 'HIGH',         // 高级别：影响核心功能
  CRITICAL = 'CRITICAL'  // 严重：系统不可用
}

/**
 * 错误恢复策略枚举
 */
export enum RecoveryStrategy {
  NONE = 'NONE',                    // 无恢复策略
  RETRY = 'RETRY',                  // 重试
  FALLBACK = 'FALLBACK',            // 降级处理
  CIRCUIT_BREAKER = 'CIRCUIT_BREAKER', // 熔断器
  GRACEFUL_DEGRADATION = 'GRACEFUL_DEGRADATION' // 优雅降级
}

/**
 * 统一错误接口
 */
export interface UnifiedError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  originalError?: Error;
  context: {
    timestamp: Date;
    requestId?: string;
    userId?: string;
    sessionId?: string;
    component: string;
    operation: string;
    metadata?: Record<string, any>;
  };
  stack?: string;
  recoveryStrategy: RecoveryStrategy;
  retryCount: number;
  maxRetries: number;
}

/**
 * 错误处理配置接口
 */
export interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableMetrics: boolean;
  enableRecovery: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  maxRetries: number;
  retryDelay: number;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number;
}

/**
 * 错误统计接口
 */
export interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<ErrorType, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  recoveryAttempts: number;
  successfulRecoveries: number;
  failedRecoveries: number;
  averageRecoveryTime: number;
  lastErrorTime: Date | null;
}

/**
 * 统一错误处理器实现
 */
export class UnifiedErrorHandler extends EventEmitter {
  private config: ErrorHandlerConfig;
  private stats: ErrorStats;
  private circuitBreakers: Map<string, { failures: number; lastFailure: Date; isOpen: boolean }> = new Map();
  private recoveryTimes: number[] = [];

  constructor(config?: Partial<ErrorHandlerConfig>) {
    super();
    
    this.config = {
      enableLogging: true,
      enableMetrics: true,
      enableRecovery: true,
      logLevel: 'error',
      maxRetries: 3,
      retryDelay: 1000,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 60000,
      ...config
    };

    this.stats = {
      totalErrors: 0,
      errorsByType: {} as Record<ErrorType, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      recoveryAttempts: 0,
      successfulRecoveries: 0,
      failedRecoveries: 0,
      averageRecoveryTime: 0,
      lastErrorTime: null
    };

    this.initializeStats();
    console.log('🛡️ 统一错误处理器已初始化');
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): void {
    Object.values(ErrorType).forEach(type => {
      this.stats.errorsByType[type] = 0;
    });
    
    Object.values(ErrorSeverity).forEach(severity => {
      this.stats.errorsBySeverity[severity] = 0;
    });
  }

  /**
   * 处理错误
   */
  async handleError(
    error: Error | UnifiedError,
    context: {
      component: string;
      operation: string;
      requestId?: string;
      userId?: string;
      sessionId?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<UnifiedError> {
    const unifiedError = this.normalizeError(error, context);
    
    // 更新统计信息
    this.updateStats(unifiedError);
    
    // 记录错误日志
    if (this.config.enableLogging) {
      this.logError(unifiedError);
    }
    
    // 触发错误事件
    this.emit('error', unifiedError);
    
    // 尝试错误恢复
    if (this.config.enableRecovery && unifiedError.recoveryStrategy !== RecoveryStrategy.NONE) {
      await this.attemptRecovery(unifiedError);
    }
    
    return unifiedError;
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(
    error: Error | UnifiedError,
    context: {
      component: string;
      operation: string;
      requestId?: string;
      userId?: string;
      sessionId?: string;
      metadata?: Record<string, any>;
    }
  ): UnifiedError {
    if (this.isUnifiedError(error)) {
      return error;
    }

    // 根据错误信息推断错误类型
    const errorType = this.inferErrorType(error);
    const severity = this.inferErrorSeverity(errorType, error);
    const recoveryStrategy = this.determineRecoveryStrategy(errorType);

    return {
      id: this.generateErrorId(),
      type: errorType,
      severity,
      message: error.message || '未知错误',
      originalError: error,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        requestId: context.requestId,
        userId: context.userId,
        sessionId: context.sessionId,
        metadata: context.metadata
      },
      stack: error.stack,
      recoveryStrategy,
      retryCount: 0,
      maxRetries: this.config.maxRetries
    };
  }

  /**
   * 判断是否为统一错误对象
   */
  private isUnifiedError(error: any): error is UnifiedError {
    return error && typeof error === 'object' && 'type' in error && 'severity' in error;
  }

  /**
   * 推断错误类型
   */
  private inferErrorType(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    // API相关错误
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorType.VALIDATION_ERROR;
    }
    if (message.includes('unauthorized') || message.includes('authentication')) {
      return ErrorType.AUTHENTICATION_ERROR;
    }
    if (message.includes('forbidden') || message.includes('permission')) {
      return ErrorType.AUTHORIZATION_ERROR;
    }
    if (message.includes('rate limit') || message.includes('too many requests')) {
      return ErrorType.RATE_LIMIT_ERROR;
    }

    // AI提供商错误
    if (message.includes('ai') || message.includes('model') || message.includes('gemini') || message.includes('doubao')) {
      if (message.includes('timeout')) return ErrorType.AI_TIMEOUT_ERROR;
      if (message.includes('quota') || message.includes('limit')) return ErrorType.AI_QUOTA_ERROR;
      return ErrorType.AI_PROVIDER_ERROR;
    }

    // 网络错误
    if (message.includes('network') || message.includes('connection') || name.includes('network')) {
      if (message.includes('timeout')) return ErrorType.TIMEOUT_ERROR;
      return ErrorType.NETWORK_ERROR;
    }

    // 缓存错误
    if (message.includes('cache')) {
      return ErrorType.CACHE_ERROR;
    }

    // 配置错误
    if (message.includes('config') || message.includes('configuration')) {
      return ErrorType.CONFIGURATION_ERROR;
    }

    // 数据处理错误
    if (message.includes('data') || message.includes('parse') || message.includes('transform')) {
      return ErrorType.DATA_PROCESSING_ERROR;
    }

    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * 推断错误严重级别
   */
  private inferErrorSeverity(type: ErrorType, error: Error): ErrorSeverity {
    // 严重错误
    if ([
      ErrorType.SYSTEM_ERROR,
      ErrorType.INITIALIZATION_ERROR,
      ErrorType.CONFIGURATION_ERROR
    ].includes(type)) {
      return ErrorSeverity.CRITICAL;
    }

    // 高级别错误
    if ([
      ErrorType.AI_PROVIDER_ERROR,
      ErrorType.VECTOR_DB_ERROR,
      ErrorType.WORKFLOW_ERROR
    ].includes(type)) {
      return ErrorSeverity.HIGH;
    }

    // 中级别错误
    if ([
      ErrorType.API_ERROR,
      ErrorType.CACHE_ERROR,
      ErrorType.DATA_PROCESSING_ERROR,
      ErrorType.NETWORK_ERROR
    ].includes(type)) {
      return ErrorSeverity.MEDIUM;
    }

    // 低级别错误
    return ErrorSeverity.LOW;
  }

  /**
   * 确定恢复策略
   */
  private determineRecoveryStrategy(type: ErrorType): RecoveryStrategy {
    switch (type) {
      case ErrorType.AI_TIMEOUT_ERROR:
      case ErrorType.NETWORK_ERROR:
      case ErrorType.TIMEOUT_ERROR:
        return RecoveryStrategy.RETRY;
        
      case ErrorType.AI_PROVIDER_ERROR:
      case ErrorType.AI_MODEL_ERROR:
        return RecoveryStrategy.FALLBACK;
        
      case ErrorType.RATE_LIMIT_ERROR:
      case ErrorType.AI_QUOTA_ERROR:
        return RecoveryStrategy.CIRCUIT_BREAKER;
        
      case ErrorType.CACHE_ERROR:
      case ErrorType.VECTOR_SEARCH_ERROR:
        return RecoveryStrategy.GRACEFUL_DEGRADATION;
        
      default:
        return RecoveryStrategy.NONE;
    }
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(error: UnifiedError): Promise<boolean> {
    const startTime = Date.now();
    this.stats.recoveryAttempts++;

    try {
      console.log(`🔄 尝试错误恢复: ${error.type} [${error.recoveryStrategy}]`);

      let recovered = false;

      switch (error.recoveryStrategy) {
        case RecoveryStrategy.RETRY:
          recovered = await this.retryOperation(error);
          break;
          
        case RecoveryStrategy.FALLBACK:
          recovered = await this.fallbackOperation(error);
          break;
          
        case RecoveryStrategy.CIRCUIT_BREAKER:
          recovered = await this.circuitBreakerOperation(error);
          break;
          
        case RecoveryStrategy.GRACEFUL_DEGRADATION:
          recovered = await this.gracefulDegradation(error);
          break;
          
        default:
          recovered = false;
      }

      const recoveryTime = Date.now() - startTime;
      this.recordRecoveryTime(recoveryTime);

      if (recovered) {
        this.stats.successfulRecoveries++;
        console.log(`✅ 错误恢复成功: ${error.id} (${recoveryTime}ms)`);
        this.emit('recoverySuccess', { error, recoveryTime });
      } else {
        this.stats.failedRecoveries++;
        console.log(`❌ 错误恢复失败: ${error.id} (${recoveryTime}ms)`);
        this.emit('recoveryFailed', { error, recoveryTime });
      }

      return recovered;

    } catch (recoveryError) {
      const recoveryTime = Date.now() - startTime;
      this.stats.failedRecoveries++;
      
      console.error(`❌ 错误恢复异常: ${error.id}`, recoveryError);
      this.emit('recoveryError', { error, recoveryError, recoveryTime });
      
      return false;
    }
  }

  /**
   * 重试操作
   */
  private async retryOperation(error: UnifiedError): Promise<boolean> {
    if (error.retryCount >= error.maxRetries) {
      console.log(`⏭️ 重试次数已达上限: ${error.retryCount}/${error.maxRetries}`);
      return false;
    }

    error.retryCount++;
    
    // 指数退避延迟
    const delay = this.config.retryDelay * Math.pow(2, error.retryCount - 1);
    console.log(`⏳ 重试延迟: ${delay}ms (第${error.retryCount}次重试)`);
    
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // 这里应该重新执行原始操作
    // 由于我们无法直接重新执行，返回true表示重试策略已应用
    return true;
  }

  /**
   * 降级操作
   */
  private async fallbackOperation(error: UnifiedError): Promise<boolean> {
    console.log(`🔄 执行降级操作: ${error.type}`);
    
    // 根据错误类型执行相应的降级策略
    if (error.type === ErrorType.AI_PROVIDER_ERROR) {
      // AI提供商降级：切换到备用提供商
      this.emit('aiProviderFallback', { error });
      return true;
    }
    
    if (error.type === ErrorType.AI_MODEL_ERROR) {
      // AI模型降级：使用默认模型
      this.emit('aiModelFallback', { error });
      return true;
    }
    
    return false;
  }

  /**
   * 熔断器操作
   */
  private async circuitBreakerOperation(error: UnifiedError): Promise<boolean> {
    const key = `${error.context.component}-${error.context.operation}`;
    const breaker = this.circuitBreakers.get(key) || { failures: 0, lastFailure: new Date(), isOpen: false };
    
    breaker.failures++;
    breaker.lastFailure = new Date();
    
    if (breaker.failures >= this.config.circuitBreakerThreshold) {
      breaker.isOpen = true;
      console.log(`🔌 熔断器开启: ${key} (失败次数: ${breaker.failures})`);
      
      // 设置熔断器恢复定时器
      setTimeout(() => {
        breaker.isOpen = false;
        breaker.failures = 0;
        console.log(`🔌 熔断器恢复: ${key}`);
      }, this.config.circuitBreakerTimeout);
    }
    
    this.circuitBreakers.set(key, breaker);
    this.emit('circuitBreakerTriggered', { error, breaker });
    
    return breaker.isOpen;
  }

  /**
   * 优雅降级
   */
  private async gracefulDegradation(error: UnifiedError): Promise<boolean> {
    console.log(`🎯 执行优雅降级: ${error.type}`);
    
    // 根据错误类型执行相应的降级策略
    if (error.type === ErrorType.CACHE_ERROR) {
      // 缓存降级：直接访问数据源
      this.emit('cacheBypass', { error });
      return true;
    }
    
    if (error.type === ErrorType.VECTOR_SEARCH_ERROR) {
      // 向量搜索降级：使用简单文本匹配
      this.emit('vectorSearchFallback', { error });
      return true;
    }
    
    return false;
  }

  /**
   * 记录错误日志
   */
  private logError(error: UnifiedError): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = this.formatErrorLog(error);
    
    switch (logLevel) {
      case 'error':
        console.error(logMessage);
        break;
      case 'warn':
        console.warn(logMessage);
        break;
      case 'info':
        console.info(logMessage);
        break;
      case 'debug':
        console.debug(logMessage);
        break;
    }
  }

  /**
   * 获取日志级别
   */
  private getLogLevel(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'debug';
    }
  }

  /**
   * 格式化错误日志
   */
  private formatErrorLog(error: UnifiedError): string {
    return `🛡️ [${error.severity}] ${error.type}: ${error.message} | ID: ${error.id} | Component: ${error.context.component} | Operation: ${error.context.operation}${error.context.requestId ? ` | Request: ${error.context.requestId}` : ''}`;
  }

  /**
   * 更新统计信息
   */
  private updateStats(error: UnifiedError): void {
    this.stats.totalErrors++;
    this.stats.errorsByType[error.type]++;
    this.stats.errorsBySeverity[error.severity]++;
    this.stats.lastErrorTime = new Date();
  }

  /**
   * 记录恢复时间
   */
  private recordRecoveryTime(time: number): void {
    this.recoveryTimes.push(time);
    
    // 只保留最近100次的记录
    if (this.recoveryTimes.length > 100) {
      this.recoveryTimes.shift();
    }
    
    // 计算平均时间
    this.stats.averageRecoveryTime = this.recoveryTimes.reduce((sum, t) => sum + t, 0) / this.recoveryTimes.length;
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `err-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查熔断器状态
   */
  isCircuitBreakerOpen(component: string, operation: string): boolean {
    const key = `${component}-${operation}`;
    const breaker = this.circuitBreakers.get(key);
    return breaker ? breaker.isOpen : false;
  }

  /**
   * 获取错误统计信息
   */
  getStats(): ErrorStats {
    return { ...this.stats };
  }

  /**
   * 获取熔断器状态
   */
  getCircuitBreakers(): Array<{ key: string; failures: number; isOpen: boolean; lastFailure: Date }> {
    return Array.from(this.circuitBreakers.entries()).map(([key, breaker]) => ({
      key,
      failures: breaker.failures,
      isOpen: breaker.isOpen,
      lastFailure: breaker.lastFailure
    }));
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.initializeStats();
    this.stats.totalErrors = 0;
    this.stats.recoveryAttempts = 0;
    this.stats.successfulRecoveries = 0;
    this.stats.failedRecoveries = 0;
    this.stats.averageRecoveryTime = 0;
    this.stats.lastErrorTime = null;
    this.recoveryTimes = [];
    
    console.log('📊 错误处理统计信息已重置');
  }

  /**
   * 重置熔断器
   */
  resetCircuitBreakers(): void {
    this.circuitBreakers.clear();
    console.log('🔌 所有熔断器已重置');
  }

  /**
   * 销毁错误处理器
   */
  destroy(): void {
    this.removeAllListeners();
    this.circuitBreakers.clear();
    this.recoveryTimes = [];
    
    console.log('🛡️ 统一错误处理器已销毁');
  }
}

// 导出单例实例
export const unifiedErrorHandler = new UnifiedErrorHandler();
