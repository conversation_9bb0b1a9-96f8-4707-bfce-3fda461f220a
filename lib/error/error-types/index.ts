/**
 * Error Types and Utilities - 错误类型定义和工具函数
 * 
 * 提供标准化的错误创建和处理工具
 */

import { 
  UnifiedError, 
  ErrorType, 
  ErrorSeverity, 
  RecoveryStrategy,
  unifiedErrorHandler 
} from '../unified-error-handler';

/**
 * 创建统一错误的工厂函数
 */
export class ErrorFactory {
  /**
   * 创建API错误
   */
  static createAPIError(
    message: string,
    context: {
      component: string;
      operation: string;
      requestId?: string;
      statusCode?: number;
      metadata?: Record<string, any>;
    }
  ): UnifiedError {
    return {
      id: this.generateErrorId(),
      type: ErrorType.API_ERROR,
      severity: ErrorSeverity.MEDIUM,
      message,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        requestId: context.requestId,
        metadata: {
          statusCode: context.statusCode,
          ...context.metadata
        }
      },
      recoveryStrategy: RecoveryStrategy.RETRY,
      retryCount: 0,
      maxRetries: 3
    };
  }

  /**
   * 创建验证错误
   */
  static createValidationError(
    message: string,
    validationErrors: string[],
    context: {
      component: string;
      operation: string;
      requestId?: string;
      field?: string;
    }
  ): UnifiedError {
    return {
      id: this.generateErrorId(),
      type: ErrorType.VALIDATION_ERROR,
      severity: ErrorSeverity.LOW,
      message,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        requestId: context.requestId,
        metadata: {
          validationErrors,
          field: context.field
        }
      },
      recoveryStrategy: RecoveryStrategy.NONE,
      retryCount: 0,
      maxRetries: 0
    };
  }

  /**
   * 创建AI提供商错误
   */
  static createAIProviderError(
    message: string,
    provider: string,
    context: {
      component: string;
      operation: string;
      requestId?: string;
      model?: string;
      prompt?: string;
    }
  ): UnifiedError {
    return {
      id: this.generateErrorId(),
      type: ErrorType.AI_PROVIDER_ERROR,
      severity: ErrorSeverity.HIGH,
      message,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        requestId: context.requestId,
        metadata: {
          provider,
          model: context.model,
          promptLength: context.prompt?.length
        }
      },
      recoveryStrategy: RecoveryStrategy.FALLBACK,
      retryCount: 0,
      maxRetries: 2
    };
  }

  /**
   * 创建缓存错误
   */
  static createCacheError(
    message: string,
    cacheType: string,
    context: {
      component: string;
      operation: string;
      requestId?: string;
      cacheKey?: string;
    }
  ): UnifiedError {
    return {
      id: this.generateErrorId(),
      type: ErrorType.CACHE_ERROR,
      severity: ErrorSeverity.MEDIUM,
      message,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        requestId: context.requestId,
        metadata: {
          cacheType,
          cacheKey: context.cacheKey
        }
      },
      recoveryStrategy: RecoveryStrategy.GRACEFUL_DEGRADATION,
      retryCount: 0,
      maxRetries: 1
    };
  }

  /**
   * 创建网络错误
   */
  static createNetworkError(
    message: string,
    context: {
      component: string;
      operation: string;
      requestId?: string;
      url?: string;
      timeout?: number;
    }
  ): UnifiedError {
    return {
      id: this.generateErrorId(),
      type: ErrorType.NETWORK_ERROR,
      severity: ErrorSeverity.MEDIUM,
      message,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        requestId: context.requestId,
        metadata: {
          url: context.url,
          timeout: context.timeout
        }
      },
      recoveryStrategy: RecoveryStrategy.RETRY,
      retryCount: 0,
      maxRetries: 3
    };
  }

  /**
   * 创建配置错误
   */
  static createConfigurationError(
    message: string,
    configKey: string,
    context: {
      component: string;
      operation: string;
      expectedType?: string;
      actualValue?: any;
    }
  ): UnifiedError {
    return {
      id: this.generateErrorId(),
      type: ErrorType.CONFIGURATION_ERROR,
      severity: ErrorSeverity.CRITICAL,
      message,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        metadata: {
          configKey,
          expectedType: context.expectedType,
          actualValue: context.actualValue
        }
      },
      recoveryStrategy: RecoveryStrategy.NONE,
      retryCount: 0,
      maxRetries: 0
    };
  }

  /**
   * 创建数据处理错误
   */
  static createDataProcessingError(
    message: string,
    context: {
      component: string;
      operation: string;
      requestId?: string;
      dataType?: string;
      processingStep?: string;
    }
  ): UnifiedError {
    return {
      id: this.generateErrorId(),
      type: ErrorType.DATA_PROCESSING_ERROR,
      severity: ErrorSeverity.MEDIUM,
      message,
      context: {
        timestamp: new Date(),
        component: context.component,
        operation: context.operation,
        requestId: context.requestId,
        metadata: {
          dataType: context.dataType,
          processingStep: context.processingStep
        }
      },
      recoveryStrategy: RecoveryStrategy.RETRY,
      retryCount: 0,
      maxRetries: 2
    };
  }

  /**
   * 生成错误ID
   */
  private static generateErrorId(): string {
    return `err-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 错误处理装饰器
 */
export function HandleErrors(
  component: string,
  operation?: string,
  options?: {
    enableRecovery?: boolean;
    maxRetries?: number;
    fallbackValue?: any;
  }
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operationName = operation || propertyKey;

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        const unifiedError = await unifiedErrorHandler.handleError(error as Error, {
          component,
          operation: operationName,
          requestId: args[0]?.requestId,
          metadata: {
            methodName: propertyKey,
            arguments: args.length
          }
        });

        // 如果启用了恢复且有降级值，返回降级值
        if (options?.enableRecovery && options.fallbackValue !== undefined) {
          console.log(`🎯 使用降级值: ${component}.${operationName}`);
          return options.fallbackValue;
        }

        // 重新抛出统一错误
        throw unifiedError;
      }
    };

    return descriptor;
  };
}

/**
 * 异步错误处理包装器
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: {
    component: string;
    operation: string;
    requestId?: string;
    metadata?: Record<string, any>;
  },
  options?: {
    enableRecovery?: boolean;
    fallbackValue?: T;
    maxRetries?: number;
  }
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    const unifiedError = await unifiedErrorHandler.handleError(error as Error, context);

    // 如果启用了恢复且有降级值，返回降级值
    if (options?.enableRecovery && options.fallbackValue !== undefined) {
      console.log(`🎯 使用降级值: ${context.component}.${context.operation}`);
      return options.fallbackValue;
    }

    // 重新抛出统一错误
    throw unifiedError;
  }
}

/**
 * 同步错误处理包装器
 */
export function withSyncErrorHandling<T>(
  operation: () => T,
  context: {
    component: string;
    operation: string;
    requestId?: string;
    metadata?: Record<string, any>;
  },
  options?: {
    fallbackValue?: T;
  }
): T {
  try {
    return operation();
  } catch (error) {
    // 同步处理错误（不等待异步恢复）
    unifiedErrorHandler.handleError(error as Error, context);

    // 如果有降级值，返回降级值
    if (options?.fallbackValue !== undefined) {
      console.log(`🎯 使用降级值: ${context.component}.${context.operation}`);
      return options.fallbackValue;
    }

    // 重新抛出原始错误
    throw error;
  }
}

/**
 * 错误边界组件（用于React组件）
 */
export class ErrorBoundary {
  static wrap<T>(
    component: () => T,
    context: {
      component: string;
      operation: string;
      fallbackComponent?: () => T;
    }
  ): T {
    try {
      return component();
    } catch (error) {
      unifiedErrorHandler.handleError(error as Error, {
        component: context.component,
        operation: context.operation,
        metadata: {
          errorBoundary: true
        }
      });

      if (context.fallbackComponent) {
        console.log(`🎯 使用降级组件: ${context.component}.${context.operation}`);
        return context.fallbackComponent();
      }

      throw error;
    }
  }
}

/**
 * 错误聚合器
 */
export class ErrorAggregator {
  private errors: UnifiedError[] = [];

  /**
   * 添加错误
   */
  add(error: UnifiedError): void {
    this.errors.push(error);
  }

  /**
   * 获取所有错误
   */
  getAll(): UnifiedError[] {
    return [...this.errors];
  }

  /**
   * 按类型获取错误
   */
  getByType(type: ErrorType): UnifiedError[] {
    return this.errors.filter(error => error.type === type);
  }

  /**
   * 按严重级别获取错误
   */
  getBySeverity(severity: ErrorSeverity): UnifiedError[] {
    return this.errors.filter(error => error.severity === severity);
  }

  /**
   * 获取错误统计
   */
  getStats(): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
  } {
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};

    this.errors.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    return {
      total: this.errors.length,
      byType,
      bySeverity
    };
  }

  /**
   * 清空错误
   */
  clear(): void {
    this.errors = [];
  }

  /**
   * 是否有错误
   */
  hasErrors(): boolean {
    return this.errors.length > 0;
  }

  /**
   * 是否有严重错误
   */
  hasCriticalErrors(): boolean {
    return this.errors.some(error => error.severity === ErrorSeverity.CRITICAL);
  }
}

// 导出常用的错误创建函数
export const createAPIError = ErrorFactory.createAPIError.bind(ErrorFactory);
export const createValidationError = ErrorFactory.createValidationError.bind(ErrorFactory);
export const createAIProviderError = ErrorFactory.createAIProviderError.bind(ErrorFactory);
export const createCacheError = ErrorFactory.createCacheError.bind(ErrorFactory);
export const createNetworkError = ErrorFactory.createNetworkError.bind(ErrorFactory);
export const createConfigurationError = ErrorFactory.createConfigurationError.bind(ErrorFactory);
export const createDataProcessingError = ErrorFactory.createDataProcessingError.bind(ErrorFactory);
