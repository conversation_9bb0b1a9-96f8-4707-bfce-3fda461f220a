/**
 * Data Validators - 数据验证器集合
 * 
 * 提供常用的数据验证器实现
 */

import { 
  DataValidator, 
  DataValidationResult, 
  DataProcessingContext 
} from '../unified-data-pipeline';

/**
 * 基础数据验证器
 */
export class BasicDataValidator implements DataValidator<any> {
  readonly name = 'BasicDataValidator';
  readonly priority = 1;

  async validate(data: any, context: DataProcessingContext): Promise<DataValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基础检查
    if (data === null || data === undefined) {
      errors.push('数据不能为空');
      return { isValid: false, errors, warnings };
    }

    // 检查数据大小
    const dataSize = JSON.stringify(data).length;
    if (dataSize > 1024 * 1024) { // 1MB
      errors.push('数据大小超过限制 (1MB)');
    } else if (dataSize > 512 * 1024) { // 512KB
      warnings.push('数据较大，可能影响处理性能');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      sanitizedData: data
    };
  }
}

/**
 * 聊天消息验证器
 */
export class ChatMessageValidator implements DataValidator<any> {
  readonly name = 'ChatMessageValidator';
  readonly priority = 2;

  async validate(data: any, context: DataProcessingContext): Promise<DataValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let sanitizedData = { ...data };

    // 验证消息字段
    if (!data.message || typeof data.message !== 'string') {
      errors.push('message字段是必填的字符串');
    } else {
      // 清理消息内容
      sanitizedData.message = data.message.trim();
      
      if (sanitizedData.message.length === 0) {
        errors.push('消息内容不能为空');
      } else if (sanitizedData.message.length > 10000) {
        errors.push('消息长度不能超过10000字符');
      } else if (sanitizedData.message.length > 5000) {
        warnings.push('消息较长，可能影响处理速度');
      }
    }

    // 验证会话ID
    if (data.sessionId && typeof data.sessionId !== 'string') {
      errors.push('sessionId必须是字符串');
    } else if (data.sessionId) {
      sanitizedData.sessionId = data.sessionId.trim();
    }

    // 验证用户ID
    if (data.userId && typeof data.userId !== 'string') {
      errors.push('userId必须是字符串');
    } else if (data.userId) {
      sanitizedData.userId = data.userId.trim();
    }

    // 验证选项
    if (data.options && typeof data.options === 'object') {
      const optionErrors = this.validateChatOptions(data.options);
      errors.push(...optionErrors);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      sanitizedData
    };
  }

  private validateChatOptions(options: any): string[] {
    const errors: string[] = [];

    if (options.temperature !== undefined) {
      if (typeof options.temperature !== 'number' || 
          options.temperature < 0 || 
          options.temperature > 2) {
        errors.push('temperature必须是0-2之间的数字');
      }
    }

    if (options.maxTokens !== undefined) {
      if (typeof options.maxTokens !== 'number' || 
          options.maxTokens < 1 || 
          options.maxTokens > 8192) {
        errors.push('maxTokens必须是1-8192之间的数字');
      }
    }

    if (options.model && typeof options.model !== 'string') {
      errors.push('model必须是字符串');
    }

    return errors;
  }
}

/**
 * 安全验证器
 */
export class SecurityValidator implements DataValidator<any> {
  readonly name = 'SecurityValidator';
  readonly priority = 3;

  private readonly suspiciousPatterns = [
    /script\s*>/i,
    /<\s*iframe/i,
    /javascript\s*:/i,
    /on\w+\s*=/i,
    /eval\s*\(/i,
    /document\s*\./i,
    /window\s*\./i
  ];

  async validate(data: any, context: DataProcessingContext): Promise<DataValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let sanitizedData = { ...data };

    // 检查字符串字段中的可疑内容
    const stringFields = this.extractStringFields(data);
    
    for (const [field, value] of stringFields) {
      const securityIssues = this.checkSecurity(value);
      
      if (securityIssues.length > 0) {
        errors.push(`字段 ${field} 包含可疑内容: ${securityIssues.join(', ')}`);
      }
      
      // 清理HTML标签
      const cleanValue = this.sanitizeHtml(value);
      if (cleanValue !== value) {
        this.setNestedField(sanitizedData, field, cleanValue);
        warnings.push(`字段 ${field} 的HTML内容已被清理`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      sanitizedData
    };
  }

  private extractStringFields(obj: any, prefix = ''): Array<[string, string]> {
    const fields: Array<[string, string]> = [];
    
    for (const [key, value] of Object.entries(obj)) {
      const fieldPath = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'string') {
        fields.push([fieldPath, value]);
      } else if (value && typeof value === 'object') {
        fields.push(...this.extractStringFields(value, fieldPath));
      }
    }
    
    return fields;
  }

  private checkSecurity(text: string): string[] {
    const issues: string[] = [];
    
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(text)) {
        issues.push(`匹配可疑模式: ${pattern.source}`);
      }
    }
    
    return issues;
  }

  private sanitizeHtml(text: string): string {
    return text
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript\s*:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  private setNestedField(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }
}

/**
 * 内容质量验证器
 */
export class ContentQualityValidator implements DataValidator<any> {
  readonly name = 'ContentQualityValidator';
  readonly priority = 4;

  async validate(data: any, context: DataProcessingContext): Promise<DataValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (data.message && typeof data.message === 'string') {
      const qualityIssues = this.checkContentQuality(data.message);
      warnings.push(...qualityIssues);
    }

    return {
      isValid: true, // 质量问题不阻止处理，只给出警告
      errors,
      warnings,
      sanitizedData: data
    };
  }

  private checkContentQuality(message: string): string[] {
    const warnings: string[] = [];

    // 检查重复字符
    if (/(.)\1{10,}/.test(message)) {
      warnings.push('消息包含过多重复字符');
    }

    // 检查全大写
    if (message.length > 20 && message === message.toUpperCase()) {
      warnings.push('消息全部为大写字母');
    }

    // 检查特殊字符比例
    const specialCharCount = (message.match(/[^\w\s\u4e00-\u9fff]/g) || []).length;
    const specialCharRatio = specialCharCount / message.length;
    if (specialCharRatio > 0.3) {
      warnings.push('消息包含过多特殊字符');
    }

    // 检查是否过于简短
    if (message.trim().length < 2) {
      warnings.push('消息过于简短');
    }

    return warnings;
  }
}

// 导出所有验证器
export const validators = {
  BasicDataValidator,
  ChatMessageValidator,
  SecurityValidator,
  ContentQualityValidator
};

// 导出预配置的验证器实例
export const basicDataValidator = new BasicDataValidator();
export const chatMessageValidator = new ChatMessageValidator();
export const securityValidator = new SecurityValidator();
export const contentQualityValidator = new ContentQualityValidator();
