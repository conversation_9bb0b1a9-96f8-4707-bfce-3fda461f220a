/**
 * Unified Data Pipeline - 统一数据处理管道
 * 
 * 核心功能：
 * - 可插拔的验证器和转换器架构
 * - 标准化的数据处理流程
 * - 错误处理和回滚机制
 * - 性能监控和优化
 */

/**
 * 数据处理上下文接口
 */
export interface DataProcessingContext {
  requestId: string;
  timestamp: Date;
  source: string;
  metadata?: Record<string, any>;
}

/**
 * 数据验证结果接口
 */
export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedData?: any;
}

/**
 * 数据转换结果接口
 */
export interface DataTransformationResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * 管道处理结果接口
 */
export interface PipelineResult<T> {
  success: boolean;
  data?: T;
  errors: string[];
  warnings: string[];
  processingTime: number;
  stepsCompleted: string[];
  metadata: Record<string, any>;
}

/**
 * 数据验证器抽象接口
 */
export interface DataValidator<T> {
  readonly name: string;
  readonly priority: number;
  validate(data: any, context: DataProcessingContext): Promise<DataValidationResult>;
}

/**
 * 数据转换器抽象接口
 */
export interface DataTransformer<TInput, TOutput> {
  readonly name: string;
  readonly priority: number;
  transform(data: TInput, context: DataProcessingContext): Promise<DataTransformationResult<TOutput>>;
}

/**
 * 管道配置接口
 */
export interface PipelineConfig {
  name: string;
  description: string;
  enableParallelValidation: boolean;
  enableParallelTransformation: boolean;
  maxRetries: number;
  timeoutMs: number;
  enableMetrics: boolean;
}

/**
 * 统一数据处理管道实现
 */
export class UnifiedDataPipeline<TInput, TOutput> {
  private validators: DataValidator<TInput>[] = [];
  private transformers: DataTransformer<any, any>[] = [];
  private config: PipelineConfig;

  constructor(config: Partial<PipelineConfig> = {}) {
    this.config = {
      name: 'UnifiedDataPipeline',
      description: '统一数据处理管道',
      enableParallelValidation: true,
      enableParallelTransformation: false,
      maxRetries: 3,
      timeoutMs: 30000,
      enableMetrics: true,
      ...config
    };
  }

  /**
   * 添加验证器
   */
  addValidator(validator: DataValidator<TInput>): this {
    this.validators.push(validator);
    this.validators.sort((a, b) => a.priority - b.priority);
    return this;
  }

  /**
   * 添加转换器
   */
  addTransformer<TNext>(transformer: DataTransformer<any, TNext>): this {
    this.transformers.push(transformer);
    this.transformers.sort((a, b) => a.priority - b.priority);
    return this;
  }

  /**
   * 处理数据
   */
  async process(
    data: TInput,
    context: DataProcessingContext
  ): Promise<PipelineResult<TOutput>> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    const stepsCompleted: string[] = [];
    const metadata: Record<string, any> = {};

    try {
      console.log(`🔄 开始数据管道处理: ${this.config.name} [${context.requestId}]`);

      // 1. 数据验证阶段
      const validationResult = await this.runValidation(data, context);
      stepsCompleted.push('validation');
      
      if (!validationResult.isValid) {
        errors.push(...validationResult.errors);
        return {
          success: false,
          errors,
          warnings: validationResult.warnings,
          processingTime: Date.now() - startTime,
          stepsCompleted,
          metadata
        };
      }

      warnings.push(...validationResult.warnings);
      let processedData = validationResult.sanitizedData || data;
      metadata.validationMetrics = {
        validatorsRun: this.validators.length,
        warnings: validationResult.warnings.length
      };

      // 2. 数据转换阶段
      const transformationResult = await this.runTransformation(processedData, context);
      stepsCompleted.push('transformation');
      
      if (!transformationResult.success) {
        errors.push(transformationResult.error || '数据转换失败');
        return {
          success: false,
          errors,
          warnings,
          processingTime: Date.now() - startTime,
          stepsCompleted,
          metadata
        };
      }

      metadata.transformationMetrics = {
        transformersRun: this.transformers.length,
        ...transformationResult.metadata
      };

      const processingTime = Date.now() - startTime;
      
      if (this.config.enableMetrics) {
        this.recordMetrics(context.requestId, processingTime, true);
      }

      console.log(`✅ 数据管道处理完成: ${this.config.name} [${context.requestId}] - ${processingTime}ms`);

      return {
        success: true,
        data: transformationResult.data,
        errors,
        warnings,
        processingTime,
        stepsCompleted,
        metadata
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      console.error(`❌ 数据管道处理失败: ${this.config.name} [${context.requestId}]:`, error);
      
      if (this.config.enableMetrics) {
        this.recordMetrics(context.requestId, processingTime, false);
      }

      return {
        success: false,
        errors: [...errors, errorMessage],
        warnings,
        processingTime,
        stepsCompleted,
        metadata
      };
    }
  }

  /**
   * 运行验证阶段
   */
  private async runValidation(
    data: TInput,
    context: DataProcessingContext
  ): Promise<DataValidationResult> {
    if (this.validators.length === 0) {
      return { isValid: true, errors: [], warnings: [] };
    }

    const allErrors: string[] = [];
    const allWarnings: string[] = [];
    let sanitizedData = data;

    try {
      if (this.config.enableParallelValidation) {
        // 并行验证
        const validationPromises = this.validators.map(validator =>
          this.runSingleValidator(validator, data, context)
        );
        
        const results = await Promise.allSettled(validationPromises);
        
        for (const result of results) {
          if (result.status === 'fulfilled') {
            const validationResult = result.value;
            allErrors.push(...validationResult.errors);
            allWarnings.push(...validationResult.warnings);
            if (validationResult.sanitizedData) {
              sanitizedData = validationResult.sanitizedData;
            }
          } else {
            allErrors.push(`验证器执行失败: ${result.reason}`);
          }
        }
      } else {
        // 串行验证
        for (const validator of this.validators) {
          const result = await this.runSingleValidator(validator, sanitizedData, context);
          allErrors.push(...result.errors);
          allWarnings.push(...result.warnings);
          if (result.sanitizedData) {
            sanitizedData = result.sanitizedData;
          }
          
          // 如果有错误，停止后续验证
          if (result.errors.length > 0) {
            break;
          }
        }
      }

      return {
        isValid: allErrors.length === 0,
        errors: allErrors,
        warnings: allWarnings,
        sanitizedData
      };

    } catch (error) {
      console.error('❌ 验证阶段失败:', error);
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : String(error)],
        warnings: allWarnings
      };
    }
  }

  /**
   * 运行单个验证器
   */
  private async runSingleValidator(
    validator: DataValidator<TInput>,
    data: any,
    context: DataProcessingContext
  ): Promise<DataValidationResult> {
    try {
      const timeoutPromise = new Promise<DataValidationResult>((_, reject) => {
        setTimeout(() => reject(new Error(`验证器 ${validator.name} 超时`)), this.config.timeoutMs);
      });

      const validationPromise = validator.validate(data, context);
      
      return await Promise.race([validationPromise, timeoutPromise]);
    } catch (error) {
      console.error(`❌ 验证器 ${validator.name} 执行失败:`, error);
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : String(error)],
        warnings: []
      };
    }
  }

  /**
   * 运行转换阶段
   */
  private async runTransformation(
    data: any,
    context: DataProcessingContext
  ): Promise<DataTransformationResult<TOutput>> {
    if (this.transformers.length === 0) {
      return { success: true, data: data as TOutput };
    }

    let currentData = data;
    const metadata: Record<string, any> = {};

    try {
      for (const transformer of this.transformers) {
        const result = await this.runSingleTransformer(transformer, currentData, context);
        
        if (!result.success) {
          return result;
        }
        
        currentData = result.data;
        if (result.metadata) {
          metadata[transformer.name] = result.metadata;
        }
      }

      return {
        success: true,
        data: currentData,
        metadata
      };

    } catch (error) {
      console.error('❌ 转换阶段失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 运行单个转换器
   */
  private async runSingleTransformer(
    transformer: DataTransformer<any, any>,
    data: any,
    context: DataProcessingContext
  ): Promise<DataTransformationResult<any>> {
    try {
      const timeoutPromise = new Promise<DataTransformationResult<any>>((_, reject) => {
        setTimeout(() => reject(new Error(`转换器 ${transformer.name} 超时`)), this.config.timeoutMs);
      });

      const transformPromise = transformer.transform(data, context);
      
      return await Promise.race([transformPromise, timeoutPromise]);
    } catch (error) {
      console.error(`❌ 转换器 ${transformer.name} 执行失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 记录性能指标
   */
  private recordMetrics(requestId: string, processingTime: number, success: boolean): void {
    console.log(`📊 管道指标 [${this.config.name}]: ${processingTime}ms, 成功: ${success}, ID: ${requestId}`);
  }

  /**
   * 获取管道配置
   */
  getConfig(): PipelineConfig {
    return { ...this.config };
  }

  /**
   * 获取管道统计信息
   */
  getStats() {
    return {
      name: this.config.name,
      validatorsCount: this.validators.length,
      transformersCount: this.transformers.length,
      validators: this.validators.map(v => ({ name: v.name, priority: v.priority })),
      transformers: this.transformers.map(t => ({ name: t.name, priority: t.priority }))
    };
  }
}
