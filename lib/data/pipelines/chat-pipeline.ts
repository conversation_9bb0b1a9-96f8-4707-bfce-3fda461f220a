/**
 * Chat Data Pipeline - 聊天数据处理管道
 * 
 * 专门用于处理聊天请求的预配置管道
 */

import { UnifiedDataPipeline } from '../unified-data-pipeline';
import {
  basicDataValidator,
  chatMessageValidator,
  securityValidator,
  contentQualityValidator
} from '../validators';
import {
  chatRequestNormalizer,
  contentEnhancer,
  contextEnricher,
  formatStandardizer
} from '../transformers';

/**
 * 聊天请求接口
 */
export interface ChatPipelineInput {
  message: string;
  sessionId?: string;
  userId?: string;
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
  };
  context?: {
    includeHistory?: boolean;
    historyLength?: number;
    includeProfile?: boolean;
  };
}

/**
 * 聊天管道输出接口
 */
export interface ChatPipelineOutput {
  message: string;
  sessionId: string;
  userId: string;
  options: {
    model: string;
    temperature: number;
    maxTokens: number;
    stream: boolean;
  };
  context: {
    includeHistory: boolean;
    historyLength: number;
    includeProfile: boolean;
  };
  metadata: {
    processingTimestamp: string;
    source: string;
    contentLength: number;
    enhancements: string[];
    standardizedAt: string;
    version: string;
  };
  processingContext: {
    requestId: string;
    timestamp: Date;
    source: string;
    pipeline: string;
    version: string;
  };
  intent: {
    primary: string;
    confidence: number;
    details: Record<string, boolean>;
  };
  sessionContext: {
    sessionId: string;
    userId: string;
    isNewSession: boolean;
    messageCount: number;
  };
}

/**
 * 创建聊天数据处理管道
 */
export function createChatPipeline(): UnifiedDataPipeline<ChatPipelineInput, ChatPipelineOutput> {
  const pipeline = new UnifiedDataPipeline<ChatPipelineInput, ChatPipelineOutput>({
    name: 'ChatDataPipeline',
    description: '聊天请求数据处理管道',
    enableParallelValidation: true,
    enableParallelTransformation: false, // 转换需要按顺序进行
    maxRetries: 3,
    timeoutMs: 10000, // 10秒超时
    enableMetrics: true
  });

  // 添加验证器（按优先级顺序）
  pipeline
    .addValidator(basicDataValidator)      // 优先级 1: 基础数据验证
    .addValidator(chatMessageValidator)    // 优先级 2: 聊天消息验证
    .addValidator(securityValidator)       // 优先级 3: 安全验证
    .addValidator(contentQualityValidator); // 优先级 4: 内容质量验证

  // 添加转换器（按优先级顺序）
  pipeline
    .addTransformer(chatRequestNormalizer) // 优先级 1: 请求标准化
    .addTransformer(contentEnhancer)       // 优先级 2: 内容增强
    .addTransformer(contextEnricher)       // 优先级 3: 上下文丰富
    .addTransformer(formatStandardizer);   // 优先级 4: 格式标准化

  return pipeline;
}

/**
 * 聊天管道单例实例
 */
export const chatPipeline = createChatPipeline();

/**
 * 聊天管道处理函数
 */
export async function processChatRequest(
  data: ChatPipelineInput,
  requestId: string,
  source: string = 'chat-api'
) {
  const context = {
    requestId,
    timestamp: new Date(),
    source,
    metadata: {
      apiVersion: '2.0.0',
      pipelineVersion: '1.0.0'
    }
  };

  console.log(`🔄 开始处理聊天请求: ${requestId}`);
  
  const result = await chatPipeline.process(data, context);
  
  if (result.success) {
    console.log(`✅ 聊天请求处理成功: ${requestId} - ${result.processingTime}ms`);
  } else {
    console.error(`❌ 聊天请求处理失败: ${requestId}`, result.errors);
  }
  
  return result;
}

/**
 * 获取聊天管道统计信息
 */
export function getChatPipelineStats() {
  return {
    ...chatPipeline.getStats(),
    config: chatPipeline.getConfig(),
    description: '专门用于处理聊天请求的数据管道，包含完整的验证和转换流程'
  };
}

/**
 * 验证聊天管道配置
 */
export function validateChatPipelineConfig() {
  const stats = chatPipeline.getStats();
  const issues: string[] = [];
  
  // 检查验证器配置
  if (stats.validatorsCount === 0) {
    issues.push('缺少数据验证器');
  }
  
  if (!stats.validators.find(v => v.name === 'BasicDataValidator')) {
    issues.push('缺少基础数据验证器');
  }
  
  if (!stats.validators.find(v => v.name === 'ChatMessageValidator')) {
    issues.push('缺少聊天消息验证器');
  }
  
  // 检查转换器配置
  if (stats.transformersCount === 0) {
    issues.push('缺少数据转换器');
  }
  
  if (!stats.transformers.find(t => t.name === 'ChatRequestNormalizer')) {
    issues.push('缺少请求标准化转换器');
  }
  
  if (!stats.transformers.find(t => t.name === 'FormatStandardizer')) {
    issues.push('缺少格式标准化转换器');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    stats
  };
}

// 导出类型
export type { ChatPipelineInput, ChatPipelineOutput };
