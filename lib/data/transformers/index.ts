/**
 * Data Transformers - 数据转换器集合
 * 
 * 提供常用的数据转换器实现
 */

import { 
  DataTransformer, 
  DataTransformationResult, 
  DataProcessingContext 
} from '../unified-data-pipeline';

/**
 * 聊天请求标准化转换器
 */
export class ChatRequestNormalizer implements DataTransformer<any, any> {
  readonly name = 'ChatRequestNormalizer';
  readonly priority = 1;

  async transform(data: any, context: DataProcessingContext): Promise<DataTransformationResult<any>> {
    try {
      const normalized = {
        message: data.message?.trim() || '',
        sessionId: data.sessionId?.trim() || this.generateSessionId(),
        userId: data.userId?.trim() || 'anonymous',
        timestamp: new Date().toISOString(),
        requestId: context.requestId,
        options: this.normalizeOptions(data.options),
        context: this.normalizeContext(data.context)
      };

      return {
        success: true,
        data: normalized,
        metadata: {
          originalFields: Object.keys(data),
          normalizedFields: Object.keys(normalized),
          transformations: [
            'message_trimmed',
            'sessionId_generated_if_missing',
            'userId_defaulted_if_missing',
            'timestamp_added',
            'requestId_added',
            'options_normalized',
            'context_normalized'
          ]
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private normalizeOptions(options: any): any {
    const defaultOptions = {
      model: 'gemini-pro',
      temperature: 0.7,
      maxTokens: 2048,
      stream: true
    };

    if (!options || typeof options !== 'object') {
      return defaultOptions;
    }

    return {
      model: options.model || defaultOptions.model,
      temperature: typeof options.temperature === 'number' ? 
        Math.max(0, Math.min(2, options.temperature)) : defaultOptions.temperature,
      maxTokens: typeof options.maxTokens === 'number' ? 
        Math.max(1, Math.min(8192, options.maxTokens)) : defaultOptions.maxTokens,
      stream: typeof options.stream === 'boolean' ? options.stream : defaultOptions.stream
    };
  }

  private normalizeContext(contextOptions: any): any {
    const defaultContext = {
      includeHistory: true,
      historyLength: 6,
      includeProfile: true
    };

    if (!contextOptions || typeof contextOptions !== 'object') {
      return defaultContext;
    }

    return {
      includeHistory: typeof contextOptions.includeHistory === 'boolean' ? 
        contextOptions.includeHistory : defaultContext.includeHistory,
      historyLength: typeof contextOptions.historyLength === 'number' ? 
        Math.max(0, Math.min(20, contextOptions.historyLength)) : defaultContext.historyLength,
      includeProfile: typeof contextOptions.includeProfile === 'boolean' ? 
        contextOptions.includeProfile : defaultContext.includeProfile
    };
  }
}

/**
 * 内容增强转换器
 */
export class ContentEnhancer implements DataTransformer<any, any> {
  readonly name = 'ContentEnhancer';
  readonly priority = 2;

  async transform(data: any, context: DataProcessingContext): Promise<DataTransformationResult<any>> {
    try {
      const enhanced = { ...data };
      const enhancements: string[] = [];

      // 增强消息内容
      if (enhanced.message) {
        const originalMessage = enhanced.message;
        enhanced.message = this.enhanceMessage(enhanced.message);
        
        if (enhanced.message !== originalMessage) {
          enhancements.push('message_enhanced');
        }
      }

      // 添加元数据
      enhanced.metadata = {
        ...enhanced.metadata,
        processingTimestamp: new Date().toISOString(),
        source: context.source,
        contentLength: enhanced.message?.length || 0,
        enhancements
      };

      return {
        success: true,
        data: enhanced,
        metadata: {
          enhancementsApplied: enhancements,
          originalContentLength: data.message?.length || 0,
          enhancedContentLength: enhanced.message?.length || 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private enhanceMessage(message: string): string {
    // 修复常见的拼写错误
    let enhanced = message
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/([.!?])\s*([a-z])/g, '$1 $2') // 确保句号后有空格
      .trim();

    // 确保问句以问号结尾
    if (this.isQuestion(enhanced) && !enhanced.endsWith('?')) {
      enhanced += '?';
    }

    return enhanced;
  }

  private isQuestion(text: string): boolean {
    const questionWords = ['what', 'how', 'why', 'when', 'where', 'who', 'which', 'whose', 'whom'];
    const lowerText = text.toLowerCase();
    
    return questionWords.some(word => lowerText.startsWith(word + ' ')) ||
           lowerText.includes('什么') ||
           lowerText.includes('怎么') ||
           lowerText.includes('为什么') ||
           lowerText.includes('如何') ||
           lowerText.includes('哪里') ||
           lowerText.includes('谁');
  }
}

/**
 * 上下文丰富转换器
 */
export class ContextEnricher implements DataTransformer<any, any> {
  readonly name = 'ContextEnricher';
  readonly priority = 3;

  async transform(data: any, context: DataProcessingContext): Promise<DataTransformationResult<any>> {
    try {
      const enriched = { ...data };
      
      // 添加处理上下文信息
      enriched.processingContext = {
        requestId: context.requestId,
        timestamp: context.timestamp,
        source: context.source,
        pipeline: 'UnifiedDataPipeline',
        version: '1.0.0'
      };

      // 分析消息意图
      if (enriched.message) {
        enriched.intent = this.analyzeIntent(enriched.message);
      }

      // 添加会话上下文
      enriched.sessionContext = {
        sessionId: enriched.sessionId,
        userId: enriched.userId,
        isNewSession: !data.sessionId,
        messageCount: 1 // 这里可以从会话历史中获取
      };

      return {
        success: true,
        data: enriched,
        metadata: {
          contextFieldsAdded: ['processingContext', 'intent', 'sessionContext'],
          intentAnalysis: enriched.intent
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private analyzeIntent(message: string): any {
    const lowerMessage = message.toLowerCase();
    
    // 简单的意图分析
    const intents = {
      question: this.isQuestion(lowerMessage),
      greeting: this.isGreeting(lowerMessage),
      request: this.isRequest(lowerMessage),
      complaint: this.isComplaint(lowerMessage),
      compliment: this.isCompliment(lowerMessage)
    };

    const primaryIntent = Object.entries(intents)
      .filter(([_, value]) => value)
      .map(([key, _]) => key)[0] || 'general';

    return {
      primary: primaryIntent,
      confidence: this.calculateConfidence(intents),
      details: intents
    };
  }

  private isQuestion(text: string): boolean {
    return text.includes('?') || 
           text.includes('什么') || 
           text.includes('怎么') || 
           text.includes('为什么') ||
           text.includes('如何') ||
           /^(what|how|why|when|where|who|which)/.test(text);
  }

  private isGreeting(text: string): boolean {
    const greetings = ['hello', 'hi', 'hey', '你好', '您好', '早上好', '下午好', '晚上好'];
    return greetings.some(greeting => text.includes(greeting));
  }

  private isRequest(text: string): boolean {
    const requestWords = ['please', 'can you', 'could you', '请', '能否', '可以'];
    return requestWords.some(word => text.includes(word));
  }

  private isComplaint(text: string): boolean {
    const complaintWords = ['problem', 'issue', 'wrong', 'error', '问题', '错误', '不对'];
    return complaintWords.some(word => text.includes(word));
  }

  private isCompliment(text: string): boolean {
    const complimentWords = ['good', 'great', 'excellent', 'thank', '好', '棒', '谢谢', '感谢'];
    return complimentWords.some(word => text.includes(word));
  }

  private calculateConfidence(intents: Record<string, boolean>): number {
    const trueCount = Object.values(intents).filter(Boolean).length;
    return trueCount > 0 ? Math.min(0.9, 0.3 + trueCount * 0.2) : 0.1;
  }
}

/**
 * 格式标准化转换器
 */
export class FormatStandardizer implements DataTransformer<any, any> {
  readonly name = 'FormatStandardizer';
  readonly priority = 4;

  async transform(data: any, context: DataProcessingContext): Promise<DataTransformationResult<any>> {
    try {
      // 确保输出格式符合API标准
      const standardized = {
        // 核心字段
        message: data.message,
        sessionId: data.sessionId,
        userId: data.userId,
        
        // 选项字段
        options: data.options,
        context: data.context,
        
        // 元数据字段
        metadata: {
          ...data.metadata,
          standardizedAt: new Date().toISOString(),
          version: '2.0.0'
        },
        
        // 处理上下文
        processingContext: data.processingContext,
        
        // 意图分析
        intent: data.intent,
        
        // 会话上下文
        sessionContext: data.sessionContext
      };

      return {
        success: true,
        data: standardized,
        metadata: {
          standardizationApplied: true,
          outputFormat: 'SelfMirror-v2.0',
          fieldsCount: Object.keys(standardized).length
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// 导出所有转换器
export const transformers = {
  ChatRequestNormalizer,
  ContentEnhancer,
  ContextEnricher,
  FormatStandardizer
};

// 导出预配置的转换器实例
export const chatRequestNormalizer = new ChatRequestNormalizer();
export const contentEnhancer = new ContentEnhancer();
export const contextEnricher = new ContextEnricher();
export const formatStandardizer = new FormatStandardizer();
