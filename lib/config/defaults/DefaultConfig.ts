/**
 * SelfMirror 默认配置
 * 提供所有系统组件的默认配置值
 */

import { SelfMirrorConfig, AppConfig, Environment } from '../interfaces/IConfig';

/**
 * 获取默认配置
 */
export function getDefaultConfig(): SelfMirrorConfig {
  const environment = detectEnvironment();
  
  return {
    app: getDefaultAppConfig(environment),
    ai: getDefaultAIConfig(environment),
    storage: getDefaultStorageConfig(environment),
    rag: getDefaultRAGConfig(),
    debug: getDefaultDebugConfig(environment),
    security: getDefaultSecurityConfig(environment),
    performance: getDefaultPerformanceConfig(environment)
  };
}

/**
 * 获取应用默认配置
 */
function getDefaultAppConfig(environment: Environment) {
  return {
    name: 'SelfMirror',
    version: '2.0.0',
    environment,
    debug: environment === 'development',
    logLevel: environment === 'production' ? 'warn' : 'debug',
    port: parseInt(process.env.PORT || '3000'),
    baseUrl: process.env.BASE_URL || 'http://localhost:3000',
    timezone: 'Asia/Shanghai',
    locale: 'zh-CN'
  } as const;
}

/**
 * 获取 AI 默认配置
 */
function getDefaultAIConfig(environment: Environment) {
  return {
    defaultProvider: 'gemini' as const,
    models: {
      gemini: {
        apiKey: process.env.GOOGLE_API_KEY || '',
        model: 'gemini-2.5-flash-preview-05-20',
        proxyUrl: environment === 'development' ? process.env.PROXY_URL : undefined,
        thinkingBudget: 0
      },
      doubao: {
        apiKey: process.env.DOUBAO_API_KEY || '',
        model: 'doubao-pro-4k',
        baseUrl: 'https://ark.cn-beijing.volces.com/api/v3'
      }
    },
    cache: {
      enabled: true,
      ttl: environment === 'production' ? 600000 : 300000, // 生产环境更长缓存
      maxSize: environment === 'production' ? 100 : 50
    },
    healthCheck: {
      enabled: true,
      interval: 60000, // 1 分钟
      timeout: 10000   // 10 秒
    },
    retry: {
      maxRetries: environment === 'production' ? 5 : 3,
      retryDelay: 1000,
      backoffFactor: 2
    }
  };
}

/**
 * 获取存储默认配置
 */
function getDefaultStorageConfig(environment: Environment) {
  const isNode = typeof window === 'undefined';
  const defaultProvider = isNode ? 'filesystem' as const : 'indexeddb' as const;

  return {
    defaultProvider,
    providers: {
      filesystem: {
        basePath: process.cwd() + '/memory',
        encoding: 'utf-8',
        fileMode: 0o644,
        dirMode: 0o755,
        createDirs: true,
        compression: environment === 'production',
        encryption: false
      },
      indexeddb: {
        databaseName: 'SelfMirrorDB',
        version: 1,
        storeName: 'documents',
        indexes: [
          { name: 'by-created', keyPath: 'metadata.createdAt' },
          { name: 'by-updated', keyPath: 'metadata.updatedAt' },
          { name: 'by-tags', keyPath: 'metadata.tags', multiEntry: true }
        ]
      },
      memory: {
        maxSize: 50 * 1024 * 1024, // 50MB
        persistent: true,
        lruSize: 1000
      }
    },
    cache: {
      enabled: true,
      ttl: 300000, // 5 分钟
      maxSize: 1000,
      strategy: 'lru' as const
    },
    backup: {
      enabled: environment === 'production',
      interval: 24 * 60 * 60 * 1000, // 24 小时
      maxBackups: 7,
      compression: true,
      encryption: false,
      location: './backups'
    }
  };
}

/**
 * 获取 RAG 默认配置
 */
function getDefaultRAGConfig() {
  return {
    embedding: {
      provider: 'local' as const,
      model: 'bge-large-zh-v1.5',
      dimensions: 1024,
      batchSize: 32,
      maxInputLength: 512,
      normalize: true
    },
    retrieval: {
      topK: 10,
      threshold: 0.7,
      algorithm: 'cosine' as const,
      reranking: {
        enabled: false,
        topK: 5
      }
    },
    indexing: {
      chunkSize: 500,
      chunkOverlap: 50,
      batchSize: 10,
      updateStrategy: 'incremental' as const
    },
    meaningRAG: {
      enabled: true,
      extractionModel: 'gemini-2.5-flash-preview-05-20',
      meaningThreshold: 0.8,
      maxMeaningChunks: 5,
      hybridWeight: 0.7
    }
  };
}

/**
 * 获取调试默认配置
 */
function getDefaultDebugConfig(environment: Environment) {
  const logLevel = environment === 'production' ? 'warn' as const : 'debug' as const;

  return {
    enabled: environment !== 'production',
    panels: {
      defaultPanel: 'chat',
      layout: {
        chatWidth: '50%',
        debugWidth: '50%',
        orientation: 'horizontal' as const
      },
      refreshInterval: 5000, // 5 秒
      maxLogEntries: 1000
    },
    logging: {
      level: logLevel,
      enableConsole: true,
      enableFile: environment === 'production',
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5
    },
    monitoring: {
      enabled: true,
      metricsInterval: 30000, // 30 秒
      healthCheckInterval: 60000, // 1 分钟
      alertThresholds: {
        memoryUsage: 0.8,      // 80%
        responseTime: 5000,    // 5 秒
        errorRate: 0.05,       // 5%
        storageUsage: 0.9      // 90%
      }
    }
  };
}

/**
 * 获取安全默认配置
 */
function getDefaultSecurityConfig(environment: Environment) {
  return {
    apiKeys: {
      rotation: environment === 'production',
      rotationInterval: 30 * 24 * 60 * 60 * 1000, // 30 天
      validation: true,
      masking: true
    },
    encryption: {
      enabled: environment === 'production',
      algorithm: 'aes-256-gcm',
      keySize: 256,
      saltSize: 16
    },
    accessControl: {
      enabled: environment === 'production',
      allowedOrigins: environment === 'development' 
        ? ['http://localhost:3000', 'http://127.0.0.1:3000']
        : [],
      rateLimiting: {
        enabled: environment === 'production',
        requestsPerMinute: 100,
        burstSize: 20
      }
    }
  };
}

/**
 * 获取性能默认配置
 */
function getDefaultPerformanceConfig(environment: Environment) {
  return {
    concurrency: {
      maxConcurrentRequests: environment === 'production' ? 50 : 10,
      queueSize: 100,
      timeout: 30000 // 30 秒
    },
    cache: {
      enabled: true,
      strategy: 'lru' as const,
      ttl: 300000, // 5 分钟
      maxSize: environment === 'production' ? 1000 : 500,
      compression: environment === 'production'
    },
    optimization: {
      enableCompression: environment === 'production',
      enableMinification: environment === 'production',
      enableLazyLoading: true,
      enablePrefetching: environment === 'production'
    }
  };
}

/**
 * 检测运行环境
 */
function detectEnvironment(): Environment {
  if (typeof window !== 'undefined') {
    return 'development'; // 浏览器环境默认为开发环境
  }
  
  const env = process.env.NODE_ENV as Environment;
  return ['development', 'test', 'staging', 'production'].includes(env) ? env : 'development';
}

/**
 * 获取环境特定的配置
 */
export function getEnvironmentConfig(environment: Environment): Partial<SelfMirrorConfig> {
  switch (environment) {
    case 'development':
      return getDevelopmentConfig();
    case 'test':
      return getTestConfig();
    case 'staging':
      return getStagingConfig();
    case 'production':
      return getProductionConfig();
    default:
      return {};
  }
}

/**
 * 开发环境配置
 */
function getDevelopmentConfig(): any {
  return {
    app: {
      debug: true,
      logLevel: 'debug'
    },
    ai: {
      models: {
        gemini: {
          proxyUrl: process.env.PROXY_URL || 'http://127.0.0.1:7897'
        }
      }
    },
    debug: {
      enabled: true,
      logging: {
        level: 'debug',
        enableConsole: true,
        enableFile: false
      }
    },
    security: {
      encryption: {
        enabled: false
      },
      accessControl: {
        enabled: false
      }
    }
  };
}

/**
 * 测试环境配置
 */
function getTestConfig(): any {
  return {
    app: {
      debug: false,
      logLevel: 'warn'
    },
    debug: {
      enabled: false
    },
    storage: {
      providers: {
        memory: {
          maxSize: 10 * 1024 * 1024 // 10MB for tests
        }
      }
    }
  };
}

/**
 * 预发布环境配置
 */
function getStagingConfig(): any {
  return {
    app: {
      debug: false,
      logLevel: 'info'
    },
    security: {
      encryption: {
        enabled: true
      },
      accessControl: {
        enabled: true
      }
    }
  };
}

/**
 * 生产环境配置
 */
function getProductionConfig(): any {
  return {
    app: {
      debug: false,
      logLevel: 'warn'
    },
    ai: {
      cache: {
        ttl: 600000, // 10 分钟
        maxSize: 100
      },
      retry: {
        maxRetries: 5
      }
    },
    debug: {
      enabled: false,
      logging: {
        enableFile: true,
        enableConsole: false
      }
    },
    security: {
      encryption: {
        enabled: true
      },
      accessControl: {
        enabled: true
      }
    },
    performance: {
      cache: {
        compression: true
      },
      optimization: {
        enableCompression: true,
        enableMinification: true,
        enablePrefetching: true
      }
    }
  };
}

export default getDefaultConfig;
