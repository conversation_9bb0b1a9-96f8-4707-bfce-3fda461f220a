/**
 * 配置工具函数
 * 提供配置操作的便捷方法
 */

import { SelfMirrorConfig } from '../interfaces/IConfig';

/**
 * 深度合并配置对象
 */
export function mergeConfigs(base: any, override: any): any {
  if (!override) return base;
  if (!base) return override;

  const result = { ...base };

  for (const key in override) {
    if (override[key] !== null && typeof override[key] === 'object' && !Array.isArray(override[key])) {
      result[key] = mergeConfigs(result[key] || {}, override[key]);
    } else {
      result[key] = override[key];
    }
  }

  return result;
}

/**
 * 获取配置值的路径
 */
export function getConfigValue(config: any, path: string): any {
  const keys = path.split('.');
  let current = config;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return undefined;
    }
  }

  return current;
}

/**
 * 设置配置值的路径
 */
export function setConfigValue(config: any, path: string, value: any): any {
  const keys = path.split('.');
  const result = { ...config };
  let current = result;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    } else {
      current[key] = { ...current[key] };
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
  return result;
}

/**
 * 删除配置值的路径
 */
export function deleteConfigValue(config: any, path: string): any {
  const keys = path.split('.');
  const result = { ...config };
  let current = result;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      return result; // 路径不存在
    }
    current[key] = { ...current[key] };
    current = current[key];
  }

  delete current[keys[keys.length - 1]];
  return result;
}

/**
 * 比较两个配置对象的差异
 */
export function getConfigDiff(oldConfig: any, newConfig: any, prefix: string = ''): string[] {
  const changes: string[] = [];

  const allKeys = new Set([
    ...Object.keys(oldConfig || {}),
    ...Object.keys(newConfig || {})
  ]);

  for (const key of allKeys) {
    const path = prefix ? `${prefix}.${key}` : key;
    const oldValue = oldConfig?.[key];
    const newValue = newConfig?.[key];

    if (oldValue !== newValue) {
      if (typeof oldValue === 'object' && typeof newValue === 'object' && 
          oldValue !== null && newValue !== null && 
          !Array.isArray(oldValue) && !Array.isArray(newValue)) {
        changes.push(...getConfigDiff(oldValue, newValue, path));
      } else {
        changes.push(path);
      }
    }
  }

  return changes;
}

/**
 * 扁平化配置对象
 */
export function flattenConfig(config: any, prefix: string = ''): Record<string, any> {
  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(config)) {
    const path = prefix ? `${prefix}.${key}` : key;

    if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
      Object.assign(result, flattenConfig(value, path));
    } else {
      result[path] = value;
    }
  }

  return result;
}

/**
 * 从扁平化对象重建配置
 */
export function unflattenConfig(flatConfig: Record<string, any>): any {
  const result: any = {};

  for (const [path, value] of Object.entries(flatConfig)) {
    const keys = path.split('.');
    let current = result;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key]) {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
  }

  return result;
}

/**
 * 清理配置对象（移除 undefined 值）
 */
export function cleanConfig(config: any): any {
  if (config === null || config === undefined) {
    return config;
  }

  if (Array.isArray(config)) {
    return config.map(cleanConfig).filter(item => item !== undefined);
  }

  if (typeof config === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(config)) {
      const cleanedValue = cleanConfig(value);
      if (cleanedValue !== undefined) {
        cleaned[key] = cleanedValue;
      }
    }
    return cleaned;
  }

  return config;
}

/**
 * 验证配置路径是否存在
 */
export function hasConfigPath(config: any, path: string): boolean {
  return getConfigValue(config, path) !== undefined;
}

/**
 * 获取配置的所有路径
 */
export function getConfigPaths(config: any, prefix: string = ''): string[] {
  const paths: string[] = [];

  for (const [key, value] of Object.entries(config)) {
    const path = prefix ? `${prefix}.${key}` : key;
    paths.push(path);

    if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
      paths.push(...getConfigPaths(value, path));
    }
  }

  return paths;
}

/**
 * 配置对象深拷贝
 */
export function cloneConfig<T>(config: T): T {
  return JSON.parse(JSON.stringify(config));
}

/**
 * 格式化配置为可读字符串
 */
export function formatConfig(config: any, indent: number = 2): string {
  return JSON.stringify(config, null, indent);
}

/**
 * 从字符串解析配置
 */
export function parseConfig(configString: string): any {
  try {
    return JSON.parse(configString);
  } catch (error) {
    throw new Error(`配置解析失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 配置对象大小计算（字节）
 */
export function getConfigSize(config: any): number {
  return new Blob([JSON.stringify(config)]).size;
}

/**
 * 配置压缩（移除空白和注释）
 */
export function compressConfig(config: any): string {
  return JSON.stringify(config);
}

/**
 * 配置美化（添加缩进和格式化）
 */
export function beautifyConfig(config: any): string {
  return JSON.stringify(config, null, 2);
}

/**
 * 配置安全化（隐藏敏感信息）
 */
export function sanitizeConfig(config: any): any {
  const sensitiveKeys = ['apiKey', 'password', 'secret', 'token', 'key'];
  
  function sanitizeValue(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitizeValue);
    }

    if (typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          sanitized[key] = typeof value === 'string' && value.length > 0 ? '***' : value;
        } else {
          sanitized[key] = sanitizeValue(value);
        }
      }
      return sanitized;
    }

    return obj;
  }

  return sanitizeValue(config);
}

/**
 * 配置版本比较
 */
export function compareConfigVersions(config1: any, config2: any): {
  isEqual: boolean;
  differences: string[];
  summary: {
    added: string[];
    removed: string[];
    modified: string[];
  };
} {
  const paths1 = new Set(getConfigPaths(config1));
  const paths2 = new Set(getConfigPaths(config2));

  const added = Array.from(paths2).filter(path => !paths1.has(path));
  const removed = Array.from(paths1).filter(path => !paths2.has(path));
  const modified: string[] = [];

  for (const path of paths1) {
    if (paths2.has(path)) {
      const value1 = getConfigValue(config1, path);
      const value2 = getConfigValue(config2, path);
      if (JSON.stringify(value1) !== JSON.stringify(value2)) {
        modified.push(path);
      }
    }
  }

  const differences = [...added, ...removed, ...modified];

  return {
    isEqual: differences.length === 0,
    differences,
    summary: { added, removed, modified }
  };
}

/**
 * 配置模板生成
 */
export function generateConfigTemplate(schema: any): any {
  function generateFromSchema(schemaNode: any): any {
    if (schemaNode.type === 'object' && schemaNode.properties) {
      const obj: any = {};
      for (const [key, prop] of Object.entries(schemaNode.properties)) {
        obj[key] = generateFromSchema(prop);
      }
      return obj;
    }

    if (schemaNode.type === 'array') {
      return [];
    }

    if (schemaNode.default !== undefined) {
      return schemaNode.default;
    }

    switch (schemaNode.type) {
      case 'string': return '';
      case 'number': return 0;
      case 'boolean': return false;
      default: return null;
    }
  }

  return generateFromSchema(schema);
}

export default {
  mergeConfigs,
  getConfigValue,
  setConfigValue,
  deleteConfigValue,
  getConfigDiff,
  flattenConfig,
  unflattenConfig,
  cleanConfig,
  hasConfigPath,
  getConfigPaths,
  cloneConfig,
  formatConfig,
  parseConfig,
  getConfigSize,
  compressConfig,
  beautifyConfig,
  sanitizeConfig,
  compareConfigVersions,
  generateConfigTemplate
};
