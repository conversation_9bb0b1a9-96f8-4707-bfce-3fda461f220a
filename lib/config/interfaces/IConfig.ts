/**
 * SelfMirror 中央配置管理系统
 * 统一管理所有系统配置，支持类型安全和热重载
 */

// TODO: 重构后重新启用
// import { AIFactoryConfig } from '@/lib/ai/interfaces/IAIModel';
type AIFactoryConfig = any; // 临时类型定义
import { IMemoryStorage } from '@/lib/storage/interfaces/IMemoryStorage';

// ==================== 核心配置接口 ====================

/**
 * SelfMirror 主配置接口
 */
export interface SelfMirrorConfig {
  /** 应用基础配置 */
  app: AppConfig;
  
  /** AI 模型配置 */
  ai: AIFactoryConfig;
  
  /** 存储系统配置 */
  storage: StorageConfig;
  
  /** RAG 系统配置 */
  rag: RAGConfig;
  
  /** 调试控制台配置 */
  debug: DebugConfig;
  
  /** 安全配置 */
  security: SecurityConfig;
  
  /** 性能配置 */
  performance: PerformanceConfig;
}

// ==================== 应用配置 ====================

/**
 * 应用基础配置
 */
export interface AppConfig {
  /** 应用名称 */
  name: string;
  
  /** 应用版本 */
  version: string;
  
  /** 运行环境 */
  environment: Environment;
  
  /** 调试模式 */
  debug: boolean;
  
  /** 日志级别 */
  logLevel: LogLevel;
  
  /** 服务端口 */
  port: number;
  
  /** 基础 URL */
  baseUrl: string;
  
  /** 时区设置 */
  timezone: string;
  
  /** 语言设置 */
  locale: string;
}

/**
 * 运行环境
 */
export type Environment = 'development' | 'test' | 'staging' | 'production';

/**
 * 日志级别
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// ==================== 存储配置 ====================

/**
 * 存储系统配置
 */
export interface StorageConfig {
  /** 默认存储提供商 */
  defaultProvider: StorageProvider;
  
  /** 存储提供商配置 */
  providers: StorageProviderConfigs;
  
  /** 缓存配置 */
  cache: StorageCacheConfig;
  
  /** 备份配置 */
  backup: BackupConfig;
}

/**
 * 存储提供商
 */
export type StorageProvider = 'filesystem' | 'indexeddb' | 'memory';

/**
 * 存储提供商配置
 */
export interface StorageProviderConfigs {
  filesystem?: FileSystemStorageConfig;
  indexeddb?: IndexedDBStorageConfig;
  memory?: MemoryStorageConfig;
}

/**
 * 文件系统存储配置
 */
export interface FileSystemStorageConfig {
  basePath: string;
  encoding: string;
  fileMode: number;
  dirMode: number;
  createDirs: boolean;
  compression: boolean;
  encryption: boolean;
}

/**
 * IndexedDB 存储配置
 */
export interface IndexedDBStorageConfig {
  databaseName: string;
  version: number;
  storeName: string;
  indexes: IndexConfig[];
}

/**
 * 内存存储配置
 */
export interface MemoryStorageConfig {
  maxSize: number;
  persistent: boolean;
  lruSize: number;
  provider?: string;
}

/**
 * 索引配置
 */
export interface IndexConfig {
  name: string;
  keyPath: string;
  multiEntry?: boolean;
  unique?: boolean;
}

/**
 * 存储缓存配置
 */
export interface StorageCacheConfig {
  enabled: boolean;
  ttl: number;
  maxSize: number;
  strategy: CacheStrategy;
}

/**
 * 缓存策略
 */
export type CacheStrategy = 'lru' | 'lfu' | 'fifo';

/**
 * 备份配置
 */
export interface BackupConfig {
  enabled: boolean;
  interval: number;
  maxBackups: number;
  compression: boolean;
  encryption: boolean;
  location: string;
}

// ==================== RAG 配置 ====================

/**
 * RAG 系统配置
 */
export interface RAGConfig {
  /** 向量模型配置 */
  embedding: EmbeddingConfig;
  
  /** 检索配置 */
  retrieval: RetrievalConfig;
  
  /** 索引配置 */
  indexing: IndexingConfig;
  
  /** 意义 RAG 配置 */
  meaningRAG: MeaningRAGConfig;
}

/**
 * 向量模型配置
 */
export interface EmbeddingConfig {
  provider: EmbeddingProvider;
  model: string;
  dimensions: number;
  batchSize: number;
  maxInputLength: number;
  normalize: boolean;
}

/**
 * 向量模型提供商
 */
export type EmbeddingProvider = 'local' | 'openai' | 'gemini';

/**
 * 检索配置
 */
export interface RetrievalConfig {
  topK: number;
  threshold: number;
  algorithm: RetrievalAlgorithm;
  reranking: RerankingConfig;
}

/**
 * 检索算法
 */
export type RetrievalAlgorithm = 'cosine' | 'euclidean' | 'dot_product';

/**
 * 重排序配置
 */
export interface RerankingConfig {
  enabled: boolean;
  model?: string;
  topK: number;
}

/**
 * 索引配置
 */
export interface IndexingConfig {
  chunkSize: number;
  chunkOverlap: number;
  batchSize: number;
  updateStrategy: IndexUpdateStrategy;
}

/**
 * 索引更新策略
 */
export type IndexUpdateStrategy = 'incremental' | 'full' | 'hybrid';

/**
 * 意义 RAG 配置
 */
export interface MeaningRAGConfig {
  enabled: boolean;
  extractionModel: string;
  meaningThreshold: number;
  maxMeaningChunks: number;
  hybridWeight: number;
}

// ==================== 调试配置 ====================

/**
 * 调试控制台配置
 */
export interface DebugConfig {
  /** 是否启用调试控制台 */
  enabled: boolean;
  
  /** 调试面板配置 */
  panels: DebugPanelConfig;
  
  /** 日志配置 */
  logging: DebugLoggingConfig;
  
  /** 性能监控配置 */
  monitoring: MonitoringConfig;
}

/**
 * 调试面板配置
 */
export interface DebugPanelConfig {
  /** 默认激活的面板 */
  defaultPanel: string;
  
  /** 面板布局 */
  layout: PanelLayout;
  
  /** 自动刷新间隔 */
  refreshInterval: number;
  
  /** 最大日志条数 */
  maxLogEntries: number;
}

/**
 * 面板布局
 */
export interface PanelLayout {
  chatWidth: string;
  debugWidth: string;
  orientation: 'horizontal' | 'vertical';
}

/**
 * 调试日志配置
 */
export interface DebugLoggingConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  maxFileSize: number;
  maxFiles: number;
}

/**
 * 监控配置
 */
export interface MonitoringConfig {
  enabled: boolean;
  metricsInterval: number;
  healthCheckInterval: number;
  alertThresholds: AlertThresholds;
}

/**
 * 告警阈值
 */
export interface AlertThresholds {
  memoryUsage: number;
  responseTime: number;
  errorRate: number;
  storageUsage: number;
}

// ==================== 安全配置 ====================

/**
 * 安全配置
 */
export interface SecurityConfig {
  /** API 密钥管理 */
  apiKeys: ApiKeyConfig;
  
  /** 数据加密 */
  encryption: EncryptionConfig;
  
  /** 访问控制 */
  accessControl: AccessControlConfig;
}

/**
 * API 密钥配置
 */
export interface ApiKeyConfig {
  rotation: boolean;
  rotationInterval: number;
  validation: boolean;
  masking: boolean;
}

/**
 * 加密配置
 */
export interface EncryptionConfig {
  enabled: boolean;
  algorithm: string;
  keySize: number;
  saltSize: number;
}

/**
 * 访问控制配置
 */
export interface AccessControlConfig {
  enabled: boolean;
  allowedOrigins: string[];
  rateLimiting: RateLimitConfig;
}

/**
 * 速率限制配置
 */
export interface RateLimitConfig {
  enabled: boolean;
  requestsPerMinute: number;
  burstSize: number;
}

// ==================== 性能配置 ====================

/**
 * 性能配置
 */
export interface PerformanceConfig {
  /** 并发配置 */
  concurrency: ConcurrencyConfig;
  
  /** 缓存配置 */
  cache: PerformanceCacheConfig;
  
  /** 优化配置 */
  optimization: OptimizationConfig;
}

/**
 * 并发配置
 */
export interface ConcurrencyConfig {
  maxConcurrentRequests: number;
  queueSize: number;
  timeout: number;
}

/**
 * 性能缓存配置
 */
export interface PerformanceCacheConfig {
  enabled: boolean;
  strategy: CacheStrategy;
  ttl: number;
  maxSize: number;
  compression: boolean;
}

/**
 * 优化配置
 */
export interface OptimizationConfig {
  enableCompression: boolean;
  enableMinification: boolean;
  enableLazyLoading: boolean;
  enablePrefetching: boolean;
}

// ==================== 配置管理接口 ====================

/**
 * 配置管理器接口
 */
export interface IConfigManager {
  /** 获取完整配置 */
  getConfig(): SelfMirrorConfig;
  
  /** 获取部分配置 */
  getPartialConfig<T extends keyof SelfMirrorConfig>(section: T): SelfMirrorConfig[T];
  
  /** 更新配置 */
  updateConfig(config: Partial<SelfMirrorConfig>): Promise<void>;
  
  /** 重置配置 */
  resetConfig(): Promise<void>;
  
  /** 验证配置 */
  validateConfig(config: Partial<SelfMirrorConfig>): ConfigValidationResult;
  
  /** 保存配置 */
  saveConfig(): Promise<void>;
  
  /** 加载配置 */
  loadConfig(): Promise<void>;
  
  /** 监听配置变化 */
  onConfigChange(callback: ConfigChangeCallback): () => void;
  
  /** 获取配置状态 */
  getConfigStatus(): ConfigStatus;
}

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  isValid: boolean;
  errors: ConfigError[];
  warnings: ConfigWarning[];
}

/**
 * 配置错误
 */
export interface ConfigError {
  path: string;
  message: string;
  code: string;
}

/**
 * 配置警告
 */
export interface ConfigWarning {
  path: string;
  message: string;
  code: string;
}

/**
 * 配置变化回调
 */
export type ConfigChangeCallback = (
  newConfig: SelfMirrorConfig,
  oldConfig: SelfMirrorConfig,
  changedPaths: string[]
) => void;

/**
 * 配置状态
 */
export interface ConfigStatus {
  isLoaded: boolean;
  isValid: boolean;
  lastUpdated: Date;
  source: ConfigSource;
  environment: Environment;
}

/**
 * 配置来源
 */
export type ConfigSource = 'default' | 'file' | 'environment' | 'runtime';
