/**
 * Layered Configuration Manager - 分层配置管理器
 * 
 * 核心功能：
 * - 多层级配置管理（环境、用户、运行时）
 * - 配置热重载和版本控制
 * - 配置验证和类型安全
 * - 配置变更监听和通知
 */

import { EventEmitter } from 'events';
import { readFileSync, writeFileSync, existsSync, watchFile, unwatchFile } from 'fs';
import { join } from 'path';

/**
 * 配置层级枚举
 */
export enum ConfigLayer {
  ENVIRONMENT = 'environment',    // 环境配置（最低优先级）
  DEFAULT = 'default',           // 默认配置
  USER = 'user',                 // 用户配置
  RUNTIME = 'runtime'            // 运行时配置（最高优先级）
}

/**
 * 配置变更事件接口
 */
export interface ConfigChangeEvent {
  layer: ConfigLayer;
  key: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
  source: string;
}

/**
 * 配置验证规则接口
 */
export interface ConfigValidationRule {
  key: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required?: boolean;
  default?: any;
  validator?: (value: any) => boolean | string;
  description?: string;
}

/**
 * 配置层定义接口
 */
export interface ConfigLayerDefinition {
  layer: ConfigLayer;
  source: 'file' | 'env' | 'memory';
  path?: string;
  watchForChanges?: boolean;
  readonly?: boolean;
}

/**
 * 配置管理器选项接口
 */
export interface ConfigManagerOptions {
  configDir: string;
  enableHotReload: boolean;
  enableVersioning: boolean;
  enableValidation: boolean;
  layers: ConfigLayerDefinition[];
}

/**
 * 分层配置管理器实现
 */
export class LayeredConfigManager extends EventEmitter {
  private configs: Map<ConfigLayer, any> = new Map();
  private validationRules: Map<string, ConfigValidationRule> = new Map();
  private watchers: Map<string, any> = new Map();
  private versions: Map<ConfigLayer, number> = new Map();
  private options: ConfigManagerOptions;
  private mergedConfig: any = {};
  private initialized: boolean = false;

  constructor(options: Partial<ConfigManagerOptions> = {}) {
    super();
    
    this.options = {
      configDir: process.cwd() + '/config',
      enableHotReload: true,
      enableVersioning: true,
      enableValidation: true,
      layers: [
        { layer: ConfigLayer.ENVIRONMENT, source: 'env', readonly: true },
        { layer: ConfigLayer.DEFAULT, source: 'file', path: 'default.json', watchForChanges: false },
        { layer: ConfigLayer.USER, source: 'file', path: 'user.json', watchForChanges: true },
        { layer: ConfigLayer.RUNTIME, source: 'memory', readonly: false }
      ],
      ...options
    };

    console.log('🔧 分层配置管理器已创建');
  }

  /**
   * 初始化配置管理器
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🔧 初始化分层配置管理器...');

      // 初始化所有配置层
      for (const layerDef of this.options.layers) {
        await this.initializeLayer(layerDef);
      }

      // 合并配置
      this.mergeConfigurations();

      // 设置文件监听
      if (this.options.enableHotReload) {
        this.setupFileWatchers();
      }

      this.initialized = true;
      console.log('✅ 分层配置管理器初始化完成');
      
      this.emit('initialized', { timestamp: new Date() });

    } catch (error) {
      console.error('❌ 分层配置管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化单个配置层
   */
  private async initializeLayer(layerDef: ConfigLayerDefinition): Promise<void> {
    const { layer, source, path, readonly } = layerDef;
    
    try {
      let config: any = {};

      switch (source) {
        case 'file':
          if (path) {
            config = this.loadConfigFromFile(path);
          }
          break;
          
        case 'env':
          config = this.loadConfigFromEnv();
          break;
          
        case 'memory':
          config = {}; // 内存配置从空开始
          break;
      }

      this.configs.set(layer, config);
      this.versions.set(layer, 1);
      
      console.log(`📁 配置层 ${layer} 已初始化 (${source})`);
      
    } catch (error) {
      console.warn(`⚠️ 配置层 ${layer} 初始化失败:`, error);
      this.configs.set(layer, {});
      this.versions.set(layer, 1);
    }
  }

  /**
   * 从文件加载配置
   */
  private loadConfigFromFile(filename: string): any {
    const filePath = join(this.options.configDir, filename);
    
    if (!existsSync(filePath)) {
      console.warn(`⚠️ 配置文件不存在: ${filePath}`);
      return {};
    }

    try {
      const content = readFileSync(filePath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      console.error(`❌ 读取配置文件失败: ${filePath}`, error);
      return {};
    }
  }

  /**
   * 从环境变量加载配置
   */
  private loadConfigFromEnv(): any {
    const envConfig: any = {};
    
    // 提取以SELFMIRROR_开头的环境变量
    for (const [key, value] of Object.entries(process.env)) {
      if (key.startsWith('SELFMIRROR_')) {
        const configKey = key.replace('SELFMIRROR_', '').toLowerCase().replace(/_/g, '.');
        this.setNestedValue(envConfig, configKey, this.parseEnvValue(value));
      }
    }

    return envConfig;
  }

  /**
   * 解析环境变量值
   */
  private parseEnvValue(value: string | undefined): any {
    if (!value) return undefined;
    
    // 尝试解析为JSON
    if (value.startsWith('{') || value.startsWith('[')) {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    
    // 解析布尔值
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;
    
    // 解析数字
    if (/^\d+$/.test(value)) return parseInt(value, 10);
    if (/^\d+\.\d+$/.test(value)) return parseFloat(value);
    
    return value;
  }

  /**
   * 设置嵌套值
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * 合并所有配置层
   */
  private mergeConfigurations(): void {
    this.mergedConfig = {};
    
    // 按优先级顺序合并（环境 < 默认 < 用户 < 运行时）
    const layerOrder = [
      ConfigLayer.ENVIRONMENT,
      ConfigLayer.DEFAULT,
      ConfigLayer.USER,
      ConfigLayer.RUNTIME
    ];
    
    for (const layer of layerOrder) {
      const config = this.configs.get(layer);
      if (config) {
        this.mergedConfig = this.deepMerge(this.mergedConfig, config);
      }
    }

    // 验证合并后的配置
    if (this.options.enableValidation) {
      this.validateConfiguration(this.mergedConfig);
    }

    console.log('🔄 配置已合并');
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * 设置文件监听器
   */
  private setupFileWatchers(): void {
    for (const layerDef of this.options.layers) {
      if (layerDef.source === 'file' && layerDef.path && layerDef.watchForChanges) {
        const filePath = join(this.options.configDir, layerDef.path);
        
        if (existsSync(filePath)) {
          const watcher = watchFile(filePath, { interval: 1000 }, () => {
            this.handleFileChange(layerDef);
          });
          
          this.watchers.set(layerDef.layer, watcher);
          console.log(`👁️ 监听配置文件: ${filePath}`);
        }
      }
    }
  }

  /**
   * 处理文件变更
   */
  private async handleFileChange(layerDef: ConfigLayerDefinition): Promise<void> {
    try {
      console.log(`🔄 检测到配置文件变更: ${layerDef.layer}`);
      
      const oldConfig = this.configs.get(layerDef.layer);
      await this.initializeLayer(layerDef);
      const newConfig = this.configs.get(layerDef.layer);
      
      // 更新版本号
      const currentVersion = this.versions.get(layerDef.layer) || 1;
      this.versions.set(layerDef.layer, currentVersion + 1);
      
      // 重新合并配置
      this.mergeConfigurations();
      
      // 发出变更事件
      this.emit('configChanged', {
        layer: layerDef.layer,
        oldConfig,
        newConfig,
        timestamp: new Date(),
        version: this.versions.get(layerDef.layer)
      });
      
      console.log(`✅ 配置热重载完成: ${layerDef.layer}`);
      
    } catch (error) {
      console.error(`❌ 配置热重载失败: ${layerDef.layer}`, error);
    }
  }

  /**
   * 获取配置值
   */
  get<T = any>(key: string, defaultValue?: T): T {
    const keys = key.split('.');
    let current = this.mergedConfig;
    
    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return defaultValue as T;
      }
    }
    
    return current as T;
  }

  /**
   * 设置运行时配置值
   */
  set(key: string, value: any, layer: ConfigLayer = ConfigLayer.RUNTIME): void {
    const layerConfig = this.configs.get(layer) || {};
    const oldValue = this.get(key);
    
    this.setNestedValue(layerConfig, key, value);
    this.configs.set(layer, layerConfig);
    
    // 更新版本号
    const currentVersion = this.versions.get(layer) || 1;
    this.versions.set(layer, currentVersion + 1);
    
    // 重新合并配置
    this.mergeConfigurations();
    
    // 发出变更事件
    this.emit('configChanged', {
      layer,
      key,
      oldValue,
      newValue: value,
      timestamp: new Date(),
      source: 'api'
    } as ConfigChangeEvent);
    
    console.log(`🔧 配置已更新: ${key} = ${JSON.stringify(value)} (${layer})`);
  }

  /**
   * 验证配置
   */
  private validateConfiguration(config: any): void {
    Array.from(this.validationRules.entries()).forEach(([key, rule]) => {
      const value = this.get(key);

      // 检查必填项
      if (rule.required && (value === undefined || value === null)) {
        throw new Error(`配置项 ${key} 是必填的`);
      }

      // 类型检查
      if (value !== undefined && !this.validateType(value, rule.type)) {
        throw new Error(`配置项 ${key} 类型错误，期望 ${rule.type}，实际 ${typeof value}`);
      }

      // 自定义验证
      if (value !== undefined && rule.validator) {
        const result = rule.validator(value);
        if (result !== true) {
          throw new Error(`配置项 ${key} 验证失败: ${result}`);
        }
      }
    });
  }

  /**
   * 验证类型
   */
  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string': return typeof value === 'string';
      case 'number': return typeof value === 'number';
      case 'boolean': return typeof value === 'boolean';
      case 'object': return typeof value === 'object' && !Array.isArray(value);
      case 'array': return Array.isArray(value);
      default: return true;
    }
  }

  /**
   * 添加验证规则
   */
  addValidationRule(rule: ConfigValidationRule): void {
    this.validationRules.set(rule.key, rule);
    console.log(`📋 添加配置验证规则: ${rule.key}`);
  }

  /**
   * 获取所有配置
   */
  getAll(): any {
    return { ...this.mergedConfig };
  }

  /**
   * 获取配置层信息
   */
  getLayerInfo(): any {
    const info: any = {};

    Array.from(this.configs.entries()).forEach(([layer, config]) => {
      info[layer] = {
        version: this.versions.get(layer),
        keys: Object.keys(config),
        size: JSON.stringify(config).length
      };
    });

    return info;
  }

  /**
   * 销毁配置管理器
   */
  destroy(): void {
    // 停止文件监听
    Array.from(this.watchers.entries()).forEach(([layer, watcher]) => {
      unwatchFile(watcher);
    });
    this.watchers.clear();
    
    // 清理配置
    this.configs.clear();
    this.validationRules.clear();
    this.versions.clear();
    
    this.initialized = false;
    console.log('🔧 分层配置管理器已销毁');
  }
}

// 导出单例实例
export const layeredConfigManager = new LayeredConfigManager();
