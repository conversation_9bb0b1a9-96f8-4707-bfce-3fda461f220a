/**
 * SelfMirror 配置管理器
 * 实现统一的配置管理、热重载和验证功能
 */

import {
  SelfMirrorConfig,
  IConfigManager,
  AppConfig,
  ConfigValidationResult,
  ConfigChangeCallback,
  ConfigStatus,
  ConfigSource,
  Environment,
  ConfigError,
  ConfigWarning
} from './interfaces/IConfig';
import { getDefaultConfig } from './defaults/DefaultConfig';
import { validateConfig } from './validation/ConfigValidator';
import { EventEmitter } from 'events';

export class ConfigManager extends EventEmitter implements IConfigManager {
  private config: SelfMirrorConfig;
  private configStatus: ConfigStatus;
  private changeCallbacks: Set<ConfigChangeCallback> = new Set();
  private watchInterval: NodeJS.Timeout | null = null;
  private configFilePath: string;

  constructor(configFilePath?: string) {
    super();
    this.configFilePath = configFilePath || this.getDefaultConfigPath();
    this.config = getDefaultConfig();
    this.configStatus = {
      isLoaded: false,
      isValid: true,
      lastUpdated: new Date(),
      source: 'default',
      environment: this.detectEnvironment()
    };

    console.log('🔧 配置管理器初始化完成');
  }

  // ==================== 核心方法 ====================

  /**
   * 获取完整配置
   */
  getConfig(): SelfMirrorConfig {
    return JSON.parse(JSON.stringify(this.config)); // 深拷贝
  }

  /**
   * 获取部分配置
   */
  getPartialConfig<T extends keyof SelfMirrorConfig>(section: T): SelfMirrorConfig[T] {
    return JSON.parse(JSON.stringify(this.config[section])); // 深拷贝
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<SelfMirrorConfig>): Promise<void> {
    try {
      console.log('🔄 更新配置...', Object.keys(newConfig));

      // 验证新配置
      const mergedConfig = this.mergeConfig(this.config, newConfig);
      const validation = this.validateConfig(mergedConfig);

      if (!validation.isValid) {
        throw new Error(`配置验证失败: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      // 记录旧配置
      const oldConfig = this.getConfig();

      // 应用新配置
      this.config = mergedConfig;
      this.configStatus.lastUpdated = new Date();
      this.configStatus.source = 'runtime';

      // 计算变化路径
      const changedPaths = this.getChangedPaths(oldConfig, this.config);

      // 触发变化回调
      this.notifyConfigChange(this.config, oldConfig, changedPaths);

      // 保存配置
      await this.saveConfig();

      console.log('✅ 配置更新成功', { changedPaths });

    } catch (error) {
      console.error('❌ 配置更新失败:', error);
      throw error;
    }
  }

  /**
   * 重置配置
   */
  async resetConfig(): Promise<void> {
    try {
      console.log('🔄 重置配置到默认值...');

      const oldConfig = this.getConfig();
      const defaultConfig = getDefaultConfig();

      this.config = defaultConfig;
      this.configStatus.lastUpdated = new Date();
      this.configStatus.source = 'default';

      const changedPaths = this.getChangedPaths(oldConfig, this.config);
      this.notifyConfigChange(this.config, oldConfig, changedPaths);

      await this.saveConfig();

      console.log('✅ 配置重置成功');

    } catch (error) {
      console.error('❌ 配置重置失败:', error);
      throw error;
    }
  }

  /**
   * 验证配置
   */
  validateConfig(config: Partial<SelfMirrorConfig>): ConfigValidationResult {
    return validateConfig(config);
  }

  /**
   * 保存配置
   */
  async saveConfig(): Promise<void> {
    try {
      if (typeof window !== 'undefined') {
        // 浏览器环境：保存到 localStorage
        localStorage.setItem('selfmirror_config', JSON.stringify(this.config));
        console.log('💾 配置已保存到 localStorage');
      } else {
        // Node.js 环境：保存到文件
        const fs = await import('fs/promises');
        await fs.writeFile(this.configFilePath, JSON.stringify(this.config, null, 2), 'utf-8');
        console.log(`💾 配置已保存到文件: ${this.configFilePath}`);
      }
    } catch (error) {
      console.error('❌ 配置保存失败:', error);
      throw error;
    }
  }

  /**
   * 加载配置
   */
  async loadConfig(): Promise<void> {
    try {
      let loadedConfig: SelfMirrorConfig | null = null;
      let source: ConfigSource = 'default';

      if (typeof window !== 'undefined') {
        // 浏览器环境：从 localStorage 加载
        const stored = localStorage.getItem('selfmirror_config');
        if (stored) {
          loadedConfig = JSON.parse(stored);
          source = 'file';
          console.log('📥 从 localStorage 加载配置');
        }
      } else {
        // Node.js 环境：从文件加载
        try {
          const fs = await import('fs/promises');
          const configData = await fs.readFile(this.configFilePath, 'utf-8');
          loadedConfig = JSON.parse(configData);
          source = 'file';
          console.log(`📥 从文件加载配置: ${this.configFilePath}`);
        } catch (fileError) {
          console.log('📄 配置文件不存在，使用默认配置');
        }
      }

      // 加载环境变量配置
      const envConfig = this.loadEnvironmentConfig();
      if (Object.keys(envConfig).length > 0) {
        loadedConfig = loadedConfig ? this.mergeConfig(loadedConfig, envConfig) : envConfig;
        source = 'environment';
        console.log('🌍 应用环境变量配置');
      }

      if (loadedConfig) {
        // 验证加载的配置
        const validation = this.validateConfig(loadedConfig);
        if (validation.isValid) {
          this.config = this.mergeConfig(getDefaultConfig(), loadedConfig);
          this.configStatus.source = source;
          console.log('✅ 配置加载成功');
        } else {
          console.warn('⚠️ 加载的配置验证失败，使用默认配置:', validation.errors);
        }
      }

      this.configStatus.isLoaded = true;
      this.configStatus.lastUpdated = new Date();

    } catch (error) {
      console.error('❌ 配置加载失败:', error);
      this.configStatus.isLoaded = false;
      throw error;
    }
  }

  /**
   * 监听配置变化
   */
  onConfigChange(callback: ConfigChangeCallback): () => void {
    this.changeCallbacks.add(callback);
    
    return () => {
      this.changeCallbacks.delete(callback);
    };
  }

  /**
   * 获取配置状态
   */
  getConfigStatus(): ConfigStatus {
    return { ...this.configStatus };
  }

  // ==================== 热重载功能 ====================

  /**
   * 启动配置热重载
   */
  startHotReload(interval: number = 5000): void {
    if (this.watchInterval) {
      this.stopHotReload();
    }

    this.watchInterval = setInterval(async () => {
      try {
        await this.checkConfigChanges();
      } catch (error) {
        console.error('❌ 配置热重载检查失败:', error);
      }
    }, interval);

    console.log(`🔥 配置热重载已启动 (间隔: ${interval}ms)`);
  }

  /**
   * 停止配置热重载
   */
  stopHotReload(): void {
    if (this.watchInterval) {
      clearInterval(this.watchInterval);
      this.watchInterval = null;
      console.log('🛑 配置热重载已停止');
    }
  }

  /**
   * 检查配置变化
   */
  private async checkConfigChanges(): Promise<void> {
    try {
      const currentConfig = this.getConfig();
      await this.loadConfig();
      const newConfig = this.getConfig();

      // 比较配置是否有变化
      if (JSON.stringify(currentConfig) !== JSON.stringify(newConfig)) {
        const changedPaths = this.getChangedPaths(currentConfig, newConfig);
        this.notifyConfigChange(newConfig, currentConfig, changedPaths);
        console.log('🔥 检测到配置变化，已热重载:', changedPaths);
      }
    } catch (error) {
      console.error('❌ 配置变化检查失败:', error);
    }
  }

  // ==================== 私有方法 ====================

  private detectEnvironment(): Environment {
    if (typeof window !== 'undefined') {
      return 'development'; // 浏览器环境默认为开发环境
    }
    
    const env = process.env.NODE_ENV as Environment;
    return ['development', 'test', 'staging', 'production'].includes(env) ? env : 'development';
  }

  private getDefaultConfigPath(): string {
    return process.cwd() + '/selfmirror.config.json';
  }

  private loadEnvironmentConfig(): Partial<SelfMirrorConfig> {
    const envConfig: Partial<SelfMirrorConfig> = {};

    // 加载 AI 配置
    if (process.env.GOOGLE_API_KEY) {
      envConfig.ai = {
        defaultProvider: 'gemini',
        models: {
          gemini: {
            apiKey: process.env.GOOGLE_API_KEY,
            model: process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20',
            proxyUrl: process.env.PROXY_URL,
            thinkingBudget: parseInt(process.env.GEMINI_THINKING_BUDGET || '0')
          }
        },
        cache: { enabled: true, ttl: 300000, maxSize: 50 },
        healthCheck: { enabled: true, interval: 60000, timeout: 10000 },
        retry: { maxRetries: 3, retryDelay: 1000, backoffFactor: 2 }
      };
    }

    // 加载应用配置
    if (process.env.PORT) {
      envConfig.app = {
        ...envConfig.app,
        port: parseInt(process.env.PORT)
      } as AppConfig;
    }

    return envConfig;
  }

  private mergeConfig(base: any, override: any): any {
    const result = { ...base };

    for (const key in override) {
      if (override[key] !== null && typeof override[key] === 'object' && !Array.isArray(override[key])) {
        result[key] = this.mergeConfig(result[key] || {}, override[key]);
      } else {
        result[key] = override[key];
      }
    }

    return result;
  }

  private getChangedPaths(oldConfig: any, newConfig: any, prefix: string = ''): string[] {
    const changes: string[] = [];

    const allKeys = new Set([...Object.keys(oldConfig || {}), ...Object.keys(newConfig || {})]);

    for (const key of allKeys) {
      const path = prefix ? `${prefix}.${key}` : key;
      const oldValue = oldConfig?.[key];
      const newValue = newConfig?.[key];

      if (oldValue !== newValue) {
        if (typeof oldValue === 'object' && typeof newValue === 'object' && 
            oldValue !== null && newValue !== null && 
            !Array.isArray(oldValue) && !Array.isArray(newValue)) {
          changes.push(...this.getChangedPaths(oldValue, newValue, path));
        } else {
          changes.push(path);
        }
      }
    }

    return changes;
  }

  private notifyConfigChange(newConfig: SelfMirrorConfig, oldConfig: SelfMirrorConfig, changedPaths: string[]): void {
    for (const callback of this.changeCallbacks) {
      try {
        callback(newConfig, oldConfig, changedPaths);
      } catch (error) {
        console.error('❌ 配置变化回调执行失败:', error);
      }
    }

    // 发出事件
    this.emit('configChange', { newConfig, oldConfig, changedPaths });
  }

  // ==================== 清理方法 ====================

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopHotReload();
    this.changeCallbacks.clear();
    this.removeAllListeners();
    console.log('🧹 配置管理器已清理');
  }
}

// ==================== 全局实例 ====================

let globalConfigManager: ConfigManager | null = null;

/**
 * 获取全局配置管理器实例
 */
export function getGlobalConfigManager(): ConfigManager {
  if (!globalConfigManager) {
    globalConfigManager = new ConfigManager();
  }
  return globalConfigManager;
}

/**
 * 设置全局配置管理器实例
 */
export function setGlobalConfigManager(manager: ConfigManager): void {
  if (globalConfigManager) {
    globalConfigManager.cleanup();
  }
  globalConfigManager = manager;
}

/**
 * 初始化配置管理器
 */
export async function initializeConfigManager(configPath?: string): Promise<ConfigManager> {
  const manager = new ConfigManager(configPath);
  await manager.loadConfig();
  setGlobalConfigManager(manager);
  
  // 启动热重载
  if (manager.getConfig().app.environment === 'development') {
    manager.startHotReload();
  }
  
  return manager;
}

export default ConfigManager;
