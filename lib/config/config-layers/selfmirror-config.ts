/**
 * SelfMirror Configuration Layers - SelfMirror配置层定义
 * 
 * 定义SelfMirror系统的所有配置项和验证规则
 */

import { ConfigValidationRule, ConfigLayer, layeredConfigManager } from '../layered-config-manager';

/**
 * SelfMirror配置接口
 */
export interface SelfMirrorConfig {
  // AI提供商配置
  ai: {
    defaultProvider: 'gemini' | 'doubao';
    providers: {
      gemini: {
        apiKey: string;
        model: string;
        temperature: number;
        maxTokens: number;
        timeout: number;
      };
      doubao: {
        apiKey: string;
        model: string;
        temperature: number;
        maxTokens: number;
        timeout: number;
      };
    };
    cache: {
      enabled: boolean;
      maxSize: number;
      ttl: number;
      maxMemoryMB: number;
    };
  };

  // 三引擎架构配置
  engines: {
    navigator: {
      enabled: boolean;
      model: string;
      temperature: number;
      maxRetries: number;
    };
    contextRetriever: {
      enabled: boolean;
      maxChunks: number;
      similarityThreshold: number;
      timeout: number;
    };
    integrationGenerator: {
      enabled: boolean;
      model: string;
      temperature: number;
      maxTokens: number;
    };
  };

  // 双核系统配置
  dualCore: {
    retrievalOptimizer: {
      enabled: boolean;
      maxItems: number;
      decayRate: number;
      noiseThreshold: number;
      qualityThreshold: number;
    };
    contextPackaging: {
      enabled: boolean;
      maxContextLength: number;
      priorityWeights: {
        recent: number;
        relevant: number;
        profile: number;
      };
    };
  };

  // 向量数据库配置
  vectorDatabase: {
    hotStore: {
      maxItems: number;
      decayDays: number;
      cleanupInterval: number;
    };
    coldStore: {
      maxItems: number;
      compressionEnabled: boolean;
      archiveThreshold: number;
    };
  };

  // API配置
  api: {
    rateLimit: {
      enabled: boolean;
      maxRequests: number;
      windowMs: number;
    };
    cors: {
      enabled: boolean;
      origins: string[];
    };
    timeout: number;
    maxBodySize: string;
  };

  // 调试和监控配置
  debug: {
    enabled: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    performanceMonitoring: boolean;
    cacheMonitoring: boolean;
    errorTracking: boolean;
  };

  // 系统配置
  system: {
    environment: 'development' | 'production' | 'test';
    dataDir: string;
    tempDir: string;
    maxConcurrentRequests: number;
    gracefulShutdownTimeout: number;
  };
}

/**
 * 默认配置
 */
export const defaultConfig: SelfMirrorConfig = {
  ai: {
    defaultProvider: 'gemini',
    providers: {
      gemini: {
        apiKey: '',
        model: 'gemini-pro',
        temperature: 0.7,
        maxTokens: 2048,
        timeout: 30000
      },
      doubao: {
        apiKey: '',
        model: 'doubao-pro',
        temperature: 0.7,
        maxTokens: 2048,
        timeout: 30000
      }
    },
    cache: {
      enabled: true,
      maxSize: 1000,
      ttl: 3600000, // 1小时
      maxMemoryMB: 100
    }
  },

  engines: {
    navigator: {
      enabled: true,
      model: 'gemini-pro',
      temperature: 0.3,
      maxRetries: 3
    },
    contextRetriever: {
      enabled: true,
      maxChunks: 10,
      similarityThreshold: 0.7,
      timeout: 10000
    },
    integrationGenerator: {
      enabled: true,
      model: 'gemini-pro',
      temperature: 0.7,
      maxTokens: 2048
    }
  },

  dualCore: {
    retrievalOptimizer: {
      enabled: true,
      maxItems: 1000,
      decayRate: 0.8,
      noiseThreshold: 0.05,
      qualityThreshold: 0.7
    },
    contextPackaging: {
      enabled: true,
      maxContextLength: 8000,
      priorityWeights: {
        recent: 1.0,
        relevant: 0.8,
        profile: 0.6
      }
    }
  },

  vectorDatabase: {
    hotStore: {
      maxItems: 10000,
      decayDays: 30,
      cleanupInterval: 3600000 // 1小时
    },
    coldStore: {
      maxItems: 100000,
      compressionEnabled: true,
      archiveThreshold: 90 // 90天
    }
  },

  api: {
    rateLimit: {
      enabled: true,
      maxRequests: 100,
      windowMs: 60000 // 1分钟
    },
    cors: {
      enabled: true,
      origins: ['http://localhost:3000']
    },
    timeout: 30000,
    maxBodySize: '10mb'
  },

  debug: {
    enabled: process.env.NODE_ENV === 'development',
    logLevel: 'info',
    performanceMonitoring: true,
    cacheMonitoring: true,
    errorTracking: true
  },

  system: {
    environment: (process.env.NODE_ENV as any) || 'development',
    dataDir: './data',
    tempDir: './temp',
    maxConcurrentRequests: 100,
    gracefulShutdownTimeout: 10000
  }
};

/**
 * 配置验证规则
 */
export const configValidationRules: ConfigValidationRule[] = [
  // AI配置验证
  {
    key: 'ai.defaultProvider',
    type: 'string',
    required: true,
    validator: (value: any) => ['gemini', 'doubao'].includes(value) || 'AI提供商必须是 gemini 或 doubao',
    description: 'AI提供商选择'
  },
  {
    key: 'ai.providers.gemini.temperature',
    type: 'number',
    validator: (value: any) => (typeof value === 'number' && value >= 0 && value <= 2) || '温度参数必须在0-2之间',
    description: 'Gemini温度参数'
  },
  {
    key: 'ai.providers.doubao.temperature',
    type: 'number',
    validator: (value: any) => (typeof value === 'number' && value >= 0 && value <= 2) || '温度参数必须在0-2之间',
    description: 'Doubao温度参数'
  },
  {
    key: 'ai.cache.maxSize',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 100,
    description: 'AI缓存最大大小'
  },
  {
    key: 'ai.cache.ttl',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 60000, // 1分钟
    description: 'AI缓存TTL'
  },

  // 引擎配置验证
  {
    key: 'engines.navigator.temperature',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 0,
    description: 'Navigator引擎温度'
  },
  {
    key: 'engines.contextRetriever.maxChunks',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 1,
    description: '上下文检索最大块数'
  },
  {
    key: 'engines.contextRetriever.similarityThreshold',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 0,
    description: '相似度阈值'
  },

  // 双核系统验证
  {
    key: 'dualCore.retrievalOptimizer.decayRate',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 0.1,
    description: '检索优化器衰减率'
  },
  {
    key: 'dualCore.contextPackaging.maxContextLength',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 1000,
    description: '最大上下文长度'
  },

  // 系统配置验证
  {
    key: 'system.environment',
    type: 'string',
    validator: (value: any) => ['development', 'production', 'test'].includes(value) || '环境必须是 development, production 或 test',
    description: '系统环境'
  },
  {
    key: 'system.maxConcurrentRequests',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 1,
    description: '最大并发请求数'
  },

  // API配置验证
  {
    key: 'api.rateLimit.maxRequests',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 1,
    description: '速率限制最大请求数'
  },
  {
    key: 'api.timeout',
    type: 'number',
    validator: (value: any) => typeof value === "number" && value >= 1000,
    description: 'API超时时间'
  },

  // 调试配置验证
  {
    key: 'debug.logLevel',
    type: 'string',
    validator: (value: any) => ['error', 'warn', 'info', 'debug'].includes(value) || '日志级别必须是 error, warn, info 或 debug',
    description: '日志级别'
  }
];

/**
 * 初始化SelfMirror配置
 */
export function initializeSelfMirrorConfig(): void {
  console.log('🔧 初始化SelfMirror配置系统...');

  // 添加验证规则
  configValidationRules.forEach(rule => {
    layeredConfigManager.addValidationRule(rule);
  });

  // 设置默认配置
  Object.entries(flattenConfig(defaultConfig)).forEach(([key, value]) => {
    layeredConfigManager.set(key, value, ConfigLayer.DEFAULT);
  });

  // 从环境变量加载配置
  loadEnvironmentConfig();

  console.log('✅ SelfMirror配置系统初始化完成');
}

/**
 * 从环境变量加载配置
 */
function loadEnvironmentConfig(): void {
  const envMappings = {
    'GEMINI_API_KEY': 'ai.providers.gemini.apiKey',
    'DOUBAO_API_KEY': 'ai.providers.doubao.apiKey',
    'AI_DEFAULT_PROVIDER': 'ai.defaultProvider',
    'NODE_ENV': 'system.environment',
    'DEBUG_ENABLED': 'debug.enabled',
    'LOG_LEVEL': 'debug.logLevel',
    'MAX_CONCURRENT_REQUESTS': 'system.maxConcurrentRequests',
    'API_TIMEOUT': 'api.timeout',
    'CACHE_ENABLED': 'ai.cache.enabled',
    'CACHE_MAX_SIZE': 'ai.cache.maxSize'
  };

  Object.entries(envMappings).forEach(([envKey, configKey]) => {
    const envValue = process.env[envKey];
    if (envValue !== undefined) {
      let parsedValue: any = envValue;
      
      // 类型转换
      if (envValue === 'true') parsedValue = true;
      else if (envValue === 'false') parsedValue = false;
      else if (!isNaN(Number(envValue))) parsedValue = Number(envValue);
      
      layeredConfigManager.set(configKey, parsedValue, ConfigLayer.ENVIRONMENT);
      console.log(`🌍 从环境变量加载配置: ${configKey} = ${parsedValue}`);
    }
  });
}

/**
 * 扁平化配置对象
 */
function flattenConfig(obj: any, prefix = ''): Record<string, any> {
  const flattened: Record<string, any> = {};
  
  Object.keys(obj).forEach(key => {
    const value = obj[key];
    const newKey = prefix ? `${prefix}.${key}` : key;
    
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      Object.assign(flattened, flattenConfig(value, newKey));
    } else {
      flattened[newKey] = value;
    }
  });
  
  return flattened;
}

/**
 * 获取类型化的SelfMirror配置
 */
export function getSelfMirrorConfig(): SelfMirrorConfig {
  const flatConfig = layeredConfigManager.getAll();
  return unflattenConfig(flatConfig) as SelfMirrorConfig;
}

/**
 * 反扁平化配置对象
 */
function unflattenConfig(flatObj: Record<string, any>): any {
  const result: any = {};
  
  Object.keys(flatObj).forEach(key => {
    const keys = key.split('.');
    let current = result;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = flatObj[key];
  });
  
  return result;
}

/**
 * 更新SelfMirror配置
 */
export function updateSelfMirrorConfig(updates: Partial<SelfMirrorConfig>, layer: ConfigLayer = ConfigLayer.USER): void {
  const flatUpdates = flattenConfig(updates);
  
  Object.entries(flatUpdates).forEach(([key, value]) => {
    layeredConfigManager.set(key, value, layer);
  });
  
  console.log(`🔧 SelfMirror配置已更新 [${layer}]:`, Object.keys(flatUpdates));
}

// 自动初始化
initializeSelfMirrorConfig();
