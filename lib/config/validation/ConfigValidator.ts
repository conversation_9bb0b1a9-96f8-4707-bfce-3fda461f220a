/**
 * SelfMirror 配置验证器
 * 提供完整的配置验证和错误检查功能
 */

import {
  SelfMirrorConfig,
  ConfigValidationResult,
  ConfigError,
  ConfigWarning,
  Environment,
  LogLevel,
  StorageProvider,
  EmbeddingProvider,
  RetrievalAlgorithm,
  CacheStrategy
} from '../interfaces/IConfig';

/**
 * 验证完整配置
 */
export function validateConfig(config: Partial<SelfMirrorConfig>): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证各个配置部分
  if (config.app) {
    const appValidation = validateAppConfig(config.app);
    errors.push(...appValidation.errors);
    warnings.push(...appValidation.warnings);
  }

  if (config.ai) {
    const aiValidation = validateAIConfig(config.ai);
    errors.push(...aiValidation.errors);
    warnings.push(...aiValidation.warnings);
  }

  if (config.storage) {
    const storageValidation = validateStorageConfig(config.storage);
    errors.push(...storageValidation.errors);
    warnings.push(...storageValidation.warnings);
  }

  if (config.rag) {
    const ragValidation = validateRAGConfig(config.rag);
    errors.push(...ragValidation.errors);
    warnings.push(...ragValidation.warnings);
  }

  if (config.debug) {
    const debugValidation = validateDebugConfig(config.debug);
    errors.push(...debugValidation.errors);
    warnings.push(...debugValidation.warnings);
  }

  if (config.security) {
    const securityValidation = validateSecurityConfig(config.security);
    errors.push(...securityValidation.errors);
    warnings.push(...securityValidation.warnings);
  }

  if (config.performance) {
    const performanceValidation = validatePerformanceConfig(config.performance);
    errors.push(...performanceValidation.errors);
    warnings.push(...performanceValidation.warnings);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证应用配置
 */
function validateAppConfig(config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证必需字段
  if (!config.name) {
    errors.push(createError('app.name', '应用名称不能为空', 'REQUIRED_FIELD'));
  }

  if (!config.version) {
    errors.push(createError('app.version', '应用版本不能为空', 'REQUIRED_FIELD'));
  }

  // 验证环境
  if (config.environment && !isValidEnvironment(config.environment)) {
    errors.push(createError('app.environment', '无效的环境值', 'INVALID_VALUE'));
  }

  // 验证日志级别
  if (config.logLevel && !isValidLogLevel(config.logLevel)) {
    errors.push(createError('app.logLevel', '无效的日志级别', 'INVALID_VALUE'));
  }

  // 验证端口
  if (config.port && (!Number.isInteger(config.port) || config.port < 1 || config.port > 65535)) {
    errors.push(createError('app.port', '端口必须是 1-65535 之间的整数', 'INVALID_VALUE'));
  }

  // 验证 URL
  if (config.baseUrl && !isValidUrl(config.baseUrl)) {
    warnings.push(createWarning('app.baseUrl', '基础 URL 格式可能不正确', 'INVALID_FORMAT'));
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证 AI 配置
 */
function validateAIConfig(config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证默认提供商
  if (!config.defaultProvider) {
    errors.push(createError('ai.defaultProvider', '必须指定默认 AI 提供商', 'REQUIRED_FIELD'));
  }

  // 验证模型配置
  if (!config.models || Object.keys(config.models).length === 0) {
    errors.push(createError('ai.models', '必须配置至少一个 AI 模型', 'REQUIRED_FIELD'));
  } else {
    // 验证默认提供商是否有配置
    if (config.defaultProvider && !config.models[config.defaultProvider]) {
      errors.push(createError('ai.models', `默认提供商 ${config.defaultProvider} 缺少配置`, 'MISSING_CONFIG'));
    }

    // 验证各个模型配置
    for (const [provider, modelConfig] of Object.entries(config.models)) {
      const modelValidation = validateModelConfig(provider, modelConfig);
      errors.push(...modelValidation.errors.map(e => ({ ...e, path: `ai.models.${provider}.${e.path}` })));
      warnings.push(...modelValidation.warnings.map(w => ({ ...w, path: `ai.models.${provider}.${w.path}` })));
    }
  }

  // 验证缓存配置
  if (config.cache) {
    if (config.cache.ttl && config.cache.ttl < 0) {
      errors.push(createError('ai.cache.ttl', 'TTL 不能为负数', 'INVALID_VALUE'));
    }
    if (config.cache.maxSize && config.cache.maxSize < 1) {
      errors.push(createError('ai.cache.maxSize', '缓存大小必须大于 0', 'INVALID_VALUE'));
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证模型配置
 */
function validateModelConfig(provider: string, config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  switch (provider) {
    case 'gemini':
      if (!config.apiKey) {
        errors.push(createError('apiKey', 'Gemini API 密钥不能为空', 'REQUIRED_FIELD'));
      }
      if (!config.model) {
        errors.push(createError('model', 'Gemini 模型名称不能为空', 'REQUIRED_FIELD'));
      }
      if (config.thinkingBudget && (config.thinkingBudget < 0 || config.thinkingBudget > 100)) {
        warnings.push(createWarning('thinkingBudget', 'thinking budget 建议在 0-100 之间', 'INVALID_RANGE'));
      }
      break;

    case 'doubao':
      if (!config.apiKey) {
        errors.push(createError('apiKey', '豆包 API 密钥不能为空', 'REQUIRED_FIELD'));
      }
      if (!config.model) {
        errors.push(createError('model', '豆包模型名称不能为空', 'REQUIRED_FIELD'));
      }
      if (!config.baseUrl) {
        errors.push(createError('baseUrl', '豆包 API 基础 URL 不能为空', 'REQUIRED_FIELD'));
      }
      break;

    default:
      warnings.push(createWarning('provider', `未知的 AI 提供商: ${provider}`, 'UNKNOWN_PROVIDER'));
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证存储配置
 */
function validateStorageConfig(config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证默认提供商
  if (!config.defaultProvider) {
    errors.push(createError('storage.defaultProvider', '必须指定默认存储提供商', 'REQUIRED_FIELD'));
  } else if (!isValidStorageProvider(config.defaultProvider)) {
    errors.push(createError('storage.defaultProvider', '无效的存储提供商', 'INVALID_VALUE'));
  }

  // 验证提供商配置
  if (config.providers) {
    for (const [provider, providerConfig] of Object.entries(config.providers)) {
      const providerValidation = validateStorageProviderConfig(provider, providerConfig);
      errors.push(...providerValidation.errors.map(e => ({ ...e, path: `storage.providers.${provider}.${e.path}` })));
      warnings.push(...providerValidation.warnings.map(w => ({ ...w, path: `storage.providers.${provider}.${w.path}` })));
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证存储提供商配置
 */
function validateStorageProviderConfig(provider: string, config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  switch (provider) {
    case 'filesystem':
      if (!config.basePath) {
        errors.push(createError('basePath', '文件系统基础路径不能为空', 'REQUIRED_FIELD'));
      }
      break;

    case 'indexeddb':
      if (!config.databaseName) {
        errors.push(createError('databaseName', 'IndexedDB 数据库名称不能为空', 'REQUIRED_FIELD'));
      }
      if (!config.storeName) {
        errors.push(createError('storeName', 'IndexedDB 存储名称不能为空', 'REQUIRED_FIELD'));
      }
      break;

    case 'memory':
      if (config.maxSize && config.maxSize < 1024 * 1024) {
        warnings.push(createWarning('maxSize', '内存存储大小建议至少 1MB', 'SMALL_SIZE'));
      }
      break;
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证 RAG 配置
 */
function validateRAGConfig(config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证向量模型配置
  if (config.embedding) {
    if (!isValidEmbeddingProvider(config.embedding.provider)) {
      errors.push(createError('rag.embedding.provider', '无效的向量模型提供商', 'INVALID_VALUE'));
    }
    if (config.embedding.dimensions && config.embedding.dimensions < 1) {
      errors.push(createError('rag.embedding.dimensions', '向量维度必须大于 0', 'INVALID_VALUE'));
    }
  }

  // 验证检索配置
  if (config.retrieval) {
    if (config.retrieval.topK && config.retrieval.topK < 1) {
      errors.push(createError('rag.retrieval.topK', 'topK 必须大于 0', 'INVALID_VALUE'));
    }
    if (config.retrieval.threshold && (config.retrieval.threshold < 0 || config.retrieval.threshold > 1)) {
      errors.push(createError('rag.retrieval.threshold', '阈值必须在 0-1 之间', 'INVALID_VALUE'));
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证调试配置
 */
function validateDebugConfig(config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证面板配置
  if (config.panels) {
    if (config.panels.refreshInterval && config.panels.refreshInterval < 1000) {
      warnings.push(createWarning('debug.panels.refreshInterval', '刷新间隔建议不少于 1 秒', 'TOO_FREQUENT'));
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证安全配置
 */
function validateSecurityConfig(config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证加密配置
  if (config.encryption && config.encryption.enabled) {
    if (!config.encryption.algorithm) {
      errors.push(createError('security.encryption.algorithm', '启用加密时必须指定算法', 'REQUIRED_FIELD'));
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * 验证性能配置
 */
function validatePerformanceConfig(config: any): ConfigValidationResult {
  const errors: ConfigError[] = [];
  const warnings: ConfigWarning[] = [];

  // 验证并发配置
  if (config.concurrency) {
    if (config.concurrency.maxConcurrentRequests && config.concurrency.maxConcurrentRequests < 1) {
      errors.push(createError('performance.concurrency.maxConcurrentRequests', '最大并发请求数必须大于 0', 'INVALID_VALUE'));
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

// ==================== 辅助函数 ====================

function createError(path: string, message: string, code: string): ConfigError {
  return { path, message, code };
}

function createWarning(path: string, message: string, code: string): ConfigWarning {
  return { path, message, code };
}

function isValidEnvironment(env: string): env is Environment {
  return ['development', 'test', 'staging', 'production'].includes(env);
}

function isValidLogLevel(level: string): level is LogLevel {
  return ['debug', 'info', 'warn', 'error'].includes(level);
}

function isValidStorageProvider(provider: string): provider is StorageProvider {
  return ['filesystem', 'indexeddb', 'memory'].includes(provider);
}

function isValidEmbeddingProvider(provider: string): provider is EmbeddingProvider {
  return ['local', 'openai', 'gemini'].includes(provider);
}

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export default validateConfig;
