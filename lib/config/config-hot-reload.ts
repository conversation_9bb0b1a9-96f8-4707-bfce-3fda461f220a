/**
 * Configuration Hot Reload Service - 配置热重载服务
 * 
 * 核心功能：
 * - 监听配置变更并自动应用
 * - 通知相关服务配置更新
 * - 配置变更验证和回滚
 * - 配置变更日志记录
 */

import { EventEmitter } from 'events';
import { layeredConfigManager, ConfigChangeEvent } from './layered-config-manager';
import { getSelfMirrorConfig, SelfMirrorConfig } from './config-layers/selfmirror-config';

/**
 * 配置变更处理器接口
 */
export interface ConfigChangeHandler {
  name: string;
  pattern: string | RegExp;
  handler: (key: string, newValue: any, oldValue: any) => Promise<void> | void;
  priority: number;
}

/**
 * 配置热重载统计接口
 */
export interface HotReloadStats {
  totalChanges: number;
  successfulReloads: number;
  failedReloads: number;
  lastReloadTime: Date | null;
  registeredHandlers: number;
  averageReloadTime: number;
}

/**
 * 配置热重载服务
 */
export class ConfigHotReloadService extends EventEmitter {
  private handlers: ConfigChangeHandler[] = [];
  private stats: HotReloadStats = {
    totalChanges: 0,
    successfulReloads: 0,
    failedReloads: 0,
    lastReloadTime: null,
    registeredHandlers: 0,
    averageReloadTime: 0
  };
  private reloadTimes: number[] = [];
  private isInitialized = false;

  constructor() {
    super();
    this.initialize();
  }

  /**
   * 初始化热重载服务
   */
  private initialize(): void {
    if (this.isInitialized) return;

    // 监听配置变更事件
    layeredConfigManager.on('configChanged', this.handleConfigChange.bind(this));
    layeredConfigManager.on('configReloaded', this.handleConfigReload.bind(this));

    // 注册默认处理器
    this.registerDefaultHandlers();

    this.isInitialized = true;
    console.log('🔥 配置热重载服务已初始化');
  }

  /**
   * 注册默认配置变更处理器
   */
  private registerDefaultHandlers(): void {
    // AI提供商配置变更处理器
    this.registerHandler({
      name: 'AI Provider Config',
      pattern: /^ai\.(defaultProvider|providers\.)/,
      priority: 1,
      handler: async (key, newValue, oldValue) => {
        console.log(`🤖 AI配置变更: ${key} = ${JSON.stringify(newValue)}`);
        
        // 通知AI工厂重新配置
        this.emit('aiConfigChanged', { key, newValue, oldValue });
        
        // 如果是默认提供商变更，需要重新初始化
        if (key === 'ai.defaultProvider') {
          console.log(`🔄 AI默认提供商变更为: ${newValue}`);
          this.emit('aiProviderSwitched', { from: oldValue, to: newValue });
        }
      }
    });

    // 缓存配置变更处理器
    this.registerHandler({
      name: 'Cache Config',
      pattern: /^ai\.cache\.|^dualCore\.retrievalOptimizer\./,
      priority: 2,
      handler: async (key, newValue, oldValue) => {
        console.log(`💾 缓存配置变更: ${key} = ${JSON.stringify(newValue)}`);
        
        // 通知缓存层重新配置
        this.emit('cacheConfigChanged', { key, newValue, oldValue });
        
        // 特殊处理缓存启用/禁用
        if (key === 'ai.cache.enabled') {
          console.log(`🔄 AI缓存${newValue ? '启用' : '禁用'}`);
          this.emit('cacheToggled', { enabled: newValue });
        }
      }
    });

    // 引擎配置变更处理器
    this.registerHandler({
      name: 'Engine Config',
      pattern: /^engines\./,
      priority: 3,
      handler: async (key, newValue, oldValue) => {
        console.log(`⚙️ 引擎配置变更: ${key} = ${JSON.stringify(newValue)}`);
        
        // 通知三引擎工作流重新配置
        this.emit('engineConfigChanged', { key, newValue, oldValue });
        
        // 检查引擎启用/禁用
        if (key.endsWith('.enabled')) {
          const engineName = key.split('.')[1];
          console.log(`🔄 ${engineName}引擎${newValue ? '启用' : '禁用'}`);
          this.emit('engineToggled', { engine: engineName, enabled: newValue });
        }
      }
    });

    // API配置变更处理器
    this.registerHandler({
      name: 'API Config',
      pattern: /^api\./,
      priority: 4,
      handler: async (key, newValue, oldValue) => {
        console.log(`🌐 API配置变更: ${key} = ${JSON.stringify(newValue)}`);
        
        // 通知API服务重新配置
        this.emit('apiConfigChanged', { key, newValue, oldValue });
        
        // 特殊处理速率限制变更
        if (key.startsWith('api.rateLimit.')) {
          console.log(`🚦 API速率限制配置变更`);
          this.emit('rateLimitConfigChanged', { key, newValue, oldValue });
        }
      }
    });

    // 调试配置变更处理器
    this.registerHandler({
      name: 'Debug Config',
      pattern: /^debug\./,
      priority: 5,
      handler: async (key, newValue, oldValue) => {
        console.log(`🐛 调试配置变更: ${key} = ${JSON.stringify(newValue)}`);
        
        // 通知调试服务重新配置
        this.emit('debugConfigChanged', { key, newValue, oldValue });
        
        // 特殊处理日志级别变更
        if (key === 'debug.logLevel') {
          console.log(`📝 日志级别变更为: ${newValue}`);
          this.emit('logLevelChanged', { level: newValue });
        }
      }
    });

    this.stats.registeredHandlers = this.handlers.length;
  }

  /**
   * 注册配置变更处理器
   */
  registerHandler(handler: ConfigChangeHandler): void {
    this.handlers.push(handler);
    this.handlers.sort((a, b) => a.priority - b.priority);
    this.stats.registeredHandlers = this.handlers.length;
    
    console.log(`📝 已注册配置变更处理器: ${handler.name} (优先级: ${handler.priority})`);
  }

  /**
   * 处理配置变更事件
   */
  private async handleConfigChange(event: ConfigChangeEvent): Promise<void> {
    const startTime = Date.now();
    this.stats.totalChanges++;
    
    console.log(`🔥 配置变更触发: ${event.key} [${event.layer}]`);
    
    try {
      // 查找匹配的处理器
      const matchingHandlers = this.handlers.filter(handler => {
        if (typeof handler.pattern === 'string') {
          return event.key.startsWith(handler.pattern);
        } else {
          return handler.pattern.test(event.key);
        }
      });

      // 按优先级执行处理器
      for (const handler of matchingHandlers) {
        try {
          console.log(`🔄 执行配置处理器: ${handler.name}`);
          await handler.handler(event.key, event.newValue, event.oldValue);
        } catch (error) {
          console.error(`❌ 配置处理器执行失败 [${handler.name}]:`, error);
          this.emit('handlerError', { handler: handler.name, error, event });
        }
      }

      const reloadTime = Date.now() - startTime;
      this.recordReloadTime(reloadTime);
      this.stats.successfulReloads++;
      this.stats.lastReloadTime = new Date();

      console.log(`✅ 配置变更处理完成: ${event.key} (${reloadTime}ms)`);
      this.emit('reloadCompleted', { event, reloadTime, handlersExecuted: matchingHandlers.length });

    } catch (error) {
      const reloadTime = Date.now() - startTime;
      this.stats.failedReloads++;
      
      console.error(`❌ 配置变更处理失败: ${event.key}`, error);
      this.emit('reloadFailed', { event, error, reloadTime });
    }
  }

  /**
   * 处理配置重新加载事件
   */
  private handleConfigReload(event: any): void {
    console.log('🔄 配置文件重新加载，触发全量配置检查...');
    
    // 获取当前配置
    const currentConfig = getSelfMirrorConfig();
    
    // 触发全量配置变更事件
    this.emit('fullConfigReloaded', { config: currentConfig, timestamp: event.timestamp });
  }

  /**
   * 记录重载时间
   */
  private recordReloadTime(time: number): void {
    this.reloadTimes.push(time);
    
    // 只保留最近100次的记录
    if (this.reloadTimes.length > 100) {
      this.reloadTimes.shift();
    }
    
    // 计算平均时间
    this.stats.averageReloadTime = this.reloadTimes.reduce((sum, t) => sum + t, 0) / this.reloadTimes.length;
  }

  /**
   * 获取热重载统计信息
   */
  getStats(): HotReloadStats {
    return { ...this.stats };
  }

  /**
   * 获取注册的处理器列表
   */
  getHandlers(): Array<{ name: string; pattern: string; priority: number }> {
    return this.handlers.map(h => ({
      name: h.name,
      pattern: h.pattern.toString(),
      priority: h.priority
    }));
  }

  /**
   * 手动触发配置重载
   */
  async triggerReload(): Promise<void> {
    console.log('🔄 手动触发配置重载...');
    layeredConfigManager.reloadAll();
  }

  /**
   * 测试配置变更处理
   */
  async testConfigChange(key: string, value: any): Promise<void> {
    console.log(`🧪 测试配置变更: ${key} = ${JSON.stringify(value)}`);
    
    const oldValue = layeredConfigManager.get(key);
    
    // 模拟配置变更事件
    const testEvent: ConfigChangeEvent = {
      layer: 'runtime' as any,
      key,
      oldValue,
      newValue: value,
      timestamp: new Date(),
      source: 'test'
    };
    
    await this.handleConfigChange(testEvent);
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalChanges: 0,
      successfulReloads: 0,
      failedReloads: 0,
      lastReloadTime: null,
      registeredHandlers: this.handlers.length,
      averageReloadTime: 0
    };
    this.reloadTimes = [];
    
    console.log('📊 热重载统计信息已重置');
  }

  /**
   * 销毁热重载服务
   */
  destroy(): void {
    // 移除事件监听器
    layeredConfigManager.removeAllListeners('configChanged');
    layeredConfigManager.removeAllListeners('configReloaded');
    
    // 清理处理器
    this.handlers = [];
    
    // 清理统计信息
    this.resetStats();
    
    this.isInitialized = false;
    console.log('🔥 配置热重载服务已销毁');
  }
}

// 导出单例实例
export const configHotReloadService = new ConfigHotReloadService();
