/**
 * SelfMirror 中央配置管理系统 - 主入口
 * 提供统一的配置管理接口和便捷的初始化方法
 */

// ==================== 核心导出 ====================

// 主要类和接口
export { ConfigManager, getGlobalConfigManager, setGlobalConfigManager, initializeConfigManager } from './ConfigManager';

// 接口和类型
export type {
  SelfMirrorConfig,
  IConfigManager,
  AppConfig,
  StorageConfig,
  RAGConfig,
  DebugConfig,
  SecurityConfig,
  PerformanceConfig,
  ConfigValidationResult,
  ConfigError,
  ConfigWarning,
  ConfigChangeCallback,
  ConfigStatus,
  ConfigSource,
  Environment,
  LogLevel,
  StorageProvider,
  EmbeddingProvider,
  RetrievalAlgorithm,
  CacheStrategy
} from './interfaces/IConfig';

// 默认配置
export { getDefaultConfig, getEnvironmentConfig } from './defaults/DefaultConfig';

// 配置验证
export { validateConfig } from './validation/ConfigValidator';

// 配置工具
export {
  mergeConfigs,
  getConfigValue,
  setConfigValue,
  deleteConfigValue,
  getConfigDiff,
  flattenConfig,
  unflattenConfig,
  cleanConfig,
  hasConfigPath,
  getConfigPaths,
  cloneConfig,
  formatConfig,
  parseConfig,
  getConfigSize,
  compressConfig,
  beautifyConfig,
  sanitizeConfig,
  compareConfigVersions,
  generateConfigTemplate
} from './utils/ConfigUtils';

// ==================== 便捷初始化函数 ====================

/**
 * 快速初始化配置管理器
 * 使用默认配置和环境检测
 */
export async function quickInitializeConfig(): Promise<InstanceType<typeof import('./ConfigManager').ConfigManager>> {
  const { ConfigManager } = await import('./ConfigManager');
  const manager = new ConfigManager();
  await manager.loadConfig();

  // 在开发环境启动热重载
  if (manager.getConfig().app.environment === 'development') {
    manager.startHotReload();
  }

  return manager;
}

/**
 * 使用自定义配置文件初始化
 */
export async function initializeConfigWithFile(configPath: string): Promise<InstanceType<typeof import('./ConfigManager').ConfigManager>> {
  const { initializeConfigManager } = await import('./ConfigManager');
  return initializeConfigManager(configPath);
}

/**
 * 使用内存配置初始化
 */
export async function initializeConfigWithObject(config: Partial<import('./interfaces/IConfig').SelfMirrorConfig>): Promise<InstanceType<typeof import('./ConfigManager').ConfigManager>> {
  const { ConfigManager } = await import('./ConfigManager');
  const { getDefaultConfig } = await import('./defaults/DefaultConfig');
  const { mergeConfigs } = await import('./utils/ConfigUtils');
  
  const manager = new ConfigManager();
  const defaultConfig = getDefaultConfig();
  const mergedConfig = mergeConfigs(defaultConfig, config);
  
  await manager.updateConfig(mergedConfig);
  return manager;
}

// ==================== 配置预设 ====================

/**
 * 开发环境配置预设
 */
export async function createDevelopmentConfig(): Promise<Partial<import('./interfaces/IConfig').SelfMirrorConfig>> {
  const { getEnvironmentConfig } = await import('./defaults/DefaultConfig');
  return getEnvironmentConfig('development');
}

/**
 * 生产环境配置预设
 */
export async function createProductionConfig(): Promise<Partial<import('./interfaces/IConfig').SelfMirrorConfig>> {
  const { getEnvironmentConfig } = await import('./defaults/DefaultConfig');
  return getEnvironmentConfig('production');
}

/**
 * 测试环境配置预设
 */
export async function createTestConfig(): Promise<Partial<import('./interfaces/IConfig').SelfMirrorConfig>> {
  const { getEnvironmentConfig } = await import('./defaults/DefaultConfig');
  return getEnvironmentConfig('test');
}

// ==================== 配置构建器 ====================

/**
 * 配置构建器类
 */
export class ConfigBuilder {
  private config: Partial<import('./interfaces/IConfig').SelfMirrorConfig> = {};

  /**
   * 设置应用配置
   */
  setApp(appConfig: Partial<import('./interfaces/IConfig').AppConfig>): this {
    this.config.app = { ...this.config.app, ...appConfig } as any;
    return this;
  }

  /**
   * 设置 AI 配置
   */
  setAI(aiConfig: Partial<any>): this {
    this.config.ai = { ...this.config.ai, ...aiConfig } as any;
    return this;
  }

  /**
   * 设置存储配置
   */
  setStorage(storageConfig: Partial<import('./interfaces/IConfig').StorageConfig>): this {
    this.config.storage = { ...this.config.storage, ...storageConfig } as any;
    return this;
  }

  /**
   * 设置 RAG 配置
   */
  setRAG(ragConfig: Partial<import('./interfaces/IConfig').RAGConfig>): this {
    this.config.rag = { ...this.config.rag, ...ragConfig } as any;
    return this;
  }

  /**
   * 设置调试配置
   */
  setDebug(debugConfig: Partial<import('./interfaces/IConfig').DebugConfig>): this {
    this.config.debug = { ...this.config.debug, ...debugConfig } as any;
    return this;
  }

  /**
   * 设置安全配置
   */
  setSecurity(securityConfig: Partial<import('./interfaces/IConfig').SecurityConfig>): this {
    this.config.security = { ...this.config.security, ...securityConfig } as any;
    return this;
  }

  /**
   * 设置性能配置
   */
  setPerformance(performanceConfig: Partial<import('./interfaces/IConfig').PerformanceConfig>): this {
    this.config.performance = { ...this.config.performance, ...performanceConfig } as any;
    return this;
  }

  /**
   * 设置环境
   */
  setEnvironment(environment: import('./interfaces/IConfig').Environment): this {
    if (!this.config.app) this.config.app = {} as any;
    (this.config.app as any).environment = environment;
    return this;
  }

  /**
   * 启用调试模式
   */
  enableDebug(): this {
    if (!this.config.app) this.config.app = {} as any;
    if (!this.config.debug) this.config.debug = {} as any;

    (this.config.app as any).debug = true;
    (this.config.debug as any).enabled = true;
    return this;
  }

  /**
   * 禁用调试模式
   */
  disableDebug(): this {
    if (!this.config.app) this.config.app = {} as any;
    if (!this.config.debug) this.config.debug = {} as any;

    (this.config.app as any).debug = false;
    (this.config.debug as any).enabled = false;
    return this;
  }

  /**
   * 构建配置
   */
  build(): Partial<import('./interfaces/IConfig').SelfMirrorConfig> {
    return { ...this.config };
  }

  /**
   * 构建并验证配置
   */
  async buildAndValidate(): Promise<Partial<import('./interfaces/IConfig').SelfMirrorConfig>> {
    const config = this.build();
    const { validateConfig } = await import('./validation/ConfigValidator');
    
    const validation = validateConfig(config);
    if (!validation.isValid) {
      throw new Error(`配置验证失败: ${validation.errors.map(e => e.message).join(', ')}`);
    }
    
    if (validation.warnings.length > 0) {
      console.warn('⚠️ 配置警告:', validation.warnings.map(w => w.message));
    }
    
    return config;
  }
}

// ==================== 配置监听器 ====================

/**
 * 配置变化监听器
 */
export class ConfigWatcher {
  private manager: InstanceType<typeof import('./ConfigManager').ConfigManager>;
  private listeners: Map<string, Set<(value: any) => void>> = new Map();
  private unsubscribe: (() => void) | null = null;

  constructor(manager: InstanceType<typeof import('./ConfigManager').ConfigManager>) {
    this.manager = manager;
    this.setupWatcher();
  }

  /**
   * 监听配置路径变化
   */
  watch<T>(path: string, callback: (value: T) => void): () => void {
    if (!this.listeners.has(path)) {
      this.listeners.set(path, new Set());
    }
    
    this.listeners.get(path)!.add(callback);
    
    // 立即调用一次回调
    const { getConfigValue } = require('./utils/ConfigUtils');
    const currentValue = getConfigValue(this.manager.getConfig(), path);
    callback(currentValue);
    
    return () => {
      const pathListeners = this.listeners.get(path);
      if (pathListeners) {
        pathListeners.delete(callback);
        if (pathListeners.size === 0) {
          this.listeners.delete(path);
        }
      }
    };
  }

  /**
   * 停止监听
   */
  stop(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.listeners.clear();
  }

  private setupWatcher(): void {
    this.unsubscribe = this.manager.onConfigChange((newConfig, oldConfig, changedPaths) => {
      for (const changedPath of changedPaths) {
        // 检查是否有监听器关心这个路径或其父路径
        for (const [watchPath, callbacks] of this.listeners) {
          if (changedPath.startsWith(watchPath) || watchPath.startsWith(changedPath)) {
            const { getConfigValue } = require('./utils/ConfigUtils');
            const newValue = getConfigValue(newConfig, watchPath);
            for (const callback of callbacks) {
              try {
                callback(newValue);
              } catch (error) {
                console.error(`配置监听器回调失败 (${watchPath}):`, error);
              }
            }
          }
        }
      }
    });
  }
}

// ==================== 便捷函数 ====================

/**
 * 获取当前配置
 */
export function getCurrentConfig(): import('./interfaces/IConfig').SelfMirrorConfig {
  const { getGlobalConfigManager } = require('./ConfigManager');
  return getGlobalConfigManager().getConfig();
}

/**
 * 获取配置值
 */
export function getConfig<T>(path: string): T {
  const { getConfigValue } = require('./utils/ConfigUtils');
  return getConfigValue(getCurrentConfig(), path);
}

/**
 * 更新配置值
 */
export async function updateConfig(path: string, value: any): Promise<void> {
  const { getGlobalConfigManager } = require('./ConfigManager');
  const { setConfigValue } = require('./utils/ConfigUtils');
  const manager = getGlobalConfigManager();
  const currentConfig = manager.getConfig();
  const newConfig = setConfigValue(currentConfig, path, value);
  await manager.updateConfig(newConfig);
}

/**
 * 创建配置监听器
 */
export function createConfigWatcher(): ConfigWatcher {
  const { getGlobalConfigManager } = require('./ConfigManager');
  return new ConfigWatcher(getGlobalConfigManager());
}

// ==================== 默认导出 ====================

const { ConfigManager: ConfigManagerClass, initializeConfigManager: initializeConfigManagerFunc } = require('./ConfigManager');
const { getDefaultConfig } = require('./defaults/DefaultConfig');
const { validateConfig } = require('./validation/ConfigValidator');
const { mergeConfigs, getConfigValue: getConfigValueFunc, setConfigValue: setConfigValueFunc } = require('./utils/ConfigUtils');

export default {
  // 核心类
  ConfigManager: ConfigManagerClass,
  ConfigBuilder,
  ConfigWatcher,
  
  // 初始化函数
  quickInitializeConfig,
  initializeConfigWithFile,
  initializeConfigWithObject,
  initializeConfigManager: initializeConfigManagerFunc,
  
  // 预设配置
  createDevelopmentConfig,
  createProductionConfig,
  createTestConfig,
  
  // 便捷函数
  getCurrentConfig,
  getConfig,
  updateConfig,
  createConfigWatcher,
  
  // 工具函数
  getDefaultConfig,
  validateConfig,
  mergeConfigs,
  getConfigValue: getConfigValueFunc,
  setConfigValue: setConfigValueFunc
};
