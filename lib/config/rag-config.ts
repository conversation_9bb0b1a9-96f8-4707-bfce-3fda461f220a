/**
 * 简化的RAG配置文件
 * TODO: 重构后重新实现完整的RAG配置
 */

// 简单的RAG配置接口
export interface RAGConfig {
  enabled: boolean;
  vectorDimension: number;
  chunkSize: number;
  chunkOverlap: number;
  maxResults: number;
  embedding: {
    maxTokens: number;
    model: string;
    dimensions: number;
  };
  retrieval: {
    maxCandidates: number;
    similarityThreshold: number;
    maxResults: number;
    topK: number;
    filterRatio: number;
  };
  storage: {
    dbName: string;
    version: number;
  };
}

// 默认配置
export const DEFAULT_RAG_CONFIG: RAGConfig = {
  enabled: false, // 暂时禁用RAG功能
  vectorDimension: 1536,
  chunkSize: 1000,
  chunkOverlap: 200,
  maxResults: 10,
  embedding: {
    maxTokens: 8192,
    model: 'text-embedding-ada-002',
    dimensions: 1536
  },
  retrieval: {
    maxCandidates: 50,
    similarityThreshold: 0.7,
    maxResults: 10,
    topK: 5,
    filterRatio: 0.3
  },
  storage: {
    dbName: 'SelfMirrorRAG',
    version: 1
  }
};

// 获取RAG配置
export function getRAGConfig(): RAGConfig {
  return DEFAULT_RAG_CONFIG;
}

// 更新RAG配置
export function updateRAGConfig(config: Partial<RAGConfig>): void {
  console.log('RAG配置更新（模拟）:', config);
  // TODO: 重构后实现真实的配置更新
}

// 验证RAG配置
export function validateRAGConfig(config: RAGConfig): boolean {
  return config.vectorDimension > 0 && config.chunkSize > 0;
}

// 兼容性导出（为了保持向后兼容）
export const RAG_CONFIG = DEFAULT_RAG_CONFIG;
export const PERFORMANCE_CONFIG = {
  maxConcurrentRequests: 5,
  timeout: 30000,
  retryAttempts: 3,
  enableCache: true,
  cacheSize: 1000,
  enableBatching: false,
  batchSize: 10,
  batchTimeout: 100,
  embeddingTimeout: 30000,
  maxTextLength: 8192,
  maxConcurrency: 3
};
export const DEBUG_CONFIG = {
  enabled: true,
  logLevel: 'info' as const
};
export const EMOTIONAL_TAGS = ['happy', 'sad', 'angry', 'excited', 'calm'];
export const COGNITIVE_THEMES = ['learning', 'problem-solving', 'creativity', 'analysis'];
export const MEANING_WEIGHTS = {
  semantic: 0.4,
  emotional: 0.3,
  temporal: 0.2,
  contextual: 0.1,
  profileRelevance: 0.25,
  insightRelevance: 0.25,
  temporalRelevance: 0.25,
  meaningScore: 0.25
};
