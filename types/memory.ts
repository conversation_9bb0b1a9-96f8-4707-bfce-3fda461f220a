// 记忆系统核心类型定义

// 用户画像
export interface UserProfile {
  cognitiveTraits: {
    thinkingPattern: string;
    emotionalSensitivity: string;
    valueSystem: string;
    communicationStyle: string;
  };
  behaviorPatterns: {
    conflictResponse: string;
    decisionMaking: string;
    relationshipStyle: string;
  };
  growthTrajectory: {
    currentPhase: string;
    coreStruggles: string[];
    strengths: string[];
  };
  lastUpdated: string;
}

// 关键事件
export interface KeyEvent {
  id: string;
  timeframe: string;
  title: string;
  impact: string;
  emotionalWeight: 'low' | 'medium' | 'high' | 'transformative';
  cognitiveShift: string;
  connectedThemes: string[];
  createdAt: string;
}

// 要素索引
export interface ElementIndex {
  emotionalTags: string[];
  conceptualTags: string[];
  relationshipTags: string[];
  projectTags: string[];
  quickAccess: {
    recentConcerns: string[];
    coreValues: string[];
    currentGoals: string[];
  };
  lastUpdated: string;
}

// 对话存档
export interface ConversationArchive {
  conversations: ArchivedConversation[];
}

export interface ArchivedConversation {
  timestamp: string;
  dialogueId: string;
  userOriginal: string;
  userAnnotations: {
    emotionalState: string;
    coreNeeds: string[];
    hiddenConcerns: string[];
  };
  aiResponse: string;
  aiAnnotations: {
    strategy: string;
    technique: string;
    nextStep: string;
  };
  contextTags: string[];
  vectorizable: boolean;
}

// 四文件系统
export interface MemorySystem {
  userProfile: UserProfile;
  keyEvents: KeyEvent[];
  elementIndex: ElementIndex;
  conversationArchive: ConversationArchive;
}

// 更新指令
export interface MemoryUpdate {
  updateProfile?: Partial<UserProfile>;
  addEvent?: Omit<KeyEvent, 'id' | 'createdAt'>;
  updateIndex?: Partial<ElementIndex>;
  archiveDialogue?: Omit<ArchivedConversation, 'timestamp' | 'dialogueId'>;
} 