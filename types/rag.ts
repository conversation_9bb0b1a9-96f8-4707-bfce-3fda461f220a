// 意义RAG系统的类型定义
//
// ⚠️ 已弃用：此文件将在下一个版本中移除
// 请使用 ./unified-rag.ts 中的统一类型定义
//
// 为了向后兼容性，此文件暂时保留
//

// 导入统一类型定义
export * from './unified-rag';

export interface ChunkPair {
  id: string;
  parentChunk: string;        // 母块：原始文本片段
  childChunk: string;         // 子块：LLM生成的意义摘要
  vector: number[];           // 子块的向量表示
  sourceDocumentId: string;   // 来源文档ID
  sourceDocumentType: 'user_profile' | 'key_events' | 'daily_insight_cold' | 'daily_insight_hot' | 'dialogue_history';
  metadata: ChunkMetadata;
  createdAt: string;
  updatedAt: string;
}

export interface ChunkMetadata {
  chunkIndex: number;         // 在源文档中的块序号
  parentLength: number;       // 母块字符长度
  childLength: number;        // 子块字符长度
  emotionalTags?: string[];   // 情感标签
  cognitiveThemes?: string[]; // 认知主题
  meaningScore?: number;      // 意义分数（0-1）
  lastAccessTime?: string;    // 最后访问时间
  accessCount?: number;       // 访问次数
}

export interface VectorSearchCandidate {
  chunkPair: ChunkPair;
  similarity: number;         // 向量相似度分数
  rank: number;              // 初始排名
}

export interface WeightedCandidate extends VectorSearchCandidate {
  dynamicWeight: number;      // 动态意义权重
  weightingFactors: {
    profileRelevance: number;    // 与用户画像的相关性
    insightRelevance: number;    // 与当前洞察的相关性
    temporalRelevance: number;   // 时间相关性
    emotionalRelevance: number;  // 情感相关性
    accessFrequency: number;     // 访问频率权重
  };
  finalRank: number;          // 最终排名
}

export interface RetrievalContext {
  latestQuery: string;        // 用户最新输入
  userProfile: string;        // 用户画像内容
  dailyInsight: string;       // 每日洞察内容
  conversationHistory?: string[]; // 对话历史（可选）
  emotionalState?: string;    // 当前情感状态（可选）
}

export interface RetrievalResult {
  memories: string[];         // 检索到的记忆片段（母块）
  metadata: {
    totalCandidates: number;
    filteredCandidates: number;
    finalResults: number;
    processingTime: number;
    averageRelevanceScore: number;
  };
  debugInfo?: {
    topCandidates: WeightedCandidate[];
    weightingBreakdown: any;
  };
}

// RAG调试参数类型定义
export type RAGRetrievalMode = 'Vector Only' | 'Keyword Only' | 'Hybrid Weighted';

export interface RAGDebugParams {
  mode: RAGRetrievalMode;      // 检索模式
  w1: number;                  // 语义分权重 (0.0-1.0)
  w2: number;                  // 标签分权重 (0.0-1.0)
  w3: number;                  // 记忆重要性权重 (0.0-1.0)
  topK: number;                // 候选项数量
  finalCount: number;          // 最终上下文数量
  // 新增的语义控制参数
  semanticAlgorithm?: 'cosine' | 'euclidean' | 'dot_product'; // 语义匹配算法
  similarityThreshold?: number;     // 相似度阈值 (0.0-1.0)
  enableSemanticFiltering?: boolean; // 启用语义过滤
}

// ==================== 高级调试台配置接口 ====================

/**
 * 索引配置接口 - 控制文档分块和意义生成
 */
export interface IndexingConfig {
  // 分块策略
  chunkingStrategy: 'recursive' | 'sentence' | 'paragraph' | 'semantic';
  chunkSize: number;           // 块大小 (100-2000)
  chunkOverlap: number;        // 块重叠 (0-500)

  // AI增强配置
  meaningGenerationPrompt: string;     // 意义子块生成提示词
  connectionTaggingPrompt: string;     // 连接标签生成提示词
  enableAIEnhancement: boolean;        // 启用AI增强

  // 情感和认知分析
  emotionalAnalysisPrompt: string;     // 情感分析提示词
  cognitiveAnalysisPrompt: string;     // 认知分析提示词

  // 性能配置
  batchSize: number;           // 批处理大小
  maxConcurrency: number;      // 最大并发数
  enableCaching: boolean;      // 启用缓存
}

/**
 * 检索配置接口 - 控制检索算法和权重计算
 */
export interface RetrievalConfig {
  // 基础检索配置
  retrievalMode: 'vector_only' | 'keyword_only' | 'hybrid_weighted';
  semanticAlgorithm: 'cosine' | 'euclidean' | 'dot_product';
  similarityThreshold: number;         // 相似度阈值 (0.0-1.0)

  // 动态权重规则
  weightingRules: DynamicWeightRule[];
  enableDynamicWeighting: boolean;

  // 过滤规则
  filterRules: FilterRule[];
  filterPercentage: number;    // 抛弃权重最低的百分比 (0-50)

  // 检索数量控制
  topK: number;               // 候选项数量
  finalCount: number;         // 最终返回数量

  // 高级配置
  enableReranking: boolean;   // 启用重排序
  diversityFactor: number;    // 多样性因子 (0.0-1.0)
  temporalDecay: number;      // 时间衰减因子 (0.0-1.0)
}

/**
 * 动态权重规则 - 根据条件动态调整权重
 */
export interface DynamicWeightRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;

  // 触发条件
  condition: {
    // 标签匹配
    tags?: string[];
    tagsOperator?: 'AND' | 'OR';

    // 情感状态
    emotions?: string[];
    emotionsOperator?: 'AND' | 'OR';

    // 认知主题
    topics?: string[];
    topicsOperator?: 'AND' | 'OR';

    // 时间条件
    recentlyMentioned?: boolean;
    timeWindow?: number;        // 时间窗口（小时）

    // 重要性条件
    minImportanceScore?: number;
    maxImportanceScore?: number;

    // 自定义条件
    customCondition?: string;   // JavaScript表达式
  };

  // 权重调整
  boost: number;              // 权重提升倍数 (0.1-10.0)
  boostType: 'multiply' | 'add' | 'set';

  // 优先级
  priority: number;           // 规则优先级 (1-100)
}

/**
 * 过滤规则 - 控制候选项过滤逻辑
 */
export interface FilterRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;

  // 过滤类型
  filterType: 'similarity' | 'importance' | 'recency' | 'diversity' | 'custom';

  // 过滤参数
  parameters: {
    threshold?: number;
    operator?: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'ne';
    value?: any;
    customFilter?: string;    // JavaScript表达式
  };

  // 执行顺序
  order: number;
}

/**
 * 配置验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

/**
 * 权重分析结果
 */
export interface WeightAnalysis {
  candidateId: string;
  originalScore: number;
  finalScore: number;
  appliedRules: AppliedRule[];
  scoreBreakdown: ScoreBreakdown;
}

export interface AppliedRule {
  ruleId: string;
  ruleName: string;
  matched: boolean;
  boost: number;
  contribution: number;
}

export interface ScoreBreakdown {
  semanticScore: number;
  tagScore: number;
  importanceScore: number;
  temporalScore: number;
  dynamicBoosts: number;
  finalScore: number;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  indexingTime: number;       // 索引时间 (ms)
  retrievalTime: number;      // 检索时间 (ms)
  totalTime: number;          // 总时间 (ms)

  // 详细时间分解
  timeBreakdown: {
    vectorization: number;
    search: number;
    weighting: number;
    filtering: number;
    reranking: number;
  };

  // 资源使用
  memoryUsage: number;        // 内存使用 (MB)
  cpuUsage: number;          // CPU使用率 (%)

  // 质量指标
  candidatesFound: number;
  candidatesFiltered: number;
  finalResults: number;
  averageRelevance: number;
}

/**
 * 测试结果
 */
export interface TestResult {
  query: string;
  results: RetrievalResult[];
  weightAnalysis: WeightAnalysis[];
  performanceMetrics: PerformanceMetrics;
  timestamp: string;
  configSnapshot: {
    indexing: IndexingConfig;
    retrieval: RetrievalConfig;
  };
}

/**
 * 配置预设
 */
export interface ConfigPreset {
  id: string;
  name: string;
  description: string;
  category: 'default' | 'emotional' | 'technical' | 'creative' | 'custom';

  // 配置内容
  indexingConfig: IndexingConfig;
  retrievalConfig: RetrievalConfig;

  // 元数据
  createdAt: string;
  updatedAt: string;
  author: string;
  version: string;
  tags: string[];

  // 使用统计
  usageCount: number;
  lastUsed: string;

  // 性能基准
  benchmarkResults?: {
    averageRetrievalTime: number;
    averageRelevance: number;
    testCases: number;
  };
}

/**
 * 配置历史记录
 */
export interface ConfigHistory {
  id: string;
  timestamp: string;
  action: 'create' | 'update' | 'delete' | 'import' | 'export';
  configType: 'indexing' | 'retrieval' | 'preset';
  changes: ConfigChange[];
  description?: string;
}

export interface ConfigChange {
  field: string;
  oldValue: any;
  newValue: any;
  changeType: 'add' | 'update' | 'delete';
}

/**
 * 调试会话
 */
export interface DebugSession {
  id: string;
  name: string;
  startTime: string;
  endTime?: string;

  // 会话配置
  indexingConfig: IndexingConfig;
  retrievalConfig: RetrievalConfig;

  // 测试历史
  testHistory: TestResult[];

  // 配置变更历史
  configHistory: ConfigHistory[];

  // 会话统计
  totalTests: number;
  totalIndexedDocuments: number;
  averagePerformance: PerformanceMetrics;
}

/**
 * 实时状态
 */
export interface DebuggerState {
  // 当前配置
  currentIndexingConfig: IndexingConfig;
  currentRetrievalConfig: RetrievalConfig;

  // 运行状态
  isIndexing: boolean;
  isTesting: boolean;
  indexingProgress: number;

  // 最近结果
  lastTestResult?: TestResult;
  lastError?: string;

  // 会话信息
  currentSession: DebugSession;

  // UI状态
  activeTab: 'indexing' | 'retrieval' | 'testing';
  selectedPreset?: string;
  unsavedChanges: boolean;
}

export interface RAGDebugResult extends RetrievalResult {
  debugParams: RAGDebugParams; // 使用的调试参数
  modeSpecificInfo?: {
    vectorOnlyResults?: VectorSearchCandidate[];
    keywordOnlyResults?: any[];
    hybridWeightBreakdown?: {
      semanticScore: number;
      tagScore: number;
      memoryImportanceScore: number;
      finalScore: number;
    }[];
  };
}

export interface IndexingProgress {
  documentId: string;
  totalChunks: number;
  processedChunks: number;
  currentStage: 'chunking' | 'meaning_generation' | 'vectorization' | 'storage' | 'complete';
  startTime: string;
  estimatedCompletion?: string;
  errors?: string[];
}

export interface RAGConfig {
  embedding: {
    modelName: string;
    dimensions: number;
    maxTokens: number;
  };
  chunking: {
    maxChunkSize: number;
    overlapSize: number;
    minChunkSize: number;
  };
  retrieval: {
    maxCandidates: number;
    topK: number;
    similarityThreshold: number;
    filterRatio: number; // 过滤掉权重最低的比例
  };
  storage: {
    dbName: string;
    version: number;
    maxStorageSize: number; // MB
  };
}

export interface EmbeddingModel {
  name: string;
  loaded: boolean;
  dimensions: number;
  encode(text: string): Promise<number[]>;
  encodeBatch(texts: string[]): Promise<number[][]>;
}

export interface VectorDatabase {
  initialized: boolean;
  totalVectors: number;
  search(query: number[], topK: number): Promise<VectorSearchCandidate[]>;
  add(vector: number[], metadata: any): Promise<string>;
  update(id: string, vector: number[], metadata: any): Promise<void>;
  delete(id: string): Promise<void>;
  clear(): Promise<void>;
}

// 错误类型定义
export class RAGError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'RAGError';
  }
}

export class EmbeddingError extends RAGError {
  constructor(message: string, details?: any) {
    super(message, 'EMBEDDING_ERROR', details);
  }
}

export class ChunkingError extends RAGError {
  constructor(message: string, details?: any) {
    super(message, 'CHUNKING_ERROR', details);
  }
}

export class RetrievalError extends RAGError {
  constructor(message: string, details?: any) {
    super(message, 'RETRIEVAL_ERROR', details);
  }
}

export class StorageError extends RAGError {
  constructor(message: string, details?: any) {
    super(message, 'STORAGE_ERROR', details);
  }
}
