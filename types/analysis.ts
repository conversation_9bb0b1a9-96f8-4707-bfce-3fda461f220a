// 深度分析输出类型定义

// 分析报告
export interface AnalysisReport {
  summary: string;              // 这次对话的关键发现
  emotionalPatterns: string;    // 情绪模式观察
  cognitiveInsights: string;    // 认知洞察
  keyFindings: string[];        // 主要发现列表
  recommendations: string;      // 后续建议
}

// 用户画像更新指令
export interface ProfileUpdateInstruction {
  cognitiveTraits?: {
    thinkingPattern?: string;        // 新发现的思维模式
    emotionalSensitivity?: string;   // 新的情感敏感度观察
    valueSystem?: string;            // 价值观的新发现
    communicationStyle?: string;     // 沟通风格的新观察
  };
  behaviorPatterns?: {
    conflictResponse?: string;       // 冲突应对的新模式
    decisionMaking?: string;         // 决策方式的新发现
    relationshipStyle?: string;      // 关系模式的新观察
  };
  growthTrajectory?: {
    currentPhase?: string;           // 当前阶段的更新
    coreStruggles?: string[];        // 新发现的困扰（只添加）
    strengths?: string[];            // 新发现的优势（只添加）
  };
}

// 关键事件添加指令
export interface EventAddInstruction {
  title: string;                     // 简短标题（10字以内）
  timeframe: string;                 // 时间段（如"最近"、"上周"）
  impact: string;                    // 自然语言描述影响
  emotionalWeight: 'low' | 'medium' | 'high' | 'transformative';
  cognitiveShift?: string;           // 认知变化描述
  insights: string[];                // 这个事件的深层含义
  connectedThemes: string[];         // 相关主题
}

// 要素索引更新指令
export interface IndexUpdateInstruction {
  emotionalTags?: string[];          // 新的情绪标签
  conceptualTags?: string[];         // 新的概念标签
  relationshipTags?: string[];       // 新的关系标签
  projectTags?: string[];            // 新的项目标签
  updateQuickAccess?: {
    recentConcerns?: string[];       // 替换最近3个关注点
    coreValues?: string[];           // 更新核心价值观
    currentGoals?: string[];         // 更新当前目标
  };
}

// 对话归档指令
export interface DialogueArchiveInstruction {
  userSaid: string;                  // 用户的原话
  aiResponse: string;                // AI的回应（精简版）
  context: string;                   // 当时的情绪和背景
  emotionalState: string;            // 用户的情绪状态
  coreNeeds: string[];               // 识别的核心需求
  hiddenConcerns: string[];          // 潜在的担忧
  aiStrategy: string;                // AI使用的策略
  technique: string;                 // 具体技巧
  significance: string;              // 为什么这段对话重要
  contextTags: string[];             // 相关标签
}

// 更新指令集合
export interface UpdateInstructions {
  addToProfile?: ProfileUpdateInstruction;
  addEvent?: EventAddInstruction;
  addToIndex?: IndexUpdateInstruction;
  archiveDialogue?: DialogueArchiveInstruction;
}

// 深度分析完整输出
export interface DeepAnalysisOutput {
  analysisReport: AnalysisReport;
  updateInstructions: UpdateInstructions;
}

// 执行结果
export interface UpdateExecutionResult {
  success: boolean;
  profileUpdated: boolean;
  eventAdded: boolean;
  indexUpdated: boolean;
  dialogueArchived: boolean;
  errors?: string[];
} 