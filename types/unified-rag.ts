/**
 * SelfMirror 统一RAG系统 - 核心类型定义
 * 
 * 这个文件合并了 rag.ts 和 meaning-rag.ts 中的重复类型定义
 * 提供统一的接口，避免类型冲突
 */

// ==================== 基础数据结构 ====================

/**
 * 统一的块元数据接口
 * 合并了两个文件中的 ChunkMetadata 定义
 */
export interface UnifiedChunkMetadata {
  id: string;
  parentChunkId?: string;      // 母块ID（如果是子块）
  documentId: string;
  chunkIndex: number;          // 在源文档中的块序号
  content: string;
  
  // 长度信息
  parentLength?: number;       // 母块字符长度
  childLength?: number;        // 子块字符长度
  
  // 意义标注
  emotionalTags?: string[];    // 情感标签
  cognitiveThemes?: string[];  // 认知主题
  meaningScore?: number;       // 意义分数（0-1）
  importanceScore?: number;    // 重要性分数
  keywords?: string[];         // 关键词
  summary?: string;           // 摘要
  
  // 访问统计
  lastAccessTime?: string;     // 最后访问时间
  accessCount?: number;        // 访问次数
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
}

/**
 * 块对接口（母块-子块对）
 */
export interface ChunkPair {
  id: string;
  parentChunk: string;        // 母块：原始文本片段
  childChunk: string;         // 子块：LLM生成的意义摘要
  vector: number[];           // 子块的向量表示
  sourceDocumentId: string;   // 来源文档ID
  sourceDocumentType: 'user_profile' | 'key_events' | 'daily_insight_cold' | 'daily_insight_hot' | 'dialogue_history';
  metadata: UnifiedChunkMetadata;
  createdAt: string;
  updatedAt: string;
}

// ==================== 配置接口 ====================

/**
 * 文档分块方法
 */
export type ChunkingMethod = 'recursive' | 'sentence' | 'paragraph' | 'semantic';

/**
 * 统一的分块规则配置
 */
export interface UnifiedChunkingRules {
  method: ChunkingMethod;
  chunkSize: number;          // 块大小（字符数）
  overlap: number;            // 重叠字符数
  separators?: string[];      // 自定义分隔符
  preserveStructure: boolean; // 是否保持文档结构
}

/**
 * 意义批注规则配置
 */
export interface MeaningAnnotationRules {
  extractEmotions: boolean;     // 提取情感标签
  extractThemes: boolean;       // 提取认知主题
  extractImportance: boolean;   // 计算重要性分数
  extractKeywords: boolean;     // 提取关键词
  generateSummary: boolean;     // 生成摘要
}

/**
 * 统一的索引配置接口
 * 合并了两个文件中的 IndexingConfig 定义
 */
export interface UnifiedIndexingConfig {
  // 分块配置
  chunkingRules: UnifiedChunkingRules;
  
  // 意义生成配置
  childChunkPrompt: string;     // 意义子块生成提示词
  meaningAnnotationRules: MeaningAnnotationRules;
  
  // 向量模型配置
  vectorModel: {
    modelName: string;
    dimensions: number;
    maxTokens?: number;
  };
  
  // 处理配置
  batchSize: number;           // 批处理大小
  
  // 高级配置（来自原 rag.ts）
  enableMeaningExtraction?: boolean;
  enableEmotionalAnalysis?: boolean;
  enableThematicClustering?: boolean;
  enableTemporalWeighting?: boolean;
}

/**
 * 检索配置接口
 */
export interface RetrievalConfig {
  // 基础检索配置
  retrievalMode: 'vector_only' | 'keyword_only' | 'hybrid_weighted';
  semanticAlgorithm: 'cosine' | 'euclidean' | 'dot_product';
  similarityThreshold: number;         // 相似度阈值 (0.0-1.0)
  
  // 权重配置
  weightingFactors: {
    profileRelevance: number;    // 与用户画像的相关性
    insightRelevance: number;    // 与当前洞察的相关性
    temporalRelevance: number;   // 时间相关性
    emotionalRelevance: number;  // 情感相关性
    accessFrequency: number;     // 访问频率权重
  };
  
  // 结果配置
  maxResults: number;              // 最大返回结果数
  diversityThreshold: number;      // 多样性阈值
  enableReranking: boolean;        // 启用重排序
}

// ==================== 检索相关类型 ====================

/**
 * 向量搜索候选项
 */
export interface VectorSearchCandidate {
  chunkPair: ChunkPair;
  similarity: number;         // 向量相似度分数
  rank: number;              // 初始排名
}

/**
 * 加权候选项
 */
export interface WeightedCandidate extends VectorSearchCandidate {
  dynamicWeight: number;      // 动态意义权重
  weightingFactors: {
    profileRelevance: number;    // 与用户画像的相关性
    insightRelevance: number;    // 与当前洞察的相关性
    temporalRelevance: number;   // 时间相关性
    emotionalRelevance: number;  // 情感相关性
    accessFrequency: number;     // 访问频率权重
  };
  finalRank: number;          // 最终排名
}

/**
 * 检索上下文
 */
export interface RetrievalContext {
  latestQuery: string;        // 用户最新输入
  userProfile: string;        // 用户画像内容
  dailyInsight: string;       // 每日洞察内容
  conversationHistory?: string[]; // 对话历史（可选）
  emotionalState?: string;    // 当前情感状态（可选）
}

// ==================== 向后兼容性类型别名 ====================

/**
 * 向后兼容性：保持原有的类型名称
 */
export type ChunkMetadata = UnifiedChunkMetadata;
export type IndexingConfig = UnifiedIndexingConfig;
export type ChunkingRules = UnifiedChunkingRules;

// ==================== 导出所有类型 ====================

// 注意：不直接导出所有类型以避免冲突
// 用户应该直接从此文件导入统一的类型定义
