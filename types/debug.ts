/**
 * Debug Types - 统一调试相关类型定义
 * 
 * 整合所有调试组件的类型定义，避免重复声明
 */

// ===== 基础类型 =====

export interface APIResponse<T = any> {
  success: boolean
  data: T
  metadata: {
    timestamp: string
    version: string
    requestParams?: Record<string, any>
  }
  error?: {
    code: string
    message: string
  }
}

export interface DebuggerState {
  isLoading: boolean
  lastUpdated: Date | null
  error: string | null
  status: 'idle' | 'loading' | 'success' | 'error'
}

// ===== 测试框架类型 =====

export interface TestSuite {
  id: string
  name: string
  description: string
  testCount: number
  status: 'idle' | 'running' | 'completed' | 'failed'
  lastRun?: Date
  passRate?: number
}

export interface TestResult {
  id: string
  name: string
  status: 'passed' | 'failed' | 'skipped'
  duration: number
  error?: string
  assertions?: {
    total: number
    passed: number
    failed: number
    details: Array<{
      description: string
      passed: boolean
      expected: any
      actual: any
    }>
  }
}

export interface TestingStats {
  totalSuites: number
  totalTests: number
  lastRunResults?: {
    passed: number
    failed: number
    passRate: number
    duration: number
  }
  executionStatus: {
    isRunning: boolean
    currentSuite?: string
    progress?: number
  }
}

export interface CoverageReport {
  overall: number
  byFile: Record<string, {
    lines: number
    functions: number
    branches: number
    statements: number
  }>
  uncoveredLines: Array<{
    file: string
    line: number
    content: string
  }>
}

// ===== 性能监控类型 =====

export type MetricType = 
  | 'RESPONSE_TIME'
  | 'MEMORY_USAGE'
  | 'CPU_USAGE'
  | 'CACHE_HIT_RATE'
  | 'ERROR_RATE'
  | 'THROUGHPUT'
  | 'CONCURRENT_USERS'
  | 'DATABASE_QUERY_TIME'
  | 'AI_PROCESSING_TIME'
  | 'NETWORK_LATENCY'

export interface PerformanceMetric {
  name: string
  type: MetricType
  value: number
  unit: string
  trend: 'up' | 'down' | 'stable'
  status: 'good' | 'warning' | 'critical'
  timestamp: Date
  component: string
  metadata?: Record<string, any>
}

export interface PerformanceAlert {
  id: string
  level: 'warning' | 'critical'
  metricType: MetricType
  component: string
  message: string
  currentValue: number
  threshold: number
  timestamp: Date
  resolved: boolean
  resolvedAt?: Date
}

export interface PerformanceStats {
  metricType: MetricType
  component: string
  current: number
  average: number
  min: number
  max: number
  p50: number
  p95: number
  p99: number
  count: number
  trend: 'improving' | 'stable' | 'degrading'
  lastUpdated: Date
}

export interface SystemHealth {
  healthScore: number
  status: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
  systemMetrics: {
    memoryUsage: {
      usagePercent: number
      heapUsed: number
      heapTotal: number
    }
    uptime: number
    processId: number
  }
  alerts: {
    critical: number
    warning: number
    total: number
  }
  recommendations: string[]
}

export interface BenchmarkResult {
  name: string
  iterations: number
  averageTime: number
  minTime: number
  maxTime: number
  throughput: number
  memoryUsage: number
  timestamp: Date
}

// ===== 健康检查类型 =====

export type HealthStatus = 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'DOWN' | 'UNKNOWN'

export type ComponentType = 
  | 'API'
  | 'AI_PROVIDER'
  | 'CACHE'
  | 'DATABASE'
  | 'CONFIGURATION'
  | 'ERROR_HANDLER'
  | 'PERFORMANCE_MONITOR'
  | 'SYSTEM'

export interface HealthCheck {
  id: string
  name: string
  component: ComponentType
  description: string
  enabled: boolean
  interval: number
  timeout: number
  retries: number
  status: HealthStatus
  lastCheck?: Date
  message?: string
  responseTime?: number
}

export interface ComponentHealth {
  component: ComponentType
  status: HealthStatus
  score: number
  checks: HealthCheck[]
  lastUpdated: Date
}

export interface SystemHealthReport {
  id: string
  timestamp: Date
  overallStatus: HealthStatus
  overallScore: number
  uptime: number
  components: Record<ComponentType, ComponentHealth>
  alerts: Array<{
    level: 'info' | 'warning' | 'critical'
    component: ComponentType
    message: string
    timestamp: Date
  }>
  recommendations: string[]
  trends: {
    availability: 'improving' | 'stable' | 'degrading'
    performance: 'improving' | 'stable' | 'degrading'
    reliability: 'improving' | 'stable' | 'degrading'
  }
}

export interface RecoveryAction {
  id: string
  name: string
  component: ComponentType
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  autoExecute: boolean
  lastExecuted?: Date
  successRate?: number
}

export interface SystemDiagnostic {
  id: string
  timestamp: Date
  healthReport: SystemHealthReport
  systemInfo: {
    memory: {
      heapUsed: number
      heapTotal: number
      usagePercent: number
    }
    uptime: number
    nodeVersion: string
    platform: string
    arch: string
  }
  issues: Array<{
    type: 'critical' | 'warning' | 'info'
    category: 'overall' | 'component' | 'memory' | 'performance'
    component?: ComponentType
    message: string
    score?: number
    usagePercent?: number
  }>
  recommendations: string[]
}

// ===== 配置管理类型 =====

export interface ConfigurationItem {
  key: string
  value: any
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  description?: string
  category: string
  editable: boolean
  sensitive: boolean
  lastModified?: Date
  modifiedBy?: string
}

export interface ConfigurationCategory {
  name: string
  description: string
  items: ConfigurationItem[]
  collapsed?: boolean
}

export interface ConfigurationState {
  categories: ConfigurationCategory[]
  searchTerm: string
  selectedCategory?: string
  hasUnsavedChanges: boolean
  lastSaved?: Date
}

// ===== 错误处理类型 =====

export interface ErrorInfo {
  id: string
  type: string
  message: string
  component: string
  operation?: string
  timestamp: Date
  stack?: string
  context?: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
  resolved: boolean
  resolvedAt?: Date
}

export interface ErrorStats {
  totalErrors: number
  recoveryAttempts: number
  successfulRecoveries: number
  failedRecoveries: number
  errorsByType: Record<string, number>
  errorsByComponent: Record<string, number>
  recentErrors: ErrorInfo[]
}

export interface CircuitBreakerInfo {
  id: string
  name: string
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN'
  failureCount: number
  threshold: number
  timeout: number
  lastFailure?: Date
  nextAttempt?: Date
}

// ===== 缓存管理类型 =====

export interface CacheLayerInfo {
  type: string
  name: string
  enabled: boolean
  hitRate: number
  totalRequests: number
  totalHits: number
  size: number
  maxSize: number
  ttl: number
  lastCleared?: Date
}

export interface CacheCoordinationMetrics {
  overallHitRate: number
  totalRequests: number
  totalHits: number
  layerMetrics: Record<string, CacheLayerInfo>
  consistencyChecks: number
  lastConsistencyCheck?: Date
}

export interface CacheOperation {
  id: string
  operation: 'get' | 'set' | 'delete' | 'clear'
  key: string
  layer: string
  success: boolean
  duration: number
  timestamp: Date
  error?: string
}

// ===== 通用工具类型 =====

export interface TimeRange {
  start: Date
  end: Date
}

export interface ChartDataPoint {
  timestamp: Date
  value: number
  label?: string
  metadata?: Record<string, any>
}

export interface TableColumn<T = any> {
  key: keyof T
  title: string
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: T) => React.ReactNode
}

export interface PaginationInfo {
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
}

export interface FilterOption {
  label: string
  value: string | number
  count?: number
}

export interface SortOption {
  field: string
  direction: 'asc' | 'desc'
}

// ===== 导出所有类型 =====

export type {
  // 重新导出以确保兼容性
  APIResponse as DebugAPIResponse,
  DebuggerState as BaseDebuggerState,
  TestSuite as DebugTestSuite,
  TestResult as DebugTestResult,
  PerformanceMetric as DebugPerformanceMetric,
  HealthCheck as DebugHealthCheck,
  SystemHealthReport as DebugSystemHealthReport
}
