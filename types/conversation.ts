// 对话系统类型定义

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  mode: 'quick' | 'deep';
  thinkingBudget: number;
  processingTime?: number;
  contextSize?: number;
}



export interface ConversationState {
  messages: Message[];
  currentMode: 'quick' | 'deep';
  contextWindow: Message[]; // 最近7-8条消息
  isProcessing: boolean;
  lastAnalysisTime?: string;
}

export interface DialogueBlock {
  userMessage: Message;
  aiResponse: Message;
  analysis?: DialogueAnalysis;
}

export interface DialogueAnalysis {
  cognitivePattern?: string;
  keyThemes: string[];
  memoryUpdateSuggestion?: any; // MemoryUpdate type
}

// API请求类型
export interface ChatRequest {
  message: string;
  mode?: 'quick' | 'deep';
  includeMemory?: boolean;
  conversationId?: string;
}

export interface ChatResponse {
  response: string;
  shouldTriggerAnalysis?: boolean;
  metadata?: MessageMetadata;
}