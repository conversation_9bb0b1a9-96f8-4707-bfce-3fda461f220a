/**
 * SelfMirror 意义RAG系统 - 核心类型定义
 *
 * ⚠️ 已弃用：此文件将在下一个版本中移除
 * 请使用 ./unified-rag.ts 中的统一类型定义
 *
 * 为了向后兼容性，此文件暂时保留
 */

// 导入统一类型定义
export * from './unified-rag';

// ==================== 索引相关类型 ====================

/**
 * 文档分块方法
 */
export type ChunkingMethod = 'recursive' | 'sentence' | 'paragraph' | 'semantic';

/**
 * 分块规则配置
 */
export interface ChunkingRules {
  method: ChunkingMethod;
  chunkSize: number;        // 块大小（字符数）
  overlap: number;          // 重叠字符数
  separators?: string[];    // 自定义分隔符
  preserveStructure: boolean; // 是否保持文档结构
}

/**
 * 意义批注规则配置
 */
export interface MeaningAnnotationRules {
  extractEmotions: boolean;     // 提取情感标签
  extractThemes: boolean;       // 提取认知主题
  extractImportance: boolean;   // 计算重要性分数
  extractKeywords: boolean;     // 提取关键词
  generateSummary: boolean;     // 生成摘要
}

/**
 * 索引配置
 */
export interface IndexingConfig {
  chunkingRules: ChunkingRules;
  childChunkPrompt: string;     // 意义子块生成提示词
  meaningAnnotationRules: MeaningAnnotationRules;
  vectorModel: {
    modelName: string;
    dimensions: number;
  };
  batchSize: number;           // 批处理大小
}

/**
 * 块元数据
 */
export interface ChunkMetadata {
  id: string;
  parentChunkId?: string;      // 母块ID（如果是子块）
  documentId: string;
  chunkIndex: number;
  content: string;
  childChunk?: string;         // 意义子块内容
  vector?: number[];           // 向量表示
  
  // 意义批注
  emotionalTags: string[];     // 情感标签
  cognitiveThemes: string[];   // 认知主题
  keywords: string[];          // 关键词
  summary?: string;            // 摘要
  importanceScore: number;     // 重要性分数 (0-1)
  
  // 元信息
  createdAt: Date;
  processingTime: number;      // 处理时间(ms)
  tokenCount: number;          // token数量
}

/**
 * 索引结果
 */
export interface IndexingResult {
  documentId: string;
  totalChunks: number;
  processedChunks: number;
  failedChunks: number;
  indexingTime: number;        // 总处理时间(ms)
  chunkMetadata: ChunkMetadata[];
  
  // 统计信息
  stats: {
    averageChunkSize: number;
    averageImportanceScore: number;
    totalTokens: number;
    uniqueEmotions: string[];
    uniqueThemes: string[];
  };
  
  // 错误信息
  errors?: Array<{
    chunkIndex: number;
    error: string;
  }>;
}

// ==================== 检索相关类型 ====================

/**
 * 检索模式
 */
export type RetrievalMode = 'vector' | 'keyword' | 'hybrid' | 'semantic' | 'meaning';

/**
 * 权重参数配置
 */
export interface WeightingParams {
  semanticRelevance: number;    // 语义相关性权重 (0-1)
  emotionalMatch: number;       // 情感匹配权重 (0-1)
  themeRelevance: number;       // 主题相关性权重 (0-1)
  importanceScore: number;      // 重要性分数权重 (0-1)
  temporalDecay: number;        // 时间衰减权重 (0-1)
  profileMatch: number;         // 用户画像匹配权重 (0-1)
  
  // 权重归一化检查
  _normalized?: boolean;
}

/**
 * 过滤规则
 */
export interface FilterRules {
  minWeightThreshold: number;   // 最低权重阈值
  maxResults: number;           // 最大结果数
  deduplication: {
    enabled: boolean;
    similarityThreshold: number; // 去重相似度阈值
  };
  temporalFilter: {
    enabled: boolean;
    maxAge: number;             // 最大年龄（天）
  };
  importanceFilter: {
    enabled: boolean;
    minImportance: number;      // 最低重要性分数
  };
}

/**
 * 增强检索配置
 */
export interface EnhancedRetrievalConfig {
  mode: RetrievalMode;
  vectorSearch: {
    topK: number;               // 向量搜索候选数
    similarityThreshold: number; // 相似度阈值
  };
  keywordSearch: {
    enabled: boolean;
    boost: number;              // 关键词匹配加权
  };
  weightingParams: WeightingParams;
  filterRules: FilterRules;
  finalCount: number;           // 最终返回数量
  
  // 调试选项
  debug: {
    enabled: boolean;
    includeScores: boolean;
    includeWeightBreakdown: boolean;
  };
}

/**
 * 检索候选项
 */
export interface RetrievalCandidate {
  chunk: ChunkMetadata;
  scores: {
    vectorSimilarity: number;
    keywordMatch: number;
    semanticRelevance: number;
    emotionalMatch: number;
    themeRelevance: number;
    importanceScore: number;
    temporalScore: number;
    profileMatch: number;
  };
  finalWeight: number;
  rank: number;
}

/**
 * 权重计算详情
 */
export interface WeightBreakdown {
  chunkId: string;
  rawScores: RetrievalCandidate['scores'];
  weightedScores: {
    [key in keyof WeightingParams]: number;
  };
  finalWeight: number;
  explanation: string[];        // 权重计算说明
}

/**
 * 详细检索结果
 */
export interface DetailedRetrievalResult {
  query: string;
  finalMemories: ChunkMetadata[];
  
  // 调试信息
  debugInfo: {
    totalCandidates: number;
    candidatesAfterFiltering: number;
    processingTime: number;
    
    // 详细分析
    candidateBreakdown: RetrievalCandidate[];
    weightBreakdown: WeightBreakdown[];
    filteringStats: {
      removedByThreshold: number;
      removedByDeduplication: number;
      removedByTemporal: number;
      removedByImportance: number;
    };
    
    // 配置快照
    configSnapshot: EnhancedRetrievalConfig;
  };
  
  // 性能指标
  performance: {
    vectorSearchTime: number;
    weightingTime: number;
    filteringTime: number;
    totalTime: number;
  };
}

// ==================== UI相关类型 ====================

/**
 * 索引进度状态
 */
export interface IndexingProgress {
  status: 'idle' | 'processing' | 'completed' | 'error';
  currentStep: string;
  progress: number;             // 0-100
  processedChunks: number;
  totalChunks: number;
  estimatedTimeRemaining?: number; // 秒
  logs: Array<{
    timestamp: Date;
    level: 'info' | 'warn' | 'error';
    message: string;
  }>;
}

/**
 * 检索测试状态
 */
export interface RetrievalTestState {
  isRunning: boolean;
  lastQuery?: string;
  lastResult?: DetailedRetrievalResult;
  lastError?: string;
  testHistory: Array<{
    query: string;
    timestamp: Date;
    resultCount: number;
    processingTime: number;
  }>;
}

/**
 * 配置预设
 */
export interface ConfigPreset {
  id: string;
  name: string;
  description: string;
  type: 'indexing' | 'retrieval';
  config: IndexingConfig | EnhancedRetrievalConfig;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ==================== API相关类型 ====================

/**
 * API响应基础类型
 */
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
  processingTime: number;
}

/**
 * 索引API请求
 */
export interface IndexRequest {
  documentText: string;
  documentId?: string;
  config: IndexingConfig;
  options?: {
    overwrite: boolean;
    validateOnly: boolean;
  };
}

/**
 * 检索API请求
 */
export interface SearchRequest {
  query: string;
  config: EnhancedRetrievalConfig;
  context: {
    userProfile: string;
    dailyInsight: string;
    conversationHistory?: string[];
  };
  options?: {
    includeDebugInfo: boolean;
    cacheResults: boolean;
  };
}

/**
 * 配置API请求
 */
export interface ConfigRequest {
  action: 'get' | 'save' | 'delete' | 'list';
  configType: 'indexing' | 'retrieval';
  presetId?: string;
  config?: IndexingConfig | EnhancedRetrievalConfig;
  preset?: Omit<ConfigPreset, 'id' | 'createdAt' | 'updatedAt'>;
}

// ==================== 错误类型 ====================

/**
 * 意义RAG系统错误
 */
export class MeaningRAGError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'MeaningRAGError';
  }
}

/**
 * 索引错误
 */
export class IndexingError extends MeaningRAGError {
  constructor(message: string, details?: any) {
    super(message, 'INDEXING_ERROR', details);
    this.name = 'IndexingError';
  }
}

/**
 * 检索错误
 */
export class RetrievalError extends MeaningRAGError {
  constructor(message: string, details?: any) {
    super(message, 'RETRIEVAL_ERROR', details);
    this.name = 'RetrievalError';
  }
}

// ==================== 工具类型 ====================

/**
 * 深度部分类型（用于配置更新）
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
