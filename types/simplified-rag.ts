/**
 * 简化的RAG类型定义
 * 替代复杂的meaning-rag类型，保持核心功能
 */

// 基础请求和响应类型
export interface IndexRequest {
  documentText: string;
  documentId?: string;
  metadata?: Record<string, any>;
  config?: any;
  options?: any;
}

export interface IndexingResult {
  success: boolean;
  documentId: string;
  chunksCreated: number;
  processingTime: number;
  metadata?: Record<string, any>;
  totalChunks?: number;
  processedChunks?: number;
  failedChunks?: number;
  indexingTime?: number;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

export class IndexingError extends Error {
  public code: string;
  public details?: any;

  constructor(message: string, details?: any) {
    super(message);
    this.name = 'IndexingError';
    this.code = 'INDEXING_ERROR';
    this.details = details;
  }
}

// 增强检索配置（简化版）
export interface EnhancedRetrievalConfig {
  topK: number;
  threshold: number;
  enableReranking: boolean;
  filters?: Record<string, any>;
}

// 搜索相关类型
export interface SearchRequest {
  query: string;
  topK?: number;
  threshold?: number;
  filters?: Record<string, any>;
}

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata?: Record<string, any>;
}

export interface SearchResponse {
  results: SearchResult[];
  totalFound: number;
  processingTime: number;
  query: string;
}

// 详细检索结果
export interface DetailedRetrievalResult {
  results: SearchResult[];
  totalFound: number;
  processingTime: number;
  query: string;
  metadata?: Record<string, any>;
}

// 检索错误
export class RetrievalError extends Error {
  public code: string;
  public details?: any;

  constructor(message: string, details?: any) {
    super(message);
    this.name = 'RetrievalError';
    this.code = 'RETRIEVAL_ERROR';
    this.details = details;
  }
}

// 配置相关类型
export interface RAGConfig {
  chunkSize: number;
  chunkOverlap: number;
  topK: number;
  similarityThreshold: number;
  enableMetadata: boolean;
}

export interface ConfigUpdateRequest {
  config: Partial<RAGConfig>;
}

export interface ConfigResponse {
  config: RAGConfig;
  lastUpdated: string;
}

// 状态和统计类型
export interface RAGStatus {
  isInitialized: boolean;
  documentsIndexed: number;
  totalChunks: number;
  lastIndexed?: string;
}

export interface RAGStats {
  totalDocuments: number;
  totalChunks: number;
  averageChunkSize: number;
  indexSize: string;
  lastActivity: string;
}
