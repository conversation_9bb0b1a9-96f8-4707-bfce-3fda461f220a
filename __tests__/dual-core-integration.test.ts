/**
 * 双核抽象层端到端集成测试
 * 测试完整工作流：用户输入 → Navigator → 智能缓存层 → 上下文打包工厂 → Integration Generator
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { IntelligentCacheLayer } from '@/lib/services/intelligent-cache-layer/intelligent-cache-layer';
import { ContextPackagingFactory } from '@/lib/services/context-packaging-factory/context-packaging-factory';
import { getIntelligentCacheAdapter } from '@/lib/services/three-engine/intelligent-cache-adapter';
import { dualCoreDebugController } from '@/lib/services/dual-core-debug-controller';
import type { 
  RetrievalResult, 
  ContextPackagingConfig,
  IntelligentCacheConfig 
} from '@/lib/services/dual-core-abstraction';

describe('双核抽象层集成测试', () => {
  let cacheLayer: IntelligentCacheLayer;
  let packagingFactory: ContextPackagingFactory;
  let cacheAdapter: any;

  beforeEach(() => {
    // 初始化测试实例
    cacheLayer = new IntelligentCacheLayer({
      debugMode: true,
      weighting: {
        weights: { current: 1.0, 'T-1': 0.8, 'T-2': 0.6, 'T-3': 0.4 },
        enableTimeDecay: true,
        timeDecayFactor: 0.95
      },
      elimination: {
        enabled: true,
        maxRetainedIds: 10,
        eliminationThreshold: 0.8,
        minRetainedIds: 3
      },
      reset: {
        enabled: true,
        noveltyThreshold: 0.5,
        topicChangeThreshold: 0.7,
        forceResetInterval: 24
      }
    });

    packagingFactory = new ContextPackagingFactory({
      defaultStrategy: 'balanced',
      globalSettings: {
        maxContextLength: 2000,
        debugMode: true,
        enableQualityAssessment: true,
        defaultFormattingStyle: 'conversational'
      }
    });

    cacheAdapter = getIntelligentCacheAdapter();
  });

  afterEach(() => {
    // 清理测试数据
    jest.clearAllMocks();
  });

  describe('完整工作流测试', () => {
    test('应该成功执行完整的检索到打包工作流', async () => {
      // 1. 模拟检索结果
      const mockRetrievalResult: RetrievalResult = {
        timestamp: new Date().toISOString(),
        parentChunkIds: [
          '20250101-T001_conv',
          '20250101-T002_conv',
          '20250102-T001_conv',
          'chunk_001_memory',
          'chunk_002_context'
        ],
        queryContext: '用户询问关于AI技术的问题'
      };

      // 2. 智能缓存层处理
      const cacheResult = await cacheLayer.processNewRetrieval(mockRetrievalResult);
      
      expect(cacheResult.success).toBe(true);
      expect(cacheResult.operation).toBe('weighted_ranking');
      
      const rankedIds = cacheLayer.getCurrentRanking();
      expect(rankedIds.length).toBeGreaterThan(0);
      expect(rankedIds.length).toBeLessThanOrEqual(mockRetrievalResult.parentChunkIds.length);

      // 3. 上下文打包工厂处理
      const packagingInputs = {
        userProfile: {
          id: 'test_user',
          content: '测试用户：AI技术爱好者',
          preferences: { responseStyle: 'detailed' },
          timestamp: new Date().toISOString()
        },
        deepHistoryMemory: {
          source: 'intelligent_cache_layer',
          ids: rankedIds,
          relevanceScores: rankedIds.reduce((acc, id, index) => {
            acc[id] = 1.0 - (index * 0.1);
            return acc;
          }, {} as Record<string, number>),
          retrievalMethod: 'hybrid',
          processingTime: cacheResult.result?.statistics.processingTimeMs || 0,
          metadata: {
            queryIntent: '测试查询',
            searchStrategy: 'weighted_historical_ranking',
            cacheHitRate: 0.8,
            contentMap: rankedIds.reduce((acc, id) => {
              acc[id] = `模拟内容 for ${id}`;
              return acc;
            }, {} as Record<string, string>)
          }
        },
        recentDialogue: {
          contexts: [
            { id: 'recent_1', content: '最近的对话1', timestamp: new Date().toISOString(), relevanceScore: 0.9 },
            { id: 'recent_2', content: '最近的对话2', timestamp: new Date().toISOString(), relevanceScore: 0.7 }
          ],
          summary: { emotionalState: 'positive', mainTopics: ['AI', '技术'] },
          emotionalState: 'positive'
        },
        completeHistory: {
          fullConversation: ['用户: 你好', 'AI: 你好！', '用户: 请介绍AI技术'],
          sessionInfo: { sessionId: 'test_session', startTime: new Date().toISOString() },
          totalTurns: 3,
          sessionDuration: 300000
        }
      };

      const contextPackage = await packagingFactory.packageContext(packagingInputs);
      
      expect(contextPackage).toBeDefined();
      expect(contextPackage.segments.length).toBeGreaterThan(0);
      expect(contextPackage.finalContext.length).toBeGreaterThan(0);
      expect(contextPackage.statistics.totalTokens).toBeGreaterThan(0);
      expect(contextPackage.quality.relevanceScore).toBeGreaterThan(0);

      // 4. 验证数据流完整性
      expect(contextPackage.segments.some(seg => seg.type === 'user_profile')).toBe(true);
      expect(contextPackage.segments.some(seg => seg.type === 'deep_history')).toBe(true);
      expect(contextPackage.finalContext).toContain('SelfMirror 上下文包');
    });

    test('应该正确处理空检索结果', async () => {
      const emptyRetrievalResult: RetrievalResult = {
        timestamp: new Date().toISOString(),
        parentChunkIds: [],
        queryContext: '空查询测试'
      };

      const cacheResult = await cacheLayer.processNewRetrieval(emptyRetrievalResult);
      expect(cacheResult.success).toBe(true);
      
      const rankedIds = cacheLayer.getCurrentRanking();
      expect(rankedIds.length).toBe(0);
    });
  });

  describe('边界情况测试', () => {
    test('应该正确处理缓存层重置', async () => {
      // 先添加一些数据
      const initialResult: RetrievalResult = {
        timestamp: new Date().toISOString(),
        parentChunkIds: ['id1', 'id2', 'id3'],
        queryContext: '初始数据'
      };
      
      await cacheLayer.processNewRetrieval(initialResult);
      expect(cacheLayer.getCurrentRanking().length).toBe(3);

      // 触发重置
      const resetResult = await cacheLayer.forceReset();
      expect(resetResult.success).toBe(true);
      expect(resetResult.operation).toBe('reset');
      expect(cacheLayer.getCurrentRanking().length).toBe(0);
    });

    test('应该正确处理末位淘汰', async () => {
      // 添加超过最大保留数的数据
      const largeResult: RetrievalResult = {
        timestamp: new Date().toISOString(),
        parentChunkIds: Array.from({ length: 15 }, (_, i) => `id_${i}`),
        queryContext: '大量数据测试'
      };

      const result = await cacheLayer.processNewRetrieval(largeResult);
      expect(result.success).toBe(true);
      
      const rankedIds = cacheLayer.getCurrentRanking();
      expect(rankedIds.length).toBeLessThanOrEqual(10); // 最大保留数
      expect(rankedIds.length).toBeGreaterThanOrEqual(3); // 最小保留数
    });

    test('应该正确处理打包工厂降级', async () => {
      const invalidInputs = {
        userProfile: null,
        deepHistoryMemory: null,
        recentDialogue: null,
        completeHistory: null
      };

      // 应该不抛出错误，而是返回降级结果
      await expect(packagingFactory.packageContext(invalidInputs)).resolves.toBeDefined();
    });
  });

  describe('性能基准测试', () => {
    test('缓存层处理性能应该在可接受范围内', async () => {
      const testData: RetrievalResult = {
        timestamp: new Date().toISOString(),
        parentChunkIds: Array.from({ length: 50 }, (_, i) => `perf_test_${i}`),
        queryContext: '性能测试'
      };

      const startTime = Date.now();
      const result = await cacheLayer.processNewRetrieval(testData);
      const processingTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(processingTime).toBeLessThan(100); // 应该在100ms内完成
      expect(result.result?.statistics.processingTimeMs).toBeLessThan(50);
    });

    test('上下文打包性能应该在可接受范围内', async () => {
      const testInputs = {
        userProfile: {
          id: 'perf_user',
          content: '性能测试用户画像'.repeat(10),
          preferences: {},
          timestamp: new Date().toISOString()
        },
        deepHistoryMemory: {
          source: 'performance_test',
          ids: Array.from({ length: 20 }, (_, i) => `perf_id_${i}`),
          relevanceScores: {},
          retrievalMethod: 'hybrid' as const,
          processingTime: 10,
          metadata: {
            contentMap: Array.from({ length: 20 }, (_, i) => [`perf_id_${i}`, `性能测试内容 ${i}`.repeat(20)]).reduce((acc, [id, content]) => {
              acc[id] = content;
              return acc;
            }, {} as Record<string, string>)
          }
        },
        recentDialogue: {
          contexts: Array.from({ length: 5 }, (_, i) => ({
            id: `recent_perf_${i}`,
            content: `近期对话内容 ${i}`.repeat(10),
            timestamp: new Date().toISOString(),
            relevanceScore: 0.8
          })),
          summary: { emotionalState: 'neutral' },
          emotionalState: 'neutral'
        },
        completeHistory: {
          fullConversation: Array.from({ length: 10 }, (_, i) => `历史消息 ${i}`),
          sessionInfo: { sessionId: 'perf_session' },
          totalTurns: 10,
          sessionDuration: 600000
        }
      };

      const startTime = Date.now();
      const result = await packagingFactory.packageContext(testInputs);
      const processingTime = Date.now() - startTime;

      expect(result).toBeDefined();
      expect(processingTime).toBeLessThan(200); // 应该在200ms内完成
      expect(result.statistics.totalTokens).toBeGreaterThan(0);
    });
  });

  describe('配置变更测试', () => {
    test('应该正确响应缓存层配置变更', async () => {
      const newConfig: Partial<IntelligentCacheConfig> = {
        weighting: {
          weights: { current: 1.2, 'T-1': 0.9, 'T-2': 0.7, 'T-3': 0.5 },
          enableTimeDecay: false,
          timeDecayFactor: 0.9
        }
      };

      await dualCoreDebugController.updateCacheConfig(newConfig);
      
      // 验证配置已更新
      const updatedConfig = cacheLayer.getConfig();
      expect(updatedConfig.weighting.weights.current).toBe(1.2);
      expect(updatedConfig.weighting.enableTimeDecay).toBe(false);
    });

    test('应该正确响应打包工厂配置变更', async () => {
      const newConfig: Partial<ContextPackagingConfig> = {
        globalSettings: {
          maxContextLength: 3000,
          debugMode: false,
          enableQualityAssessment: false,
          defaultFormattingStyle: 'technical'
        }
      };

      // 这个测试验证配置更新不会抛出错误
      await expect(dualCoreDebugController.updatePackagingConfig(newConfig)).resolves.not.toThrow();
    });
  });

  describe('调试台功能测试', () => {
    test('应该正确执行模拟检索测试', async () => {
      const result = await dualCoreDebugController.handleTestAction('simulate_retrieval', {
        mockIds: ['test1', 'test2', 'test3']
      });

      expect(result.success).toBe(true);
      expect(result.result?.finalRanking).toContain('test1');
    });

    test('应该正确导出调试数据', () => {
      const debugData = dualCoreDebugController.exportDebugData();
      
      expect(debugData).toBeDefined();
      expect(debugData.controller).toBeDefined();
      expect(debugData.cacheAdapter).toBeDefined();
      expect(debugData.controller.timestamp).toBeDefined();
    });
  });
});
