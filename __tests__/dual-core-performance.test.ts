/**
 * 双核抽象层性能基准测试
 * 验证双核系统相比原始系统的性能改进
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { IntelligentCacheLayer } from '@/lib/services/intelligent-cache-layer/intelligent-cache-layer';
import { ContextPackagingFactory } from '@/lib/services/context-packaging-factory/context-packaging-factory';
import type { RetrievalResult } from '@/lib/services/dual-core-abstraction';

interface PerformanceMetrics {
  averageTime: number;
  minTime: number;
  maxTime: number;
  throughput: number; // operations per second
  memoryUsage: number;
  successRate: number;
}

describe('双核抽象层性能基准测试', () => {
  let cacheLayer: IntelligentCacheLayer;
  let packagingFactory: ContextPackagingFactory;

  beforeAll(() => {
    cacheLayer = new IntelligentCacheLayer({
      debugMode: false, // 关闭调试模式以获得更准确的性能数据
      weighting: {
        weights: { current: 1.0, 'T-1': 0.8, 'T-2': 0.6, 'T-3': 0.4 },
        enableTimeDecay: true,
        timeDecayFactor: 0.95
      },
      elimination: {
        enabled: true,
        maxRetainedIds: 50,
        eliminationThreshold: 0.8,
        minRetainedIds: 10
      },
      reset: {
        enabled: true,
        noveltyThreshold: 0.5,
        topicChangeThreshold: 0.7,
        forceResetInterval: 24
      }
    });

    packagingFactory = new ContextPackagingFactory({
      defaultStrategy: 'balanced',
      globalSettings: {
        maxContextLength: 4000,
        debugMode: false,
        enableQualityAssessment: true,
        defaultFormattingStyle: 'conversational'
      }
    });
  });

  afterAll(() => {
    // 清理资源
  });

  /**
   * 运行性能测试并收集指标
   */
  async function runPerformanceTest<T>(
    testName: string,
    operation: () => Promise<T>,
    iterations: number = 100
  ): Promise<PerformanceMetrics> {
    console.log(`开始性能测试: ${testName} (${iterations} 次迭代)`);
    
    const times: number[] = [];
    let successCount = 0;
    const startMemory = process.memoryUsage().heapUsed;

    const overallStart = Date.now();

    for (let i = 0; i < iterations; i++) {
      const start = Date.now();
      
      try {
        await operation();
        const end = Date.now();
        times.push(end - start);
        successCount++;
      } catch (error) {
        console.error(`迭代 ${i} 失败:`, error);
        times.push(0); // 失败的操作记录为0时间
      }
    }

    const overallEnd = Date.now();
    const endMemory = process.memoryUsage().heapUsed;

    const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times.filter(t => t > 0));
    const maxTime = Math.max(...times);
    const throughput = (successCount / (overallEnd - overallStart)) * 1000; // ops/sec
    const memoryUsage = endMemory - startMemory;
    const successRate = successCount / iterations;

    const metrics: PerformanceMetrics = {
      averageTime,
      minTime,
      maxTime,
      throughput,
      memoryUsage,
      successRate
    };

    console.log(`${testName} 性能指标:`, {
      平均时间: `${averageTime.toFixed(2)}ms`,
      最小时间: `${minTime.toFixed(2)}ms`,
      最大时间: `${maxTime.toFixed(2)}ms`,
      吞吐量: `${throughput.toFixed(2)} ops/sec`,
      内存使用: `${(memoryUsage / 1024 / 1024).toFixed(2)}MB`,
      成功率: `${(successRate * 100).toFixed(1)}%`
    });

    return metrics;
  }

  /**
   * 生成测试数据
   */
  function generateTestData(size: 'small' | 'medium' | 'large'): {
    retrievalResult: RetrievalResult;
    packagingInputs: any;
  } {
    const sizes = {
      small: { ids: 5, content: 50 },
      medium: { ids: 20, content: 200 },
      large: { ids: 50, content: 500 }
    };

    const config = sizes[size];
    const parentChunkIds = Array.from({ length: config.ids }, (_, i) => `${size}_test_${i}`);

    const retrievalResult: RetrievalResult = {
      timestamp: new Date().toISOString(),
      parentChunkIds,
      queryContext: `${size} 规模性能测试查询`
    };

    const packagingInputs = {
      userProfile: {
        id: `${size}_user`,
        content: '性能测试用户画像 '.repeat(config.content / 20),
        preferences: { responseStyle: 'detailed' },
        timestamp: new Date().toISOString()
      },
      deepHistoryMemory: {
        source: 'performance_test',
        ids: parentChunkIds,
        relevanceScores: parentChunkIds.reduce((acc, id, index) => {
          acc[id] = 1.0 - (index * 0.02);
          return acc;
        }, {} as Record<string, number>),
        retrievalMethod: 'hybrid' as const,
        processingTime: 10,
        metadata: {
          contentMap: parentChunkIds.reduce((acc, id) => {
            acc[id] = `性能测试内容 for ${id} `.repeat(config.content / parentChunkIds.length);
            return acc;
          }, {} as Record<string, string>)
        }
      },
      recentDialogue: {
        contexts: Array.from({ length: Math.min(5, config.ids) }, (_, i) => ({
          id: `recent_${size}_${i}`,
          content: `近期对话内容 ${i} `.repeat(config.content / 50),
          timestamp: new Date().toISOString(),
          relevanceScore: 0.8 - (i * 0.1)
        })),
        summary: { emotionalState: 'neutral', mainTopics: ['performance', 'test'] },
        emotionalState: 'neutral'
      },
      completeHistory: {
        fullConversation: Array.from({ length: Math.min(10, config.ids) }, (_, i) => 
          `历史消息 ${i} `.repeat(config.content / 100)
        ),
        sessionInfo: { sessionId: `${size}_session`, startTime: new Date().toISOString() },
        totalTurns: Math.min(10, config.ids),
        sessionDuration: 300000
      }
    };

    return { retrievalResult, packagingInputs };
  }

  describe('智能缓存层性能测试', () => {
    test('小规模数据处理性能', async () => {
      const { retrievalResult } = generateTestData('small');
      
      const metrics = await runPerformanceTest(
        '智能缓存层-小规模',
        () => cacheLayer.processNewRetrieval(retrievalResult),
        50
      );

      expect(metrics.averageTime).toBeLessThan(10); // 平均处理时间应小于10ms
      expect(metrics.successRate).toBeGreaterThan(0.95); // 成功率应大于95%
      expect(metrics.throughput).toBeGreaterThan(50); // 吞吐量应大于50 ops/sec
    });

    test('中等规模数据处理性能', async () => {
      const { retrievalResult } = generateTestData('medium');
      
      const metrics = await runPerformanceTest(
        '智能缓存层-中等规模',
        () => cacheLayer.processNewRetrieval(retrievalResult),
        30
      );

      expect(metrics.averageTime).toBeLessThan(25); // 平均处理时间应小于25ms
      expect(metrics.successRate).toBeGreaterThan(0.9); // 成功率应大于90%
      expect(metrics.throughput).toBeGreaterThan(20); // 吞吐量应大于20 ops/sec
    });

    test('大规模数据处理性能', async () => {
      const { retrievalResult } = generateTestData('large');
      
      const metrics = await runPerformanceTest(
        '智能缓存层-大规模',
        () => cacheLayer.processNewRetrieval(retrievalResult),
        20
      );

      expect(metrics.averageTime).toBeLessThan(50); // 平均处理时间应小于50ms
      expect(metrics.successRate).toBeGreaterThan(0.85); // 成功率应大于85%
      expect(metrics.throughput).toBeGreaterThan(10); // 吞吐量应大于10 ops/sec
    });
  });

  describe('上下文打包工厂性能测试', () => {
    test('小规模上下文打包性能', async () => {
      const { packagingInputs } = generateTestData('small');
      
      const metrics = await runPerformanceTest(
        '上下文打包-小规模',
        () => packagingFactory.packageContext(packagingInputs),
        30
      );

      expect(metrics.averageTime).toBeLessThan(50); // 平均处理时间应小于50ms
      expect(metrics.successRate).toBeGreaterThan(0.95); // 成功率应大于95%
    });

    test('中等规模上下文打包性能', async () => {
      const { packagingInputs } = generateTestData('medium');
      
      const metrics = await runPerformanceTest(
        '上下文打包-中等规模',
        () => packagingFactory.packageContext(packagingInputs),
        20
      );

      expect(metrics.averageTime).toBeLessThan(100); // 平均处理时间应小于100ms
      expect(metrics.successRate).toBeGreaterThan(0.9); // 成功率应大于90%
    });

    test('大规模上下文打包性能', async () => {
      const { packagingInputs } = generateTestData('large');
      
      const metrics = await runPerformanceTest(
        '上下文打包-大规模',
        () => packagingFactory.packageContext(packagingInputs),
        10
      );

      expect(metrics.averageTime).toBeLessThan(200); // 平均处理时间应小于200ms
      expect(metrics.successRate).toBeGreaterThan(0.8); // 成功率应大于80%
    });
  });

  describe('端到端工作流性能测试', () => {
    test('完整工作流性能基准', async () => {
      const { retrievalResult, packagingInputs } = generateTestData('medium');
      
      const metrics = await runPerformanceTest(
        '端到端工作流',
        async () => {
          // 1. 缓存层处理
          const cacheResult = await cacheLayer.processNewRetrieval(retrievalResult);
          
          // 2. 更新打包输入
          const rankedIds = cacheLayer.getCurrentRanking();
          const updatedInputs = {
            ...packagingInputs,
            deepHistoryMemory: {
              ...packagingInputs.deepHistoryMemory,
              ids: rankedIds
            }
          };
          
          // 3. 上下文打包
          const contextPackage = await packagingFactory.packageContext(updatedInputs);
          
          return { cacheResult, contextPackage };
        },
        15
      );

      expect(metrics.averageTime).toBeLessThan(150); // 端到端处理时间应小于150ms
      expect(metrics.successRate).toBeGreaterThan(0.9); // 成功率应大于90%
      expect(metrics.throughput).toBeGreaterThan(5); // 吞吐量应大于5 ops/sec
    });
  });

  describe('内存使用和资源效率测试', () => {
    test('长时间运行内存稳定性', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // 运行大量操作
      for (let i = 0; i < 200; i++) {
        const { retrievalResult } = generateTestData('small');
        await cacheLayer.processNewRetrieval(retrievalResult);
        
        // 每50次操作检查一次内存
        if (i % 50 === 0) {
          const currentMemory = process.memoryUsage().heapUsed;
          const memoryGrowth = currentMemory - initialMemory;
          
          // 内存增长应该在合理范围内（小于50MB）
          expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024);
        }
      }
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const totalGrowth = finalMemory - initialMemory;
      
      // 最终内存增长应该在合理范围内
      expect(totalGrowth).toBeLessThan(100 * 1024 * 1024); // 小于100MB
    });

    test('并发处理性能', async () => {
      const concurrentOperations = 10;
      const { retrievalResult } = generateTestData('medium');
      
      const startTime = Date.now();
      
      // 并发执行多个操作
      const promises = Array.from({ length: concurrentOperations }, () =>
        cacheLayer.processNewRetrieval({
          ...retrievalResult,
          timestamp: new Date().toISOString(),
          parentChunkIds: retrievalResult.parentChunkIds.map(id => `${id}_${Math.random()}`)
        })
      );
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      const totalTime = endTime - startTime;
      const averageTimePerOperation = totalTime / concurrentOperations;
      
      // 验证所有操作都成功
      expect(results.every(r => r.success)).toBe(true);
      
      // 并发处理应该比串行处理更高效
      expect(averageTimePerOperation).toBeLessThan(50); // 平均每个操作小于50ms
      
      console.log(`并发性能测试: ${concurrentOperations}个操作总计${totalTime}ms, 平均${averageTimePerOperation.toFixed(2)}ms/操作`);
    });
  });
});
