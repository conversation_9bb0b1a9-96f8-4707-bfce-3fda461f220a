/**
 * Integration Generator双核抽象层集成测试
 * 验证完整数据流：Navigator → 智能缓存层 → 上下文打包工厂 → Integration Generator
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import { IntegrationGenerator, IntegrationInput } from '@/lib/services/three-engine/integration-generator';
import { ContextPackagingFactory } from '@/lib/services/context-packaging-factory/context-packaging-factory';
import { IntelligentCacheLayer } from '@/lib/services/intelligent-cache-layer/intelligent-cache-layer';

// 模拟AI工厂
jest.mock('@/lib/ai/factory/three-engine-ai-factory', () => ({
  threeEngineAIFactory: {
    initialize: jest.fn().mockResolvedValue(undefined),
    executeEngineRequest: jest.fn().mockResolvedValue({
      text: '这是一个基于双核抽象层优化上下文生成的个性化响应。我理解您的需求，并基于您的历史记忆和当前情感状态提供了深度洞察。',
      modelUsed: 'gemini-pro',
      usage: { inputTokens: 1000, outputTokens: 200 }
    })
  }
}));

// 模拟配置管理器
jest.mock('@/lib/config/three-engine-config', () => ({
  threeEngineConfigManager: {
    getEngineConfig: jest.fn().mockReturnValue({
      modelId: 'gemini-pro',
      provider: 'google',
      modelName: 'gemini-pro',
      temperature: 0.7
    })
  }
}));

describe('Integration Generator双核抽象层集成测试', () => {
  let integrationGenerator: IntegrationGenerator;
  let packagingFactory: ContextPackagingFactory;
  let cacheLayer: IntelligentCacheLayer;

  beforeEach(async () => {
    integrationGenerator = IntegrationGenerator.getInstance();
    await integrationGenerator.initialize();

    packagingFactory = new ContextPackagingFactory();
    cacheLayer = new IntelligentCacheLayer();
  });

  describe('优化上下文包处理', () => {
    test('应该正确处理双核抽象层的优化上下文包', async () => {
      // 1. 模拟智能缓存层处理
      const mockRetrievalResult = {
        timestamp: new Date().toISOString(),
        parentChunkIds: ['20250101-T001_conv', '20250101-T002_conv', 'chunk_001'],
        queryContext: '用户询问关于AI技术的问题'
      };

      const cacheResult = await cacheLayer.processNewRetrieval(mockRetrievalResult);
      const rankedIds = cacheLayer.getCurrentRanking();

      // 2. 模拟上下文打包工厂处理
      const packagingInputs = {
        userProfile: {
          id: 'test_user',
          content: '技术爱好者，关注AI发展',
          preferences: { responseStyle: 'detailed' },
          timestamp: new Date().toISOString()
        },
        deepHistoryMemory: {
          source: 'intelligent_cache_layer',
          ids: rankedIds,
          relevanceScores: rankedIds.reduce((acc, id, index) => {
            acc[id] = 1.0 - (index * 0.1);
            return acc;
          }, {} as Record<string, number>),
          retrievalMethod: 'hybrid' as const,
          processingTime: 25,
          metadata: {
            contentMap: rankedIds.reduce((acc, id) => {
              acc[id] = `模拟内容 for ${id}: 这是关于AI技术的深度讨论内容`;
              return acc;
            }, {} as Record<string, string>)
          }
        },
        recentDialogue: {
          contexts: [
            { id: 'recent_1', content: '最近讨论了机器学习的发展', timestamp: new Date().toISOString(), relevanceScore: 0.9 }
          ],
          summary: { emotionalState: 'curious', mainTopics: ['AI', '技术'] },
          emotionalState: 'curious'
        },
        completeHistory: {
          fullConversation: ['用户: 请介绍AI技术', 'AI: AI技术包括...'],
          sessionInfo: { sessionId: 'test_session' },
          totalTurns: 2,
          sessionDuration: 300000
        }
      };

      const contextPackage = await packagingFactory.packageContext(packagingInputs);

      // 3. 构建Integration Generator输入，包含优化上下文包
      const integrationInput: IntegrationInput = {
        userInput: '请详细介绍AI技术的最新发展',
        userInputId: 'test_input_001',
        userProfile: '技术爱好者，关注AI发展',
        conversationHistory: ['用户: 请介绍AI技术', 'AI: AI技术包括...'],
        currentSession: {
          sessionId: 'test_session',
          messageCount: 2,
          topics: ['AI', '技术'],
          emotionalState: 'curious'
        },
        retrievalInstructions: {
          strategy: { searchType: 'semantic', priority: 'high' },
          keywords: { primary: ['AI', '技术'], secondary: ['发展'], emotional: ['好奇'] }
        } as any,
        lightweightContext: {
          contexts: [
            { id: 'ctx_1', content: '轻量级上下文内容', type: 'recent', relevanceScore: 0.8 }
          ],
          summary: { emotionalState: 'curious' }
        } as any,
        deepMemories: [
          { chunkId: 'mem_1', content: '深度记忆内容', similarity: 0.85, metadata: { sourceDocumentType: 'conversation' } }
        ] as any,
        rankedParentChunkIds: rankedIds,
        optimizedContextPackage: contextPackage, // 🔥 关键：包含优化上下文包
        preferences: {
          responseStyle: 'detailed',
          emotionalTone: 'thoughtful',
          includeReflections: true,
          includeQuestions: true,
          maxLength: 2000
        }
      };

      // 4. 执行Integration Generator
      const response = await integrationGenerator.generateIntegratedResponse(integrationInput);

      // 5. 验证结果
      expect(response).toBeDefined();
      expect(response.response).toContain('双核抽象层');
      expect(response.quality.overall).toBeGreaterThan(0);
      expect(response.contextUsage.totalContexts).toBe(contextPackage.segments.length);
      expect(response.metadata.sourceAttribution).toBeDefined();

      // 验证日志输出包含双核抽象层信息
      expect(console.log).toHaveBeenCalledWith('🎯 使用双核抽象层优化上下文包');
    });

    test('应该正确降级到原始上下文构建逻辑', async () => {
      // 构建不包含优化上下文包的输入
      const integrationInput: IntegrationInput = {
        userInput: '请介绍AI技术',
        userInputId: 'test_input_002',
        userProfile: '技术爱好者',
        conversationHistory: ['用户: 你好'],
        currentSession: {
          sessionId: 'test_session',
          messageCount: 1,
          topics: ['AI'],
          emotionalState: 'neutral'
        },
        retrievalInstructions: {
          strategy: { searchType: 'semantic', priority: 'medium' },
          keywords: { primary: ['AI'], secondary: [], emotional: [] }
        } as any,
        lightweightContext: {
          contexts: [
            { id: 'ctx_1', content: '轻量级上下文', type: 'recent', relevanceScore: 0.7 }
          ],
          summary: { emotionalState: 'neutral' }
        } as any,
        deepMemories: [
          { chunkId: 'mem_1', content: '深度记忆', similarity: 0.8, metadata: { sourceDocumentType: 'note' } }
        ] as any,
        rankedParentChunkIds: ['chunk_001', 'chunk_002'],
        // 🔥 关键：不包含optimizedContextPackage
        preferences: {
          responseStyle: 'conversational',
          emotionalTone: 'neutral',
          includeReflections: false,
          includeQuestions: false,
          maxLength: 1000
        }
      };

      const response = await integrationGenerator.generateIntegratedResponse(integrationInput);

      // 验证降级逻辑正常工作
      expect(response).toBeDefined();
      expect(response.response).toBeDefined();
      expect(response.contextUsage.hotStoreUsed).toBe(1);
      expect(response.contextUsage.coldStoreUsed).toBe(1);

      // 验证日志输出包含降级信息
      expect(console.log).toHaveBeenCalledWith('⚠️ 双核抽象层不可用，降级到原始上下文构建');
    });
  });

  describe('上下文部分构建', () => {
    test('应该为优化上下文生成正确的格式', async () => {
      const mockContext = {
        isOptimized: true,
        segments: [
          { type: 'user_profile', content: '用户画像内容' },
          { type: 'deep_history', content: '深度历史内容' }
        ],
        statistics: {
          totalTokens: 500,
          sourceBreakdown: { 'intelligent_cache_layer': 2, 'user_profile': 1 }
        },
        quality: {
          relevanceScore: 0.85,
          coherenceScore: 0.90,
          completenessScore: 0.80,
          efficiencyScore: 0.88
        },
        strategy: {
          name: 'balanced',
          description: '平衡策略'
        },
        optimizedContext: '这是优化后的上下文内容...'
      };

      const mockInput = {
        userInput: '测试输入',
        preferences: { responseStyle: 'detailed' }
      } as IntegrationInput;

      // 使用反射访问私有方法进行测试
      const buildContextSection = (integrationGenerator as any).buildContextSection.bind(integrationGenerator);
      const contextSection = buildContextSection(mockContext, mockInput);

      expect(contextSection).toContain('🎯 双核抽象层优化上下文');
      expect(contextSection).toContain('相关性: 85.0%');
      expect(contextSection).toContain('连贯性: 90.0%');
      expect(contextSection).toContain('策略名称: balanced');
      expect(contextSection).toContain('这是优化后的上下文内容...');
    });

    test('应该为原始上下文生成正确的格式', async () => {
      const mockContext = {
        isOptimized: false,
        hotStoreContexts: [
          { type: 'recent', content: '最近内容', relevanceScore: 0.8 }
        ],
        coldStoreContexts: [
          { chunkId: 'chunk_1', metadata: { sourceDocumentType: 'note' }, similarity: 0.75 }
        ],
        cachedContexts: [
          { data: 'cached data' }
        ],
        rankedParentChunkIds: ['id_1', 'id_2', 'id_3']
      };

      const mockInput = {
        userInput: '测试输入'
      } as IntegrationInput;

      const buildContextSection = (integrationGenerator as any).buildContextSection.bind(integrationGenerator);
      const contextSection = buildContextSection(mockContext, mockInput);

      expect(contextSection).toContain('⚠️ 原始上下文信息 (双核抽象层不可用)');
      expect(contextSection).toContain('Hot Store快速上下文 (1个)');
      expect(contextSection).toContain('Cold Store深度记忆 (1个)');
      expect(contextSection).toContain('🧠 智能缓存层排序ID (3个)');
    });
  });

  describe('性能和质量验证', () => {
    test('使用优化上下文包应该提高响应质量', async () => {
      // 创建两个相似的输入，一个有优化上下文包，一个没有
      const baseInput: Partial<IntegrationInput> = {
        userInput: '请介绍机器学习的应用',
        userInputId: 'test_input',
        userProfile: '技术研究者',
        conversationHistory: ['用户: 你好'],
        currentSession: {
          sessionId: 'test_session',
          messageCount: 1,
          topics: ['机器学习'],
          emotionalState: 'interested'
        },
        retrievalInstructions: {
          strategy: { searchType: 'semantic', priority: 'high' },
          keywords: { primary: ['机器学习'], secondary: ['应用'], emotional: ['兴趣'] }
        } as any,
        lightweightContext: {
          contexts: [{ id: 'ctx_1', content: '机器学习基础', type: 'concept', relevanceScore: 0.9 }],
          summary: { emotionalState: 'interested' }
        } as any,
        deepMemories: [
          { chunkId: 'ml_1', content: '机器学习应用案例', similarity: 0.88, metadata: { sourceDocumentType: 'article' } }
        ] as any
      };

      // 有优化上下文包的输入
      const optimizedInput: IntegrationInput = {
        ...baseInput,
        optimizedContextPackage: {
          segments: [
            { type: 'user_profile', content: '技术研究者画像' },
            { type: 'deep_history', content: '机器学习相关历史' }
          ],
          finalContext: '优化后的机器学习上下文内容，包含用户画像和相关历史记忆...',
          statistics: { totalSegments: 2, totalTokens: 400, sourceBreakdown: {} },
          quality: { relevanceScore: 0.9, coherenceScore: 0.85, completenessScore: 0.88, efficiencyScore: 0.92 },
          strategy: { name: 'ml_focused', description: '机器学习专用策略' }
        }
      } as IntegrationInput;

      // 没有优化上下文包的输入
      const standardInput: IntegrationInput = {
        ...baseInput,
        rankedParentChunkIds: ['ml_chunk_1', 'ml_chunk_2']
      } as IntegrationInput;

      // 执行两次生成
      const optimizedResponse = await integrationGenerator.generateIntegratedResponse(optimizedInput);
      const standardResponse = await integrationGenerator.generateIntegratedResponse(standardInput);

      // 验证优化版本的质量指标
      expect(optimizedResponse.quality.overall).toBeGreaterThan(0);
      expect(standardResponse.quality.overall).toBeGreaterThan(0);

      // 验证上下文使用情况
      expect(optimizedResponse.contextUsage.totalContexts).toBe(2); // 优化上下文包的片段数
      expect(standardResponse.contextUsage.totalContexts).toBe(2); // hotStore + coldStore

      console.log('优化版本质量分数:', optimizedResponse.quality.overall);
      console.log('标准版本质量分数:', standardResponse.quality.overall);
    });
  });
});
