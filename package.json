{"name": "selfmirror2025", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env http_proxy=http://127.0.0.1:7897 https_proxy=http://127.0.0.1:7897 NODE_TLS_REJECT_UNAUTHORIZED=0 next dev", "dev:turbo": "cross-env http_proxy=http://127.0.0.1:7897 https_proxy=http://127.0.0.1:7897 NODE_TLS_REJECT_UNAUTHORIZED=0 next dev --turbopack", "dev:no-proxy": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@heroicons/react": "^2.2.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "undici": "^7.10.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}