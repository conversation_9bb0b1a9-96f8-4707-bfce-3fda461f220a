# 🚀 SelfMirror 双核抽象层生产环境部署检查清单

## 📋 部署前检查

### ✅ 环境配置验证

#### 1. 环境变量配置
- [ ] 复制 `.env.production` 到生产服务器
- [ ] 根据服务器资源调整内存限制配置
- [ ] 设置 `DUAL_CORE_DEBUG_MODE=false`
- [ ] 设置 `DUAL_CORE_TEST_MODE=false`
- [ ] 配置适当的日志级别 (`DUAL_CORE_LOG_LEVEL=info`)
- [ ] 设置性能监控参数

#### 2. 依赖包验证
```bash
# 检查必要的依赖包
npm list --depth=0 | grep -E "(next|react|typescript)"

# 如果需要高级图表功能，安装可选依赖
npm install recharts --save-optional

# 如果需要WebSocket实时功能，安装WebSocket依赖
npm install ws @types/ws --save-optional

# 安装测试报告生成器（可选）
npm install jest-html-reporters --save-dev
```

#### 3. TypeScript编译验证
```bash
# 检查TypeScript编译
npx tsc --noEmit

# 检查双核抽象层相关文件
npx tsc --noEmit lib/services/intelligent-cache-layer/*.ts
npx tsc --noEmit lib/services/context-packaging-factory/*.ts
npx tsc --noEmit lib/services/dual-core-debug-controller.ts
```

### ✅ 系统资源检查

#### 1. 内存要求
- [ ] 最小可用内存: 512MB
- [ ] 推荐内存: 1GB+
- [ ] 设置适当的 `DUAL_CORE_MAX_MEMORY_USAGE_MB` 值

#### 2. 磁盘空间要求
- [ ] 日志存储空间: 至少100MB
- [ ] 策略配置存储: 至少10MB
- [ ] 性能数据存储: 至少50MB

#### 3. 网络配置
- [ ] 确保调试控制台端口可访问（如果启用）
- [ ] 配置适当的防火墙规则
- [ ] 设置负载均衡器健康检查

### ✅ 安全配置

#### 1. 访问控制
- [ ] 启用调试控制台身份验证 (`DUAL_CORE_DEBUG_CONSOLE_AUTH_REQUIRED=true`)
- [ ] 配置API访问限制 (`DUAL_CORE_API_RATE_LIMIT`)
- [ ] 设置操作超时时间

#### 2. 数据保护
- [ ] 确保敏感配置信息加密存储
- [ ] 设置日志轮转和清理策略
- [ ] 配置数据备份策略

## 🔧 部署步骤

### 1. 代码部署
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装依赖
npm ci --production

# 3. 构建应用
npm run build

# 4. 复制环境配置
cp .env.production .env.local
```

### 2. 配置验证
```bash
# 验证环境变量
node -e "
const config = {
  debugMode: process.env.DUAL_CORE_DEBUG_MODE === 'true',
  cacheSize: parseInt(process.env.DUAL_CORE_CACHE_SIZE || '50'),
  maxMemory: parseInt(process.env.DUAL_CORE_MAX_MEMORY_USAGE_MB || '100')
};
console.log('双核配置:', config);
"
```

### 3. 数据库/存储初始化
```sql
-- 如果使用数据库存储策略配置
CREATE DATABASE IF NOT EXISTS selfmirror_dual_core;

USE selfmirror_dual_core;

-- 策略配置表
CREATE TABLE IF NOT EXISTS dual_core_strategies (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  config JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_created_at (created_at)
);

-- 性能日志表
CREATE TABLE IF NOT EXISTS dual_core_performance_logs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  operation_type VARCHAR(100) NOT NULL,
  processing_time_ms INT NOT NULL,
  memory_usage_mb FLOAT,
  success BOOLEAN NOT NULL,
  error_message TEXT,
  metadata JSON,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_operation_type (operation_type),
  INDEX idx_timestamp (timestamp),
  INDEX idx_success (success)
);

-- 系统健康检查表
CREATE TABLE IF NOT EXISTS dual_core_health_checks (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  component VARCHAR(100) NOT NULL,
  status ENUM('healthy', 'warning', 'error') NOT NULL,
  details JSON,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_component (component),
  INDEX idx_status (status),
  INDEX idx_timestamp (timestamp)
);
```

### 4. 文件系统准备
```bash
# 创建必要的目录
mkdir -p /data/dual-core/strategies
mkdir -p /data/dual-core/logs
mkdir -p /data/dual-core/performance

# 设置权限
chown -R app:app /data/dual-core
chmod -R 755 /data/dual-core
```

## 🧪 部署后验证

### 1. 功能验证
```bash
# 启动应用
npm start

# 等待应用启动
sleep 10

# 验证健康检查端点
curl -f http://localhost:3000/api/health || echo "健康检查失败"

# 验证双核API端点
curl -f http://localhost:3000/api/debug/dual-core?action=status || echo "双核API失败"
```

### 2. 性能验证
```bash
# 运行性能测试
PERFORMANCE_TEST=true npm test -- --config jest.config.dual-core.js --testNamePattern="性能基准"

# 检查内存使用
node -e "
setInterval(() => {
  const usage = process.memoryUsage();
  console.log('内存使用:', {
    rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB',
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + 'MB'
  });
}, 5000);
" &

# 运行5分钟后停止
sleep 300 && kill %1
```

### 3. 集成验证
- [ ] 访问调试控制台: `http://your-domain/debug-console`
- [ ] 测试双核抽象层标签页的所有功能
- [ ] 验证实时状态更新
- [ ] 测试参数调整功能
- [ ] 验证性能图表显示

## 📊 监控设置

### 1. 性能监控
```javascript
// 添加到监控系统
const performanceAlerts = {
  processingTime: {
    threshold: 500, // ms
    action: 'alert'
  },
  memoryUsage: {
    threshold: 80, // MB
    action: 'warning'
  },
  errorRate: {
    threshold: 0.1, // 10%
    action: 'critical'
  }
};
```

### 2. 日志监控
```bash
# 设置日志轮转
cat > /etc/logrotate.d/dual-core << EOF
/data/dual-core/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 app app
}
EOF
```

### 3. 健康检查
```bash
# 添加到系统监控
cat > /etc/systemd/system/dual-core-health.service << EOF
[Unit]
Description=Dual Core Health Check
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/curl -f http://localhost:3000/api/debug/dual-core?action=status
User=app

[Install]
WantedBy=multi-user.target
EOF

# 添加定时器
cat > /etc/systemd/system/dual-core-health.timer << EOF
[Unit]
Description=Run Dual Core Health Check every 5 minutes
Requires=dual-core-health.service

[Timer]
OnCalendar=*:0/5
Persistent=true

[Install]
WantedBy=timers.target
EOF

systemctl enable dual-core-health.timer
systemctl start dual-core-health.timer
```

## 🚨 故障排除

### 常见问题

#### 1. 内存使用过高
```bash
# 检查内存使用
ps aux | grep node
free -h

# 调整配置
export DUAL_CORE_MAX_MEMORY_USAGE_MB=50
export DUAL_CORE_CACHE_SIZE=30
```

#### 2. 处理时间过长
```bash
# 检查性能日志
tail -f /data/dual-core/logs/performance.log

# 调整配置
export DUAL_CORE_MAX_PROCESSING_TIME_MS=100
export DUAL_CORE_CACHE_ELIMINATION_THRESHOLD=0.9
```

#### 3. 调试控制台无法访问
```bash
# 检查端口
netstat -tlnp | grep :3000

# 检查防火墙
ufw status

# 检查配置
echo $DUAL_CORE_DEBUG_CONSOLE_ENABLED
```

## ✅ 部署完成确认

- [ ] 所有环境变量正确配置
- [ ] 应用成功启动并运行
- [ ] 健康检查通过
- [ ] 性能指标在正常范围内
- [ ] 调试控制台可正常访问
- [ ] 监控和告警系统已设置
- [ ] 备份和恢复策略已实施
- [ ] 文档已更新

## 📞 支持联系

如果在部署过程中遇到问题，请检查：
1. 应用日志: `/data/dual-core/logs/`
2. 系统日志: `journalctl -u your-app-service`
3. 性能指标: 调试控制台性能图表
4. 配置验证: `npm run config:validate`（如果实现）

---

**部署日期**: ___________
**部署人员**: ___________
**版本**: v2.1.0
**环境**: Production
