# SelfMirror MVP 系统架构图

## 📁 文件清单

### 前端组件文件
- `app/page.tsx` - 主页面入口
- `app/layout.tsx` - 应用布局
- `app/providers.tsx` - 全局Provider
- `components/chat/ChatInterface.tsx` - 聊天界面组件

### API处理文件
- `app/api/chat/route.ts` - 快速响应API路由
- `app/api/analyze/route.ts` - 深度分析API路由

### 数据管理文件
- `lib/storage/indexed-db.ts` - IndexedDB数据存储管理
- `lib/services/conversation.ts` - 对话管理服务
- `lib/services/emotion.ts` - 情绪检测服务

### 配置文件
- `lib/config.ts` - 应用配置
- `lib/prompts/system-prompts.ts` - 系统提示词配置
- `PROMPTS.md` - 提示词文档

### 类型定义文件
- `types/memory.ts` - 记忆系统类型
- `types/conversation.ts` - 对话系统类型
- `types/emotion.ts` - 情绪系统类型

## 🏗️ 系统架构图

> 提示：以下架构图展示了SelfMirror MVP的完整系统结构。图中使用中文标注说明每个组件的作用。

## 🔄 数据流向详解

### 1. 快速响应模式路径（热路径）
```
用户输入 → ChatInterface → chat/route.ts → 自定义Fetch(thinkingBudget=0) → Gemini API → 流式响应 → UI更新
```
- **响应时间**: < 2秒
- **特点**: 实时对话，无深度思考
- **数据处理**: 仅维护对话上下文窗口（8条消息）

### 2. 深度分析模式路径（冷路径）
```
对话累积(7-8轮) → ConversationManager触发 → analyze/route.ts → 自定义Fetch(thinkingBudget=8192) → Gemini API → 四文件更新
```
- **执行时机**: 后台异步处理
- **特点**: 深度分析用户特征，更新记忆系统
- **数据处理**: 更新全部四个JSON文件

## 🔧 关键交互机制

### 1. 双模式切换逻辑
- **判断条件**: 对话轮数达到7-8轮
- **切换方式**: ConversationManager自动触发
- **执行特点**: 后台静默执行，不影响前台对话

### 2. Thinking控制实现
```typescript
// 自定义fetch注入thinking参数
const customFetch = (url, options) => {
  const body = JSON.parse(options.body);
  body.generationConfig = {
    ...body.generationConfig,
    response_modalities: ["text"],
    thinking_config: {
      allowed: true,
      max_thinking_tokens: mode === 'quick' ? 0 : 8192
    }
  };
  // 使用undici ProxyAgent支持代理
};
```

### 3. 四文件系统更新机制
- **user_profile.json**: 累积更新用户认知特征
- **key_events.json**: 识别并记录重要事件
- **element_index.json**: 提取关键词和标签
- **conversation_archive.json**: 定期归档对话历史

### 4. 错误处理机制
- **网络错误**: 自动重试3次
- **API限流**: 指数退避策略
- **数据一致性**: IndexedDB事务保证

## 🎯 性能优化点

### 1. 热路径优化
- 流式响应减少首字延迟
- 对话上下文窗口限制（8条）
- 预加载UI组件

### 2. 冷路径优化
- 批量更新四文件
- 后台任务队列
- IndexedDB索引优化

### 3. 内存管理
- 对话历史定期归档
- 向量数据延迟加载
- 组件懒加载

## 🚀 技术栈依赖

### 核心框架
- **Next.js 15**: React服务端渲染框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 原子化CSS

### AI集成
- **Google Gemini 2.5 Flash**: AI模型
- **AI SDK**: Vercel AI SDK
- **自定义Fetch**: thinking控制

### 数据存储
- **IndexedDB**: 本地持久化存储
- **JSON**: 数据格式

### 网络
- **undici**: HTTP客户端（代理支持）
- **ProxyAgent**: 代理配置

## 📊 系统容量规划

### 存储容量
- 单个对话: ~2KB
- 用户画像: ~10KB
- 关键事件: ~5KB/事件
- IndexedDB限制: 通常>50MB

### 性能指标
- 快速响应: <2秒
- 深度分析: 10-30秒（后台）
- UI刷新率: 60fps

## 🔐 安全与隐私

### 数据安全
- 本地存储，不上传服务器
- IndexedDB加密（浏览器提供）
- 无第三方数据共享

### API安全
- API密钥本地管理
- HTTPS加密传输
- 代理配置保护

## 📌 架构总结

### 核心特性
1. **双模式架构**: 快速响应（<2秒）+ 深度分析（后台）
2. **四文件记忆系统**: 完整的用户认知和历史管理
3. **本地优先**: 所有数据存储在用户本地
4. **探针式共情**: 温柔引导而非直接回答

### 关键创新
1. **Thinking控制**: 通过自定义fetch实现双模式切换
2. **滑动窗口**: 8条消息的上下文管理
3. **异步分析**: 不影响用户体验的后台处理
4. **情绪可视化**: 动态UI效果（开发中）

### 快速导航
- 🚀 [快速响应API](app/api/chat/route.ts)
- 🧠 [深度分析API](app/api/analyze/route.ts)
- 💬 [聊天界面](components/chat/ChatInterface.tsx)
- 📝 [提示词配置](lib/prompts/system-prompts.ts)
- 💾 [数据存储](lib/storage/indexed-db.ts)
- 🎯 [对话管理](lib/services/conversation.ts)

### 开发建议
1. **性能优化**: 关注快速响应路径的延迟
2. **内存管理**: 定期清理过期对话数据
3. **用户体验**: 优先保证对话流畅性
4. **扩展性**: 预留向量数据库接口

## 🎯 下一步计划

### 即将实现
- [x] 快速响应模式包含三个画像文件
- [x] 深度分析输出记录功能
- [x] 提示词分板块管理
- [ ] 情绪可视化UI组件
- [ ] 向量数据库集成
- [ ] 多用户支持
- [ ] 数据导出功能

### 长期规划
- [ ] 语音输入支持
- [ ] 多模态理解
- [ ] 社区分享功能
- [ ] 插件系统

## 🆕 最新更新功能

### 1. 快速响应模式增强
- **功能**: 自动读取并打包三个本地画像文件（user_profile、key_events、element_index）
- **位置**: `app/api/chat/route.ts`
- **效果**: 每次对话都有完整的用户上下文，提升回应质量

### 2. 深度分析日志系统
- **功能**: 记录每次深度分析的完整输出和结果
- **存储**: IndexedDB中的`deepAnalysisLogs`表
- **调试页面**: `/debug` - 可查看所有分析日志
- **包含内容**:
  - 分析报告（摘要、洞察、发现、建议）
  - 记忆更新状态
  - 原始AI输出
  - 分析的对话内容

### 3. 提示词模块化管理
- **结构**:
  - `lib/prompts/quick-response.ts` - 快速响应提示词
  - `lib/prompts/deep-analysis.ts` - 深度分析提示词
  - `lib/prompts/memory-management.ts` - 记忆管理规则
  - `lib/prompts/empathy.ts` - 探针式共情技巧
- **优势**: 方便调试和独立优化各个模块

### 4. 深度分析API优化
- **实现方式**: AI SDK + 自定义Fetch
- **特性**:
  - 自动注入thinkingBudget=8192
  - 完整的错误处理和日志
  - 自动执行记忆系统更新
  - 支持代理配置

---

> 💡 **提示**: 本架构图反映了当前MVP版本的设计。随着产品迭代，架构可能会有所调整。请参考最新的代码实现。

> 📅 **更新时间**: 2024年1月 