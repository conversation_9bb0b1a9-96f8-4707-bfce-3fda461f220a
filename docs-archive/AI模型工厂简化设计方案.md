# SelfMirror AI 模型工厂简化设计方案

**设计时间**: 2025-06-28  
**版本**: v2.0 (简化版)  
**设计理念**: 简单 + 实用 + 专注核心需求

---

## 🎯 设计调整说明

### 移除的功能

1. ❌ **智能降级功能**: 不再自动切换备用模型
2. ❌ **本地 LLM 支持**: 避免占用用户端计算资源
3. ❌ **复杂的故障转移**: 简化错误处理逻辑

### 保留的核心功能

1. ✅ **统一接口设计**: 所有 AI 调用使用相同 API
2. ✅ **配置驱动切换**: 通过配置轻松切换模型
3. ✅ **向后兼容性**: 现有代码无需修改
4. ✅ **类型安全**: 完整的 TypeScript 支持

### 新增的专门功能

1. ✨ **向量模型接口**: 专门用于 RAG 系统的本地嵌入
2. ✨ **分离架构**: LLM 和向量模型完全分离管理

---

## 🏗️ 简化后的架构

### 整体架构图

```mermaid
graph TB
    A[业务层] --> B[AI模型工厂]
    A --> C[向量模型工厂]
    
    B --> D[Gemini模型]
    B --> E[豆包模型]
    
    C --> F[本地向量模型]
    C --> G[云端向量模型]
    
    H[配置管理] --> B
    H --> C
    
    subgraph "LLM 模型 (云端)"
        D --> D1[Vercel AI SDK]
        E --> E1[豆包 SDK]
    end
    
    subgraph "向量模型 (本地优先)"
        F --> F1[本地推理引擎]
        G --> G1[云端 API]
    end
```

### 核心组件

1. **IAIModel 接口**: 统一的 LLM 调用接口
2. **IEmbeddingModel 接口**: 统一的向量模型接口
3. **AIModelFactory**: LLM 模型工厂
4. **EmbeddingModelFactory**: 向量模型工厂
5. **配置管理**: 简化的配置系统

---

## 🎭 支持的模型类型

### LLM 模型 (云端)

| 模型 | 用途 | 特点 |
|------|------|------|
| **Gemini** | 主要 LLM 服务 | 高质量、功能全面 |
| **豆包** | 成本控制替代 | 性价比高、API 兼容 |

### 向量模型 (本地优先)

| 模型 | 用途 | 特点 |
|------|------|------|
| **本地向量模型** | RAG 文档嵌入 | 隐私保护、无网络依赖 |
| **云端向量模型** | 备用方案 | 高精度、无本地资源消耗 |

---

## 🔧 核心接口设计

### LLM 统一接口

```typescript
interface IAIModel {
  // 🎯 核心方法
  generateText(prompt: string, options?: GenerateOptions): Promise<GenerateResult>;
  generateTextStream(prompt: string, options?: GenerateOptions): AsyncIterable<StreamChunk>;
  
  // 💬 对话方法
  chat(messages: ChatMessage[], options?: ChatOptions): Promise<ChatResult>;
  chatStream(messages: ChatMessage[], options?: ChatOptions): AsyncIterable<ChatChunk>;
  
  // 🧠 分析方法
  analyze(text: string, task: AnalysisTask, options?: AnalysisOptions): Promise<AnalysisResult>;
  
  // 📊 信息方法
  getModelInfo(): ModelInfo;
  healthCheck(): Promise<HealthStatus>;
}
```

### 向量模型接口

```typescript
interface IEmbeddingModel {
  // 🎯 核心方法
  embed(text: string, options?: EmbedOptions): Promise<EmbedResult>;
  embedBatch(texts: string[], options?: EmbedOptions): Promise<EmbedBatchResult>;
  
  // 📏 信息方法
  getDimensions(): number;
  getModelInfo(): EmbeddingModelInfo;
  healthCheck(): Promise<EmbeddingHealthStatus>;
}
```

---

## 🚀 使用方式

### LLM 模型使用

```typescript
import { AIModelFactory } from '@/lib/ai';

// 配置工厂
AIModelFactory.configure({
  defaultProvider: 'gemini',  // 或 'doubao'
  models: {
    gemini: { apiKey: '...', model: '...' },
    doubao: { apiKey: '...', model: '...' }
  }
});

// 使用默认模型
const result = await AIModelFactory.generateText('你好');

// 指定特定模型
const geminiResult = await AIModelFactory.generateText('你好', {}, 'gemini');
const doubaoResult = await AIModelFactory.generateText('你好', {}, 'doubao');
```

### 向量模型使用

```typescript
import { EmbeddingModelFactory } from '@/lib/ai/embedding';

// 配置向量模型工厂
EmbeddingModelFactory.configure({
  defaultProvider: 'local',
  models: {
    local: {
      modelPath: './models/embedding-model.gguf',
      dimensions: 768,
      device: 'cpu'
    }
  }
});

// 生成文档嵌入
const embedding = await EmbeddingModelFactory.embed('这是一段文档内容');

// 批量生成嵌入
const embeddings = await EmbeddingModelFactory.embedBatch([
  '文档1内容',
  '文档2内容',
  '文档3内容'
]);
```

### 模型切换

```typescript
// 🔧 简单的配置切换
// 开发环境：使用 Gemini
AIModelFactory.configure({
  defaultProvider: 'gemini',
  models: { gemini: { /* 配置 */ } }
});

// 生产环境：切换到豆包控制成本
AIModelFactory.configure({
  defaultProvider: 'doubao',
  models: { doubao: { /* 配置 */ } }
});

// 使用方式完全不变
const result = await AIModelFactory.generateText('你好');
```

---

## 📊 配置示例

### 开发环境配置

```typescript
const devConfig = {
  defaultProvider: 'gemini',
  models: {
    gemini: {
      apiKey: process.env.GOOGLE_API_KEY,
      model: 'gemini-2.5-flash-preview-05-20',
      proxyUrl: 'http://127.0.0.1:7897',  // 开发代理
      thinkingBudget: 0
    }
  },
  cache: { enabled: true, ttl: 300000, maxSize: 50 },
  healthCheck: { enabled: true, interval: 60000, timeout: 10000 },
  retry: { maxRetries: 3, retryDelay: 1000, backoffFactor: 2 }
};
```

### 生产环境配置

```typescript
const prodConfig = {
  defaultProvider: 'doubao',  // 生产环境使用豆包控制成本
  models: {
    gemini: {
      apiKey: process.env.GOOGLE_API_KEY,
      model: 'gemini-2.5-flash-preview-05-20',
      // 生产环境不使用代理
      thinkingBudget: 0
    },
    doubao: {
      apiKey: process.env.DOUBAO_API_KEY,
      model: 'doubao-pro-4k',
      baseUrl: 'https://ark.cn-beijing.volces.com/api/v3'
    }
  },
  cache: { enabled: true, ttl: 600000, maxSize: 100 },
  healthCheck: { enabled: true, interval: 60000, timeout: 10000 },
  retry: { maxRetries: 5, retryDelay: 1000, backoffFactor: 2 }
};
```

### 向量模型配置

```typescript
const embeddingConfig = {
  defaultProvider: 'local',
  models: {
    local: {
      modelPath: './models/bge-large-zh-v1.5.gguf',
      dimensions: 1024,
      device: 'cpu',
      threads: 4,
      maxMemory: 2048  // 2GB
    }
  },
  cache: {
    enabled: true,
    ttl: 3600000,  // 1小时
    maxSize: 1000,
    cacheEmbeddings: true  // 缓存向量结果
  }
};
```

---

## ✅ 兼容性保证

### 现有代码完全兼容

```typescript
// 这些代码完全不需要修改！
import { streamText, generateText } from 'ai';
import { google } from '@ai-sdk/google';

// 自动使用新的 AI 工厂
const result = streamText({
  model: google('gemini-2.5-flash-preview-05-20'),
  messages: [{ role: 'user', content: '你好' }]
});
```

### 调试控制台兼容

- ✅ **系统提示词编辑器**: 正常工作
- ✅ **RAG 调试器**: AI 调用正常
- ✅ **意义RAG调试器**: 流式响应正常
- ✅ **所有现有功能**: 无缝兼容

---

## 🎯 实现优势

### 简化的架构

1. **更少的复杂性**: 移除了智能降级等复杂功能
2. **更清晰的职责**: LLM 和向量模型分离管理
3. **更容易维护**: 减少了故障点和调试难度

### 专注核心需求

1. **云端 LLM**: 专注于 Gemini 和豆包的统一管理
2. **本地向量**: 专注于 RAG 系统的隐私保护
3. **成本控制**: 简单的模型切换实现成本优化

### 用户体验优化

1. **资源友好**: 不在用户端运行大型 LLM
2. **隐私保护**: 文档嵌入可以完全本地化
3. **性能稳定**: 避免了复杂的降级逻辑

---

## 📋 实现计划

### 第一阶段：LLM 工厂完善 (1 天)

1. ✅ **Gemini 模型**: 已完成
2. 🔄 **豆包模型**: 实现豆包 API 适配
3. ✅ **兼容适配器**: 已完成

### 第二阶段：向量模型工厂 (2 天)

1. 🔄 **向量模型接口**: 实现 IEmbeddingModel
2. 🔄 **本地向量模型**: 集成本地推理引擎
3. 🔄 **向量模型工厂**: 实现 EmbeddingModelFactory

### 第三阶段：集成测试 (1 天)

1. 🔄 **功能验证**: 确保所有功能正常
2. 🔄 **性能测试**: 验证性能表现
3. 🔄 **文档完善**: 更新使用文档

---

## 🎉 总结

### 设计亮点

- 🎯 **专注核心**: 移除不必要的复杂性
- 🔄 **简单切换**: 配置驱动的模型管理
- 🛡️ **隐私保护**: 本地向量模型支持
- 💰 **成本控制**: 轻松切换到豆包
- ✅ **完全兼容**: 现有代码零修改

### 技术价值

- 降低了架构复杂度
- 提升了维护效率
- 保持了扩展性
- 满足了核心需求

这个简化的设计方案更加务实，专注于解决你的核心需求：**统一 AI 调用接口** + **轻松模型切换** + **RAG 系统本地化**。

---

**设计完成**: 2025-06-28  
**下一步**: 实现豆包模型和向量模型工厂  
**状态**: 🎯 设计优化完成
