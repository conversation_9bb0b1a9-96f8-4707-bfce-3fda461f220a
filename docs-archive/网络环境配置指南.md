# 中国大陆网络环境开发配置指南

## 🌐 概述
在中国大陆进行国际服务（如 Google、OpenAI、GitHub 等）的开发时，需要通过代理服务来访问。本指南整理了各种场景下的最佳配置方案。

## 🛠️ 基础环境设置

### 1. Clash 代理配置
- **推荐工具**：Clash Verge / Clash for Windows
- **混合代理端口**：通常为 `7897`
- **HTTP代理端口**：通常为 `7890`
- **SOCKS5代理端口**：通常为 `7891`

**重要设置**：
```bash
# Clash 常用端口配置
HTTP代理: 127.0.0.1:7890
SOCKS5代理: 127.0.0.1:7891
混合代理: 127.0.0.1:7897  # 推荐使用
```

### 2. 系统环境变量设置
```bash
# 在 ~/.zshrc 或 ~/.bash_profile 中添加
export HTTP_PROXY=http://127.0.0.1:7897
export HTTPS_PROXY=http://127.0.0.1:7897
export ALL_PROXY=socks5://127.0.0.1:7891

# 不使用代理的地址
export NO_PROXY=localhost,127.0.0.1,10.*,192.168.*,*.local
```

## 📦 Node.js 项目配置

### 方案1：环境变量法（推荐）
在项目启动前设置环境变量：
```bash
# package.json 中的脚本
{
  "scripts": {
    "dev": "HTTP_PROXY=http://127.0.0.1:7897 HTTPS_PROXY=http://127.0.0.1:7897 next dev",
    "start": "HTTP_PROXY=http://127.0.0.1:7897 HTTPS_PROXY=http://127.0.0.1:7897 next start"
  }
}
```

### 方案2：undici ProxyAgent（最稳定）
```javascript
// 安装依赖
npm install undici

// 代码配置
const { ProxyAgent, fetch: undiciFetch } = require('undici');

const createProxyFetch = () => {
  if (process.env.NODE_ENV === 'development') {
    const proxyAgent = new ProxyAgent('http://127.0.0.1:7897');
    return (url, options) => undiciFetch(url, {
      ...options,
      dispatcher: proxyAgent
    });
  }
  return fetch; // 生产环境使用默认 fetch
};

// 使用示例
const customFetch = createProxyFetch();
const response = await customFetch('https://api.openai.com/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});
```

### 方案3：https-proxy-agent（备选）
```javascript
// 安装依赖
npm install https-proxy-agent

// 代码配置
const { HttpsProxyAgent } = require('https-proxy-agent');

const agent = process.env.NODE_ENV === 'development' 
  ? new HttpsProxyAgent('http://127.0.0.1:7897') 
  : undefined;

// 在 fetch 调用中使用
const response = await fetch(url, {
  ...options,
  agent // 注意：只在 Node.js runtime 中有效，Edge runtime 不支持
});
```

## 🔧 特定框架配置

### Next.js 项目
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 确保使用 nodejs runtime 而不是 edge runtime
  experimental: {
    runtime: 'nodejs'
  }
}

// API 路由中
export const runtime = 'nodejs'; // 不要使用 'edge'
```

### React/Vite 项目
```javascript
// vite.config.js
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'https://api.openai.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, options) => {
          // 如果需要，可以在这里配置代理
        }
      }
    }
  }
})
```

## 🧪 测试连接

### 1. 命令行测试
```bash
# 测试代理连接
curl -x http://127.0.0.1:7897 https://www.google.com

# 测试 API 连接
curl -x http://127.0.0.1:7897 \
  -H "Authorization: Bearer YOUR_API_KEY" \
  https://api.openai.com/v1/models

# 测试 Google API
curl -x http://127.0.0.1:7897 \
  "https://generativelanguage.googleapis.com/v1beta/models?key=YOUR_API_KEY"
```

### 2. Node.js 测试脚本
```javascript
// test-proxy.js
const { ProxyAgent, fetch } = require('undici');

async function testProxy() {
  const proxyAgent = new ProxyAgent('http://127.0.0.1:7897');
  
  try {
    const response = await fetch('https://api.ipify.org?format=json', {
      dispatcher: proxyAgent
    });
    const data = await response.json();
    console.log('代理IP:', data.ip);
    console.log('✅ 代理连接成功');
  } catch (error) {
    console.error('❌ 代理连接失败:', error.message);
  }
}

testProxy();
```

## 🚨 常见问题排查

### 问题1：连接超时 (Connect Timeout Error)
**原因**：代理未正确配置或Clash未启动
**解决**：
1. 确认 Clash 正在运行且启用全局模式
2. 检查代理端口是否正确（通常是7897）
3. 尝试重启 Clash

### 问题2：API 密钥错误
**原因**：环境变量未正确加载或API密钥无效
**解决**：
1. 硬编码测试确认API密钥有效性
2. 检查 `.env` 文件是否被 Git 跟踪
3. 确认环境变量命名正确

### 问题3：Edge Runtime 不支持代理
**原因**：Vercel Edge Runtime 不支持 Node.js 代理
**解决**：
```javascript
// 将 runtime 改为 nodejs
export const runtime = 'nodejs'; // 而不是 'edge'
```

### 问题4：HTTPS 证书问题
**原因**：代理可能会导致证书验证问题
**解决**：
```javascript
// 仅在开发环境禁用证书验证
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}
```

## 📋 开发流程检查清单

### 新项目启动前
- [ ] 确认 Clash 正在运行
- [ ] 测试代理连接：`curl -x http://127.0.0.1:7897 https://www.google.com`
- [ ] 设置项目代理配置
- [ ] 测试 API 连接

### 部署前
- [ ] 移除开发环境的代理配置
- [ ] 确保生产环境不使用代理
- [ ] 测试生产环境 API 连接

### 调试步骤
1. **确认代理工作**：使用 curl 测试
2. **检查环境变量**：`echo $HTTP_PROXY`
3. **查看应用日志**：添加详细的调试日志
4. **测试简化版本**：先用最简单的配置测试

## 🔒 安全注意事项

1. **API 密钥管理**：
   - 生产环境使用环境变量
   - 开发环境可以考虑硬编码（非开源项目）
   - 定期轮换 API 密钥

2. **代理安全**：
   - 确保代理服务器安全可信
   - 不要在公共网络使用不安全的代理
   - 定期更新代理工具

3. **代码安全**：
   - 不要将代理配置提交到公共仓库
   - 使用条件判断（开发/生产环境）

## 📚 推荐工具和资源

### 代理工具
- **Clash Verge**：跨平台，界面友好
- **Clash for Windows**：Windows 专用
- **ClashX**：macOS 专用

### 调试工具
- **Postman**：API 测试
- **Insomnia**：API 测试
- **curl**：命令行测试

### 监控工具
```javascript
// 简单的代理监控函数
function createMonitoredFetch(originalFetch) {
  return async (url, options) => {
    const start = Date.now();
    console.log(`🚀 请求开始: ${url}`);
    
    try {
      const response = await originalFetch(url, options);
      const duration = Date.now() - start;
      console.log(`✅ 请求成功: ${response.status} (${duration}ms)`);
      return response;
    } catch (error) {
      const duration = Date.now() - start;
      console.error(`❌ 请求失败: ${error.message} (${duration}ms)`);
      throw error;
    }
  };
}
```

---

**💡 小贴士**：将此指南保存在项目根目录，方便团队成员参考。根据具体项目需求调整配置参数。 