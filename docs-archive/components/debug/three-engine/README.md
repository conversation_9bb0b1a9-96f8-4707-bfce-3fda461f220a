# SelfMirror 三引擎调试控制台

## 🎯 概览

三引擎调试控制台是SelfMirror的核心监控和管理界面，提供对三引擎协作工作流系统的全面监控、配置管理和实时测试功能。

## 🏗️ 架构设计

### 界面布局
- **左侧面板 (50%)**: 三引擎聊天测试区域
- **右侧面板 (50%)**: 多标签调试面板
- **深色主题**: 使用shadcn/ui组件库构建的专业调试界面
- **响应式设计**: 适配不同屏幕尺寸和分辨率

### 核心组件

#### 1. ThreeEngineMonitor - 三引擎性能监控
**文件**: `components/debug/three-engine/ThreeEngineMonitor.tsx`

**功能特性**:
- **系统概览**: 引擎状态、性能指标、最新工作流结果
- **引擎状态**: Navigator、Context Retriever、Integration Generator的详细状态
- **性能分析**: 时间序列图表、性能趋势分析（开发中）
- **工作流监控**: 实时工作流状态、执行日志（开发中）

**监控指标**:
```typescript
{
  totalWorkflows: number,        // 总工作流数
  averageTime: number,          // 平均处理时间
  averageQuality: number,       // 平均质量分数
  cacheHitRate: number,         // 缓存命中率
  systemHealth: string,         // 系统健康状态
  parallelEfficiency: number    // 并行执行效率
}
```

#### 2. ThreeEngineConfig - 三引擎配置管理
**文件**: `components/debug/three-engine/ThreeEngineConfig.tsx`

**配置功能**:
- **引擎配置**: AI模型动态切换、参数调优
- **工作流设置**: 并行执行、超时配置、并发控制
- **缓存策略**: 缓存开关、TTL设置、键策略选择
- **全局设置**: 降级机制、重试策略、系统参数

**模型配置**:
```typescript
{
  navigator: {
    provider: 'doubao',           // 成本优化
    modelName: 'doubao-lite-4k',
    temperature: 0.3,
    maxTokens: 1000
  },
  contextRetriever: {
    provider: 'gemini',           // 速度优化
    modelName: 'gemini-2.5-flash-preview-05-20',
    temperature: 0.1,
    maxTokens: 500
  },
  integrationGenerator: {
    provider: 'gemini',           // 质量优化
    modelName: 'gemini-2.5-flash-preview-05-20',
    temperature: 0.7,
    maxTokens: 2000
  }
}
```

#### 3. ThreeEngineChatTest - 聊天测试界面
**文件**: `components/debug/three-engine/ThreeEngineChatTest.tsx`

**测试功能**:
- **模式切换**: 三引擎模式 vs 传统模式对比测试
- **实时对话**: 支持流式响应和实时生成
- **性能展示**: 显示每条消息的处理时间、质量分数、缓存使用情况
- **会话管理**: 会话ID追踪、对话历史管理

**消息元数据**:
```typescript
{
  workflowId: string,
  totalTime: number,
  quality: number,
  engineTimes: {
    navigator: number,
    contextRetriever: number,
    integrationGenerator: number
  },
  cacheUsed: boolean
}
```

## 🔧 技术实现

### 数据管理Hook
**文件**: `hooks/useThreeEngineData.ts`

**功能特性**:
- **实时数据更新**: 自动轮询和WebSocket支持
- **缓存管理**: 智能缓存和数据去重
- **错误处理**: 完善的错误恢复机制
- **性能优化**: 批量请求和数据合并

**使用方式**:
```typescript
const {
  stats,
  latestResult,
  history,
  derivedData,
  isLoading,
  error,
  refreshData,
  clearData
} = useThreeEngineData({
  autoRefresh: true,
  refreshInterval: 5000,
  enableWebSocket: false
});
```

### API集成
**端点**: `/api/debug/three-engine-result`

**支持的操作**:
```bash
# 获取最新结果
GET /api/debug/three-engine-result?type=latest

# 获取历史记录
GET /api/debug/three-engine-result?type=history&limit=10

# 获取统计信息
GET /api/debug/three-engine-result?type=stats

# 清空调试数据
DELETE /api/debug/three-engine-result
```

### WebSocket支持
**端点**: `/api/debug/three-engine-ws` (开发中)

**实时更新**:
- 工作流完成通知
- 性能指标更新
- 系统状态变化
- 错误和警告推送

## 🎨 UI/UX设计

### 设计原则
- **深色主题**: 专业的调试环境，减少眼部疲劳
- **信息密度**: 在有限空间内展示最大信息量
- **实时反馈**: 即时的状态更新和视觉反馈
- **直观操作**: 简单明了的交互设计

### 颜色系统
```css
/* 背景色 */
--bg-primary: #18181B;    /* 主背景 */
--bg-secondary: #1F1F23;  /* 卡片背景 */
--bg-tertiary: #27272A;   /* 边框和分割线 */

/* 状态色 */
--status-healthy: #22C55E;    /* 健康状态 */
--status-degraded: #EAB308;   /* 降级状态 */
--status-unhealthy: #EF4444;  /* 异常状态 */

/* 引擎色 */
--navigator: #A855F7;         /* Navigator Engine */
--retriever: #3B82F6;         /* Context Retriever */
--generator: #10B981;         /* Integration Generator */
```

### 响应式设计
- **桌面端**: 50/50分栏布局，最佳的监控体验
- **平板端**: 自适应布局，保持核心功能
- **移动端**: 垂直堆叠，优化触摸操作

## 📊 监控指标

### 系统健康评估
```typescript
systemHealth = {
  'excellent': quality > 0.8 && cacheHitRate > 0.6,
  'good': quality > 0.6 && cacheHitRate > 0.4,
  'fair': quality > 0.4,
  'poor': quality <= 0.4
}
```

### 性能趋势分析
- **处理时间趋势**: 基于历史数据的性能变化
- **质量分数趋势**: 响应质量的时间序列分析
- **缓存效率**: 缓存命中率和时间节省统计
- **并行效率**: 多引擎协作的效率评估

### 实时状态指标
- **活跃状态**: 最近30秒内有数据更新
- **数据新鲜度**: 数据是否在1分钟内更新
- **连接状态**: WebSocket连接和API响应状态

## 🚀 使用指南

### 快速开始
1. **启动调试控制台**: 访问 `/debug-console`
2. **选择三引擎监控**: 点击"三引擎监控"标签
3. **开始聊天测试**: 在左侧聊天区域输入消息
4. **观察实时数据**: 右侧面板显示实时监控数据

### 配置管理
1. **切换到配置面板**: 点击"三引擎配置"标签
2. **选择引擎**: 在"引擎配置"中选择要配置的引擎
3. **调整参数**: 修改模型、温度、Token数等参数
4. **保存配置**: 点击"保存配置"按钮应用更改

### 性能优化
1. **监控缓存命中率**: 目标 > 60%
2. **观察并行效率**: 目标 > 80%
3. **检查质量分数**: 目标 > 0.8
4. **调整模型参数**: 根据性能数据优化配置

## 🔮 未来扩展

### 计划功能
- **性能图表**: 时间序列图表和趋势分析
- **工作流可视化**: 引擎协作流程的可视化展示
- **智能告警**: 基于阈值的自动告警系统
- **配置模板**: 预设的配置模板和快速切换
- **导出功能**: 性能报告和配置导出

### 技术改进
- **WebSocket实时更新**: 完整的实时数据推送
- **数据持久化**: 历史数据的本地存储
- **性能优化**: 更高效的数据处理和渲染
- **移动端优化**: 更好的移动设备支持

## 🧪 测试和验证

### 测试工具
**文件**: `components/debug/three-engine/ThreeEngineTestUtils.tsx`

**功能特性**:
- **模拟数据生成**: 生成真实的三引擎工作流结果用于测试
- **API集成测试**: 验证与调试API端点的集成
- **数据清理**: 清空测试数据和重置状态
- **实时验证**: 测试实时数据更新和通知功能

### 实时通知系统
**文件**: `lib/services/three-engine-debug-notifier.ts`

**功能特性**:
- **事件驱动**: 基于发布-订阅模式的实时通知
- **多种通知类型**: 工作流完成、错误、统计更新、状态变化
- **自动集成**: 与三引擎协调器无缝集成
- **错误处理**: 完善的错误恢复和重试机制

### 验证步骤
1. **访问调试控制台**: `http://localhost:3000/debug-console`
2. **选择三引擎监控**: 点击"三引擎监控"标签
3. **生成测试数据**: 使用测试工具生成模拟工作流结果
4. **验证实时更新**: 观察监控面板的实时数据更新
5. **测试聊天功能**: 在左侧聊天区域测试三引擎模式

## 🔧 技术架构总结

### 数据流架构
```
三引擎协调器 → 调试通知服务 → 数据提供者 → UI组件
     ↓              ↓              ↓         ↓
工作流执行 → 实时通知推送 → 状态管理 → 界面更新
     ↓              ↓              ↓         ↓
API端点存储 → WebSocket广播 → 自动刷新 → 用户反馈
```

### 组件层次结构
```
ThreeEngineMonitor (主组件)
├── ThreeEngineDataProvider (数据提供者)
│   ├── useThreeEngineData (数据Hook)
│   ├── useThreeEngineDebugNotifications (通知Hook)
│   └── ConnectionStatusIndicator (连接状态)
├── ThreeEngineStatusPanel (状态面板)
│   ├── DataSummary (数据摘要)
│   ├── DataRefreshControls (刷新控制)
│   └── ThreeEngineTestUtils (测试工具)
├── ThreeEngineConfig (配置管理)
└── ThreeEngineChatTest (聊天测试)
```

### 核心特性
- ✅ **完整的监控体系**: 系统状态、性能指标、工作流结果
- ✅ **实时数据更新**: 自动刷新和事件驱动的通知系统
- ✅ **配置管理**: 动态模型切换和参数调优
- ✅ **聊天测试**: 三引擎模式vs传统模式对比测试
- ✅ **测试工具**: 模拟数据生成和验证功能
- ✅ **错误处理**: 完善的错误恢复和状态管理
- ✅ **响应式设计**: 适配不同屏幕尺寸的专业界面

---

**当前状态**: ✅ 完整实现完成
**核心成就**: 构建了专业级的三引擎调试控制台，提供全面的监控、配置和测试能力
**下一步**: 性能图表和工作流可视化功能开发
