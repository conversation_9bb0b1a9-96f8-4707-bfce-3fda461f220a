# SelfMirror 新功能测试指南

## 🎯 测试目标
验证以下新功能是否正常工作：
1. 快速响应模式包含三个画像文件
2. 深度分析输出记录功能
3. 提示词分板块管理

## 📝 测试步骤

### 1. 快速响应模式测试
1. 访问 http://localhost:3000
2. 输入任意对话内容
3. 检查控制台日志，应该看到：
   ```
   📂 读取用户画像文件...
   📊 已加载用户画像: {
     hasProfile: true/false,
     keyEventsCount: 0,
     hasElementIndex: true/false
   }
   ```

### 2. 深度分析触发测试
1. 进行7-8轮连续对话
2. 观察控制台是否出现深度分析相关日志：
   ```
   🧠 收到深度分析请求
   📊 准备分析对话: { dialogueCount: 7 }
   🤖 开始调用 Gemini API进行深度分析...
   ✨ 深度分析完成
   💾 保存深度分析日志...
   ```

### 3. 调试页面测试
1. 访问 http://localhost:3000/debug
2. 应该看到"深度分析调试面板"
3. 如果有分析记录，可以：
   - 查看分析报告
   - 查看记忆更新状态
   - 查看原始AI输出

### 4. 提示词模块化验证
检查以下文件是否存在并包含正确内容：
- `lib/prompts/quick-response.ts` - 快速响应提示词
- `lib/prompts/deep-analysis.ts` - 深度分析提示词
- `lib/prompts/memory-management.ts` - 记忆管理规则
- `lib/prompts/empathy.ts` - 探针式共情技巧

## 🐛 常见问题

### 问题1: 深度分析未触发
- 确保进行了足够多轮对话（7-8轮）
- 检查ConversationManager是否正确计数
- 查看浏览器控制台错误信息

### 问题2: 调试页面无数据
- 深度分析需要先触发并完成
- 检查IndexedDB是否正确初始化
- 尝试刷新页面

### 问题3: 画像文件读取失败
- 首次使用时可能没有数据是正常的
- 检查IndexedDB权限
- 查看控制台错误信息

## 📊 验证清单

- [ ] 快速响应模式正常工作
- [ ] 控制台显示画像文件加载日志
- [ ] 7-8轮对话后触发深度分析
- [ ] 深度分析日志成功保存
- [ ] 调试页面可以查看分析记录
- [ ] 提示词文件结构正确

## 💡 调试技巧

1. **打开浏览器开发者工具**
   - Network标签：查看API请求
   - Console标签：查看日志输出
   - Application标签：查看IndexedDB数据

2. **使用测试对话**
   ```
   用户: 我最近感觉很焦虑
   AI: [观察回应]
   用户: 工作压力很大
   AI: [探针式提问]
   ...继续到7-8轮
   ```

3. **检查数据存储**
   - 打开Application > IndexedDB > SelfMirrorDB
   - 查看各个存储表的内容

## 🎉 功能亮点

1. **智能上下文**：每次对话都包含用户的完整画像
2. **深度洞察**：后台分析用户的认知和情感模式
3. **调试友好**：完整的日志和调试界面
4. **模块化设计**：提示词分离，便于优化

---

祝测试顺利！如有问题请查看控制台日志。 