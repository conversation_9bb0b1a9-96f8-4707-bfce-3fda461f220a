# SelfMirror 架构更新说明

## 🔧 问题修复

### 原问题
- 在服务器端（API路由）直接调用 IndexedDB 导致 `indexedDB is not defined` 错误
- IndexedDB 是浏览器端API，无法在 Node.js 环境中使用

### 解决方案
采用混合存储架构：
1. **客户端存储**：继续使用 IndexedDB 存储用户画像数据
2. **服务器端存储**：使用文件系统存储深度分析日志和待同步的更新

## 📊 新的数据流

### 1. 快速响应模式
```
客户端读取IndexedDB → 发送画像数据到API → API使用画像生成响应 → 返回给客户端
```

### 2. 深度分析模式
```
触发分析 → 服务器分析并保存日志到文件 → 生成记忆更新保存到文件 → 客户端定期同步更新
```

## 🆕 新增组件

### 1. 文件存储系统 (`lib/storage/file-storage.ts`)
- 保存深度分析日志到 `.selfmirror-data/analysis-logs.json`
- 保存待同步的记忆更新到 `.selfmirror-data/pending-updates.json`
- 自动管理文件和目录创建

### 2. 新API路由
- `/api/debug/logs` - 获取深度分析日志（用于调试页面）
- `/api/memory/sync` - 获取/清除待同步的记忆更新

### 3. 记忆同步机制
- 客户端每30秒检查一次服务器端的记忆更新
- 自动应用更新到本地IndexedDB
- 更新完成后清除服务器端的待处理更新

## 📂 文件结构
```
.selfmirror-data/
├── analysis-logs.json      # 深度分析日志（最近20条）
└── pending-updates.json    # 待同步的记忆更新
```

## ✅ 优势
1. **分离关注点**：客户端负责UI和本地存储，服务器负责AI处理和日志
2. **避免环境冲突**：不再尝试在服务器端访问浏览器API
3. **持久化日志**：深度分析日志保存在文件系统，重启不丢失
4. **异步同步**：记忆更新不会阻塞用户交互

## 🔍 调试建议
1. 查看 `.selfmirror-data/` 目录下的文件内容
2. 观察浏览器控制台的同步日志
3. 使用调试页面查看分析历史

## ⚠️ 注意事项
- `.selfmirror-data/` 目录已添加到 `.gitignore`
- 首次运行会自动创建必要的目录和文件
- 文件存储仅保留最近的记录以控制大小 