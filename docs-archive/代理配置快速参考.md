# 🚀 代理配置快速参考卡片

## 基础配置
```bash
# Clash 端口
混合代理: 127.0.0.1:7897 ✅ 推荐
HTTP: 127.0.0.1:7890
SOCKS5: 127.0.0.1:7891
```

## Node.js 项目通用解决方案

### 方法1: 启动时设置环境变量
```json
{
  "scripts": {
    "dev": "HTTP_PROXY=http://127.0.0.1:7897 HTTPS_PROXY=http://127.0.0.1:7897 npm run dev:next"
  }
}
```

### 方法2: undici ProxyAgent (最稳定)
```javascript
// 1. 安装
npm install undici

// 2. 使用
const { ProxyAgent, fetch: undiciFetch } = require('undici');
const proxyAgent = new ProxyAgent('http://127.0.0.1:7897');

const response = await undiciFetch(url, {
  ...options,
  dispatcher: proxyAgent
});
```

## 常用 API 配置模板

### Google Gemini
```javascript
const google = createGoogleGenerativeAI({
  apiKey: "YOUR_API_KEY",
  fetch: (url, options) => {
    const { ProxyAgent, fetch } = require('undici');
    const agent = new ProxyAgent('http://127.0.0.1:7897');
    return fetch(url, { ...options, dispatcher: agent });
  }
});
```

### OpenAI
```javascript
const openai = new OpenAI({
  apiKey: "YOUR_API_KEY",
  httpAgent: new (require('undici').ProxyAgent)('http://127.0.0.1:7897')
});
```

## 快速测试
```bash
# 测试代理
curl -x http://127.0.0.1:7897 https://www.google.com

# 测试 API
curl -x http://127.0.0.1:7897 "https://generativelanguage.googleapis.com/v1beta/models?key=YOUR_KEY"
```

## Next.js 特殊注意
```javascript
// API 路由中必须使用
export const runtime = 'nodejs'; // 不是 'edge'
```

## 常见错误快速修复
- **连接超时** → 检查 Clash 是否运行，端口是否正确
- **证书错误** → 添加 `process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'`
- **Edge Runtime 错误** → 改为 `nodejs` runtime 