# SelfMirror 文件位置说明

## 📂 提示词文件位置

所有提示词文件都在 `lib/prompts/` 文件夹中：

```
lib/prompts/
├── quick-response.ts      # 快速响应模式提示词
├── deep-analysis.ts       # 深度分析模式提示词
├── memory-management.ts   # 记忆管理规则
├── empathy.ts            # 探针式共情技巧
├── system-prompts.ts     # 系统提示词（原始版本）
└── analysis.ts           # 分析提示词（原始版本）
```

### 各文件作用：
- **quick-response.ts**: 控制 AI 的快速回应风格，包括温暖、简洁的对话方式
- **deep-analysis.ts**: 定义深度分析的维度和输出格式
- **memory-management.ts**: 四文件系统的更新规则和标准
- **empathy.ts**: 探针式共情的具体技巧和句式

## 🗄️ 四个记忆文件位置

四个记忆文件（用户画像、关键事件、要素索引、对话存档）存储在两个地方：

### 1. 客户端存储（IndexedDB - 浏览器内部）
- **位置**: 浏览器的 IndexedDB 数据库
- **数据库名**: `SelfMirrorDB`
- **访问方式**: 
  1. 打开浏览器开发者工具 (F12)
  2. 进入 Application/应用程序 标签
  3. 左侧找到 IndexedDB → SelfMirrorDB

存储表：
- `userProfile` - 用户画像
- `keyEvents` - 关键事件
- `elementIndex` - 要素索引
- `conversationArchive` - 对话存档

### 2. 服务器端存储（文件系统）
- **位置**: `.selfmirror-data/` 目录
- **文件格式**: JSON

当前包含：
```
.selfmirror-data/
├── analysis-logs.json      # 深度分析日志（需要触发深度分析后生成）
└── pending-updates.json    # 待同步的记忆更新（需要触发深度分析后生成）
```

## 📊 数据流向

```
用户对话
  ↓
客户端 (IndexedDB)
  ↓ 读取画像数据
发送到服务器
  ↓
快速响应
  ↓
7-8轮对话后
  ↓
深度分析（服务器端）
  ↓ 保存到文件
.selfmirror-data/
  ↓ 客户端定期同步
更新 IndexedDB
```

## 🔍 如何查看数据

### 查看 IndexedDB 数据（客户端）
1. 在浏览器中访问 http://localhost:3000
2. 打开开发者工具 (F12)
3. Application → IndexedDB → SelfMirrorDB
4. 点击各个表查看数据

### 查看文件系统数据（服务器端）
```bash
# 查看分析日志
cat .selfmirror-data/analysis-logs.json

# 查看待同步更新
cat .selfmirror-data/pending-updates.json
```

### 通过调试页面查看
访问 http://localhost:3000/debug 可以查看深度分析历史

## 💡 注意事项

1. **首次使用**：所有数据都是空的，需要通过对话逐步积累
2. **触发深度分析**：需要连续对话 7-8 轮才会生成分析日志
3. **数据同步**：客户端每 30 秒自动同步服务器端的更新
4. **隐私保护**：所有数据都存储在本地，不会上传到云端

## 🛠️ 修改提示词

如果您想修改 AI 的行为，可以编辑对应的提示词文件：

1. **修改对话风格**: 编辑 `lib/prompts/quick-response.ts`
2. **修改分析逻辑**: 编辑 `lib/prompts/deep-analysis.ts`
3. **修改记忆规则**: 编辑 `lib/prompts/memory-management.ts`

修改后重启开发服务器即可生效。 