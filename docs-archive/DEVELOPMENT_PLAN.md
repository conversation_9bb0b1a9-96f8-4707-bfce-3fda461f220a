# SelfMirror 开发计划

## 项目概述
SelfMirror是一个基于深度理解的AI认知伙伴产品，通过"探针式共情"帮助用户实现自我认知和情感抱持。

## 技术栈
- **前端框架**: Next.js 15.3.2 (App Router)
- **UI库**: React 19 + TypeScript
- **AI集成**: Gemini 2.5 Flash (通过AI SDK)
- **状态管理**: Zustand
- **动画**: Framer Motion
- **数据存储**: IndexedDB
- **样式**: TailwindCSS

## 开发阶段

### 🚀 第一阶段：MVP (第1-2周)

#### Week 1: 核心功能实现
- [x] 项目架构搭建
- [x] 类型定义系统
- [x] IndexedDB存储层
- [x] 提示词系统（探针式共情）
- [x] 对话管理服务
- [x] 情绪检测服务
- [ ] 修复API路由类型问题
- [ ] 完善chat路由以支持快响应模式
- [ ] 实现基础UI界面

#### Week 2: 四文件系统与双模式
- [ ] 完善记忆管理服务
- [ ] 实现深度分析引擎
- [ ] 双模式切换逻辑
- [ ] 滑动窗口机制
- [ ] 基础UI动画（线条+海平面）
- [ ] 第一轮用户测试

### 🎨 第二阶段：UI/UX完善 (第3-4周)

#### Week 3: 情绪可视化
- [ ] 实现情绪线条动画系统
  - M形（悲伤）
  - 波浪形（焦虑）
  - 平直舒展（平静）
  - 交织螺旋（思考）
- [ ] 海平面效果
  - 涟漪动画
  - 镜面反射
  - 情绪色彩变化
- [ ] 时间氛围系统
  - 深夜/清晨/白天/黄昏

#### Week 4: 交互优化
- [ ] 文字渐现效果
- [ ] 打字响应动画
- [ ] 对话气泡美化
- [ ] 响应速度优化
- [ ] 移动端适配
- [ ] 性能调优

### 🔧 第三阶段：功能增强 (第5-6周)

#### Week 5: 高级功能
- [ ] 向量检索集成
- [ ] 历史对话搜索
- [ ] 情绪趋势分析
- [ ] 用户画像可视化
- [ ] 数据导出功能

#### Week 6: 测试与优化
- [ ] 完整功能测试
- [ ] 性能压力测试
- [ ] 用户体验测试
- [ ] Bug修复
- [ ] 文档完善

## 当前待解决问题

### 1. API路由类型问题
- analyze路由中的AI SDK集成需要修复类型错误
- 考虑统一使用assistant-ui的API模式

### 2. 双模式实现细节
- 快响应模式的上下文优化
- 深度分析的触发时机调优
- 后台分析的并发控制

### 3. UI实现优先级
- 先实现基础对话界面
- 再逐步添加动画效果
- 最后优化视觉细节

## 关键技术决策

### 1. 使用assistant-ui
- 已有成熟的流式响应处理
- 支持Gemini集成
- 需要扩展以支持双模式

### 2. IndexedDB vs LocalStorage
- 选择IndexedDB因为需要存储大量结构化数据
- 支持索引和复杂查询
- 更好的性能和容量

### 3. 动画实现方案
- Framer Motion处理复杂动画
- CSS动画处理简单效果
- Canvas/WebGL备选（如需要）

## 开发原则

1. **用户体验优先**
   - 响应速度 < 2秒
   - 流畅的动画效果
   - 直观的交互设计

2. **代码质量**
   - TypeScript严格模式
   - 组件化和模块化
   - 完善的错误处理

3. **隐私保护**
   - 所有数据本地存储
   - 不上传个人对话
   - 用户可控的数据管理

4. **渐进式开发**
   - MVP优先核心功能
   - 逐步添加高级特性
   - 持续用户反馈迭代

## 下一步行动

1. **立即修复**：
   - 解决analyze路由的类型问题
   - 更新chat路由支持模式切换

2. **本周完成**：
   - 基础UI界面实现
   - 连接前后端数据流
   - 实现基本对话功能

3. **优先验证**：
   - 探针式共情效果
   - 双模式切换体验
   - 记忆系统准确性

## 资源需求

- **开发环境**：已配置完成
- **API密钥**：已硬编码
- **代理设置**：已配置
- **测试用户**：需要招募5-10名

## 风险管理

1. **技术风险**
   - Gemini API稳定性
   - thinking模式兼容性
   - 性能瓶颈

2. **用户风险**
   - 隐私担忧
   - 使用门槛
   - 情感依赖

3. **缓解措施**
   - 多模型备选方案
   - 清晰的隐私说明
   - 使用引导教程

---

**让每一个孤独的灵魂都有回声** 🌊