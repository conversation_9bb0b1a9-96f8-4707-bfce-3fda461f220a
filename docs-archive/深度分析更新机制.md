# 深度分析更新机制说明

## 🎯 设计理念

深度分析系统采用"增量更新"而非"完整替换"的方式，输出自然语言的更新指令，确保：
- 保持数据的连续性
- 避免覆盖已有信息
- 使用用户易懂的自然语言
- 只更新真正有变化的部分

## 📊 输出结构

### 1. 分析报告 (analysisReport)
```json
{
  "analysisReport": {
    "summary": "这次对话的关键发现...",
    "emotionalPatterns": "观察到用户的情绪模式...",
    "cognitiveInsights": "认知层面的洞察...",
    "keyFindings": ["具体发现1", "具体发现2"],
    "recommendations": "给AI的后续对话建议"
  }
}
```

### 2. 更新指令 (updateInstructions)

#### 用户画像更新 (addToProfile)
只包含新发现的内容：
```json
{
  "addToProfile": {
    "cognitiveTraits": {
      "thinkingPattern": "发现用户倾向于理性分析问题"
    },
    "growthTrajectory": {
      "coreStruggles": ["工作压力大"],
      "strengths": ["自我反思能力强"]
    }
  }
}
```

#### 关键事件添加 (addEvent)
只记录真正重要的事件：
```json
{
  "addEvent": {
    "title": "职业转型焦虑",
    "timeframe": "最近",
    "impact": "让用户重新思考人生方向",
    "emotionalWeight": "high",
    "insights": ["对未来的不确定感", "渴望找到意义"],
    "connectedThemes": ["职业", "成长", "焦虑"]
  }
}
```

#### 要素索引更新 (addToIndex)
添加新标签和更新快速访问：
```json
{
  "addToIndex": {
    "emotionalTags": ["焦虑", "期待"],
    "conceptualTags": ["职业转型", "人生意义"],
    "updateQuickAccess": {
      "recentConcerns": ["工作压力", "未来方向", "个人成长"]
    }
  }
}
```

#### 对话归档 (archiveDialogue)
选择最有价值的对话片段：
```json
{
  "archiveDialogue": {
    "userSaid": "我不知道自己是否做了正确的选择",
    "aiResponse": "听起来你在为这个决定感到困扰...",
    "context": "用户正在经历职业转型期",
    "emotionalState": "迷茫、焦虑",
    "coreNeeds": ["被理解", "获得确定感"],
    "hiddenConcerns": ["害怕失败", "担心他人评价"],
    "aiStrategy": "探针式共情",
    "technique": "情绪验证+开放式提问",
    "significance": "展现了用户深层的不安全感",
    "contextTags": ["职业", "选择", "焦虑"]
  }
}
```

## 🔧 更新执行流程

### 1. 深度分析生成更新指令
```
对话累积 (7-8轮)
    ↓
AI 深度分析
    ↓
生成更新指令 (JSON)
    ↓
保存到文件系统
```

### 2. 客户端同步执行更新
```
定期检查更新 (30秒)
    ↓
获取待处理指令
    ↓
MemoryUpdater 执行
    ↓
增量更新 IndexedDB
```

### 3. 增量更新逻辑

#### 用户画像
- **字符串字段**：直接覆盖
- **数组字段**：追加并去重
- **保留历史**：不删除已有内容

#### 标签系统
- **合并新旧标签**：避免重复
- **数量限制**：每类标签最多20个
- **保留最新**：超出限制时删除最旧的

## 📝 实现细节

### 文件位置
- **类型定义**: `types/analysis.ts`
- **更新执行器**: `lib/services/memory-updater.ts`
- **提示词**: `lib/prompts/deep-analysis.ts`

### 关键函数
```typescript
// 执行更新
memoryUpdater.executeMemoryUpdates(instructions)

// 增量更新用户画像
updateProfile(instruction)

// 合并标签
mergeAndLimitTags(existing, newTags, limit)
```

## ⚠️ 注意事项

1. **自然语言优先**：所有描述使用用户能理解的语言
2. **谨慎更新**：只在有充分证据时更新
3. **保持连续性**：新数据要与历史数据保持一致
4. **避免过度**：不要频繁更新同一内容

## 🚀 使用示例

当用户表达工作压力时，系统可能生成：

```json
{
  "analysisReport": {
    "summary": "用户正经历职业转型带来的焦虑，需要情感支持和方向引导"
  },
  "updateInstructions": {
    "addToProfile": {
      "cognitiveTraits": {
        "thinkingPattern": "在压力下倾向于过度分析"
      }
    },
    "addToIndex": {
      "emotionalTags": ["工作焦虑"],
      "updateQuickAccess": {
        "recentConcerns": ["职业发展", "工作压力", "未来规划"]
      }
    }
  }
}
```

这样的设计确保了系统能够逐步构建对用户的理解，而不是每次都重写全部数据。 