# SelfMirror 项目架构

## 目录结构

```
selfmirror2025/
├── app/
│   ├── api/
│   │   ├── chat/
│   │   │   └── route.ts          # 主对话API（已实现）
│   │   ├── analyze/
│   │   │   └── route.ts          # 深度分析API
│   │   └── memory/
│   │       └── route.ts          # 记忆管理API
│   ├── layout.tsx
│   └── page.tsx                  # 主页面
│
├── components/
│   ├── chat/
│   │   ├── ChatInterface.tsx    # 对话界面
│   │   ├── MessageBubble.tsx    # 消息气泡
│   │   └── InputArea.tsx        # 输入区域
│   ├── visualization/
│   │   ├── EmotionalLines.tsx   # 情绪线条
│   │   ├── SeaLevel.tsx         # 海平面效果
│   │   └── TimeAtmosphere.tsx   # 时间氛围
│   └── ui/                       # 基础UI组件
│
├── lib/
│   ├── services/
│   │   ├── conversation.ts      # 对话管理服务
│   │   ├── memory.ts           # 记忆管理服务
│   │   ├── analysis.ts         # 分析引擎服务
│   │   └── emotion.ts          # 情绪识别服务
│   ├── storage/
│   │   ├── indexed-db.ts       # IndexedDB封装
│   │   ├── file-manager.ts     # 四文件管理
│   │   └── cache.ts            # 缓存策略
│   ├── prompts/
│   │   ├── empathy.ts          # 探针式共情提示词
│   │   ├── analysis.ts         # 深度分析提示词
│   │   └── templates.ts        # 提示词模板
│   └── utils/
│       ├── emotion-detector.ts  # 情绪检测工具
│       └── context-builder.ts   # 上下文构建器
│
├── hooks/
│   ├── useConversation.ts      # 对话管理Hook
│   ├── useMemory.ts            # 记忆管理Hook
│   └── useEmotion.ts           # 情绪状态Hook
│
├── stores/
│   ├── conversation.ts         # 对话状态管理
│   ├── user.ts                 # 用户状态管理
│   └── ui.ts                   # UI状态管理
│
└── types/
    ├── memory.ts               # 记忆系统类型定义
    ├── conversation.ts         # 对话类型定义
    └── emotion.ts              # 情绪类型定义
```

## 核心模块说明

### 1. 对话管理器 (ConversationManager)
- 管理快响应和慢思考双模式
- 维护对话上下文窗口
- 协调API调用和响应处理

### 2. 记忆管理器 (MemoryManager)
- 四文件的CRUD操作
- 更新策略和冲突解决
- 数据完整性保证

### 3. 分析引擎 (AnalysisEngine)
- 情绪状态识别
- 认知模式分析
- 更新建议生成

### 4. 可视化系统 (VisualizationSystem)
- 情绪线条动画
- 海平面效果
- 时间氛围渲染

## 技术栈选择理由

- **Next.js 15.3.2**: App Router提供优秀的性能和开发体验
- **Zustand**: 轻量级状态管理，适合复杂状态同步
- **Framer Motion**: 流畅的动画效果实现
- **IndexedDB**: 大容量本地存储，支持复杂查询
- **React Query**: 优雅的异步状态管理
- **TypeScript**: 强类型保证代码质量和可维护性 