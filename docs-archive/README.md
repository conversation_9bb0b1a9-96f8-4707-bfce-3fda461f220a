# SelfMirror2025

基于 [assistant-ui](https://github.com/Yonom/assistant-ui) 构建的智能助手项目。

## 🚀 开始使用

### 环境变量配置

首先，在项目根目录创建 `.env.local` 文件，配置必要的环境变量：

```bash
# Google Gemini API 配置
GOOGLE_API_KEY=your_google_api_key_here

# 代理配置 (开发环境，如果需要)
PROXY_URL=http://127.0.0.1:7897

# 应用环境
NODE_ENV=development
```

### 启动项目

然后运行开发服务器：

```bash
npm run dev
# 或者
yarn dev
# 或者
pnpm dev
# 或者
bun dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看结果。

您可以通过修改 `app/page.tsx` 来编辑页面，文件保存后页面会自动更新。

## 🌐 网络配置

如果您在中国大陆，需要配置代理来访问 Google Gemini API。项目已经配置了代理支持。

- 默认代理地址：`http://127.0.0.1:7897`
- 可通过 `PROXY_URL` 环境变量自定义
- 详细的网络配置说明请参考项目根目录的配置指南文档

## 📁 项目结构

```
├── app/                 # Next.js App Router
│   ├── api/chat/       # Gemini API 路由
│   ├── layout.tsx      # 根布局
│   └── page.tsx        # 首页
├── components/         # React 组件
│   ├── ui/            # UI 基础组件
│   └── assistant-ui/  # 助手相关组件
├── lib/               # 工具库
│   ├── config.ts      # 配置常量
│   └── utils.ts       # 工具函数
└── hooks/             # 自定义 Hook
```

## ⚙️ 配置说明

项目配置已集中管理在 `lib/config.ts` 中，包括：
- Gemini API 配置
- 代理设置
- 调试日志控制
- 其他应用常量
