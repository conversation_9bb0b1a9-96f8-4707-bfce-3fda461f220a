# SelfMirror AI 模型工厂设计方案

**设计时间**: 2025-06-28  
**目标**: 统一 AI 模型调用，支持多模型切换  
**设计理念**: 解耦 + 灵活性 + 易扩展

---

## 🎯 设计目标

### 核心需求
1. **统一接口**: 提供 `generateTextStream()` 等统一方法
2. **轻松切换**: 通过配置切换 Gemini → 豆包 → 本地模型
3. **易于扩展**: 新增模型支持无需重构调用逻辑
4. **向后兼容**: 现有代码无缝迁移

### 当前痛点分析
- ❌ **硬编码配置**: API 密钥分散在各文件
- ❌ **重复代码**: 每个 API 路由都有相似的 AI 调用逻辑
- ❌ **难以切换**: 换模型需要修改多个文件
- ❌ **缺乏降级**: 模型失败时没有备用方案

---

## 🏗️ 架构设计

### 整体架构图

```mermaid
graph TB
    A[业务层] --> B[AI服务工厂]
    B --> C[统一AI接口]
    C --> D[Gemini模型]
    C --> E[豆包模型]
    C --> F[本地模型]
    
    G[配置管理] --> B
    H[健康检查] --> C
    I[降级策略] --> B
    
    subgraph "模型实现"
        D --> D1[Vercel AI SDK]
        E --> E1[豆包 SDK]
        F --> F1[本地推理]
    end
```

### 核心组件

1. **IAIModel 接口**: 统一的模型调用接口
2. **AIModelFactory**: 智能模型工厂
3. **具体模型实现**: GeminiModel, DoubaoModel, LocalModel
4. **配置管理**: 模型配置和切换逻辑
5. **兼容适配器**: 保持现有代码兼容

---

## 🔧 核心接口设计

### 统一 AI 接口

```typescript
// lib/ai/interfaces/IAIModel.ts
export interface IAIModel {
  // 🎯 核心方法：统一的文本生成
  generateText(prompt: string, options?: GenerateOptions): Promise<GenerateResult>;
  
  // 🌊 流式生成：实时对话
  generateTextStream(prompt: string, options?: GenerateOptions): AsyncIterable<StreamChunk>;
  
  // 💬 对话模式：多轮对话
  chat(messages: ChatMessage[], options?: ChatOptions): Promise<ChatResult>;
  
  // 🌊 流式对话：实时聊天
  chatStream(messages: ChatMessage[], options?: ChatOptions): AsyncIterable<ChatChunk>;
  
  // 🧠 文本分析：意义提取、情感分析等
  analyze(text: string, task: AnalysisTask, options?: AnalysisOptions): Promise<AnalysisResult>;
  
  // 📊 模型信息
  getModelInfo(): ModelInfo;
  
  // ❤️ 健康检查
  healthCheck(): Promise<HealthStatus>;
}
```

### 消息和选项类型

```typescript
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
}

export interface GenerateOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  systemPrompt?: string;
}

export interface GenerateResult {
  text: string;
  finishReason: 'stop' | 'length' | 'content_filter' | 'error';
  usage: TokenUsage;
  metadata?: Record<string, any>;
}

export interface StreamChunk {
  text: string;
  isComplete: boolean;
  usage?: TokenUsage;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost?: number; // 成本估算
}
```

---

## 🏭 AI 模型工厂

### 工厂核心逻辑

```typescript
// lib/ai/AIModelFactory.ts
export class AIModelFactory {
  private static instances: Map<string, IAIModel> = new Map();
  private static config: AIFactoryConfig;

  // 🎛️ 配置工厂
  static configure(config: AIFactoryConfig): void {
    this.config = config;
  }

  // 🏗️ 创建模型实例
  static async createModel(provider?: ModelProvider): Promise<IAIModel> {
    const targetProvider = provider || this.config.defaultProvider;
    const cacheKey = this.getCacheKey(targetProvider);

    // 检查缓存
    if (this.instances.has(cacheKey)) {
      const instance = this.instances.get(cacheKey)!;
      
      // 健康检查
      const health = await instance.healthCheck();
      if (health.isHealthy) {
        return instance;
      } else {
        console.warn(`⚠️ 模型 ${targetProvider} 健康检查失败，重新创建`);
        this.instances.delete(cacheKey);
      }
    }

    // 创建新实例
    const model = await this.createModelInstance(targetProvider);
    
    // 健康检查
    const health = await model.healthCheck();
    if (!health.isHealthy) {
      throw new AIModelError(`模型 ${targetProvider} 创建失败: ${health.error}`);
    }

    // 缓存实例
    this.instances.set(cacheKey, model);
    console.log(`✅ 模型 ${targetProvider} 创建成功`);
    
    return model;
  }

  // 🎯 智能模型选择（带降级）
  static async createSmartModel(): Promise<IAIModel> {
    const providers = [
      this.config.defaultProvider,
      ...this.config.fallbackProviders
    ];

    for (const provider of providers) {
      try {
        const model = await this.createModel(provider);
        console.log(`🎯 使用模型: ${provider}`);
        return model;
      } catch (error) {
        console.warn(`⚠️ 模型 ${provider} 不可用:`, error.message);
        continue;
      }
    }

    throw new AIModelError('所有模型都不可用');
  }

  // 🔧 便捷方法
  static async generateText(prompt: string, options?: GenerateOptions): Promise<GenerateResult> {
    const model = await this.createSmartModel();
    return model.generateText(prompt, options);
  }

  static async generateTextStream(prompt: string, options?: GenerateOptions): AsyncIterable<StreamChunk> {
    const model = await this.createSmartModel();
    return model.generateTextStream(prompt, options);
  }

  static async chat(messages: ChatMessage[], options?: ChatOptions): Promise<ChatResult> {
    const model = await this.createSmartModel();
    return model.chat(messages, options);
  }

  static async chatStream(messages: ChatMessage[], options?: ChatOptions): AsyncIterable<ChatChunk> {
    const model = await this.createSmartModel();
    return model.chatStream(messages, options);
  }
}
```

### 配置接口

```typescript
export interface AIFactoryConfig {
  defaultProvider: ModelProvider;
  fallbackProviders: ModelProvider[];
  models: {
    gemini: GeminiConfig;
    doubao: DoubaoConfig;
    local: LocalConfig;
  };
  cache: {
    enabled: boolean;
    ttl: number; // 缓存时间（毫秒）
  };
  healthCheck: {
    enabled: boolean;
    interval: number; // 检查间隔（毫秒）
  };
}

export type ModelProvider = 'gemini' | 'doubao' | 'local';

export interface GeminiConfig {
  apiKey: string;
  model: string;
  proxyUrl?: string;
  baseUrl?: string;
}

export interface DoubaoConfig {
  apiKey: string;
  model: string;
  baseUrl: string;
}

export interface LocalConfig {
  modelPath: string;
  device: 'cpu' | 'gpu';
  maxMemory?: number;
}
```

---

## 🎭 具体模型实现

### Gemini 模型实现

```typescript
// lib/ai/models/GeminiModel.ts
export class GeminiModel implements IAIModel {
  private client: GoogleGenerativeAI;
  private config: GeminiConfig;

  constructor(config: GeminiConfig) {
    this.config = config;
    this.client = new GoogleGenerativeAI({
      apiKey: config.apiKey,
      fetch: this.createCustomFetch()
    });
  }

  async generateText(prompt: string, options?: GenerateOptions): Promise<GenerateResult> {
    try {
      const model = this.client.getGenerativeModel({ model: this.config.model });
      
      const result = await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: this.buildGenerationConfig(options),
        systemInstruction: options?.systemPrompt
      });

      return this.formatResult(result);
    } catch (error) {
      throw new AIModelError('Gemini 文本生成失败', error);
    }
  }

  async *generateTextStream(prompt: string, options?: GenerateOptions): AsyncIterable<StreamChunk> {
    const model = this.client.getGenerativeModel({ model: this.config.model });
    
    const stream = await model.generateContentStream({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: this.buildGenerationConfig(options),
      systemInstruction: options?.systemPrompt
    });

    for await (const chunk of stream.stream) {
      yield this.formatStreamChunk(chunk);
    }
  }

  async chat(messages: ChatMessage[], options?: ChatOptions): Promise<ChatResult> {
    // 实现多轮对话逻辑
  }

  async *chatStream(messages: ChatMessage[], options?: ChatOptions): AsyncIterable<ChatChunk> {
    // 实现流式多轮对话
  }

  async analyze(text: string, task: AnalysisTask, options?: AnalysisOptions): Promise<AnalysisResult> {
    // 实现文本分析任务
  }

  getModelInfo(): ModelInfo {
    return {
      provider: 'gemini',
      model: this.config.model,
      capabilities: ['text', 'chat', 'stream', 'analysis'],
      maxTokens: 1000000,
      costPer1kTokens: 0.001
    };
  }

  async healthCheck(): Promise<HealthStatus> {
    try {
      const testResult = await this.generateText('Hello', { maxTokens: 10 });
      return {
        isHealthy: true,
        latency: Date.now(), // 实际应该测量延迟
        provider: 'gemini'
      };
    } catch (error) {
      return {
        isHealthy: false,
        error: error.message,
        provider: 'gemini'
      };
    }
  }

  // 私有方法
  private createCustomFetch() {
    // 复用现有的代理逻辑
    if (process.env.NODE_ENV === 'development' && this.config.proxyUrl) {
      const { ProxyAgent, fetch: undiciFetch } = require('undici');
      const proxyAgent = new ProxyAgent(this.config.proxyUrl);
      
      return async (url: string, options: any) => {
        // 注入 thinkingBudget: 0
        if (options.body && options.method === 'POST') {
          try {
            const body = JSON.parse(options.body);
            if (!body.generationConfig) body.generationConfig = {};
            body.generationConfig.thinkingConfig = { thinkingBudget: 0 };
            options.body = JSON.stringify(body);
          } catch (e) {
            console.error('⚠️ 无法修改请求体:', e);
          }
        }
        
        return undiciFetch(url, { ...options, dispatcher: proxyAgent });
      };
    }
    return fetch;
  }

  private buildGenerationConfig(options?: GenerateOptions) {
    return {
      temperature: options?.temperature ?? 0.7,
      maxOutputTokens: options?.maxTokens ?? 500,
      topP: options?.topP ?? 0.9,
      stopSequences: options?.stopSequences,
      thinkingConfig: { thinkingBudget: 0 }
    };
  }

  private formatResult(result: any): GenerateResult {
    return {
      text: result.response.text(),
      finishReason: this.mapFinishReason(result.response.candidates[0]?.finishReason),
      usage: {
        promptTokens: result.response.usageMetadata?.promptTokenCount || 0,
        completionTokens: result.response.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: result.response.usageMetadata?.totalTokenCount || 0
      }
    };
  }

  private formatStreamChunk(chunk: any): StreamChunk {
    return {
      text: chunk.text(),
      isComplete: false,
      usage: chunk.usageMetadata ? {
        promptTokens: chunk.usageMetadata.promptTokenCount || 0,
        completionTokens: chunk.usageMetadata.candidatesTokenCount || 0,
        totalTokens: chunk.usageMetadata.totalTokenCount || 0
      } : undefined
    };
  }

  private mapFinishReason(reason: string): 'stop' | 'length' | 'content_filter' | 'error' {
    switch (reason) {
      case 'STOP': return 'stop';
      case 'MAX_TOKENS': return 'length';
      case 'SAFETY': return 'content_filter';
      default: return 'error';
    }
  }
}
```

---

## 🔄 向后兼容策略

### 兼容适配器

```typescript
// lib/ai/adapters/LegacyAIAdapter.ts
export class LegacyAIAdapter {
  // 🔄 兼容现有的 streamText 调用
  static async streamText(options: {
    model: any;
    messages: any[];
    system?: string;
    temperature?: number;
    maxTokens?: number;
    onFinish?: (response: any) => void;
  }) {
    const aiOptions: GenerateOptions = {
      temperature: options.temperature,
      maxTokens: options.maxTokens,
      systemPrompt: options.system
    };

    // 转换消息格式
    const lastMessage = options.messages[options.messages.length - 1];
    const prompt = lastMessage?.content || '';

    // 使用新的 AI 工厂
    const stream = AIModelFactory.generateTextStream(prompt, aiOptions);
    
    // 返回兼容的流对象
    return {
      textStream: stream,
      onFinish: options.onFinish
    };
  }

  // 🔄 兼容现有的 generateText 调用
  static async generateText(options: {
    model: any;
    prompt: string;
    temperature?: number;
    maxTokens?: number;
  }) {
    const result = await AIModelFactory.generateText(options.prompt, {
      temperature: options.temperature,
      maxTokens: options.maxTokens
    });

    return {
      text: result.text,
      usage: result.usage,
      finishReason: result.finishReason
    };
  }
}
```

---

## 🎯 使用示例

### 新 API 使用方式

```typescript
// 1. 配置工厂
AIModelFactory.configure({
  defaultProvider: 'gemini',
  fallbackProviders: ['doubao', 'local'],
  models: {
    gemini: {
      apiKey: process.env.GOOGLE_API_KEY!,
      model: 'gemini-2.5-flash-preview-05-20',
      proxyUrl: process.env.PROXY_URL
    },
    doubao: {
      apiKey: process.env.DOUBAO_API_KEY!,
      model: 'doubao-pro-4k',
      baseUrl: 'https://ark.cn-beijing.volces.com/api/v3'
    },
    local: {
      modelPath: './models/qwen-7b.gguf',
      device: 'cpu'
    }
  },
  cache: { enabled: true, ttl: 300000 },
  healthCheck: { enabled: true, interval: 60000 }
});

// 2. 简单使用
const result = await AIModelFactory.generateText('你好，请介绍一下自己');
console.log(result.text);

// 3. 流式使用
for await (const chunk of AIModelFactory.generateTextStream('写一首诗')) {
  process.stdout.write(chunk.text);
}

// 4. 对话使用
const chatResult = await AIModelFactory.chat([
  { role: 'user', content: '你好' },
  { role: 'assistant', content: '你好！有什么可以帮助你的吗？' },
  { role: 'user', content: '请介绍一下你自己' }
]);
```

### 现有代码兼容

```typescript
// 现有代码无需修改，自动使用新的 AI 工厂
import { streamText } from 'ai';

// 这个调用会被适配器拦截并使用新的 AI 工厂
const result = streamText({
  model: google(MODEL_NAME),
  messages,
  system: context.systemPrompt,
  temperature: 0.7,
  maxTokens: 500
});
```

---

## 📊 迁移计划

### 阶段一：基础设施（1-2 天）
1. 实现核心接口和工厂
2. 实现 Gemini 模型适配
3. 创建兼容适配器

### 阶段二：功能验证（1 天）
1. 测试现有 API 路由兼容性
2. 验证调试控制台功能
3. 性能基准测试

### 阶段三：扩展模型（2-3 天）
1. 实现豆包模型适配
2. 实现本地模型适配
3. 完善降级策略

### 阶段四：优化完善（1 天）
1. 性能优化
2. 错误处理完善
3. 文档和测试

---

**设计完成**: 2025-06-28  
**预计实现时间**: 5-7 天  
**兼容性**: 100% 向后兼容  
**扩展性**: 支持任意新模型
