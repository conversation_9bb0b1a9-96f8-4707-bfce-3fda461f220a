# SelfMirror 双向量数据库架构 - Phase 1 实现

## 🎯 架构概览

SelfMirror的革命性"Meaning RAG"系统现已实现双向量数据库架构，从传统的embedding驱动转向LLM智能推理驱动的检索系统。

### 核心架构转变

**从**: 传统embedding驱动RAG，语义相似性作为主要智能
**到**: LLM驱动智能检索系统，大语言模型作为"大脑"进行推理规划，向量数据库作为"手脚"执行检索任务

## 🏗️ Phase 1: 双向量数据库架构

### 1. 双向量存储设计

#### Hot Store - 每日洞察数据库
- **用途**: 存储每日洞察日志和衍生的父子块
- **特性**: 小数据量，高频读写操作
- **核心功能**:
  - 温度衰减机制 (每天衰减10%)
  - LRU淘汰策略 (最大1000个向量)
  - 快速访问优化
  - 自动维护任务

#### Cold Store - 长期记忆归档
- **用途**: 存储完整对话历史和用户导入的大文档
- **特性**: 大数据量，批量写入，深度搜索
- **核心功能**:
  - 层次化索引 (IVF算法)
  - 数据压缩 (gzip)
  - 深度搜索能力
  - 存储优化

### 2. 全局唯一ID系统

#### ID格式规范
- **基础格式**: `YYYYMMDD-T###` (例: `20250701-T023`)
- **衍生格式**: `YYYYMMDD-T###-D###` (例: `20250701-T023-D001`)
- **覆盖范围**: 所有用户输入消息和衍生子块
- **元数据集成**: 完整的数据血缘追踪

#### 功能特性
- 自动生成用户输入ID
- 衍生内容ID管理
- 完整的来源链追踪
- 30天数据保留策略

### 3. 智能数据路由

#### 路由策略
```typescript
// 数据类型决定存储位置
if (daysDiff <= 7 && (type === 'daily_insight_hot' || type === 'dialogue_history')) {
  return 'hot'; // 存储到Hot Store
} else {
  return 'cold'; // 存储到Cold Store
}
```

#### 搜索策略
- **adaptive**: 自适应搜索 (默认)
- **hot_first**: 优先搜索Hot Store
- **cold_first**: 优先搜索Cold Store  
- **parallel**: 并行搜索两个存储

### 4. 向后兼容性

#### 遗留适配器
- 完全兼容现有`vector-database.ts`接口
- 透明的双向量后端切换
- 无需修改现有代码
- 平滑的数据迁移支持

## 📁 文件结构

```
lib/services/vector-database/
├── interfaces.ts              # 核心接口定义
├── global-id-system.ts        # 全局ID管理系统
├── hot-store.ts              # Hot Store实现
├── cold-store.ts             # Cold Store实现
├── dual-vector-manager.ts    # 双向量管理器
├── legacy-adapter.ts         # 向后兼容适配器
├── performance-test.ts       # 性能测试工具
├── index.ts                  # 主入口文件
└── README.md                 # 本文档

lib/config/
└── dual-vector-config.ts     # 双向量配置管理

lib/services/
└── vector-database.ts        # 更新的遗留接口
```

## 🚀 使用方式

### 基础使用 (推荐)

```typescript
import { dualVectorSystem } from '@/lib/services/vector-database';

// 初始化系统
await dualVectorSystem.initialize();

// 添加向量
const chunkId = await dualVectorSystem.addVector(vector, metadata);

// 搜索向量
const results = await dualVectorSystem.searchVector(queryVector, {
  searchStrategy: 'adaptive',
  maxResults: 10
});

// 获取系统统计
const stats = await dualVectorSystem.getStats();
```

### 高级使用

```typescript
import { createDualVectorManager } from '@/lib/services/vector-database';

// 创建独立实例
const manager = createDualVectorManager(hotConfig, coldConfig);
await manager.initialize();

// 手动数据迁移
await manager.promoteToHot(['chunk1', 'chunk2']);
await manager.demoteToCold(['chunk3', 'chunk4']);

// 执行维护
const maintenanceResult = await manager.performMaintenance();
```

### 向后兼容使用

```typescript
import { vectorDatabase } from '@/lib/services/vector-database';

// 现有代码无需修改，自动使用双向量后端
await vectorDatabase.initialize();
const results = await vectorDatabase.search(queryVector, 10);
```

## ⚙️ 配置管理

### 环境配置

```typescript
// 开发环境
- Hot Store: 500个向量
- 维护间隔: 1小时
- 搜索超时: 5秒

// 生产环境  
- Hot Store: 2000个向量
- 维护间隔: 24小时
- 搜索超时: 15秒

// 测试环境
- Hot Store: 100个向量
- 自动迁移: 关闭
- 压缩: 关闭
```

### 自定义配置

```typescript
import { dualVectorConfigManager } from '@/lib/config/dual-vector-config';

// 更新配置
dualVectorConfigManager.updateConfig({
  global: {
    migrationThreshold: 14 // 14天后迁移
  },
  searchStrategy: {
    defaultStrategy: 'parallel'
  }
});
```

## 📊 性能测试

### 运行性能测试

```typescript
import { runQuickPerformanceTest } from '@/lib/services/vector-database/performance-test';

// 快速测试 (100个向量)
const result = await runQuickPerformanceTest();
console.log(`添加性能: ${result.addPerformance.throughput} 向量/秒`);
console.log(`搜索性能: ${result.searchPerformance.throughput} 查询/秒`);
```

### 性能指标

- **添加性能**: 向量/秒
- **搜索性能**: 查询/秒  
- **内存使用**: MB
- **数据分布**: Hot/Cold比例
- **系统健康**: 健康状态评估

## 🔧 维护操作

### 自动维护
- 每24小时执行一次 (生产环境)
- 温度衰减处理
- LRU淘汰执行
- 索引优化
- 存储压缩

### 手动维护

```typescript
// 执行完整维护
const result = await dualVectorSystem.performMaintenance();

// 归档旧数据
const archivedCount = await manager.archiveOldData(new Date('2025-06-01'));

// 重建索引
await vectorDatabase.rebuild();
```

## 🎯 Phase 2 预览

Phase 1完成了双向量数据库基础架构，Phase 2将实现：

1. **三引擎协作工作流**
   - Navigator Engine (低成本模型)
   - Lightweight Context Retriever  
   - Integration Generator (高级模型)

2. **智能缓存层**
   - 检索指令比较
   - 缓存命中优化
   - 来源归属标记

3. **API端点重构**
   - `/api/chat`端点并行引擎架构
   - 上下文包装优化
   - 响应生成改进

## 📈 成功指标

Phase 1已实现的关键指标：

✅ **双向量数据库运行** - Hot Store + Cold Store架构
✅ **全局ID系统** - YYYYMMDD-T###格式追踪
✅ **向后兼容性** - 现有功能无缝运行  
✅ **智能数据路由** - 自动Hot/Cold分配
✅ **性能优化** - 温度衰减和LRU淘汰
✅ **配置管理** - 环境特定配置支持

## 🚨 注意事项

1. **数据迁移**: 首次启动时会自动检测并迁移现有数据
2. **性能监控**: 建议定期运行性能测试以监控系统状态
3. **配置调优**: 根据实际使用情况调整Hot Store大小和迁移阈值
4. **错误处理**: 系统具备完善的错误恢复机制，但建议监控日志

---

**Phase 1 完成状态**: ✅ 已完成
**下一步**: Phase 2 - 三引擎协作工作流实现
