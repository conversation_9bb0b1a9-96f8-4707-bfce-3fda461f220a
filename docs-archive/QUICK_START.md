# SelfMirror 快速开始指南

## 立即可做的任务

### 1. 修复现有的chat路由以支持探针式共情

更新 `app/api/chat/route.ts`，整合快响应模式：

```typescript
// 在现有的route.ts中添加
import { getQuickResponsePrompt } from '@/lib/prompts/empathy';
import { dbManager } from '@/lib/storage/indexed-db';

// 在消息处理前添加
const memory = await dbManager.getMemorySystem();
const recentMessages = await dbManager.getArchivedConversations(8);

// 使用探针式共情提示词
const empathyPrompt = getQuickResponsePrompt(
  recentMessages,
  memory.userProfile,
  '未检测到明确情绪' // 暂时硬编码，后续集成情绪检测
);
```

### 2. 创建基础UI界面

创建 `components/chat/ChatInterface.tsx`：

```tsx
'use client';

import { useState } from 'react';
import { conversationManager } from '@/lib/services/conversation';

export function ChatInterface() {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleSend = async () => {
    if (!input.trim() || isLoading) return;
    
    setIsLoading(true);
    try {
      const response = await conversationManager.processUserMessage(input);
      setMessages(conversationManager.getMessages());
      setInput('');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-b from-blue-900 to-blue-950">
      {/* 消息区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((msg) => (
          <div key={msg.id} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-md p-3 rounded-lg ${
              msg.role === 'user' 
                ? 'bg-blue-600 text-white' 
                : 'bg-white/10 text-white backdrop-blur'
            }`}>
              {msg.content}
            </div>
          </div>
        ))}
      </div>
      
      {/* 输入区域 */}
      <div className="p-4 border-t border-white/10">
        <div className="flex gap-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSend()}
            placeholder="告诉我你的感受..."
            className="flex-1 p-3 rounded-lg bg-white/10 text-white placeholder-white/50 backdrop-blur"
          />
          <button
            onClick={handleSend}
            disabled={isLoading}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            {isLoading ? '思考中...' : '发送'}
          </button>
        </div>
      </div>
    </div>
  );
}
```

### 3. 更新主页面

更新 `app/page.tsx`：

```tsx
import { ChatInterface } from '@/components/chat/ChatInterface';

export default function Home() {
  return <ChatInterface />;
}
```

### 4. 初始化对话管理器

创建 `app/providers.tsx`：

```tsx
'use client';

import { useEffect } from 'react';
import { conversationManager } from '@/lib/services/conversation';

export function Providers({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // 初始化对话管理器
    conversationManager.initialize();
  }, []);

  return <>{children}</>;
}
```

更新 `app/layout.tsx` 使用Provider：

```tsx
import { Providers } from './providers';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh">
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
```

### 5. 安装必需的依赖

```bash
npm install framer-motion zustand
```

## 测试步骤

1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 打开浏览器访问 `http://localhost:3000`

3. 测试对话功能：
   - 输入："我感觉很累"
   - 期待回应：探针式共情的温柔询问
   - 观察是否有情绪识别和替代表达

## 常见问题解决

### 1. TypeScript类型错误
- 暂时使用 `as any` 绕过复杂类型
- 后续统一修复类型定义

### 2. API调用失败
- 检查代理设置是否正确
- 确认API密钥有效
- 查看控制台错误信息

### 3. IndexedDB初始化失败
- 清理浏览器缓存
- 使用隐私模式测试
- 检查浏览器兼容性

## 下一步优化

1. **添加情绪可视化**
   - 集成Framer Motion
   - 实现基础线条动画
   - 添加海平面效果

2. **完善双模式**
   - 实现模式切换逻辑
   - 优化上下文管理
   - 添加分析触发机制

3. **提升用户体验**
   - 添加加载状态
   - 实现错误处理
   - 优化响应速度

---

**立即开始，让每个灵魂都有回声！** 🌊 