# 极简记忆系统说明

## 系统概述

这是一个极简的AI陪伴系统，使用纯Markdown文件存储记忆，支持两种模式：
- **日常对话**：快速响应，基于记忆提供温暖陪伴
- **每日沉淀**：深度思考，将洞察追加到记忆文件

## 核心理念
- 使用纯Markdown文件存储，无JSON验证
- 自然语言记录，人类可读可编辑
- 最小化自动更新，避免破坏手动维护的内容

## 文件结构

```
memory/
├── 用户画像.md          # 手动维护的用户核心信息
├── 关键事件.md          # 手动维护的重要事件记录
├── 每日洞察今天.md      # 自动更新 - 今天的对话洞察（热日志）
├── 每日洞察归档.md      # 手动归档 - 历史洞察记录（冷日志）
├── 对话历史.md          # 自动更新 - 所有对话的完整记录
└── prompts/            # 系统提示词（Markdown格式）
    ├── 系统提示词.md
    ├── 日常对话模式.md
    └── 每日沉淀模式.md
```

## 两种模式

### 日常对话模式
- **触发**：每次用户发送消息
- **处理**：快速响应（thinkingBudget: 0）
- **上下文**（从不变到动态的顺序）：
  1. 系统提示词
  2. 日常对话模式提示词
  3. 用户画像
  4. 关键事件
  5. 历史洞察（全部）
  6. 每日洞察今天（全部）
  7. 最近对话（正常6轮，沉淀时8轮）
- **输出**：直接回复用户

### 每日沉淀模式
- **触发**：每6轮对话自动触发
- **处理**：深度思考（thinkingBudget: 8192）
- **上下文**（从不变到动态的顺序）：
  1. 系统提示词
  2. 每日沉淀模式提示词
  3. 用户画像
  4. 关键事件
  5. 历史洞察（全部）
  6. 每日洞察今天（全部）
  7. 待沉淀的对话内容（6轮）
- **输出**：追加到 `每日洞察今天.md`

## 使用方法

### 手动维护记忆

1. **编辑用户画像**：
   - 打开 `memory/用户画像.md`
   - 使用自然语言描述用户特征
   - 保存文件

2. **记录关键事件**：
   - 打开 `memory/关键事件.md`
   - 添加重要的生活事件
   - 保存文件

3. **查看每日洞察**：
   - 打开 `memory/每日洞察今天.md` 查看今天的洞察
   - 打开 `memory/每日洞察归档.md` 查看历史洞察
   - 系统自动生成，包含时间戳

4. **归档今天的洞察**：
   - 访问 `/debug` 页面
   - 点击"手动归档当天洞察"按钮
   - 或调用 `POST /api/memory/archive`

### 自定义提示词

所有提示词都在 `memory/prompts/` 目录下，可以直接编辑：
- `系统提示词.md`：定义AI的基本人格和原则
- `日常对话模式.md`：控制日常对话的风格
- `每日沉淀模式.md`：指导如何进行深度思考

## 技术特点

1. **极简设计**：
   - 无JSON格式要求
   - 无复杂验证逻辑
   - 纯文本追加操作

2. **自然语言**：
   - 所有记忆都是自然语言
   - 便于人工阅读和编辑
   - AI理解更自然

3. **灵活扩展**：
   - 可随时添加新的记忆文件
   - 提示词可自由修改
   - 无需修改代码

4. **性能优化**：
   - 热/冷日志分离架构
   - 动态上下文窗口调整
   - 防止信息丢失机制

## API接口

### 日常对话
```
POST /api/chat
{
  "messages": [...]
}
```

### 每日沉淀（自动触发）
```
POST /api/daily-reflection
{
  "dialogueBlocks": [...]
}
```

### 归档洞察
```
POST /api/memory/archive
```

## 注意事项

1. 记忆文件使用UTF-8编码
2. 每日洞察会自动添加时间戳
3. 建议定期备份 `memory/` 目录
4. 可以手动清理过旧的每日洞察内容 