# SelfMirror - 让每个灵魂都有回声 🌊

## 🚀 快速开始

应用已经成功启动！现在可以访问：

**http://localhost:3000**

## 🎯 测试功能

### 1. 基础对话测试
试试输入以下内容，体验探针式共情：
- "我感觉很累"
- "最近压力很大"  
- "有点迷茫，不知道该做什么"
- "今天很开心，想分享一下"

### 2. 情绪识别测试
观察系统是否能够：
- ✅ 检测到你的情绪状态
- ✅ 给出温柔的探索性回应
- ✅ 提供替代表达帮助你说出内心的话

### 3. 预期效果

**输入**: "我感觉很累"

**预期回应类似于**:
- "听起来这种累让你很不舒服。这种累，是像身体被掏空的那种，还是更像心里装了太多事情的那种？"
- "我能感受到你的疲惫。这种累是最近才有的，还是已经持续了一段时间？"

## 🛠️ 当前功能状态

### ✅ 已实现
- 基础对话界面
- 探针式共情提示词系统
- 情绪检测服务
- IndexedDB 本地存储
- 快响应模式（thinkingBudget=0）
- 美观的深夜主题UI

### 🚧 开发中
- 情绪可视化（线条动画）
- 海平面效果
- 深度分析模式
- 四文件系统完整集成
- 对话历史记录展示

## 🐛 已知问题

1. **首次使用**：由于数据库为空，可能没有历史记忆
2. **响应速度**：受代理和网络影响可能有延迟
3. **情绪检测**：目前基于关键词，准确度有限

## 📝 开发调试

查看控制台日志了解系统运行状态：
- 🚀 初始化信息
- 🎭 情绪检测结果
- 📨 API请求详情
- ✅ thinking模式状态

## 🔒 隐私保护

- 所有对话数据仅存储在浏览器本地（IndexedDB）
- 不会上传任何个人信息到服务器
- 可以随时清空浏览器数据来删除所有记录

## 🎨 下一步计划

1. **视觉增强**
   - 添加情绪线条动画
   - 实现海平面镜像效果
   - 时间氛围背景变化

2. **功能完善**
   - 深度分析模式集成
   - 用户画像构建
   - 对话存档与检索

3. **体验优化**
   - 流式响应显示
   - 更自然的打字效果
   - 移动端适配

---

**立即体验：http://localhost:3000**

让 SelfMirror 成为你的认知伙伴，陪伴你探索内心世界 💙 