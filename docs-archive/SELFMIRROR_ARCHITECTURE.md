# SelfMirror 架构实现总结

## 🏗️ 核心架构

### 1. UI布局
- **布局方式**：使用 flex 布局，输入框固定在底部
- **样式设计**：深蓝渐变背景，半透明对话气泡
- **交互体验**：流式响应，加载动画，自动滚动

### 2. API设计
```typescript
// app/api/chat/route.ts
- 使用 nodejs runtime 支持 undici
- 硬编码 API key 和代理配置
- AI SDK + 自定义 Fetch（经过验证的方案）
- 通过 undici ProxyAgent 支持代理
- 自动注入 thinkingBudget=0
- 保持流式输出
```

### 3. 对话管理
```typescript
// lib/services/conversation.ts
- 维护 8 条消息的上下文窗口
- 快速响应模式处理前端对话
- 7-8 轮对话后触发后台分析
- 不阻塞用户交互
```

### 4. 数据存储
```typescript
// lib/storage/indexed-db.ts
- 四文件系统完整实现
- 客户端 IndexedDB 存储
- 支持增量更新
```

## 🚀 关键特性

### 快速响应模式
- **thinkingBudget**: 0（关闭思考模式）
- **响应时间**: < 2秒
- **上下文**: 最近 8 条消息
- **温度**: 0.7（自然对话）

### 慢速分析模式（后台）
- **触发时机**: 每 7-8 轮对话
- **处理方式**: 异步后台执行
- **更新内容**: 用户画像、关键事件、要素索引
- **不影响**: 前端对话体验

## 🔧 技术决策

### 1. 移除的功能
- ❌ 情绪检测的后端集成（仅用于 UI）
- ❌ 提示词设计（用户自行管理）
- ❌ 环境变量配置（直接硬编码）

### 2. 保留的核心
- ✅ 双模式架构（快速+分析）
- ✅ 四文件记忆系统
- ✅ 流式响应体验
- ✅ 本地数据存储

### 3. 代理支持
```bash
# package.json 中配置
npm run dev # 自动使用代理
npm run dev:no-proxy # 不使用代理
```

## 📁 文件结构

```
app/
├── api/
│   ├── chat/route.ts      # 快速响应 API
│   └── analyze/route.ts   # 深度分析 API
components/
├── chat/
│   └── ChatInterface.tsx  # 主对话界面
lib/
├── services/
│   └── conversation.ts    # 对话管理服务
├── storage/
│   └── indexed-db.ts      # 数据持久化
└── prompts/
    ├── empathy.ts         # 提示词架构（接口）
    └── system-prompts.ts  # 系统提示词配置
```

## 📝 提示词管理

所有提示词集中在以下位置管理：
- **PROMPTS.md** - 提示词文档，方便直接编辑
- **lib/prompts/system-prompts.ts** - 代码中的提示词配置

修改方式：
1. 直接编辑 `PROMPTS.md` 中的提示词
2. 复制到 `system-prompts.ts` 对应位置
3. 或在API调用时传入 `system` 参数覆盖

## 🎯 使用说明

### 1. 启动应用
```bash
npm run dev
# 访问 http://localhost:3000
```

### 2. 提示词配置
用户需要自行实现提示词系统，可以：
- 在 API 调用时传入 `system` 参数
- 基于 `PromptTemplate` 接口扩展
- 使用 `PromptContextBuilder` 构建上下文

### 3. 数据管理
- 所有数据存储在浏览器本地
- 可通过 `dbManager` 访问四文件系统
- 支持导出和备份功能

## 🔒 隐私保护

- **本地存储**：所有对话数据仅存在客户端
- **无数据上传**：不会上传到任何服务器
- **用户可控**：可随时清除浏览器数据

## 🚧 待优化项

1. **向量检索**：第四文件（对话存档）已预留向量化支持
2. **情绪可视化**：UI 线条动画系统
3. **性能优化**：大量对话时的内存管理
4. **错误处理**：更完善的异常处理机制

---

**核心理念**：架构服务于产品，让每个孤独的灵魂都有回声 🌊 