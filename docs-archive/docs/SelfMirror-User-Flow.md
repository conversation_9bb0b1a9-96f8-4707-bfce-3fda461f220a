# SelfMirror 用户流程与数据流分析

## 📖 概述

本文档详细分析 SelfMirror 的用户使用流程、数据流向、上下文管理机制和双模式运行逻辑。

---

## 1. 用户使用流程分析

### 🚀 应用启动流程

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant NextJS
    participant Config
    participant Memory
    participant AI
    
    User->>Browser: 访问 localhost:3001
    Browser->>NextJS: GET /
    NextJS->>Config: 加载配置
    Config-->>NextJS: 返回配置
    NextJS->>Memory: 初始化记忆系统
    Memory-->>NextJS: 返回状态
    NextJS->>AI: 初始化 AI 路由器
    AI-->>NextJS: 返回状态
    NextJS-->>Browser: 渲染主界面
    Browser-->>User: 显示聊天界面
```

#### 启动时序详解

```typescript
// 应用启动流程
export default function RootLayout({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // 1. 初始化配置系统
    initializeConfig()
    
    // 2. 加载用户偏好设置
    loadUserPreferences()
    
    // 3. 初始化 AI 路由器
    initializeAIRouter()
    
    // 4. 恢复上次会话状态
    restoreSessionState()
  }, [])
  
  return (
    <html lang="zh-CN" className="dark">
      <body className={inter.className}>
        <div className="min-h-screen bg-background text-foreground">
          {children}
        </div>
      </body>
    </html>
  )
}
```

### 💬 聊天交互流程

#### 完整对话流程
```mermaid
sequenceDiagram
    participant User
    participant ChatInterface
    participant MessageInput
    participant API
    participant AIRouter
    participant AIProvider
    participant Memory
    
    User->>MessageInput: 输入消息
    MessageInput->>ChatInterface: 触发 onSend
    ChatInterface->>API: POST /api/chat
    API->>AIRouter: 路由到当前模型
    AIRouter->>AIProvider: 调用 generateStream
    AIProvider-->>AIRouter: 流式响应
    AIRouter-->>API: 转发流式数据
    API-->>ChatInterface: Server-Sent Events
    ChatInterface->>Memory: 保存对话
    ChatInterface->>User: 显示完整响应
```

#### 消息处理详细实现
```typescript
// 聊天界面消息处理
export function ChatInterface() {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  const sendMessage = async (content: string) => {
    // 1. 添加用户消息
    const userMessage: ChatMessage = {
      id: generateId(),
      content,
      role: 'user',
      timestamp: new Date()
    }
    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)
    
    try {
      // 2. 创建助手消息占位符
      const assistantMessage: ChatMessage = {
        id: generateId(),
        content: '',
        role: 'assistant',
        timestamp: new Date(),
        isStreaming: true
      }
      setMessages(prev => [...prev, assistantMessage])
      
      // 3. 发送流式请求
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          scenario: 'chat'
        })
      })
      
      // 4. 处理流式响应
      await handleStreamResponse(response, assistantMessage.id)
      
      // 5. 保存完整对话到记忆系统
      await saveConversationToMemory([...messages, userMessage, assistantMessage])
      
    } catch (error) {
      console.error('发送消息失败:', error)
      // 错误处理逻辑
    } finally {
      setIsLoading(false)
    }
  }
  
  const handleStreamResponse = async (response: Response, messageId: string) => {
    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    
    while (true) {
      const { done, value } = await reader.read()
      if (done) break
      
      const chunk = decoder.decode(value)
      const lines = chunk.split('\n')
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6)
          if (data === '[DONE]') {
            // 流式响应结束
            setMessages(prev => prev.map(msg => 
              msg.id === messageId 
                ? { ...msg, isStreaming: false }
                : msg
            ))
            return
          }
          
          try {
            const parsed = JSON.parse(data)
            // 更新消息内容
            setMessages(prev => prev.map(msg => 
              msg.id === messageId 
                ? { ...msg, content: msg.content + parsed.content }
                : msg
            ))
          } catch (error) {
            console.error('解析流式数据失败:', error)
          }
        }
      }
    }
  }
}
```

### 🔧 调试控制台使用流程

#### 模型切换流程
```mermaid
sequenceDiagram
    participant User
    participant DebugConsole
    participant AIModelManager
    participant SwitchAPI
    participant AIRouter
    participant Config
    
    User->>DebugConsole: 访问 /debug-console
    DebugConsole->>AIModelManager: 加载模型状态
    User->>AIModelManager: 点击切换模型
    AIModelManager->>SwitchAPI: POST /api/debug/ai-model/switch
    SwitchAPI->>AIRouter: 执行模型切换
    AIRouter->>Config: 更新配置
    Config-->>AIRouter: 确认更新
    AIRouter-->>SwitchAPI: 返回切换结果
    SwitchAPI-->>AIModelManager: 返回成功状态
    AIModelManager->>User: 显示切换成功
```

#### 配置管理流程
```typescript
// 配置管理流程
export function SimpleConfigManager() {
  const [config, setConfig] = useState<SimpleConfig>(defaultConfig)
  const [hasChanges, setHasChanges] = useState(false)
  
  // 配置变更处理
  const updateModelConfig = (provider: 'gemini' | 'doubao', scenario: string, modelName: string) => {
    setConfig(prev => ({
      ...prev,
      ai: {
        ...prev.ai,
        providers: {
          ...prev.ai.providers,
          [provider]: {
            ...prev.ai.providers[provider],
            models: {
              ...prev.ai.providers[provider].models,
              [scenario]: modelName
            }
          }
        }
      }
    }))
    setHasChanges(true)
    validateConfig() // 实时验证
  }
  
  // 保存配置
  const saveConfig = async () => {
    try {
      const response = await fetch('/api/debug/config/simple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ config })
      })
      
      if (response.ok) {
        setHasChanges(false)
        setLastSaved(new Date())
        console.log('✅ 配置保存成功')
      }
    } catch (error) {
      console.error('❌ 保存配置失败:', error)
    }
  }
}
```

---

## 2. 数据流和状态管理

### 📊 数据流架构

```mermaid
graph TB
    subgraph "前端状态层"
        ReactState[React State]
        LocalStorage[Local Storage]
        SessionStorage[Session Storage]
    end
    
    subgraph "API 层"
        ChatAPI[Chat API]
        ConfigAPI[Config API]
        MemoryAPI[Memory API]
        RAGAPI[RAG API]
    end
    
    subgraph "业务逻辑层"
        AIRouter[AI Router]
        ConfigManager[Config Manager]
        MemorySystem[Memory System]
        RAGSystem[RAG System]
    end
    
    subgraph "数据存储层"
        FileSystem[File System]
        ConfigFiles[Config Files]
        ConversationFiles[Conversation Files]
        DocumentFiles[Document Files]
    end
    
    ReactState --> ChatAPI
    ReactState --> ConfigAPI
    LocalStorage --> ReactState
    
    ChatAPI --> AIRouter
    ConfigAPI --> ConfigManager
    MemoryAPI --> MemorySystem
    RAGAPI --> RAGSystem
    
    ConfigManager --> ConfigFiles
    MemorySystem --> ConversationFiles
    MemorySystem --> DocumentFiles
    RAGSystem --> DocumentFiles
```

### 🔄 状态管理模式

#### React 状态管理
```typescript
// 全局状态管理 Hook
export function useGlobalState() {
  const [appState, setAppState] = useState<AppState>({
    currentModel: 'gemini',
    isLoading: false,
    conversations: [],
    config: defaultConfig
  })
  
  // 模型切换
  const switchModel = useCallback(async (provider: 'gemini' | 'doubao') => {
    setAppState(prev => ({ ...prev, isLoading: true }))
    
    try {
      const response = await fetch('/api/debug/ai-model/switch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      })
      
      if (response.ok) {
        setAppState(prev => ({ 
          ...prev, 
          currentModel: provider,
          isLoading: false 
        }))
      }
    } catch (error) {
      console.error('切换模型失败:', error)
      setAppState(prev => ({ ...prev, isLoading: false }))
    }
  }, [])
  
  // 添加对话
  const addConversation = useCallback((conversation: Conversation) => {
    setAppState(prev => ({
      ...prev,
      conversations: [...prev.conversations, conversation]
    }))
  }, [])
  
  return {
    appState,
    switchModel,
    addConversation
  }
}
```

#### 本地存储策略
```typescript
// 本地存储管理
class LocalStorageManager {
  private static readonly KEYS = {
    USER_PREFERENCES: 'selfmirror_user_preferences',
    SESSION_STATE: 'selfmirror_session_state',
    CHAT_HISTORY: 'selfmirror_chat_history'
  }
  
  static saveUserPreferences(preferences: UserPreferences): void {
    localStorage.setItem(
      this.KEYS.USER_PREFERENCES, 
      JSON.stringify(preferences)
    )
  }
  
  static loadUserPreferences(): UserPreferences | null {
    const stored = localStorage.getItem(this.KEYS.USER_PREFERENCES)
    return stored ? JSON.parse(stored) : null
  }
  
  static saveSessionState(state: SessionState): void {
    sessionStorage.setItem(
      this.KEYS.SESSION_STATE,
      JSON.stringify(state)
    )
  }
  
  static loadSessionState(): SessionState | null {
    const stored = sessionStorage.getItem(this.KEYS.SESSION_STATE)
    return stored ? JSON.parse(stored) : null
  }
}
```

---

## 3. 上下文管理机制

### 🧠 对话上下文管理

#### 上下文存储结构
```typescript
// 对话上下文接口
interface ConversationContext {
  id: string
  messages: ChatMessage[]
  metadata: {
    startTime: Date
    lastActivity: Date
    model: string
    totalTokens: number
  }
  summary?: string
  tags?: string[]
}

// 上下文管理器
class ContextManager {
  private contexts: Map<string, ConversationContext> = new Map()
  private maxContexts: number = 10
  private maxMessagesPerContext: number = 50
  
  // 创建新上下文
  createContext(model: string): ConversationContext {
    const context: ConversationContext = {
      id: generateId(),
      messages: [],
      metadata: {
        startTime: new Date(),
        lastActivity: new Date(),
        model,
        totalTokens: 0
      }
    }
    
    this.contexts.set(context.id, context)
    this.cleanupOldContexts()
    
    return context
  }
  
  // 添加消息到上下文
  addMessage(contextId: string, message: ChatMessage): void {
    const context = this.contexts.get(contextId)
    if (!context) return
    
    context.messages.push(message)
    context.metadata.lastActivity = new Date()
    
    // 限制消息数量
    if (context.messages.length > this.maxMessagesPerContext) {
      context.messages = context.messages.slice(-this.maxMessagesPerContext)
    }
  }
  
  // 获取上下文消息
  getContextMessages(contextId: string, maxMessages?: number): ChatMessage[] {
    const context = this.contexts.get(contextId)
    if (!context) return []
    
    const messages = context.messages
    return maxMessages ? messages.slice(-maxMessages) : messages
  }
  
  // 清理旧上下文
  private cleanupOldContexts(): void {
    if (this.contexts.size <= this.maxContexts) return
    
    const sortedContexts = Array.from(this.contexts.entries())
      .sort(([, a], [, b]) => 
        a.metadata.lastActivity.getTime() - b.metadata.lastActivity.getTime()
      )
    
    // 删除最旧的上下文
    const toDelete = sortedContexts.slice(0, this.contexts.size - this.maxContexts)
    toDelete.forEach(([id]) => this.contexts.delete(id))
  }
}
```

### 🔗 组件间上下文传递

#### Context Provider 模式
```typescript
// 全局上下文 Provider
const SelfMirrorContext = createContext<SelfMirrorContextType | null>(null)

export function SelfMirrorProvider({ children }: { children: React.ReactNode }) {
  const [currentContext, setCurrentContext] = useState<ConversationContext | null>(null)
  const [aiRouter] = useState(() => new AIRouter(defaultConfig.ai))
  const [contextManager] = useState(() => new ContextManager())
  
  const startNewConversation = useCallback((model: string) => {
    const context = contextManager.createContext(model)
    setCurrentContext(context)
    return context
  }, [contextManager])
  
  const sendMessage = useCallback(async (content: string) => {
    if (!currentContext) {
      const context = startNewConversation(aiRouter.currentProvider)
      setCurrentContext(context)
    }
    
    // 添加用户消息
    const userMessage: ChatMessage = {
      id: generateId(),
      content,
      role: 'user',
      timestamp: new Date()
    }
    
    contextManager.addMessage(currentContext.id, userMessage)
    
    // 获取 AI 响应
    const response = await aiRouter.generateForScenario(
      AIScenario.CHAT,
      content,
      { context: contextManager.getContextMessages(currentContext.id, 10) }
    )
    
    // 添加助手消息
    const assistantMessage: ChatMessage = {
      id: generateId(),
      content: response,
      role: 'assistant',
      timestamp: new Date()
    }
    
    contextManager.addMessage(currentContext.id, assistantMessage)
    
    return assistantMessage
  }, [currentContext, aiRouter, contextManager])
  
  const value: SelfMirrorContextType = {
    currentContext,
    aiRouter,
    contextManager,
    startNewConversation,
    sendMessage
  }
  
  return (
    <SelfMirrorContext.Provider value={value}>
      {children}
    </SelfMirrorContext.Provider>
  )
}
```

---

## 4. 双模式运行逻辑

### 🔧 开发模式 vs 生产模式

#### 环境配置差异
```typescript
// 环境配置管理
interface EnvironmentConfig {
  mode: 'development' | 'production'
  debug: boolean
  proxy: ProxyConfig | null
  logging: LoggingConfig
  features: FeatureFlags
}

const developmentConfig: EnvironmentConfig = {
  mode: 'development',
  debug: true,
  proxy: {
    enabled: true,
    url: 'http://127.0.0.1:7897',
    bypassSSL: true
  },
  logging: {
    level: 'debug',
    enableConsole: true,
    enableFile: false
  },
  features: {
    debugConsole: true,
    performanceMonitoring: true,
    hotReload: true
  }
}

const productionConfig: EnvironmentConfig = {
  mode: 'production',
  debug: false,
  proxy: null,
  logging: {
    level: 'warn',
    enableConsole: false,
    enableFile: true
  },
  features: {
    debugConsole: false,
    performanceMonitoring: false,
    hotReload: false
  }
}
```

#### 代理设置和网络配置
```typescript
// 网络配置适配器
class NetworkAdapter {
  private config: EnvironmentConfig
  
  constructor(config: EnvironmentConfig) {
    this.config = config
  }
  
  createFetchClient(): typeof fetch {
    if (this.config.mode === 'development' && this.config.proxy?.enabled) {
      return this.createProxyFetch()
    }
    
    return fetch
  }
  
  private createProxyFetch(): typeof fetch {
    if (typeof window !== 'undefined') {
      // 浏览器环境不使用代理
      return fetch
    }
    
    try {
      const { ProxyAgent, fetch: undiciFetch } = require('undici')
      const proxyAgent = new ProxyAgent(this.config.proxy!.url)
      
      return (url: string, options: any = {}) => {
        return undiciFetch(url, {
          ...options,
          dispatcher: proxyAgent
        })
      }
    } catch (error) {
      console.warn('⚠️ 代理配置失败，使用默认 fetch:', error)
      return fetch
    }
  }
}
```

#### 调试功能的环境适配
```typescript
// 调试功能管理
class DebugManager {
  private isEnabled: boolean
  
  constructor(config: EnvironmentConfig) {
    this.isEnabled = config.features.debugConsole
  }
  
  // 条件渲染调试控制台
  renderDebugConsole(): React.ReactNode | null {
    if (!this.isEnabled) return null
    
    return (
      <div className="debug-console">
        <SimpleAIModelManager />
        <SimpleConfigManager />
      </div>
    )
  }
  
  // 条件日志输出
  log(level: 'debug' | 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    if (!this.isEnabled && level === 'debug') return
    
    console[level](`[SelfMirror] ${message}`, ...args)
  }
}
```

---

## 5. 会话状态持久化策略

### 💾 持久化机制

#### 会话状态保存
```typescript
// 会话状态管理
interface SessionState {
  currentConversationId?: string
  activeModel: string
  userPreferences: UserPreferences
  recentConversations: string[]
}

class SessionManager {
  private static readonly SESSION_KEY = 'selfmirror_session'
  
  static saveSession(state: SessionState): void {
    try {
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(state))
    } catch (error) {
      console.error('保存会话状态失败:', error)
    }
  }
  
  static loadSession(): SessionState | null {
    try {
      const stored = localStorage.getItem(this.SESSION_KEY)
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.error('加载会话状态失败:', error)
      return null
    }
  }
  
  static clearSession(): void {
    localStorage.removeItem(this.SESSION_KEY)
  }
}
```

#### 自动保存机制
```typescript
// 自动保存 Hook
export function useAutoSave<T>(data: T, key: string, delay: number = 1000) {
  const timeoutRef = useRef<NodeJS.Timeout>()
  
  useEffect(() => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    // 设置新的定时器
    timeoutRef.current = setTimeout(() => {
      try {
        localStorage.setItem(key, JSON.stringify(data))
      } catch (error) {
        console.error('自动保存失败:', error)
      }
    }, delay)
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [data, key, delay])
}
```

---

*最后更新: 2025-06-29*
*文档版本: v2.0.0*
