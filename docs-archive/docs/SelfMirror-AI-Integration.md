# SelfMirror AI 模型集成架构

## 📖 概述

本文档详细介绍 SelfMirror 的 AI 模型集成架构，包括双模型支持、场景化配置、API 代理机制和错误处理策略。

---

## 1. AI 模型架构概览

### 🤖 双模型支持策略

```mermaid
graph TB
    subgraph "用户界面"
        UI[聊天界面]
        Debug[调试控制台]
    end
    
    subgraph "AI 路由层"
        Router[AI 路由器]
        Switch[模型切换器]
    end
    
    subgraph "模型适配层"
        GeminiAdapter[Gemini 适配器]
        DoubaoAdapter[豆包适配器]
    end
    
    subgraph "外部 API"
        GeminiAPI[Gemini API]
        DoubaoAPI[豆包 API]
    end
    
    subgraph "代理层"
        Proxy[HTTP 代理]
    end
    
    UI --> Router
    Debug --> Switch
    Router --> GeminiAdapter
    Router --> DoubaoAdapter
    Switch --> Router
    
    GeminiAdapter --> Proxy
    DoubaoAdapter --> DoubaoAPI
    Proxy --> GeminiAPI
```

### 🎯 设计原则

#### 1. 统一接口设计
```typescript
// 统一的 AI 提供商接口
interface AIProvider {
  name: 'gemini' | 'doubao'
  generateText: (prompt: string, options?: GenerateOptions) => Promise<string>
  generateStream: (prompt: string, options?: StreamOptions) => AsyncIterable<string>
  testConnection: () => Promise<boolean>
}

// 统一的配置接口
interface AIProviderConfig {
  apiKey: string
  baseUrl?: string
  proxyUrl?: string
  models: {
    default: string
    chat: string
    rag: string
    meaning: string
    prompt: string
    insight: string
  }
}
```

#### 2. 场景化模型配置
```typescript
// 使用场景枚举
enum AIScenario {
  CHAT = 'chat',           // 日常对话
  RAG = 'rag',             // RAG 检索增强
  MEANING = 'meaning',     // 意义提取
  PROMPT = 'prompt',       // 系统提示词处理
  INSIGHT = 'insight'      // 每日洞察生成
}

// 场景配置映射
const scenarioConfigs = {
  [AIScenario.CHAT]: {
    gemini: 'gemini-2.5-flash-preview-05-20',
    doubao: 'doubao-pro-4k',
    description: '用于主要的聊天交互和日常对话'
  },
  [AIScenario.RAG]: {
    gemini: 'gemini-1.5-flash',
    doubao: 'doubao-lite-4k',
    description: '用于知识检索和问答增强'
  },
  [AIScenario.MEANING]: {
    gemini: 'gemini-2.5-flash-preview-05-20',
    doubao: 'doubao-pro-4k',
    description: '用于文档理解和语义分析'
  },
  [AIScenario.PROMPT]: {
    gemini: 'gemini-1.5-pro',
    doubao: 'doubao-pro-32k',
    description: '用于提示词优化和处理'
  },
  [AIScenario.INSIGHT]: {
    gemini: 'gemini-2.5-flash-preview-05-20',
    doubao: 'doubao-pro-4k',
    description: '用于总结分析和洞察生成'
  }
}
```

---

## 2. Gemini 集成实现

### 🔧 Gemini 适配器

```typescript
// Gemini API 适配器
class GeminiAdapter implements AIProvider {
  name = 'gemini' as const
  private config: GeminiConfig
  
  constructor(config: GeminiConfig) {
    this.config = config
  }
  
  async generateText(prompt: string, options?: GenerateOptions): Promise<string> {
    const requestBody = {
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: {
        maxOutputTokens: options?.maxTokens || 2048,
        temperature: options?.temperature || 0.7,
        topP: options?.topP || 0.9
      }
    }
    
    const response = await this.makeRequest('generateContent', requestBody)
    return this.extractTextFromResponse(response)
  }
  
  async *generateStream(prompt: string, options?: StreamOptions): AsyncIterable<string> {
    const requestBody = {
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: {
        maxOutputTokens: options?.maxTokens || 2048,
        temperature: options?.temperature || 0.7
      }
    }
    
    const response = await this.makeStreamRequest('streamGenerateContent', requestBody)
    
    for await (const chunk of this.parseStreamResponse(response)) {
      yield chunk
    }
  }
  
  private async makeRequest(endpoint: string, body: any): Promise<any> {
    const url = `https://generativelanguage.googleapis.com/v1beta/models/${this.config.model}:${endpoint}?key=${this.config.apiKey}`
    
    const fetchOptions: RequestInit = {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    }
    
    // 代理配置
    if (this.config.proxyUrl && typeof window === 'undefined') {
      fetchOptions.agent = new ProxyAgent(this.config.proxyUrl)
    }
    
    const response = await fetch(url, fetchOptions)
    
    if (!response.ok) {
      throw new Error(`Gemini API 错误: ${response.status} ${response.statusText}`)
    }
    
    return response.json()
  }
  
  async testConnection(): Promise<boolean> {
    try {
      await this.generateText('Hello', { maxTokens: 10 })
      return true
    } catch (error) {
      console.error('Gemini 连接测试失败:', error)
      return false
    }
  }
}
```

### 🌐 代理配置

```typescript
// 代理配置管理
class ProxyManager {
  private proxyUrl?: string
  
  constructor(proxyUrl?: string) {
    this.proxyUrl = proxyUrl
  }
  
  createFetchWithProxy(): typeof fetch {
    if (!this.proxyUrl || typeof window !== 'undefined') {
      return fetch
    }
    
    try {
      const { ProxyAgent, fetch: undiciFetch } = require('undici')
      const proxyAgent = new ProxyAgent(this.proxyUrl)
      
      return (url: string, options: any = {}) => {
        return undiciFetch(url, {
          ...options,
          dispatcher: proxyAgent
        })
      }
    } catch (error) {
      console.warn('⚠️ 无法创建代理，使用默认 fetch:', error)
      return fetch
    }
  }
}
```

---

## 3. 豆包 (Doubao) 集成实现

### 🚀 豆包适配器

```typescript
// 豆包 API 适配器
class DoubaoAdapter implements AIProvider {
  name = 'doubao' as const
  private config: DoubaoConfig
  
  constructor(config: DoubaoConfig) {
    this.config = config
  }
  
  async generateText(prompt: string, options?: GenerateOptions): Promise<string> {
    const requestBody = {
      model: this.config.model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: options?.maxTokens || 2048,
      temperature: options?.temperature || 0.7,
      top_p: options?.topP || 0.9
    }
    
    const response = await this.makeRequest('chat/completions', requestBody)
    return response.choices[0]?.message?.content || ''
  }
  
  async *generateStream(prompt: string, options?: StreamOptions): AsyncIterable<string> {
    const requestBody = {
      model: this.config.model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: options?.maxTokens || 2048,
      temperature: options?.temperature || 0.7,
      stream: true
    }
    
    const response = await this.makeStreamRequest('chat/completions', requestBody)
    
    for await (const chunk of this.parseStreamResponse(response)) {
      yield chunk
    }
  }
  
  private async makeRequest(endpoint: string, body: any): Promise<any> {
    const url = `${this.config.baseUrl}/${endpoint}`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(body)
    })
    
    if (!response.ok) {
      throw new Error(`豆包 API 错误: ${response.status} ${response.statusText}`)
    }
    
    return response.json()
  }
  
  async testConnection(): Promise<boolean> {
    try {
      await this.generateText('Hello', { maxTokens: 10 })
      return true
    } catch (error) {
      console.error('豆包连接测试失败:', error)
      return false
    }
  }
}
```

---

## 4. AI 路由和切换机制

### 🔀 AI 路由器

```typescript
// AI 路由器 - 统一管理所有 AI 调用
class AIRouter {
  private providers: Map<string, AIProvider> = new Map()
  private currentProvider: string = 'gemini'
  private scenarioConfigs: Map<AIScenario, string> = new Map()
  
  constructor(config: AIConfig) {
    this.initializeProviders(config)
    this.loadScenarioConfigs(config)
  }
  
  // 根据场景获取最佳模型
  async generateForScenario(
    scenario: AIScenario, 
    prompt: string, 
    options?: GenerateOptions
  ): Promise<string> {
    const provider = this.getProviderForScenario(scenario)
    const model = this.getModelForScenario(scenario)
    
    return provider.generateText(prompt, {
      ...options,
      model
    })
  }
  
  // 流式生成
  async *generateStreamForScenario(
    scenario: AIScenario,
    prompt: string,
    options?: StreamOptions
  ): AsyncIterable<string> {
    const provider = this.getProviderForScenario(scenario)
    const model = this.getModelForScenario(scenario)
    
    yield* provider.generateStream(prompt, {
      ...options,
      model
    })
  }
  
  // 切换默认提供商
  async switchProvider(providerName: string): Promise<boolean> {
    if (!this.providers.has(providerName)) {
      throw new Error(`未知的提供商: ${providerName}`)
    }
    
    const provider = this.providers.get(providerName)!
    const isConnected = await provider.testConnection()
    
    if (isConnected) {
      this.currentProvider = providerName
      return true
    }
    
    return false
  }
  
  private getProviderForScenario(scenario: AIScenario): AIProvider {
    // 根据场景配置选择提供商
    const providerName = this.scenarioConfigs.get(scenario) || this.currentProvider
    return this.providers.get(providerName)!
  }
  
  private getModelForScenario(scenario: AIScenario): string {
    const provider = this.getProviderForScenario(scenario)
    return scenarioConfigs[scenario][provider.name]
  }
}
```

### 🔄 模型切换 API

```typescript
// API 路由: /api/debug/ai-model/switch
export async function POST(request: NextRequest) {
  try {
    const { provider } = await request.json()
    
    if (!['gemini', 'doubao'].includes(provider)) {
      return NextResponse.json(
        { error: '无效的 AI 提供商' },
        { status: 400 }
      )
    }
    
    console.log(`🔄 切换 AI 模型到: ${provider}`)
    
    // 获取 AI 路由器实例
    const aiRouter = getGlobalAIRouter()
    
    // 执行切换
    const success = await aiRouter.switchProvider(provider)
    
    if (success) {
      console.log(`✅ AI 模型已切换到: ${provider}`)
      return NextResponse.json({
        success: true,
        provider,
        message: `已切换到 ${provider}`,
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json(
        { error: `${provider} 连接失败` },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('❌ 切换 AI 模型失败:', error)
    return NextResponse.json(
      { error: '切换失败', details: error.message },
      { status: 500 }
    )
  }
}
```

---

## 5. 错误处理和重试机制

### 🛡️ 错误处理策略

```typescript
// 统一错误处理
class AIErrorHandler {
  static async handleWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<T> {
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error
        
        console.warn(`尝试 ${attempt}/${maxRetries} 失败:`, error.message)
        
        if (attempt < maxRetries) {
          await this.delay(retryDelay * Math.pow(2, attempt - 1)) // 指数退避
        }
      }
    }
    
    throw lastError!
  }
  
  static async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  static categorizeError(error: Error): ErrorCategory {
    if (error.message.includes('401') || error.message.includes('403')) {
      return ErrorCategory.AUTHENTICATION
    }
    if (error.message.includes('429')) {
      return ErrorCategory.RATE_LIMIT
    }
    if (error.message.includes('timeout')) {
      return ErrorCategory.TIMEOUT
    }
    return ErrorCategory.UNKNOWN
  }
}

enum ErrorCategory {
  AUTHENTICATION = 'authentication',
  RATE_LIMIT = 'rate_limit',
  TIMEOUT = 'timeout',
  NETWORK = 'network',
  UNKNOWN = 'unknown'
}
```

### 🔄 自动重试和降级

```typescript
// 智能重试和降级策略
class AIResilience {
  private aiRouter: AIRouter
  private fallbackProvider: string = 'doubao'
  
  constructor(aiRouter: AIRouter) {
    this.aiRouter = aiRouter
  }
  
  async generateWithFallback(
    scenario: AIScenario,
    prompt: string,
    options?: GenerateOptions
  ): Promise<string> {
    try {
      // 首先尝试主要提供商
      return await AIErrorHandler.handleWithRetry(
        () => this.aiRouter.generateForScenario(scenario, prompt, options),
        3,
        1000
      )
    } catch (primaryError) {
      console.warn('主要提供商失败，尝试降级:', primaryError.message)
      
      try {
        // 降级到备用提供商
        const originalProvider = this.aiRouter.currentProvider
        await this.aiRouter.switchProvider(this.fallbackProvider)
        
        const result = await this.aiRouter.generateForScenario(scenario, prompt, options)
        
        // 恢复原提供商
        await this.aiRouter.switchProvider(originalProvider)
        
        return result
      } catch (fallbackError) {
        console.error('降级提供商也失败:', fallbackError.message)
        throw new Error(`所有 AI 提供商都不可用: ${primaryError.message}`)
      }
    }
  }
}
```

---

## 6. 性能优化和缓存

### ⚡ 响应缓存

```typescript
// AI 响应缓存
class AICache {
  private cache: Map<string, CacheEntry> = new Map()
  private maxSize: number = 1000
  private ttl: number = 300000 // 5 分钟
  
  private generateCacheKey(prompt: string, options?: any): string {
    return `${prompt}:${JSON.stringify(options || {})}`
  }
  
  get(prompt: string, options?: any): string | null {
    const key = this.generateCacheKey(prompt, options)
    const entry = this.cache.get(key)
    
    if (!entry) return null
    
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.response
  }
  
  set(prompt: string, response: string, options?: any): void {
    const key = this.generateCacheKey(prompt, options)
    
    // LRU 清理
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, {
      response,
      timestamp: Date.now()
    })
  }
}

interface CacheEntry {
  response: string
  timestamp: number
}
```

---

*最后更新: 2025-06-29*
*文档版本: v2.0.0*
