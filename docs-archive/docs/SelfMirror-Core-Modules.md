# SelfMirror 核心功能模块详解

## 📖 概述

本文档详细介绍 SelfMirror 的五大核心功能模块：聊天界面、调试控制台、记忆系统、RAG 系统和每日洞察。

---

## 1. 聊天界面 (ChatInterface)

### 🎯 功能定位
聊天界面是用户与 AI 交互的主要入口，提供流畅的对话体验和智能的上下文管理。

### 🏗️ 组件架构

```typescript
// 聊天界面组件结构
interface ChatInterfaceProps {
  className?: string
}

export function ChatInterface({ className }: ChatInterfaceProps) {
  // 状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [currentModel, setCurrentModel] = useState<'gemini' | 'doubao'>('gemini')
  
  // 核心功能
  const sendMessage = async (content: string) => { /* 发送消息逻辑 */ }
  const handleStream = (stream: ReadableStream) => { /* 流式响应处理 */ }
  
  return (
    <div className={cn("flex flex-col h-full", className)}>
      <MessageList messages={messages} />
      <MessageInput onSend={sendMessage} disabled={isLoading} />
    </div>
  )
}
```

### 🔄 消息流处理

#### 发送消息流程
```mermaid
sequenceDiagram
    participant User
    participant ChatInterface
    participant API
    participant AIModel
    participant Memory
    
    User->>ChatInterface: 输入消息
    ChatInterface->>API: POST /api/chat
    API->>AIModel: 调用 AI 模型
    AIModel-->>API: 流式响应
    API-->>ChatInterface: Server-Sent Events
    ChatInterface->>Memory: 保存对话
    ChatInterface->>User: 显示响应
```

#### 流式响应实现
```typescript
// 流式消息处理
async function handleStreamResponse(response: Response) {
  const reader = response.body?.getReader()
  const decoder = new TextDecoder()
  
  while (true) {
    const { done, value } = await reader.read()
    if (done) break
    
    const chunk = decoder.decode(value)
    const lines = chunk.split('\n')
    
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6)
        if (data === '[DONE]') return
        
        try {
          const parsed = JSON.parse(data)
          updateMessage(parsed.content)
        } catch (error) {
          console.error('解析流式数据失败:', error)
        }
      }
    }
  }
}
```

### 🎨 UI 组件设计

#### MessageList 组件
```typescript
interface MessageListProps {
  messages: ChatMessage[]
  onMessageEdit?: (id: string, content: string) => void
}

export function MessageList({ messages, onMessageEdit }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])
  
  return (
    <ScrollArea className="flex-1 p-4">
      {messages.map((message) => (
        <MessageBubble 
          key={message.id} 
          message={message}
          onEdit={onMessageEdit}
        />
      ))}
      <div ref={messagesEndRef} />
    </ScrollArea>
  )
}
```

#### MessageInput 组件
```typescript
interface MessageInputProps {
  onSend: (content: string) => void
  disabled?: boolean
  placeholder?: string
}

export function MessageInput({ onSend, disabled, placeholder }: MessageInputProps) {
  const [input, setInput] = useState('')
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  
  // 自动调整高度
  const adjustHeight = () => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${textarea.scrollHeight}px`
    }
  }
  
  // 发送消息
  const handleSend = () => {
    if (input.trim() && !disabled) {
      onSend(input.trim())
      setInput('')
    }
  }
  
  return (
    <div className="border-t p-4">
      <div className="flex gap-2">
        <Textarea
          ref={textareaRef}
          value={input}
          onChange={(e) => {
            setInput(e.target.value)
            adjustHeight()
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault()
              handleSend()
            }
          }}
          placeholder={placeholder || "输入消息..."}
          disabled={disabled}
          className="flex-1 min-h-[40px] max-h-[200px] resize-none"
        />
        <Button 
          onClick={handleSend} 
          disabled={disabled || !input.trim()}
          size="icon"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
```

---

## 2. 调试控制台 (DebugConsole)

### 🎯 设计理念
调试控制台经过重大简化重构，从企业级复杂功能转变为个人用户友好的简洁界面。

### 🔧 简化后的架构

#### 核心组件结构
```typescript
// 调试控制台主界面
export default function DebugConsolePage() {
  return (
    <div className="h-screen flex bg-[#18181B] text-white">
      {/* 左侧聊天区域 (50%) */}
      <div className="flex-1 overflow-auto">
        <ChatInterface />
      </div>
      
      {/* 右侧调试面板 (50%) */}
      <div className="flex-1 overflow-auto">
        <Tabs defaultValue="ai-models" className="h-full flex flex-col">
          <TabsList>
            <TabsTrigger value="ai-models">AI模型切换</TabsTrigger>
            <TabsTrigger value="config">基础设置</TabsTrigger>
          </TabsList>
          
          <TabsContent value="ai-models">
            <SimpleAIModelManager />
          </TabsContent>
          
          <TabsContent value="config">
            <SimpleConfigManager />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
```

#### SimpleAIModelManager 组件
```typescript
// 简化的 AI 模型管理器
export function SimpleAIModelManager() {
  const [currentProvider, setCurrentProvider] = useState<'gemini' | 'doubao'>('gemini')
  const [modelStatus, setModelStatus] = useState<Record<string, SimpleAIModelStatus>>({
    gemini: { provider: 'gemini', isConnected: true, lastUsed: '2分钟前', hasApiKey: true },
    doubao: { provider: 'doubao', isConnected: false, lastUsed: '1小时前', hasApiKey: false }
  })
  
  // 核心功能：模型切换
  const switchProvider = async (provider: 'gemini' | 'doubao') => {
    setIsSwitching(true)
    try {
      const response = await fetch('/api/debug/ai-model/switch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      })
      
      if (response.ok) {
        setCurrentProvider(provider)
        updateModelStatus(provider)
      }
    } catch (error) {
      console.error('切换模型失败:', error)
    } finally {
      setIsSwitching(false)
    }
  }
  
  return (
    <div className="space-y-6">
      {/* 当前状态显示 */}
      <CurrentModelStatus />
      
      {/* 模型选择卡片 */}
      <ModelSelectionCards />
      
      {/* API 密钥设置 */}
      <APIKeySettings />
    </div>
  )
}
```

#### SimpleConfigManager 组件
```typescript
// 简化的配置管理器
export function SimpleConfigManager() {
  const [config, setConfig] = useState<SimpleConfig>(defaultConfig)
  const [hasChanges, setHasChanges] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  
  // 场景化模型配置
  const updateModelConfig = (provider: 'gemini' | 'doubao', scenario: string, modelName: string) => {
    setConfig(prev => ({
      ...prev,
      ai: {
        ...prev.ai,
        providers: {
          ...prev.ai.providers,
          [provider]: {
            ...prev.ai.providers[provider],
            models: {
              ...prev.ai.providers[provider].models,
              [scenario]: modelName
            }
          }
        }
      }
    }))
    setHasChanges(true)
  }
  
  return (
    <div className="space-y-6">
      {/* 应用基础设置 */}
      <AppBasicSettings />
      
      {/* AI 提供商配置 */}
      <AIProviderSettings />
      
      {/* 使用场景配置 */}
      <ScenarioModelConfiguration />
      
      {/* 配置状态显示 */}
      <ConfigurationStatus />
    </div>
  )
}
```

### 📊 重构对比

#### 简化前后对比
```typescript
const refactoringComparison = {
  before: {
    components: [
      "AIPerformanceChart",      // 性能图表
      "AICostAnalysis",          // 成本分析
      "ConfigHistory",           // 配置历史
      "ConfigImportExport",      // 导入导出
      "7个独立配置编辑器",        // 复杂配置
      "详细监控面板"             // 企业级监控
    ],
    codeLines: "~3000+ 行",
    complexity: "企业级复杂度",
    userExperience: "需要学习成本"
  },
  
  after: {
    components: [
      "SimpleAIModelManager",    // 简化模型管理
      "SimpleConfigManager"      // 简化配置管理
    ],
    codeLines: "~826 行 (减少 70%+)",
    complexity: "个人用户友好",
    userExperience: "零学习成本"
  }
}
```

---

## 3. 记忆系统 (Memory)

### 🧠 系统架构

#### 本地存储策略
```typescript
// 记忆系统接口
interface MemorySystem {
  conversations: ConversationMemory
  documents: DocumentMemory
  insights: InsightMemory
}

// 对话记忆
interface ConversationMemory {
  save: (conversation: Conversation) => Promise<void>
  load: (date: string) => Promise<Conversation | null>
  search: (query: string) => Promise<Conversation[]>
}

// 文档记忆
interface DocumentMemory {
  store: (document: Document) => Promise<void>
  retrieve: (path: string) => Promise<Document | null>
  index: () => Promise<DocumentIndex>
}
```

#### 文件系统组织
```
memory/
├── conversations/           # 对话历史
│   ├── 2025-06-29-chat.md
│   ├── 2025-06-28-chat.md
│   └── ...
├── documents/              # 文档库
│   ├── 小镜人设提示词.md
│   ├── 用户画像.md
│   ├── 心智要素结构.md
│   └── prompts/
│       ├── 日常对话模式提示词.md
│       └── 每日洞察模式提示词.md
└── insights/               # 每日洞察
    ├── 每日洞察今天.md
    ├── 每日洞察归档.md
    └── ...
```

### 📝 文档管理

#### DocumentViewer 组件
```typescript
interface DocumentViewerProps {
  path: string
  editable?: boolean
  onSave?: (content: string) => void
}

export function DocumentViewer({ path, editable, onSave }: DocumentViewerProps) {
  const [content, setContent] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  
  // 加载文档内容
  useEffect(() => {
    loadDocument(path)
  }, [path])
  
  const loadDocument = async (documentPath: string) => {
    try {
      const response = await fetch(`/api/memory/local?path=${encodeURIComponent(documentPath)}`)
      const data = await response.json()
      setContent(data.content || '')
    } catch (error) {
      console.error('加载文档失败:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>{path}</CardTitle>
          {editable && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
            >
              {isEditing ? '预览' : '编辑'}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div>加载中...</div>
        ) : isEditing ? (
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="min-h-[400px] font-mono"
          />
        ) : (
          <div className="prose dark:prose-invert max-w-none">
            <ReactMarkdown>{content}</ReactMarkdown>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

---

## 4. RAG 系统

### 🔍 检索增强生成

#### 系统架构
```typescript
// RAG 系统接口
interface RAGSystem {
  indexer: DocumentIndexer
  retriever: DocumentRetriever
  meaningExtractor: MeaningExtractor
}

// 文档索引器
interface DocumentIndexer {
  indexDocument: (document: Document) => Promise<void>
  updateIndex: () => Promise<void>
  getIndex: () => Promise<DocumentIndex>
}

// 文档检索器
interface DocumentRetriever {
  search: (query: string, options?: SearchOptions) => Promise<SearchResult[]>
  semanticSearch: (query: string) => Promise<SemanticResult[]>
  hybridSearch: (query: string) => Promise<HybridResult[]>
}
```

#### 意义提取功能
```typescript
// 意义提取器
class MeaningExtractor {
  async extractMeaning(text: string): Promise<MeaningResult> {
    const response = await fetch('/api/rag/meaning-extract', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text })
    })
    
    return response.json()
  }
  
  async analyzeSemantic(documents: Document[]): Promise<SemanticAnalysis> {
    // 语义分析逻辑
  }
}
```

---

## 5. 每日洞察

### 💡 洞察生成系统

#### 功能架构
```typescript
// 每日洞察系统
interface InsightSystem {
  generator: InsightGenerator
  analyzer: ConversationAnalyzer
  storage: InsightStorage
}

// 洞察生成器
class InsightGenerator {
  async generateDailyInsight(conversations: Conversation[]): Promise<DailyInsight> {
    // 分析对话内容
    const analysis = await this.analyzeConversations(conversations)
    
    // 生成洞察
    const insight = await this.generateInsight(analysis)
    
    // 保存洞察
    await this.saveInsight(insight)
    
    return insight
  }
  
  private async analyzeConversations(conversations: Conversation[]): Promise<ConversationAnalysis> {
    // 对话分析逻辑
  }
  
  private async generateInsight(analysis: ConversationAnalysis): Promise<DailyInsight> {
    // 使用 AI 模型生成洞察
  }
}
```

#### InsightDisplay 组件
```typescript
export function InsightDisplay() {
  const [insights, setInsights] = useState<DailyInsight[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  
  const generateTodayInsight = async () => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/insights/generate', {
        method: 'POST'
      })
      const newInsight = await response.json()
      setInsights(prev => [newInsight, ...prev])
    } catch (error) {
      console.error('生成洞察失败:', error)
    } finally {
      setIsGenerating(false)
    }
  }
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">每日洞察</h2>
        <Button onClick={generateTodayInsight} disabled={isGenerating}>
          {isGenerating ? '生成中...' : '生成今日洞察'}
        </Button>
      </div>
      
      {insights.map(insight => (
        <InsightCard key={insight.id} insight={insight} />
      ))}
    </div>
  )
}
```

---

*最后更新: 2025-06-29*
*文档版本: v2.0.0*
