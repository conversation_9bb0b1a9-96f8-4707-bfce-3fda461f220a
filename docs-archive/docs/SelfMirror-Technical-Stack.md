# SelfMirror 技术栈详解

## 📖 概述

本文档详细介绍 SelfMirror 项目的技术栈选择、文件系统结构和开发环境配置。

---

## 1. 核心技术栈

### 🚀 前端框架

#### Next.js 15.3.2
```json
{
  "framework": "Next.js 15.3.2",
  "features": [
    "App Router (app/ 目录)",
    "Server Components",
    "API Routes",
    "TypeScript 原生支持",
    "热重载开发"
  ],
  "advantages": [
    "全栈开发能力",
    "优秀的开发体验",
    "强大的构建优化",
    "丰富的生态系统"
  ]
}
```

#### React 18
- **并发特性** - 支持 Suspense 和并发渲染
- **Hooks 生态** - 现代化的状态管理方式
- **组件化架构** - 高度可复用的组件设计

#### TypeScript
```typescript
// 类型安全示例
interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  model?: 'gemini' | 'doubao'
}

interface AIModelConfig {
  provider: 'gemini' | 'doubao'
  apiKey: string
  models: {
    chat: string
    rag: string
    meaning: string
    prompt: string
    insight: string
  }
}
```

### 🎨 UI 技术栈

#### shadcn/ui 组件库
```typescript
// 核心组件使用
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
```

**特性优势:**
- **现代化设计** - 基于 Radix UI 的无障碍组件
- **完全可定制** - 可以完全控制样式和行为
- **TypeScript 友好** - 完整的类型定义
- **深色主题支持** - 原生支持深色模式

#### Tailwind CSS
```css
/* 深色主题配置 */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;
}
```

#### Lucide React 图标
```typescript
import { 
  Zap,           // AI 模型切换
  Settings,      // 设置
  MessageSquare, // 聊天
  FileText,      // 文档
  Search,        // 搜索
  Brain          // 智能功能
} from "lucide-react"
```

---

## 2. 文件系统结构

### 📁 项目目录结构

```
selfmirror2025/
├── app/                          # Next.js App Router
│   ├── api/                      # API 路由
│   │   ├── chat/                 # 聊天相关 API
│   │   ├── debug/                # 调试功能 API
│   │   │   ├── ai-model/         # AI 模型管理
│   │   │   └── config/           # 配置管理
│   │   ├── memory/               # 记忆系统 API
│   │   └── rag/                  # RAG 系统 API
│   ├── debug-console/            # 调试控制台页面
│   ├── globals.css               # 全局样式
│   ├── layout.tsx                # 根布局
│   └── page.tsx                  # 主页面
├── components/                   # React 组件
│   ├── ui/                       # shadcn/ui 基础组件
│   ├── chat/                     # 聊天相关组件
│   ├── debug/                    # 调试控制台组件
│   └── memory/                   # 记忆管理组件
├── lib/                          # 工具库和配置
│   ├── ai/                       # AI 模型集成
│   ├── config/                   # 配置管理系统
│   ├── memory/                   # 记忆系统
│   ├── rag/                      # RAG 系统
│   └── utils.ts                  # 通用工具函数
├── memory/                       # 本地数据存储
│   ├── conversations/            # 对话历史
│   ├── documents/                # 文档库
│   └── insights/                 # 每日洞察
├── docs/                         # 项目文档
└── public/                       # 静态资源
```

### 🗂️ 核心目录详解

#### `/app` 目录 (Next.js App Router)
```typescript
// app/layout.tsx - 根布局
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" className="dark">
      <body className={inter.className}>
        <div className="min-h-screen bg-background text-foreground">
          {children}
        </div>
      </body>
    </html>
  )
}
```

#### `/components` 目录结构
```
components/
├── ui/                    # shadcn/ui 基础组件
│   ├── button.tsx
│   ├── card.tsx
│   ├── input.tsx
│   ├── tabs.tsx
│   └── ...
├── chat/                  # 聊天功能组件
│   ├── ChatInterface.tsx      # 主聊天界面
│   ├── MessageList.tsx        # 消息列表
│   ├── MessageInput.tsx       # 消息输入
│   └── StreamingMessage.tsx   # 流式消息显示
├── debug/                 # 调试控制台组件
│   ├── SimpleAIModelManager.tsx    # AI 模型管理
│   ├── SimpleConfigManager.tsx     # 配置管理
│   └── DataManagement.tsx          # 数据管理
└── memory/                # 记忆系统组件
    ├── DocumentViewer.tsx      # 文档查看器
    ├── SearchInterface.tsx     # 搜索界面
    └── InsightDisplay.tsx      # 洞察显示
```

#### `/lib` 目录架构
```typescript
// lib/config/index.ts - 配置管理
export interface SelfMirrorConfig {
  app: AppConfig
  ai: AIConfig
  storage: StorageConfig
  rag: RAGConfig
  debug: DebugConfig
}

// lib/ai/index.ts - AI 模型集成
export interface AIProvider {
  name: string
  generateText: (prompt: string) => Promise<string>
  generateStream: (prompt: string) => AsyncIterable<string>
}
```

---

## 3. 开发环境配置

### 🛠️ 开发工具链

#### Package.json 核心依赖
```json
{
  "dependencies": {
    "next": "15.3.2",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "typescript": "^5.6.3",
    "@radix-ui/react-tabs": "^1.1.1",
    "tailwindcss": "^3.4.17",
    "lucide-react": "^0.468.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "tailwind-merge": "^2.5.4"
  },
  "devDependencies": {
    "@types/node": "^22.10.2",
    "@types/react": "^18.3.17",
    "@types/react-dom": "^18.3.5",
    "eslint": "^8.57.1",
    "eslint-config-next": "15.3.2"
  }
}
```

#### TypeScript 配置
```json
// tsconfig.json
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    }
  }
}
```

#### Tailwind CSS 配置
```javascript
// tailwind.config.js
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

### 🚀 开发脚本

#### 启动命令
```bash
# 开发模式 (支持代理)
npm run dev
# 等同于: cross-env http_proxy=http://127.0.0.1:7897 https_proxy=http://127.0.0.1:7897 NODE_TLS_REJECT_UNAUTHORIZED=0 next dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint
```

#### 环境变量配置
```bash
# .env.local
GEMINI_API_KEY=your_gemini_api_key
DOUBAO_API_KEY=your_doubao_api_key
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

# 代理配置 (开发环境)
http_proxy=http://127.0.0.1:7897
https_proxy=http://127.0.0.1:7897
NODE_TLS_REJECT_UNAUTHORIZED=0
```

---

## 4. 构建和部署

### 📦 构建配置

#### Next.js 配置
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
}

module.exports = nextConfig
```

#### 生产环境优化
- **代码分割** - 自动按路由分割代码
- **静态资源优化** - 图片和字体优化
- **Tree Shaking** - 移除未使用的代码
- **压缩优化** - Gzip 和 Brotli 压缩

### 🚀 部署策略

#### 本地部署 (推荐)
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm run start
```

#### Docker 部署
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

---

## 5. 开发最佳实践

### 🎯 代码规范

#### 组件设计原则
```typescript
// 1. 单一职责原则
interface ChatMessageProps {
  message: ChatMessage
  onEdit?: (id: string, content: string) => void
}

// 2. 类型安全
export function ChatMessage({ message, onEdit }: ChatMessageProps) {
  // 组件实现
}

// 3. 错误边界
export function ErrorBoundary({ children }: { children: React.ReactNode }) {
  // 错误处理逻辑
}
```

#### API 设计规范
```typescript
// 统一的 API 响应格式
interface APIResponse<T> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

// 错误处理中间件
export function withErrorHandling(handler: NextApiHandler) {
  return async (req: NextRequest, res: NextResponse) => {
    try {
      return await handler(req, res)
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }
  }
}
```

### 📊 性能监控

#### 开发工具
- **React DevTools** - 组件调试和性能分析
- **Next.js DevTools** - 构建分析和优化建议
- **TypeScript 编译器** - 类型检查和错误提示

#### 性能指标
- **首屏加载时间** - < 2 秒
- **交互响应时间** - < 100ms
- **内存使用** - 合理的内存占用
- **包大小** - 优化后的构建产物

---

*最后更新: 2025-06-29*
*文档版本: v2.0.0*
