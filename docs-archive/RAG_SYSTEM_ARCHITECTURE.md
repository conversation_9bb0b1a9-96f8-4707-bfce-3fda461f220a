# SelfMirror 意义RAG系统架构文档

## 🎯 系统概述

SelfMirror的意义RAG系统是一个完全本地化的、专注于"意义距离"而非"语义距离"的检索增强生成系统。该系统遵循"本地优先、隐私至上、意义>语义"的核心哲学。

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    RAG系统主入口                              │
│                (lib/services/rag-system.ts)                 │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  索引器模块   │ │  检索器模块   │ │  存储模块    │
│ rag-indexer │ │ rag-retriever│ │ rag-storage │
└─────────────┘ └─────────────┘ └─────────────┘
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  嵌入模型    │ │  向量数据库   │ │  IndexedDB  │
│embedding-   │ │vector-      │ │  本地存储    │
│model        │ │database     │ │             │
└─────────────┘ └─────────────┘ └─────────────┘
```

## 📦 模块详解

### 1. 索引器模块 (rag-indexer.ts)

**职责**: 数据预处理与向量化流水线

**核心功能**:
- 文档分块处理
- 意义子块生成
- 向量化处理
- 本地存储管理

**关键函数**:
```typescript
// 主要处理函数
processAndIndexDocument(documentText: string, documentId: string, documentType: string): Promise<void>

// 待贤贤实现的核心逻辑
chunkDocument(text: string): string[]  // TODO: 递归分块规则
generateMeaningfulChildChunk(parentChunk: string): Promise<string>  // TODO: 意义子块生成
```

### 2. 检索器模块 (rag-retriever.ts)

**职责**: 实时检索与上下文打包流水线

**核心功能**:
- 向量相似度搜索
- 动态意义权重计算
- 结果过滤和排序
- 缓存管理

**关键函数**:
```typescript
// 主要检索函数
retrieveMeaningfulMemories(latestQuery: string, userProfile: string, dailyInsight: string): Promise<string[]>

// 待贤贤实现的核心逻辑
applyDynamicWeighting(candidates: VectorSearchCandidate[], profile: string, insight: string): WeightedCandidate[]  // TODO: 动态权重算法
```

### 3. 嵌入模型 (embedding-model.ts)

**技术栈**: Transformers.js + 本地模型

**特性**:
- 完全本地运行
- 支持批量处理
- 智能缓存机制
- 性能优化

**当前模型**: Qwen/Qwen2.5-0.5B-Instruct (临时)
**目标模型**: Qwen3-Embedding-4B

### 4. 向量数据库 (vector-database.ts)

**技术栈**: Voy Search Engine

**特性**:
- 高性能向量搜索
- 内存中运行
- 支持批量操作
- 自动索引管理

### 5. 本地存储 (rag-storage.ts)

**技术栈**: IndexedDB

**数据结构**:
```typescript
interface ChunkPair {
  id: string;
  parentChunk: string;        // 母块：原始文本
  childChunk: string;         // 子块：意义摘要
  vector: number[];           // 向量表示
  sourceDocumentId: string;   // 来源文档
  sourceDocumentType: string; // 文档类型
  metadata: ChunkMetadata;    // 元数据
  createdAt: string;
  updatedAt: string;
}
```

## 🔄 数据流程

### 索引流程

```
原始文档 → 文档分块 → 意义子块生成 → 向量化 → 存储
    ↓         ↓           ↓          ↓       ↓
记忆文件   母块数组    子块数组    向量数组  IndexedDB
```

### 检索流程

```
用户查询 → 查询向量化 → 向量搜索 → 动态权重 → 结果过滤 → 返回记忆
    ↓         ↓          ↓        ↓        ↓        ↓
  文本      向量      候选集    权重排序   Top-K   母块内容
```

## 🎨 核心设计理念

### 子母块架构

- **母块 (Parent Chunk)**: 原始文本片段，保持完整性
- **子块 (Child Chunk)**: AI生成的意义摘要，用于向量化和检索
- **优势**: 检索精准度高，返回内容完整

### 动态意义权重

权重因子包括:
- **用户画像相关性** (30%): 与用户核心特征的匹配度
- **洞察相关性** (25%): 与当前洞察的关联度
- **时间相关性** (20%): 时间衰减和新鲜度
- **情感相关性** (15%): 情感状态的匹配
- **访问频率** (10%): 历史访问模式

### 本地优先架构

- **零数据上传**: 所有处理在本地完成
- **离线可用**: 无需网络连接即可运行
- **隐私保护**: 用户数据完全受控

## 🔧 配置系统

### 主要配置文件

```typescript
// lib/config/rag-config.ts
export const RAG_CONFIG = {
  embedding: {
    modelName: 'Qwen/Qwen2.5-0.5B-Instruct',
    dimensions: 384,
    maxTokens: 512
  },
  chunking: {
    maxChunkSize: 1000,
    overlapSize: 100,
    minChunkSize: 200
  },
  retrieval: {
    maxCandidates: 100,
    topK: 5,
    similarityThreshold: 0.3,
    filterRatio: 0.2
  }
};
```

## 🚀 API接口

### 初始化API

```
POST /api/rag/initialize
- 初始化RAG系统
- 索引所有记忆文件
- 返回系统状态

GET /api/rag/initialize
- 获取系统状态
- 健康检查
```

### 检索API

```
POST /api/rag/search
Body: {
  "query": "用户查询",
  "context": {
    "userProfile": "...",
    "dailyInsight": "..."
  }
}

GET /api/rag/search
- 获取检索统计信息
```

## 🧪 测试系统

### 测试脚本

```bash
# 运行完整测试
npx ts-node scripts/test-rag-system.ts

# 测试组件
- 嵌入模型测试
- 向量数据库测试  
- 存储系统测试
- 集成系统测试
```

## 📊 性能指标

### 目标性能

- **索引速度**: 1000字符/秒
- **检索延迟**: <500ms
- **存储效率**: <100MB总占用
- **准确率**: >80%相关性

### 监控指标

- 向量数量和维度
- 缓存命中率
- 检索响应时间
- 存储空间使用

## 🔮 待实现功能

### 核心算法 (由贤贤实现)

1. **递归分块规则**
   - 位置: `rag-indexer.ts` → `chunkDocument()`
   - 要求: 智能的文档分割策略

2. **意义子块生成**
   - 位置: `rag-indexer.ts` → `generateMeaningfulChildChunk()`
   - 要求: 核心提示词和LLM调用逻辑

3. **动态意义权重算法**
   - 位置: `rag-retriever.ts` → `applyDynamicWeighting()`
   - 要求: 复杂的多因子权重计算

### 系统优化

1. **模型升级**: 集成Qwen3-Embedding-4B
2. **性能优化**: 并发处理和缓存策略
3. **功能扩展**: 增量更新和版本管理

## 🎯 使用指南

### 快速开始

```typescript
import { ragSystem } from '@/lib/services/rag-system';

// 1. 初始化系统
await ragSystem.initialize();

// 2. 索引记忆文件
await ragSystem.indexMemoryFile('USER_PROFILE', userProfileContent);

// 3. 检索相关记忆
const memories = await ragSystem.retrieveMemories(
  '用户的情感特点',
  userProfile,
  dailyInsight
);
```

### 集成到现有系统

RAG系统已集成到memory-manager.ts的`meaningRAGSearch`函数中，可以无缝替换原有的关键词匹配逻辑。

## 📝 总结

SelfMirror的意义RAG系统提供了一个完整的、本地化的、专注于意义理解的检索框架。系统架构清晰，接口完善，为贤贤实现核心算法预留了明确的空间。通过这个系统，SelfMirror将能够真正实现"意义距离 > 语义距离"的检索理念。
