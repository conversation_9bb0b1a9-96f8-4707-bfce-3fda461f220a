"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { 
  Play, 
  Square, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  BarChart3,
  FileText,
  Settings
} from 'lucide-react'

interface TestSuite {
  id: string
  name: string
  description: string
  testCount: number
  status: 'idle' | 'running' | 'completed' | 'failed'
  lastRun?: Date
  passRate?: number
}

interface TestResult {
  id: string
  name: string
  status: 'passed' | 'failed' | 'skipped'
  duration: number
  error?: string
}

interface TestingStats {
  totalSuites: number
  totalTests: number
  lastRunResults?: {
    passed: number
    failed: number
    passRate: number
  }
}

export default function TestingDashboard() {
  const [testSuites, setTestSuites] = useState<TestSuite[]>([])
  const [testingStats, setTestingStats] = useState<TestingStats | null>(null)
  const [isRunning, setIsRunning] = useState(false)
  const [currentProgress, setCurrentProgress] = useState(0)
  const [selectedSuite, setSelectedSuite] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTestingData()
  }, [])

  const fetchTestingData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/debug/testing?action=status')
      const data = await response.json()
      
      if (data.success) {
        setTestingStats(data.data.testStats)
        
        // 获取测试套件列表
        const suitesResponse = await fetch('/api/debug/testing?action=suites')
        const suitesData = await suitesResponse.json()
        
        if (suitesData.success) {
          const suites: TestSuite[] = suitesData.data.availableSuites.map((suite: any) => ({
            id: suite.id,
            name: suite.name,
            description: suite.description,
            testCount: suite.testCount,
            status: 'idle',
            passRate: Math.random() * 100 // 模拟数据
          }))
          setTestSuites(suites)
        }
      }
    } catch (error) {
      console.error('获取测试数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const runAllTests = async () => {
    try {
      setIsRunning(true)
      setCurrentProgress(0)
      
      const response = await fetch('/api/debug/testing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'runAll' })
      })
      
      const data = await response.json()
      
      if (data.success) {
        // 模拟进度更新
        const progressInterval = setInterval(() => {
          setCurrentProgress(prev => {
            if (prev >= 100) {
              clearInterval(progressInterval)
              setIsRunning(false)
              fetchTestingData() // 刷新数据
              return 100
            }
            return prev + 10
          })
        }, 500)
      }
    } catch (error) {
      console.error('运行测试失败:', error)
      setIsRunning(false)
    }
  }

  const runTestSuite = async (suiteId: string) => {
    try {
      setIsRunning(true)
      setSelectedSuite(suiteId)
      
      const response = await fetch('/api/debug/testing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'runSuite', suiteId })
      })
      
      const data = await response.json()
      
      if (data.success) {
        // 更新套件状态
        setTestSuites(prev => prev.map(suite => 
          suite.id === suiteId 
            ? { ...suite, status: 'running' }
            : suite
        ))
        
        // 模拟测试结果
        setTimeout(() => {
          const mockResults: TestResult[] = Array.from({ length: 5 }, (_, i) => ({
            id: `test-${i}`,
            name: `测试用例 ${i + 1}`,
            status: Math.random() > 0.2 ? 'passed' : 'failed',
            duration: Math.random() * 1000 + 100
          }))
          
          setTestResults(mockResults)
          setTestSuites(prev => prev.map(suite => 
            suite.id === suiteId 
              ? { 
                  ...suite, 
                  status: 'completed',
                  lastRun: new Date(),
                  passRate: (mockResults.filter(r => r.status === 'passed').length / mockResults.length) * 100
                }
              : suite
          ))
          setIsRunning(false)
        }, 2000)
      }
    } catch (error) {
      console.error('运行测试套件失败:', error)
      setIsRunning(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string, passRate?: number) => {
    switch (status) {
      case 'completed':
        const variant = passRate && passRate >= 80 ? 'default' : 'destructive'
        return <Badge variant={variant}>已完成</Badge>
      case 'running':
        return <Badge variant="secondary">运行中</Badge>
      case 'failed':
        return <Badge variant="destructive">失败</Badge>
      default:
        return <Badge variant="outline">待运行</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-sm text-muted-foreground">加载测试数据...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 测试概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium">测试套件</p>
                <p className="text-2xl font-bold">{testingStats?.totalSuites || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium">测试用例</p>
                <p className="text-2xl font-bold">{testingStats?.totalTests || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium">通过率</p>
                <p className="text-2xl font-bold">
                  {testingStats?.lastRunResults?.passRate?.toFixed(1) || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-500" />
              <div>
                <p className="text-sm font-medium">状态</p>
                <p className="text-sm font-semibold">
                  {isRunning ? '运行中' : '就绪'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 进度条 */}
      {isRunning && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>测试执行进度</span>
                <span>{currentProgress}%</span>
              </div>
              <Progress value={currentProgress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 主要内容 */}
      <Tabs defaultValue="suites" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="suites">测试套件</TabsTrigger>
            <TabsTrigger value="results">测试结果</TabsTrigger>
            <TabsTrigger value="coverage">覆盖率报告</TabsTrigger>
          </TabsList>
          
          <div className="space-x-2">
            <Button 
              onClick={runAllTests} 
              disabled={isRunning}
              className="space-x-2"
            >
              {isRunning ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              <span>运行所有测试</span>
            </Button>
            
            <Button variant="outline" onClick={fetchTestingData}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <TabsContent value="suites" className="space-y-4">
          <div className="grid gap-4">
            {testSuites.map((suite) => (
              <Card key={suite.id}>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{suite.name}</CardTitle>
                      <p className="text-sm text-muted-foreground mt-1">
                        {suite.description}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(suite.status, suite.passRate)}
                      <Button
                        size="sm"
                        onClick={() => runTestSuite(suite.id)}
                        disabled={isRunning}
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center text-sm text-muted-foreground">
                    <span>{suite.testCount} 个测试用例</span>
                    {suite.lastRun && (
                      <span>最后运行: {suite.lastRun.toLocaleString()}</span>
                    )}
                    {suite.passRate !== undefined && (
                      <span>通过率: {suite.passRate.toFixed(1)}%</span>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {testResults.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>测试结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {testResults.map((result) => (
                    <div key={result.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(result.status)}
                        <span className="font-medium">{result.name}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>{result.duration.toFixed(0)}ms</span>
                        {result.error && (
                          <span className="text-red-500">{result.error}</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Alert>
              <AlertDescription>
                暂无测试结果。请运行测试套件以查看结果。
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="coverage" className="space-y-4">
          <Alert>
            <AlertDescription>
              覆盖率报告功能正在开发中...
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>
    </div>
  )
}
