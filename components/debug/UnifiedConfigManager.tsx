"use client"

import { useState, useEffect } from "react"
import { BaseDebugger, useDebuggerState, BaseDebuggerConfig } from "./BaseDebugger"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Save, Download, Upload, CheckCircle, AlertTriangle } from "lucide-react"

// 统一的配置接口
interface UnifiedConfig {
  app: {
    name: string
    environment: 'development' | 'production' | 'test'
    debug: boolean
    locale: string
  }
  ai: {
    defaultProvider: 'gemini' | 'doubao'
    providers: {
      gemini: {
        apiKey: string
        proxyUrl?: string
        models: {
          default: string
          chat: string
          rag: string
          meaning: string
          prompt: string
          insight: string
        }
      }
      doubao: {
        apiKey: string
        baseUrl: string
        models: {
          default: string
          chat: string
          rag: string
          meaning: string
          prompt: string
          insight: string
        }
      }
    }
  }
  rag: {
    indexing: {
      chunkSize: number
      chunkOverlap: number
      enableMeaningExtraction: boolean
      enableEmotionalAnalysis: boolean
    }
    retrieval: {
      mode: 'vector_only' | 'keyword_only' | 'hybrid_weighted'
      topK: number
      similarityThreshold: number
      enableReranking: boolean
    }
  }
  storage: {
    type: 'filesystem' | 'indexeddb'
    path?: string
    maxSize: number
    autoBackup: boolean
  }
}

// 配置验证结果
interface ConfigValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

interface UnifiedConfigManagerProps {
  onConfigChange?: (config: UnifiedConfig) => void
  onValidationChange?: (validation: ConfigValidation) => void
}

export function UnifiedConfigManager({ onConfigChange, onValidationChange }: UnifiedConfigManagerProps) {
  // 调试器状态管理
  const { state, setLoading, setError, setSuccess } = useDebuggerState()

  // 配置状态
  const [config, setConfig] = useState<UnifiedConfig>({
    app: {
      name: 'SelfMirror',
      environment: 'development',
      debug: true,
      locale: 'zh-CN'
    },
    ai: {
      defaultProvider: 'gemini',
      providers: {
        gemini: {
          apiKey: '',
          proxyUrl: '',
          models: {
            default: 'gemini-1.5-flash',
            chat: 'gemini-1.5-flash',
            rag: 'gemini-1.5-flash',
            meaning: 'gemini-1.5-flash',
            prompt: 'gemini-1.5-flash',
            insight: 'gemini-1.5-flash'
          }
        },
        doubao: {
          apiKey: '',
          baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
          models: {
            default: 'ep-20241201105006-lmvzh',
            chat: 'ep-20241201105006-lmvzh',
            rag: 'ep-20241201105006-lmvzh',
            meaning: 'ep-20241201105006-lmvzh',
            prompt: 'ep-20241201105006-lmvzh',
            insight: 'ep-20241201105006-lmvzh'
          }
        }
      }
    },
    rag: {
      indexing: {
        chunkSize: 1000,
        chunkOverlap: 200,
        enableMeaningExtraction: true,
        enableEmotionalAnalysis: true
      },
      retrieval: {
        mode: 'hybrid_weighted',
        topK: 100,
        similarityThreshold: 0.3,
        enableReranking: true
      }
    },
    storage: {
      type: 'filesystem',
      path: './data',
      maxSize: 1024 * 1024 * 100, // 100MB
      autoBackup: true
    }
  })

  // 配置验证状态
  const [validation, setValidation] = useState<ConfigValidation>({
    isValid: true,
    errors: [],
    warnings: []
  })

  // 是否有未保存的更改
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // 调试器配置
  const debuggerConfig: BaseDebuggerConfig = {
    title: "统一配置管理器",
    description: "管理 SelfMirror 的所有配置项，包括 AI 模型、RAG 参数和存储设置",
    autoRefresh: false,
    showLastUpdated: true,
    showStatus: true
  }

  // 加载配置
  const loadConfig = async () => {
    try {
      const response = await fetch('/api/debug/config/simple')
      if (response.ok) {
        const data = await response.json()
        setConfig(data)
        validateConfig(data)
        setHasUnsavedChanges(false)
        return data
      }
      throw new Error('加载配置失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '网络错误')
    }
  }

  // 保存配置
  const saveConfig = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug/config/simple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        setHasUnsavedChanges(false)
        setSuccess()
      } else {
        throw new Error('保存配置失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '保存失败')
    }
  }

  // 重置配置
  const resetConfig = async () => {
    await loadConfig()
  }

  // 验证配置
  const validateConfig = (configToValidate: UnifiedConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证 AI 配置
    if (!configToValidate.ai.providers.gemini.apiKey && !configToValidate.ai.providers.doubao.apiKey) {
      errors.push('至少需要配置一个 AI 提供商的 API Key')
    }

    // 验证 RAG 配置
    if (configToValidate.rag.indexing.chunkSize < 100 || configToValidate.rag.indexing.chunkSize > 4000) {
      warnings.push('块大小建议在 100-4000 之间')
    }

    if (configToValidate.rag.retrieval.similarityThreshold < 0 || configToValidate.rag.retrieval.similarityThreshold > 1) {
      errors.push('相似度阈值必须在 0-1 之间')
    }

    // 验证存储配置
    if (configToValidate.storage.maxSize < 1024 * 1024) {
      warnings.push('存储大小建议至少 1MB')
    }

    const validationResult = {
      isValid: errors.length === 0,
      errors,
      warnings
    }

    setValidation(validationResult)
    onValidationChange?.(validationResult)
  }

  // 配置更新处理
  const handleConfigChange = (path: string, value: any) => {
    const newConfig = { ...config }
    const keys = path.split('.')
    let current: any = newConfig

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]]
    }
    current[keys[keys.length - 1]] = value

    setConfig(newConfig)
    setHasUnsavedChanges(true)
    validateConfig(newConfig)
    onConfigChange?.(newConfig)
  }

  // 导出配置
  const exportConfig = async () => {
    const configData = {
      config,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }
    const blob = new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `selfmirror-config-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  // 导入配置
  const importConfig = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target?.result as string)
            if (data.config) {
              setConfig(data.config)
              validateConfig(data.config)
              setHasUnsavedChanges(true)
              setSuccess()
            }
          } catch (error) {
            setError('配置文件格式错误')
          }
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  // 初始化加载配置
  useEffect(() => {
    loadConfig()
  }, [])

  // 调试器操作
  const actions = {
    onRefresh: loadConfig,
    onReset: resetConfig
  }

  // 头部操作按钮
  const headerActions = (
    <div className="flex gap-2">
      <Button
        variant={hasUnsavedChanges ? "default" : "outline"}
        size="sm"
        onClick={saveConfig}
        disabled={state.isLoading || !validation.isValid}
      >
        <Save className="w-4 h-4" />
        保存
      </Button>
      <Button variant="outline" size="sm" onClick={exportConfig}>
        <Download className="w-4 h-4" />
        导出
      </Button>
      <Button variant="outline" size="sm" onClick={importConfig}>
        <Upload className="w-4 h-4" />
        导入
      </Button>
    </div>
  )

  // 底部状态栏
  const footerActions = (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center gap-2">
        {validation.isValid ? (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle className="w-3 h-3" />
            配置有效
          </Badge>
        ) : (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertTriangle className="w-3 h-3" />
            配置无效
          </Badge>
        )}
        {hasUnsavedChanges && (
          <Badge variant="outline">有未保存的更改</Badge>
        )}
      </div>
      <div className="text-sm text-muted-foreground">
        {validation.errors.length} 错误, {validation.warnings.length} 警告
      </div>
    </div>
  )

  return (
    <BaseDebugger
      config={debuggerConfig}
      state={state}
      actions={actions}
      headerActions={headerActions}
      footerActions={footerActions}
    >
      <Tabs defaultValue="app" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="app">应用设置</TabsTrigger>
          <TabsTrigger value="ai">AI 配置</TabsTrigger>
          <TabsTrigger value="rag">RAG 配置</TabsTrigger>
          <TabsTrigger value="storage">存储配置</TabsTrigger>
        </TabsList>

        <TabsContent value="app" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>应用名称</Label>
              <Input
                value={config.app.name}
                onChange={(e) => handleConfigChange('app.name', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>环境</Label>
              <Select
                value={config.app.environment}
                onValueChange={(value) => handleConfigChange('app.environment', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="development">开发环境</SelectItem>
                  <SelectItem value="production">生产环境</SelectItem>
                  <SelectItem value="test">测试环境</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={config.app.debug}
                onCheckedChange={(checked) => handleConfigChange('app.debug', checked)}
              />
              <Label>调试模式</Label>
            </div>
            <div className="space-y-2">
              <Label>语言</Label>
              <Select
                value={config.app.locale}
                onValueChange={(value) => handleConfigChange('app.locale', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh-CN">中文</SelectItem>
                  <SelectItem value="en-US">English</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="ai" className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>默认提供商</Label>
              <Select
                value={config.ai.defaultProvider}
                onValueChange={(value) => handleConfigChange('ai.defaultProvider', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gemini">Gemini</SelectItem>
                  <SelectItem value="doubao">豆包</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-6">
              {/* Gemini 配置 */}
              <div className="space-y-3">
                <h4 className="font-medium">Gemini 配置</h4>
                <div className="space-y-2">
                  <Label>API Key</Label>
                  <Input
                    type="password"
                    value={config.ai.providers.gemini.apiKey}
                    onChange={(e) => handleConfigChange('ai.providers.gemini.apiKey', e.target.value)}
                    placeholder="输入 Gemini API Key"
                  />
                </div>
                <div className="space-y-2">
                  <Label>代理 URL (可选)</Label>
                  <Input
                    value={config.ai.providers.gemini.proxyUrl || ''}
                    onChange={(e) => handleConfigChange('ai.providers.gemini.proxyUrl', e.target.value)}
                    placeholder="https://your-proxy.com"
                  />
                </div>
              </div>

              {/* 豆包配置 */}
              <div className="space-y-3">
                <h4 className="font-medium">豆包配置</h4>
                <div className="space-y-2">
                  <Label>API Key</Label>
                  <Input
                    type="password"
                    value={config.ai.providers.doubao.apiKey}
                    onChange={(e) => handleConfigChange('ai.providers.doubao.apiKey', e.target.value)}
                    placeholder="输入豆包 API Key"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Base URL</Label>
                  <Input
                    value={config.ai.providers.doubao.baseUrl}
                    onChange={(e) => handleConfigChange('ai.providers.doubao.baseUrl', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="rag" className="space-y-4">
          <div className="grid grid-cols-2 gap-6">
            {/* 索引配置 */}
            <div className="space-y-3">
              <h4 className="font-medium">索引配置</h4>
              <div className="space-y-2">
                <Label>块大小: {config.rag.indexing.chunkSize}</Label>
                <Input
                  type="number"
                  value={config.rag.indexing.chunkSize}
                  onChange={(e) => handleConfigChange('rag.indexing.chunkSize', parseInt(e.target.value))}
                  min={100}
                  max={4000}
                />
              </div>
              <div className="space-y-2">
                <Label>块重叠: {config.rag.indexing.chunkOverlap}</Label>
                <Input
                  type="number"
                  value={config.rag.indexing.chunkOverlap}
                  onChange={(e) => handleConfigChange('rag.indexing.chunkOverlap', parseInt(e.target.value))}
                  min={0}
                  max={1000}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={config.rag.indexing.enableMeaningExtraction}
                  onCheckedChange={(checked) => handleConfigChange('rag.indexing.enableMeaningExtraction', checked)}
                />
                <Label>启用意义提取</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={config.rag.indexing.enableEmotionalAnalysis}
                  onCheckedChange={(checked) => handleConfigChange('rag.indexing.enableEmotionalAnalysis', checked)}
                />
                <Label>启用情感分析</Label>
              </div>
            </div>

            {/* 检索配置 */}
            <div className="space-y-3">
              <h4 className="font-medium">检索配置</h4>
              <div className="space-y-2">
                <Label>检索模式</Label>
                <Select
                  value={config.rag.retrieval.mode}
                  onValueChange={(value) => handleConfigChange('rag.retrieval.mode', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vector_only">仅向量</SelectItem>
                    <SelectItem value="keyword_only">仅关键词</SelectItem>
                    <SelectItem value="hybrid_weighted">混合加权</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Top K: {config.rag.retrieval.topK}</Label>
                <Input
                  type="number"
                  value={config.rag.retrieval.topK}
                  onChange={(e) => handleConfigChange('rag.retrieval.topK', parseInt(e.target.value))}
                  min={1}
                  max={1000}
                />
              </div>
              <div className="space-y-2">
                <Label>相似度阈值: {config.rag.retrieval.similarityThreshold}</Label>
                <Input
                  type="number"
                  value={config.rag.retrieval.similarityThreshold}
                  onChange={(e) => handleConfigChange('rag.retrieval.similarityThreshold', parseFloat(e.target.value))}
                  min={0}
                  max={1}
                  step={0.01}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={config.rag.retrieval.enableReranking}
                  onCheckedChange={(checked) => handleConfigChange('rag.retrieval.enableReranking', checked)}
                />
                <Label>启用重排序</Label>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="storage" className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>存储类型</Label>
              <Select
                value={config.storage.type}
                onValueChange={(value) => handleConfigChange('storage.type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="filesystem">文件系统</SelectItem>
                  <SelectItem value="indexeddb">IndexedDB</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {config.storage.type === 'filesystem' && (
              <div className="space-y-2">
                <Label>存储路径</Label>
                <Input
                  value={config.storage.path || ''}
                  onChange={(e) => handleConfigChange('storage.path', e.target.value)}
                  placeholder="./data"
                />
              </div>
            )}

            <div className="space-y-2">
              <Label>最大存储大小 (MB): {Math.round(config.storage.maxSize / 1024 / 1024)}</Label>
              <Input
                type="number"
                value={Math.round(config.storage.maxSize / 1024 / 1024)}
                onChange={(e) => handleConfigChange('storage.maxSize', parseInt(e.target.value) * 1024 * 1024)}
                min={1}
                max={10240}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                checked={config.storage.autoBackup}
                onCheckedChange={(checked) => handleConfigChange('storage.autoBackup', checked)}
              />
              <Label>自动备份</Label>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* 验证错误和警告 */}
      {(validation.errors.length > 0 || validation.warnings.length > 0) && (
        <div className="mt-4 space-y-2">
          {validation.errors.map((error, index) => (
            <div key={index} className="text-sm text-red-600 flex items-center gap-1">
              <AlertTriangle className="w-3 h-3" />
              {error}
            </div>
          ))}
          {validation.warnings.map((warning, index) => (
            <div key={index} className="text-sm text-yellow-600 flex items-center gap-1">
              <AlertTriangle className="w-3 h-3" />
              {warning}
            </div>
          ))}
        </div>
      )}
    </BaseDebugger>
  )
}
