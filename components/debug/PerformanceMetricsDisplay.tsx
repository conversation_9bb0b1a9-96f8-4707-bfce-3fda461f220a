'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { PerformanceMetrics } from '@/types/rag';

interface PerformanceMetricsDisplayProps {
  metrics: PerformanceMetrics;
}

export function PerformanceMetricsDisplay({ metrics }: PerformanceMetricsDisplayProps) {
  // 计算时间分解的百分比
  const totalBreakdownTime = Object.values(metrics.timeBreakdown).reduce((sum, time) => sum + time, 0);
  
  const getTimePercentage = (time: number) => {
    return totalBreakdownTime > 0 ? (time / totalBreakdownTime) * 100 : 0;
  };

  // 性能等级评估
  const getPerformanceLevel = (time: number) => {
    if (time < 100) return { level: '优秀', color: 'text-green-400' };
    if (time < 300) return { level: '良好', color: 'text-blue-400' };
    if (time < 500) return { level: '一般', color: 'text-yellow-400' };
    return { level: '需优化', color: 'text-red-400' };
  };

  const performanceLevel = getPerformanceLevel(metrics.totalTime);

  return (
    <div className="space-y-4">
      {/* 总体性能概览 */}
      <Card className="bg-[#0D1117] border-[#30363D]">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm text-white">总体性能</CardTitle>
            <Badge variant="outline" className={`${performanceLevel.color} border-current`}>
              {performanceLevel.level}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-white">{metrics.totalTime}ms</div>
              <div className="text-xs text-gray-400">总耗时</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-400">{metrics.finalResults}</div>
              <div className="text-xs text-gray-400">最终结果</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-400">{metrics.averageRelevance.toFixed(2)}</div>
              <div className="text-xs text-gray-400">平均相关性</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 时间分解 */}
      <Card className="bg-[#0D1117] border-[#30363D]">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-white">时间分解</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {Object.entries(metrics.timeBreakdown).map(([phase, time]) => {
            const percentage = getTimePercentage(time);
            const phaseNames: Record<string, string> = {
              vectorization: '向量化',
              search: '搜索',
              weighting: '权重计算',
              filtering: '过滤',
              reranking: '重排序'
            };
            
            return (
              <div key={phase} className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-300">{phaseNames[phase] || phase}</span>
                  <span className="text-white">{time}ms ({percentage.toFixed(1)}%)</span>
                </div>
                <Progress value={percentage} className="h-1" />
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* 检索统计 */}
      <Card className="bg-[#0D1117] border-[#30363D]">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-white">检索统计</CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">候选项发现:</span>
                <span className="text-white">{metrics.candidatesFound}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">候选项过滤:</span>
                <span className="text-yellow-400">{metrics.candidatesFiltered}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">最终结果:</span>
                <span className="text-green-400">{metrics.finalResults}</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">过滤率:</span>
                <span className="text-white">
                  {metrics.candidatesFound > 0 
                    ? ((metrics.candidatesFiltered / metrics.candidatesFound) * 100).toFixed(1)
                    : 0
                  }%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">保留率:</span>
                <span className="text-white">
                  {metrics.candidatesFound > 0 
                    ? ((metrics.finalResults / metrics.candidatesFound) * 100).toFixed(1)
                    : 0
                  }%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">平均相关性:</span>
                <span className="text-green-400">{metrics.averageRelevance.toFixed(3)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 资源使用 */}
      <Card className="bg-[#0D1117] border-[#30363D]">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-white">资源使用</CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            <div>
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-400">内存使用</span>
                <span className="text-white">{metrics.memoryUsage}MB</span>
              </div>
              <Progress value={Math.min((metrics.memoryUsage / 1024) * 100, 100)} className="h-1" />
            </div>
            
            <div>
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-400">CPU使用率</span>
                <span className="text-white">{metrics.cpuUsage}%</span>
              </div>
              <Progress value={metrics.cpuUsage} className="h-1" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 性能建议 */}
      <Card className="bg-[#0D1117] border-[#30363D]">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-white">性能建议</CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-2 text-xs">
            {metrics.totalTime > 500 && (
              <div className="flex items-start space-x-2">
                <span className="text-yellow-400">⚠️</span>
                <span className="text-gray-300">总耗时较长，建议优化向量搜索或减少候选项数量</span>
              </div>
            )}
            
            {metrics.timeBreakdown.vectorization > 100 && (
              <div className="flex items-start space-x-2">
                <span className="text-blue-400">💡</span>
                <span className="text-gray-300">向量化耗时较长，考虑使用缓存或批量处理</span>
              </div>
            )}
            
            {metrics.candidatesFiltered / metrics.candidatesFound > 0.8 && (
              <div className="flex items-start space-x-2">
                <span className="text-green-400">✅</span>
                <span className="text-gray-300">过滤效果良好，大部分无关候选项被正确过滤</span>
              </div>
            )}
            
            {metrics.averageRelevance < 0.5 && (
              <div className="flex items-start space-x-2">
                <span className="text-red-400">🔧</span>
                <span className="text-gray-300">平均相关性较低，建议调整检索参数或权重规则</span>
              </div>
            )}
            
            {metrics.memoryUsage > 512 && (
              <div className="flex items-start space-x-2">
                <span className="text-orange-400">📊</span>
                <span className="text-gray-300">内存使用较高，建议优化向量存储或减少批处理大小</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
