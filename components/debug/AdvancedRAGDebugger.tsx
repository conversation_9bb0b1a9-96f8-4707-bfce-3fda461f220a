'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  IndexingConfig, 
  RetrievalConfig, 
  TestResult, 
  DebuggerState,
  ConfigPreset,
  PerformanceMetrics,
  WeightAnalysis
} from '@/types/rag';
import { ragConfigManager } from '@/lib/services/rag-config-manager';
import { IndexingPipelineController } from './IndexingPipelineController';
import { RetrievalPipelineController } from './RetrievalPipelineController';
import { RealtimeTestingArea } from './RealtimeTestingArea';
import { ConfigPresetManager } from './ConfigPresetManager';

interface AdvancedRAGDebuggerProps {
  className?: string;
}

export function AdvancedRAGDebugger({ className }: AdvancedRAGDebuggerProps) {
  // 调试器状态
  const [debuggerState, setDebuggerState] = useState<DebuggerState>({
    currentIndexingConfig: ragConfigManager.getDefaultIndexingConfig(),
    currentRetrievalConfig: ragConfigManager.getDefaultRetrievalConfig(),
    isIndexing: false,
    isTesting: false,
    indexingProgress: 0,
    currentSession: {
      id: `session_${Date.now()}`,
      name: '新调试会话',
      startTime: new Date().toISOString(),
      indexingConfig: ragConfigManager.getDefaultIndexingConfig(),
      retrievalConfig: ragConfigManager.getDefaultRetrievalConfig(),
      testHistory: [],
      configHistory: [],
      totalTests: 0,
      totalIndexedDocuments: 0,
      averagePerformance: {
        indexingTime: 0,
        retrievalTime: 0,
        totalTime: 0,
        timeBreakdown: {
          vectorization: 0,
          search: 0,
          weighting: 0,
          filtering: 0,
          reranking: 0
        },
        memoryUsage: 0,
        cpuUsage: 0,
        candidatesFound: 0,
        candidatesFiltered: 0,
        finalResults: 0,
        averageRelevance: 0
      }
    },
    activeTab: 'indexing',
    unsavedChanges: false
  });

  // 测试结果状态
  const [lastTestResult, setLastTestResult] = useState<TestResult | null>(null);
  const [testHistory, setTestHistory] = useState<TestResult[]>([]);

  // 配置预设状态
  const [availablePresets, setAvailablePresets] = useState<ConfigPreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  // 初始化
  useEffect(() => {
    loadAvailablePresets();
    initializeDefaultSession();
  }, []);

  const loadAvailablePresets = useCallback(() => {
    const presets = ragConfigManager.getAllPresets();
    setAvailablePresets(presets);
  }, []);

  const initializeDefaultSession = useCallback(() => {
    console.log('🚀 初始化高级RAG调试器...');
    // 可以在这里加载上次的会话状态
  }, []);

  // 配置更新处理
  const handleIndexingConfigChange = useCallback((newConfig: Partial<IndexingConfig>) => {
    setDebuggerState(prev => ({
      ...prev,
      currentIndexingConfig: { ...prev.currentIndexingConfig, ...newConfig },
      unsavedChanges: true
    }));
  }, []);

  const handleRetrievalConfigChange = useCallback((newConfig: Partial<RetrievalConfig>) => {
    setDebuggerState(prev => ({
      ...prev,
      currentRetrievalConfig: { ...prev.currentRetrievalConfig, ...newConfig },
      unsavedChanges: true
    }));
  }, []);

  // 预设管理
  const handleLoadPreset = useCallback((presetId: string) => {
    const preset = availablePresets.find(p => p.id === presetId);
    if (preset) {
      setDebuggerState(prev => ({
        ...prev,
        currentIndexingConfig: { ...preset.indexingConfig },
        currentRetrievalConfig: { ...preset.retrievalConfig },
        unsavedChanges: false
      }));
      setSelectedPreset(presetId);
      console.log(`📋 已加载预设: ${preset.name}`);
    }
  }, [availablePresets]);

  const handleSaveAsPreset = useCallback((name: string, description: string, category: ConfigPreset['category']) => {
    const preset = ragConfigManager.createPreset(
      name,
      description,
      category,
      debuggerState.currentIndexingConfig,
      debuggerState.currentRetrievalConfig
    );
    
    setAvailablePresets(prev => [...prev, preset]);
    setSelectedPreset(preset.id);
    setDebuggerState(prev => ({ ...prev, unsavedChanges: false }));
    
    console.log(`💾 已保存预设: ${name}`);
  }, [debuggerState.currentIndexingConfig, debuggerState.currentRetrievalConfig]);

  // 配置导入导出
  const handleExportConfig = useCallback(() => {
    const configJson = ragConfigManager.exportConfig(
      debuggerState.currentIndexingConfig,
      debuggerState.currentRetrievalConfig
    );
    
    const blob = new Blob([configJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rag-config-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('📤 配置已导出');
  }, [debuggerState.currentIndexingConfig, debuggerState.currentRetrievalConfig]);

  const handleImportConfig = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const configJson = e.target?.result as string;
        const { indexingConfig, retrievalConfig } = ragConfigManager.importConfig(configJson);
        
        setDebuggerState(prev => ({
          ...prev,
          currentIndexingConfig: indexingConfig,
          currentRetrievalConfig: retrievalConfig,
          unsavedChanges: true
        }));
        
        console.log('📥 配置已导入');
      } catch (error) {
        console.error('❌ 配置导入失败:', error);
        alert('配置导入失败: ' + (error instanceof Error ? error.message : '未知错误'));
      }
    };
    reader.readAsText(file);
  }, []);

  // 索引执行
  const handleStartIndexing = useCallback(async (documentText: string) => {
    if (!documentText.trim()) {
      alert('请输入要索引的文档内容');
      return;
    }

    setDebuggerState(prev => ({ ...prev, isIndexing: true, indexingProgress: 0 }));
    
    try {
      console.log('🚀 开始索引文档...');
      
      // 这里应该调用实际的索引API
      // const result = await indexingAPI.processDocument(documentText, debuggerState.currentIndexingConfig);
      
      // 模拟索引过程
      for (let i = 0; i <= 100; i += 10) {
        setDebuggerState(prev => ({ ...prev, indexingProgress: i }));
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      setDebuggerState(prev => ({
        ...prev,
        isIndexing: false,
        indexingProgress: 100,
        currentSession: {
          ...prev.currentSession,
          totalIndexedDocuments: prev.currentSession.totalIndexedDocuments + 1
        }
      }));
      
      console.log('✅ 文档索引完成');
    } catch (error) {
      console.error('❌ 索引失败:', error);
      setDebuggerState(prev => ({ ...prev, isIndexing: false, indexingProgress: 0 }));
      alert('索引失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [debuggerState.currentIndexingConfig]);

  // 测试执行
  const handleRunTest = useCallback(async (query: string) => {
    if (!query.trim()) {
      alert('请输入测试查询');
      return;
    }

    setDebuggerState(prev => ({ ...prev, isTesting: true }));
    
    try {
      console.log('🔍 开始测试检索...');
      
      // 这里应该调用实际的检索API
      // const result = await retrievalAPI.search(query, debuggerState.currentRetrievalConfig);
      
      // 模拟测试结果
      const mockResult: TestResult = {
        query,
        results: [
          {
            memories: ['模拟检索结果1', '模拟检索结果2'],
            metadata: {
              totalCandidates: 10,
              filteredCandidates: 5,
              finalResults: 2,
              processingTime: 150,
              averageRelevanceScore: 0.91
            }
          }
        ],
        weightAnalysis: [
          {
            candidateId: 'result1',
            originalScore: 0.8,
            finalScore: 0.95,
            appliedRules: [
              {
                ruleId: 'recent_mention',
                ruleName: '最近提及加权',
                matched: true,
                boost: 1.5,
                contribution: 0.15
              }
            ],
            scoreBreakdown: {
              semanticScore: 0.8,
              tagScore: 0.1,
              importanceScore: 0.05,
              temporalScore: 0.0,
              dynamicBoosts: 0.15,
              finalScore: 0.95
            }
          }
        ],
        performanceMetrics: {
          indexingTime: 0,
          retrievalTime: 150,
          totalTime: 150,
          timeBreakdown: {
            vectorization: 30,
            search: 45,
            weighting: 35,
            filtering: 25,
            reranking: 15
          },
          memoryUsage: 128,
          cpuUsage: 45,
          candidatesFound: 100,
          candidatesFiltered: 20,
          finalResults: 2,
          averageRelevance: 0.91
        },
        timestamp: new Date().toISOString(),
        configSnapshot: {
          indexing: debuggerState.currentIndexingConfig,
          retrieval: debuggerState.currentRetrievalConfig
        }
      };
      
      setLastTestResult(mockResult);
      setTestHistory(prev => [mockResult, ...prev.slice(0, 9)]); // 保留最近10次测试
      
      setDebuggerState(prev => ({
        ...prev,
        isTesting: false,
        lastTestResult: mockResult,
        currentSession: {
          ...prev.currentSession,
          totalTests: prev.currentSession.totalTests + 1,
          testHistory: [mockResult, ...prev.currentSession.testHistory.slice(0, 19)]
        }
      }));
      
      console.log('✅ 测试完成');
    } catch (error) {
      console.error('❌ 测试失败:', error);
      setDebuggerState(prev => ({ ...prev, isTesting: false }));
      alert('测试失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [debuggerState.currentIndexingConfig, debuggerState.currentRetrievalConfig]);

  return (
    <div className={`h-full bg-[#0D1117] text-white ${className}`}>
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b border-[#30363D]">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold text-white">SelfMirror RAG 高级调试台</h1>
          <Badge variant="outline" className="text-blue-400 border-blue-400">
            会话: {debuggerState.currentSession.name}
          </Badge>
          {debuggerState.unsavedChanges && (
            <Badge variant="outline" className="text-yellow-400 border-yellow-400">
              未保存更改
            </Badge>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportConfig}
            className="text-gray-300 border-gray-600 hover:bg-gray-700"
          >
            导出配置
          </Button>
          
          <input
            type="file"
            accept=".json"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) handleImportConfig(file);
            }}
            className="hidden"
            id="import-config"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => document.getElementById('import-config')?.click()}
            className="text-gray-300 border-gray-600 hover:bg-gray-700"
          >
            导入配置
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* 左栏：索引流水线控制器 */}
        <div className="w-1/2 border-r border-[#30363D]">
          <IndexingPipelineController
            config={debuggerState.currentIndexingConfig}
            onConfigChange={handleIndexingConfigChange}
            onStartIndexing={handleStartIndexing}
            isIndexing={debuggerState.isIndexing}
            indexingProgress={debuggerState.indexingProgress}
          />
        </div>

        {/* 右栏：检索流水线控制器 */}
        <div className="w-1/2">
          <RetrievalPipelineController
            config={debuggerState.currentRetrievalConfig}
            onConfigChange={handleRetrievalConfigChange}
          />
        </div>
      </div>

      {/* 底部：实时测试区域 */}
      <div className="border-t border-[#30363D]">
        <RealtimeTestingArea
          onRunTest={handleRunTest}
          isTesting={debuggerState.isTesting}
          lastTestResult={lastTestResult}
          testHistory={testHistory}
        />
      </div>

      {/* 配置预设管理器（浮动面板） */}
      <ConfigPresetManager
        presets={availablePresets}
        selectedPreset={selectedPreset}
        onLoadPreset={handleLoadPreset}
        onSaveAsPreset={handleSaveAsPreset}
        onRefreshPresets={loadAvailablePresets}
      />
    </div>
  );
}
