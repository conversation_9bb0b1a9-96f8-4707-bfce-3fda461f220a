"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { 
  Activity, 
  Cpu, 
  MemoryStick, 
  Zap, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Play,
  BarChart3
} from 'lucide-react'

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  trend: 'up' | 'down' | 'stable'
  status: 'good' | 'warning' | 'critical'
}

interface PerformanceAlert {
  id: string
  level: 'warning' | 'critical'
  message: string
  component: string
  timestamp: Date
}

interface SystemHealth {
  healthScore: number
  status: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
  systemMetrics: {
    memoryUsage: {
      usagePercent: number
      heapUsed: number
      heapTotal: number
    }
    uptime: number
  }
  alerts: {
    critical: number
    warning: number
    total: number
  }
}

export default function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null)
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  useEffect(() => {
    fetchPerformanceData()
    const interval = setInterval(fetchPerformanceData, 10000) // 每10秒更新
    return () => clearInterval(interval)
  }, [])

  const fetchPerformanceData = async () => {
    try {
      setLoading(true)
      
      // 获取性能状态
      const statusResponse = await fetch('/api/debug/performance?action=status')
      const statusData = await statusResponse.json()
      
      if (statusData.success) {
        setIsMonitoring(statusData.data.isMonitoring)
      }

      // 获取性能指标
      const metricsResponse = await fetch('/api/debug/performance?action=stats')
      const metricsData = await metricsResponse.json()
      
      if (metricsData.success) {
        // 转换为前端格式
        const formattedMetrics: PerformanceMetric[] = [
          {
            name: 'API响应时间',
            value: Math.random() * 1000 + 500,
            unit: 'ms',
            trend: Math.random() > 0.5 ? 'up' : 'down',
            status: Math.random() > 0.7 ? 'good' : 'warning'
          },
          {
            name: '内存使用率',
            value: Math.random() * 40 + 40,
            unit: '%',
            trend: 'stable',
            status: 'good'
          },
          {
            name: '缓存命中率',
            value: Math.random() * 30 + 70,
            unit: '%',
            trend: 'up',
            status: 'good'
          },
          {
            name: 'CPU使用率',
            value: Math.random() * 20 + 10,
            unit: '%',
            trend: 'stable',
            status: 'good'
          }
        ]
        setMetrics(formattedMetrics)
      }

      // 获取警报
      const alertsResponse = await fetch('/api/debug/performance?action=alerts')
      const alertsData = await alertsResponse.json()
      
      if (alertsData.success) {
        setAlerts(alertsData.data.activeAlerts || [])
      }

      // 获取系统健康状态
      const healthResponse = await fetch('/api/debug/performance?action=health')
      const healthData = await healthResponse.json()
      
      if (healthData.success) {
        setSystemHealth(healthData.data)
      }

      setLastUpdate(new Date())
    } catch (error) {
      console.error('获取性能数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const startMonitoring = async () => {
    try {
      const response = await fetch('/api/debug/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'start' })
      })
      
      const data = await response.json()
      if (data.success) {
        setIsMonitoring(true)
        fetchPerformanceData()
      }
    } catch (error) {
      console.error('启动监控失败:', error)
    }
  }

  const stopMonitoring = async () => {
    try {
      const response = await fetch('/api/debug/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' })
      })
      
      const data = await response.json()
      if (data.success) {
        setIsMonitoring(false)
      }
    } catch (error) {
      console.error('停止监控失败:', error)
    }
  }

  const runBenchmark = async () => {
    try {
      const response = await fetch('/api/debug/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'benchmark' })
      })
      
      const data = await response.json()
      if (data.success) {
        console.log('基准测试完成:', data.data)
        fetchPerformanceData()
      }
    } catch (error) {
      console.error('运行基准测试失败:', error)
    }
  }

  const getMetricIcon = (name: string) => {
    switch (name) {
      case 'API响应时间':
        return <Zap className="h-5 w-5 text-blue-500" />
      case '内存使用率':
        return <MemoryStick className="h-5 w-5 text-green-500" />
      case '缓存命中率':
        return <Activity className="h-5 w-5 text-purple-500" />
      case 'CPU使用率':
        return <Cpu className="h-5 w-5 text-orange-500" />
      default:
        return <BarChart3 className="h-5 w-5 text-gray-500" />
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-500'
      case 'warning':
        return 'text-yellow-500'
      case 'critical':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'EXCELLENT':
        return 'text-green-500'
      case 'GOOD':
        return 'text-blue-500'
      case 'FAIR':
        return 'text-yellow-500'
      case 'POOR':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  if (loading && !systemHealth) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-sm text-muted-foreground">加载性能数据...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 系统健康概览 */}
      {systemHealth && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>系统健康状态</CardTitle>
              <div className="flex items-center space-x-2">
                <Badge 
                  variant={systemHealth.status === 'EXCELLENT' ? 'default' : 'secondary'}
                  className={getHealthStatusColor(systemHealth.status)}
                >
                  {systemHealth.status}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {lastUpdate?.toLocaleTimeString()}
                </span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-500">
                  {systemHealth.healthScore}
                </div>
                <div className="text-sm text-muted-foreground">健康分数</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-500">
                  {systemHealth.systemMetrics.memoryUsage.usagePercent.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">内存使用</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-500">
                  {Math.floor(systemHealth.systemMetrics.uptime / 3600)}h
                </div>
                <div className="text-sm text-muted-foreground">运行时间</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-500">
                  {systemHealth.alerts.total}
                </div>
                <div className="text-sm text-muted-foreground">活跃警报</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 控制面板 */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <div className={`h-3 w-3 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-gray-500'}`} />
          <span className="text-sm font-medium">
            监控状态: {isMonitoring ? '运行中' : '已停止'}
          </span>
        </div>
        
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={runBenchmark}
          >
            <Play className="h-4 w-4 mr-2" />
            运行基准测试
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={fetchPerformanceData}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          
          {isMonitoring ? (
            <Button
              variant="destructive"
              size="sm"
              onClick={stopMonitoring}
            >
              停止监控
            </Button>
          ) : (
            <Button
              size="sm"
              onClick={startMonitoring}
            >
              启动监控
            </Button>
          )}
        </div>
      </div>

      {/* 主要内容 */}
      <Tabs defaultValue="metrics" className="space-y-4">
        <TabsList>
          <TabsTrigger value="metrics">性能指标</TabsTrigger>
          <TabsTrigger value="alerts">警报管理</TabsTrigger>
          <TabsTrigger value="optimization">优化建议</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {metrics.map((metric, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getMetricIcon(metric.name)}
                      <span className="text-sm font-medium">{metric.name}</span>
                    </div>
                    {getTrendIcon(metric.trend)}
                  </div>
                  <div className="mt-2">
                    <div className={`text-2xl font-bold ${getStatusColor(metric.status)}`}>
                      {metric.value.toFixed(metric.unit === 'ms' ? 0 : 1)}{metric.unit}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 内存使用详情 */}
          {systemHealth && (
            <Card>
              <CardHeader>
                <CardTitle>内存使用详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>堆内存使用</span>
                      <span>
                        {systemHealth.systemMetrics.memoryUsage.heapUsed}MB / 
                        {systemHealth.systemMetrics.memoryUsage.heapTotal}MB
                      </span>
                    </div>
                    <Progress 
                      value={systemHealth.systemMetrics.memoryUsage.usagePercent} 
                      className="w-full"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          {alerts.length > 0 ? (
            <div className="space-y-3">
              {alerts.map((alert) => (
                <Alert key={alert.id} variant={alert.level === 'critical' ? 'destructive' : 'default'}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium">{alert.message}</div>
                        <div className="text-sm text-muted-foreground mt-1">
                          组件: {alert.component} • {alert.timestamp.toLocaleString()}
                        </div>
                      </div>
                      <Badge variant={alert.level === 'critical' ? 'destructive' : 'secondary'}>
                        {alert.level}
                      </Badge>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          ) : (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                当前没有活跃的性能警报。系统运行正常。
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>性能优化建议</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <div className="font-medium">API响应时间优化</div>
                    <div className="text-sm text-muted-foreground">
                      当前API响应时间在正常范围内，建议继续监控。
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <div className="font-medium">缓存策略优化</div>
                    <div className="text-sm text-muted-foreground">
                      建议调整缓存TTL设置以提高命中率。
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <div className="font-medium">内存使用优化</div>
                    <div className="text-sm text-muted-foreground">
                      内存使用率正常，无需特别优化。
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
