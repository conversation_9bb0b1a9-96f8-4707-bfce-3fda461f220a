"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Settings,
  Brain,
  Database,
  Zap,
  RotateCcw,
  Save,
  Eye,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

// TODO: 重构后重新启用
// import { useDualCoreRealtime } from '@/lib/hooks/useDualCoreRealtime';
import { PerformanceChart } from './PerformanceChart';
import { RealtimeLogViewer } from './RealtimeLogViewer';

// TODO: 重构后重新启用
// import type {
//   IntelligentCacheConfig,
//   ContextPackagingConfig,
//   CacheSnapshot,
//   WeightingConfig,
//   EliminationConfig,
//   ResetConfig,
//   ContextSortingStrategy
// } from '@/lib/services/dual-core-abstraction';

// 临时类型定义
type IntelligentCacheConfig = any;
type ContextPackagingConfig = any;
type CacheSnapshot = any;
type WeightingConfig = any;
type EliminationConfig = any;
type ResetConfig = any;
type ContextSortingStrategy = any;

interface DualCoreDebugPanelProps {
  onConfigChange?: (type: 'cache' | 'packaging', config: any) => void;
  onTestAction?: (action: string, params?: any) => void;
}

export function DualCoreDebugPanel({ onConfigChange, onTestAction }: DualCoreDebugPanelProps) {
  // TODO: 重构后重新启用实时状态管理
  // const [realtimeState, realtimeActions] = useDualCoreRealtime();
  const realtimeState = {
    isConnected: false,
    performanceData: [],
    logs: [],
    cacheSnapshot: {
      currentRanking: ['id1', 'id2', 'id3'],
      totalOperations: 42,
      lastOperation: '模拟操作',
      statistics: {
        hitRate: 0.85,
        averageLatency: 120,
        totalOperations: 42,
        resetCount: 2,
        eliminationCount: 5,
        averageProcessingTime: 125.5
      }
    },
    lastUpdate: new Date().toISOString(),
    realtimeLogs: [
      { id: '1', timestamp: new Date().toISOString(), level: 'info' as const, message: '模拟日志消息1' },
      { id: '2', timestamp: new Date().toISOString(), level: 'warn' as const, message: '模拟日志消息2' }
    ],
    systemEvents: [
      { timestamp: new Date().toISOString(), type: 'cache_update', data: {} },
      { timestamp: new Date().toISOString(), type: 'config_change', data: {} }
    ],
    performanceMetrics: {
      processingTimes: [100, 120, 90, 110],
      cacheHitRates: [0.8, 0.85, 0.9, 0.88],
      qualityScores: [0.9, 0.92, 0.88, 0.91],
      timestamps: [
        new Date(Date.now() - 3000).toISOString(),
        new Date(Date.now() - 2000).toISOString(),
        new Date(Date.now() - 1000).toISOString(),
        new Date().toISOString()
      ]
    }
  };
  const realtimeActions = {
    connect: () => {},
    disconnect: () => {},
    executeTest: async (action: string, params?: any) => ({ success: true, message: '模拟测试执行' }),
    updateCacheConfig: async (config: any) => ({ success: true }),
    updatePackagingConfig: async (config: any) => ({ success: true }),
    refreshStatus: () => {},
    exportDebugData: () => ({ data: '模拟调试数据' }),
    resetSystem: async () => ({ success: true }),
    clearLogs: () => {}
  };
  // 缓存层配置状态
  const [cacheConfig, setCacheConfig] = useState<IntelligentCacheConfig>({
    weighting: {
      weights: {
        current: 1.0,
        'T-1': 0.8,
        'T-2': 0.6,
        'T-3': 0.4
      },
      enableTimeDecay: true,
      timeDecayFactor: 0.95
    },
    elimination: {
      enabled: true,
      maxRetainedIds: 50,
      eliminationThreshold: 0.8,
      minRetainedIds: 10
    },
    reset: {
      enabled: true,
      noveltyThreshold: 0.5,
      topicChangeThreshold: 0.7,
      forceResetInterval: 24
    },
    debugMode: true,
    version: '2.1.0'
  });

  // 打包工厂配置状态
  const [packagingConfig, setPackagingConfig] = useState<Partial<ContextPackagingConfig>>({
    defaultStrategy: 'balanced',
    globalSettings: {
      maxContextLength: 4000,
      debugMode: true,
      enableQualityAssessment: true,
      defaultFormattingStyle: 'conversational'
    }
  });

  // 当前策略配置
  const [currentStrategy, setCurrentStrategy] = useState<ContextSortingStrategy>({
    name: 'balanced',
    description: '平衡策略：均衡考虑各种因素',
    priorities: {
      userProfile: 0.2,
      deepHistory: 0.3,
      recentDialogue: 0.3,
      completeHistory: 0.2
    },
    sortingRules: {
      temporalRelevance: 0.3,
      topicalRelevance: 0.4,
      emotionalContinuity: 0.2,
      conversationDepth: 0.1
    },
    specialRules: {
      prioritizeUserProfile: true,
      latestMessagesFirst: true,
      groupByTopic: false,
      maxContextTokens: 4000
    }
  });

  // 使用实时状态替代本地状态
  const systemStatus = {
    cacheSnapshot: realtimeState.cacheSnapshot,
    lastUpdate: realtimeState.lastUpdate,
    isConnected: realtimeState.isConnected
  };

  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // 更新缓存配置
  const updateCacheConfig = (updates: Partial<IntelligentCacheConfig>) => {
    const newConfig = { ...cacheConfig, ...updates };
    setCacheConfig(newConfig);
    onConfigChange?.('cache', newConfig);
    showMessage('success', '缓存层配置已更新');
  };

  // 更新打包配置
  const updatePackagingConfig = (updates: Partial<ContextPackagingConfig>) => {
    const newConfig = { ...packagingConfig, ...updates };
    setPackagingConfig(newConfig);
    onConfigChange?.('packaging', newConfig);
    showMessage('success', '打包工厂配置已更新');
  };

  // 更新策略配置
  const updateStrategyConfig = (updates: Partial<ContextSortingStrategy>) => {
    const newStrategy = { ...currentStrategy, ...updates };
    setCurrentStrategy(newStrategy);
    showMessage('info', '策略配置已更新（需保存生效）');
  };

  // 显示消息
  const showMessage = (type: 'success' | 'error' | 'info', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 3000);
  };

  // 测试操作 - 使用实时功能
  const handleTestAction = async (action: string, params?: any) => {
    try {
      showMessage('info', `执行测试操作: ${action}`);
      const result = await realtimeActions.executeTest(action, params);
      onTestAction?.(action, params);
      showMessage('success', `测试操作 ${action} 执行成功`);
      return result;
    } catch (error) {
      showMessage('error', `测试操作 ${action} 失败`);
      throw error;
    }
  };

  return (
    <div className="h-full flex flex-col space-y-4 p-4 bg-[#18181B] text-gray-100">
      {/* 顶部状态栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-blue-400" />
          <h2 className="text-lg font-semibold text-white">双核抽象层调试台</h2>
          <Badge className={`text-xs ${systemStatus.isConnected ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
            {systemStatus.isConnected ? '已连接' : '未连接'}
          </Badge>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => realtimeActions.refreshStatus()}
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            刷新状态
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTestAction('reset_system')}
          >
            <AlertTriangle className="w-4 h-4 mr-1" />
            重置系统
          </Button>
        </div>
      </div>

      {/* 消息提示 */}
      {message && (
        <Alert className={`border-${message.type === 'success' ? 'green' : message.type === 'error' ? 'red' : 'blue'}-500/30 bg-${message.type === 'success' ? 'green' : message.type === 'error' ? 'red' : 'blue'}-500/10`}>
          {message.type === 'success' ? <CheckCircle className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
          <AlertDescription className={`text-${message.type === 'success' ? 'green' : message.type === 'error' ? 'red' : 'blue'}-400`}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="cache-config" className="flex-1 flex flex-col">
        <TabsList className="bg-[#27272A] border-b border-[#27272A] rounded-none w-full justify-start">
          <TabsTrigger value="cache-config" className="text-sm">智能缓存层</TabsTrigger>
          <TabsTrigger value="packaging-config" className="text-sm">上下文打包</TabsTrigger>
          <TabsTrigger value="strategy-config" className="text-sm">排序策略</TabsTrigger>
          <TabsTrigger value="system-status" className="text-sm">系统状态</TabsTrigger>
        </TabsList>

        {/* 智能缓存层配置 */}
        <TabsContent value="cache-config" className="flex-1 space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 加权配置 */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-blue-400" />
                  历史加权衰减配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">当前轮权重 ({cacheConfig.weighting.weights.current})</Label>
                    <Slider
                      value={[cacheConfig.weighting.weights.current]}
                      onValueChange={([value]) => updateCacheConfig({
                        weighting: {
                          ...cacheConfig.weighting,
                          weights: { ...cacheConfig.weighting.weights, current: value }
                        }
                      })}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">T-1权重 ({cacheConfig.weighting.weights['T-1']})</Label>
                    <Slider
                      value={[cacheConfig.weighting.weights['T-1']]}
                      onValueChange={([value]) => updateCacheConfig({
                        weighting: {
                          ...cacheConfig.weighting,
                          weights: { ...cacheConfig.weighting.weights, 'T-1': value }
                        }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">T-2权重 ({cacheConfig.weighting.weights['T-2']})</Label>
                    <Slider
                      value={[cacheConfig.weighting.weights['T-2']]}
                      onValueChange={([value]) => updateCacheConfig({
                        weighting: {
                          ...cacheConfig.weighting,
                          weights: { ...cacheConfig.weighting.weights, 'T-2': value }
                        }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">T-3权重 ({cacheConfig.weighting.weights['T-3']})</Label>
                    <Slider
                      value={[cacheConfig.weighting.weights['T-3']]}
                      onValueChange={([value]) => updateCacheConfig({
                        weighting: {
                          ...cacheConfig.weighting,
                          weights: { ...cacheConfig.weighting.weights, 'T-3': value }
                        }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm text-gray-300">启用时间衰减</Label>
                    <p className="text-xs text-gray-400">根据ID中的时间信息进行衰减</p>
                  </div>
                  <Switch
                    checked={cacheConfig.weighting.enableTimeDecay}
                    onCheckedChange={(checked) => updateCacheConfig({
                      weighting: {
                        ...cacheConfig.weighting,
                        enableTimeDecay: checked
                      }
                    })}
                  />
                </div>

                {cacheConfig.weighting.enableTimeDecay && (
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">时间衰减因子 ({cacheConfig.weighting.timeDecayFactor})</Label>
                    <Slider
                      value={[cacheConfig.weighting.timeDecayFactor]}
                      onValueChange={([value]) => updateCacheConfig({
                        weighting: {
                          ...cacheConfig.weighting,
                          timeDecayFactor: value
                        }
                      })}
                      max={1}
                      min={0.8}
                      step={0.01}
                      className="w-full"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 淘汰配置 */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <Database className="w-4 h-4 mr-2 text-red-400" />
                  末位淘汰配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm text-gray-300">启用末位淘汰</Label>
                    <p className="text-xs text-gray-400">自动移除排名靠后的ID</p>
                  </div>
                  <Switch
                    checked={cacheConfig.elimination.enabled}
                    onCheckedChange={(checked) => updateCacheConfig({
                      elimination: {
                        ...cacheConfig.elimination,
                        enabled: checked
                      }
                    })}
                  />
                </div>

                {cacheConfig.elimination.enabled && (
                  <>
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-300">最大保留ID数</Label>
                      <Input
                        type="number"
                        value={cacheConfig.elimination.maxRetainedIds}
                        onChange={(e) => updateCacheConfig({
                          elimination: {
                            ...cacheConfig.elimination,
                            maxRetainedIds: parseInt(e.target.value) || 50
                          }
                        })}
                        className="bg-[#27272A] border-[#3F3F46]"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm text-gray-300">淘汰阈值 ({(cacheConfig.elimination.eliminationThreshold * 100).toFixed(0)}%)</Label>
                      <Slider
                        value={[cacheConfig.elimination.eliminationThreshold]}
                        onValueChange={([value]) => updateCacheConfig({
                          elimination: {
                            ...cacheConfig.elimination,
                            eliminationThreshold: value
                          }
                        })}
                        max={1}
                        min={0.5}
                        step={0.05}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm text-gray-300">最小保留数</Label>
                      <Input
                        type="number"
                        value={cacheConfig.elimination.minRetainedIds}
                        onChange={(e) => updateCacheConfig({
                          elimination: {
                            ...cacheConfig.elimination,
                            minRetainedIds: parseInt(e.target.value) || 10
                          }
                        })}
                        className="bg-[#27272A] border-[#3F3F46]"
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 重置配置 */}
          <Card className="bg-[#1F1F23] border-[#27272A]">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-gray-300 flex items-center">
                <RotateCcw className="w-4 h-4 mr-2 text-yellow-400" />
                归零重置配置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-gray-300">启用归零重置</Label>
                  <p className="text-xs text-gray-400">在检测到主题变化时自动重置</p>
                </div>
                <Switch
                  checked={cacheConfig.reset.enabled}
                  onCheckedChange={(checked) => updateCacheConfig({
                    reset: {
                      ...cacheConfig.reset,
                      enabled: checked
                    }
                  })}
                />
              </div>

              {cacheConfig.reset.enabled && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">新颖度阈值 ({(cacheConfig.reset.noveltyThreshold * 100).toFixed(0)}%)</Label>
                    <Slider
                      value={[cacheConfig.reset.noveltyThreshold]}
                      onValueChange={([value]) => updateCacheConfig({
                        reset: {
                          ...cacheConfig.reset,
                          noveltyThreshold: value
                        }
                      })}
                      max={1}
                      min={0.2}
                      step={0.05}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">主题变化阈值 ({(cacheConfig.reset.topicChangeThreshold * 100).toFixed(0)}%)</Label>
                    <Slider
                      value={[cacheConfig.reset.topicChangeThreshold]}
                      onValueChange={([value]) => updateCacheConfig({
                        reset: {
                          ...cacheConfig.reset,
                          topicChangeThreshold: value
                        }
                      })}
                      max={1}
                      min={0.3}
                      step={0.05}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">强制重置间隔（小时）</Label>
                    <Input
                      type="number"
                      value={cacheConfig.reset.forceResetInterval}
                      onChange={(e) => updateCacheConfig({
                        reset: {
                          ...cacheConfig.reset,
                          forceResetInterval: parseInt(e.target.value) || 24
                        }
                      })}
                      className="bg-[#27272A] border-[#3F3F46]"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 测试操作 */}
          <Card className="bg-[#1F1F23] border-[#27272A]">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-gray-300 flex items-center">
                <Zap className="w-4 h-4 mr-2 text-green-400" />
                缓存层测试操作
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  onClick={() => handleTestAction('simulate_retrieval')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  模拟检索
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleTestAction('force_elimination')}
                >
                  <Database className="w-4 h-4 mr-1" />
                  强制淘汰
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleTestAction('force_reset')}
                >
                  <RotateCcw className="w-4 h-4 mr-1" />
                  强制重置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 上下文打包配置 */}
        <TabsContent value="packaging-config" className="flex-1 space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 数据源权重配置 */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <Brain className="w-4 h-4 mr-2 text-purple-400" />
                  数据源优先级权重
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">用户画像权重 ({currentStrategy.priorities.userProfile})</Label>
                    <Slider
                      value={[currentStrategy.priorities.userProfile]}
                      onValueChange={([value]) => updateStrategyConfig({
                        priorities: { ...currentStrategy.priorities, userProfile: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">深度历史权重 ({currentStrategy.priorities.deepHistory})</Label>
                    <Slider
                      value={[currentStrategy.priorities.deepHistory]}
                      onValueChange={([value]) => updateStrategyConfig({
                        priorities: { ...currentStrategy.priorities, deepHistory: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">近期对话权重 ({currentStrategy.priorities.recentDialogue})</Label>
                    <Slider
                      value={[currentStrategy.priorities.recentDialogue]}
                      onValueChange={([value]) => updateStrategyConfig({
                        priorities: { ...currentStrategy.priorities, recentDialogue: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">完整历史权重 ({currentStrategy.priorities.completeHistory})</Label>
                    <Slider
                      value={[currentStrategy.priorities.completeHistory]}
                      onValueChange={([value]) => updateStrategyConfig({
                        priorities: { ...currentStrategy.priorities, completeHistory: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 排序规则权重 */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-green-400" />
                  排序规则权重
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">时间相关性 ({currentStrategy.sortingRules.temporalRelevance})</Label>
                    <Slider
                      value={[currentStrategy.sortingRules.temporalRelevance]}
                      onValueChange={([value]) => updateStrategyConfig({
                        sortingRules: { ...currentStrategy.sortingRules, temporalRelevance: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">主题相关性 ({currentStrategy.sortingRules.topicalRelevance})</Label>
                    <Slider
                      value={[currentStrategy.sortingRules.topicalRelevance]}
                      onValueChange={([value]) => updateStrategyConfig({
                        sortingRules: { ...currentStrategy.sortingRules, topicalRelevance: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">情感连续性 ({currentStrategy.sortingRules.emotionalContinuity})</Label>
                    <Slider
                      value={[currentStrategy.sortingRules.emotionalContinuity]}
                      onValueChange={([value]) => updateStrategyConfig({
                        sortingRules: { ...currentStrategy.sortingRules, emotionalContinuity: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">对话深度 ({currentStrategy.sortingRules.conversationDepth})</Label>
                    <Slider
                      value={[currentStrategy.sortingRules.conversationDepth]}
                      onValueChange={([value]) => updateStrategyConfig({
                        sortingRules: { ...currentStrategy.sortingRules, conversationDepth: value }
                      })}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 特殊规则配置 */}
          <Card className="bg-[#1F1F23] border-[#27272A]">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-gray-300 flex items-center">
                <Settings className="w-4 h-4 mr-2 text-blue-400" />
                特殊处理规则
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm text-gray-300">优先显示用户画像</Label>
                    <p className="text-xs text-gray-400">将用户画像置于上下文顶部</p>
                  </div>
                  <Switch
                    checked={currentStrategy.specialRules.prioritizeUserProfile}
                    onCheckedChange={(checked) => updateStrategyConfig({
                      specialRules: { ...currentStrategy.specialRules, prioritizeUserProfile: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm text-gray-300">最新消息置顶</Label>
                    <p className="text-xs text-gray-400">将最新的消息排在前面</p>
                  </div>
                  <Switch
                    checked={currentStrategy.specialRules.latestMessagesFirst}
                    onCheckedChange={(checked) => updateStrategyConfig({
                      specialRules: { ...currentStrategy.specialRules, latestMessagesFirst: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm text-gray-300">按主题分组</Label>
                    <p className="text-xs text-gray-400">将相同主题的内容聚合</p>
                  </div>
                  <Switch
                    checked={currentStrategy.specialRules.groupByTopic}
                    onCheckedChange={(checked) => updateStrategyConfig({
                      specialRules: { ...currentStrategy.specialRules, groupByTopic: checked }
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">最大上下文Token数</Label>
                  <Input
                    type="number"
                    value={currentStrategy.specialRules.maxContextTokens}
                    onChange={(e) => updateStrategyConfig({
                      specialRules: { ...currentStrategy.specialRules, maxContextTokens: parseInt(e.target.value) || 4000 }
                    })}
                    className="bg-[#27272A] border-[#3F3F46]"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 排序策略配置 */}
        <TabsContent value="strategy-config" className="flex-1 space-y-4">
          <Card className="bg-[#1F1F23] border-[#27272A]">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-gray-300 flex items-center">
                <Settings className="w-4 h-4 mr-2 text-blue-400" />
                当前策略: {currentStrategy.name}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">策略描述</Label>
                <Input
                  value={currentStrategy.description}
                  onChange={(e) => updateStrategyConfig({ description: e.target.value })}
                  className="bg-[#27272A] border-[#3F3F46]"
                  placeholder="策略描述..."
                />
              </div>

              <div className="flex space-x-2">
                <Button
                  size="sm"
                  onClick={() => handleTestAction('save_strategy', currentStrategy)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-4 h-4 mr-1" />
                  保存策略
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleTestAction('preview_strategy', currentStrategy)}
                >
                  <Eye className="w-4 h-4 mr-1" />
                  预览效果
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setCurrentStrategy({
                    name: 'balanced',
                    description: '平衡策略：均衡考虑各种因素',
                    priorities: { userProfile: 0.2, deepHistory: 0.3, recentDialogue: 0.3, completeHistory: 0.2 },
                    sortingRules: { temporalRelevance: 0.3, topicalRelevance: 0.4, emotionalContinuity: 0.2, conversationDepth: 0.1 },
                    specialRules: { prioritizeUserProfile: true, latestMessagesFirst: true, groupByTopic: false, maxContextTokens: 4000 }
                  })}
                >
                  <RotateCcw className="w-4 h-4 mr-1" />
                  重置为默认
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 系统状态监控 */}
        <TabsContent value="system-status" className="flex-1 space-y-4">
          {/* 性能图表 */}
          <PerformanceChart
            metrics={realtimeState.performanceMetrics}
            className="mb-4"
          />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 缓存层状态 */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <Database className="w-4 h-4 mr-2 text-blue-400" />
                  智能缓存层状态
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {systemStatus.cacheSnapshot ? (
                  <>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-400">当前排名数量</span>
                      <span className="text-sm text-white">{systemStatus.cacheSnapshot.currentRanking.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-400">总操作次数</span>
                      <span className="text-sm text-white">{systemStatus.cacheSnapshot.statistics.totalOperations}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-400">重置次数</span>
                      <span className="text-sm text-white">{systemStatus.cacheSnapshot.statistics.resetCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-400">平均处理时间</span>
                      <span className="text-sm text-white">{systemStatus.cacheSnapshot.statistics.averageProcessingTime.toFixed(1)}ms</span>
                    </div>
                  </>
                ) : (
                  <div className="text-center text-gray-400 py-4">
                    <span className="text-sm">暂无缓存层数据</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 系统健康状态 */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-green-400" />
                  系统健康状态
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">连接状态</span>
                  <Badge className={`text-xs ${systemStatus.isConnected ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                    {systemStatus.isConnected ? '已连接' : '未连接'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">最后更新</span>
                  <span className="text-sm text-white">
                    {systemStatus.lastUpdate ? new Date(systemStatus.lastUpdate).toLocaleTimeString() : '未知'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">配置版本</span>
                  <span className="text-sm text-white">{cacheConfig.version}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">调试模式</span>
                  <Badge className={`text-xs ${cacheConfig.debugMode ? 'bg-yellow-500/20 text-yellow-400' : 'bg-gray-500/20 text-gray-400'}`}>
                    {cacheConfig.debugMode ? '已启用' : '已禁用'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">实时日志数量</span>
                  <Badge variant="outline" className="text-xs">
                    {realtimeState.realtimeLogs.length}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">系统事件数量</span>
                  <Badge variant="outline" className="text-xs">
                    {realtimeState.systemEvents.length}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 实时日志查看器 */}
          <RealtimeLogViewer
            logs={realtimeState.realtimeLogs}
            onClearLogs={realtimeActions.clearLogs}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
