"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus, Clock, Zap, Target } from 'lucide-react';

interface PerformanceMetrics {
  processingTimes: number[];
  cacheHitRates: number[];
  qualityScores: number[];
  timestamps: string[];
}

interface PerformanceChartProps {
  metrics: PerformanceMetrics;
  className?: string;
}

export function PerformanceChart({ metrics, className = "" }: PerformanceChartProps) {
  // 计算趋势
  const calculateTrend = (values: number[]): 'up' | 'down' | 'stable' => {
    if (values.length < 2) return 'stable';
    
    const recent = values.slice(-3);
    const earlier = values.slice(-6, -3);
    
    if (recent.length === 0 || earlier.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length;
    
    const diff = recentAvg - earlierAvg;
    const threshold = earlierAvg * 0.1; // 10% 变化阈值
    
    if (diff > threshold) return 'up';
    if (diff < -threshold) return 'down';
    return 'stable';
  };

  // 获取最新值
  const getLatestValue = (values: number[]): number => {
    return values.length > 0 ? values[values.length - 1] : 0;
  };

  // 获取平均值
  const getAverageValue = (values: number[]): number => {
    if (values.length === 0) return 0;
    return values.reduce((a, b) => a + b, 0) / values.length;
  };

  // 渲染趋势图标
  const renderTrendIcon = (trend: 'up' | 'down' | 'stable', isGoodTrend: boolean = true) => {
    const colorClass = trend === 'stable' ? 'text-gray-400' : 
                      (trend === 'up' && isGoodTrend) || (trend === 'down' && !isGoodTrend) ? 'text-green-400' : 'text-red-400';
    
    switch (trend) {
      case 'up':
        return <TrendingUp className={`w-4 h-4 ${colorClass}`} />;
      case 'down':
        return <TrendingDown className={`w-4 h-4 ${colorClass}`} />;
      default:
        return <Minus className={`w-4 h-4 ${colorClass}`} />;
    }
  };

  // 渲染简化的折线图
  const renderMiniChart = (values: number[], color: string = 'blue') => {
    if (values.length < 2) {
      return (
        <div className="h-8 flex items-center justify-center text-gray-400 text-xs">
          暂无数据
        </div>
      );
    }

    const max = Math.max(...values);
    const min = Math.min(...values);
    const range = max - min || 1;

    const points = values.map((value, index) => {
      const x = (index / (values.length - 1)) * 100;
      const y = 100 - ((value - min) / range) * 100;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="h-8 w-full">
        <svg width="100%" height="100%" viewBox="0 0 100 100" className="overflow-visible">
          <polyline
            points={points}
            fill="none"
            stroke={`var(--${color}-400)`}
            strokeWidth="2"
            className="drop-shadow-sm"
          />
          {/* 数据点 */}
          {values.map((value, index) => {
            const x = (index / (values.length - 1)) * 100;
            const y = 100 - ((value - min) / range) * 100;
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="1.5"
                fill={`var(--${color}-400)`}
                className="drop-shadow-sm"
              />
            );
          })}
        </svg>
      </div>
    );
  };

  const processingTimeTrend = calculateTrend(metrics.processingTimes);
  const cacheHitRateTrend = calculateTrend(metrics.cacheHitRates);
  const qualityScoreTrend = calculateTrend(metrics.qualityScores);

  const latestProcessingTime = getLatestValue(metrics.processingTimes);
  const latestCacheHitRate = getLatestValue(metrics.cacheHitRates);
  const latestQualityScore = getLatestValue(metrics.qualityScores);

  const avgProcessingTime = getAverageValue(metrics.processingTimes);
  const avgCacheHitRate = getAverageValue(metrics.cacheHitRates);
  const avgQualityScore = getAverageValue(metrics.qualityScores);

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-3 gap-4 ${className}`}>
      {/* 处理时间指标 */}
      <Card className="bg-[#1F1F23] border-[#27272A]">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-gray-300 flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2 text-blue-400" />
              处理时间
            </div>
            {renderTrendIcon(processingTimeTrend, false)} {/* 处理时间越低越好 */}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-400">当前</span>
            <Badge variant="outline" className="text-blue-400 border-blue-400/30">
              {latestProcessingTime.toFixed(1)}ms
            </Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-400">平均</span>
            <span className="text-xs text-gray-300">{avgProcessingTime.toFixed(1)}ms</span>
          </div>
          {renderMiniChart(metrics.processingTimes, 'blue')}
        </CardContent>
      </Card>

      {/* 缓存命中率指标 */}
      <Card className="bg-[#1F1F23] border-[#27272A]">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-gray-300 flex items-center justify-between">
            <div className="flex items-center">
              <Zap className="w-4 h-4 mr-2 text-green-400" />
              缓存命中率
            </div>
            {renderTrendIcon(cacheHitRateTrend, true)} {/* 命中率越高越好 */}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-400">当前</span>
            <Badge variant="outline" className="text-green-400 border-green-400/30">
              {(latestCacheHitRate * 100).toFixed(1)}%
            </Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-400">平均</span>
            <span className="text-xs text-gray-300">{(avgCacheHitRate * 100).toFixed(1)}%</span>
          </div>
          {renderMiniChart(metrics.cacheHitRates.map(rate => rate * 100), 'green')}
        </CardContent>
      </Card>

      {/* 质量评分指标 */}
      <Card className="bg-[#1F1F23] border-[#27272A]">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-gray-300 flex items-center justify-between">
            <div className="flex items-center">
              <Target className="w-4 h-4 mr-2 text-purple-400" />
              质量评分
            </div>
            {renderTrendIcon(qualityScoreTrend, true)} {/* 质量评分越高越好 */}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-400">当前</span>
            <Badge variant="outline" className="text-purple-400 border-purple-400/30">
              {(latestQualityScore * 100).toFixed(1)}%
            </Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-400">平均</span>
            <span className="text-xs text-gray-300">{(avgQualityScore * 100).toFixed(1)}%</span>
          </div>
          {renderMiniChart(metrics.qualityScores.map(score => score * 100), 'purple')}
        </CardContent>
      </Card>
    </div>
  );
}
