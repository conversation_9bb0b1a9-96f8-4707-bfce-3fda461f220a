"use client"

import React, { useEffect, useRef, useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Eye, 
  Trash2, 
  Download, 
  Filter,
  Info,
  AlertTriangle,
  XCircle,
  CheckCircle,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'success';
  message: string;
  data?: any;
}

interface RealtimeLogViewerProps {
  logs: LogEntry[];
  onClearLogs: () => void;
  className?: string;
}

export function RealtimeLogViewer({ logs, onClearLogs, className = "" }: RealtimeLogViewerProps) {
  const [filter, setFilter] = useState<'all' | 'info' | 'warn' | 'error' | 'success'>('all');
  const [autoScroll, setAutoScroll] = useState(true);
  const [expandedLogs, setExpandedLogs] = useState<Set<string>>(new Set());
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [logs, autoScroll]);

  // 过滤日志
  const filteredLogs = logs.filter(log => filter === 'all' || log.level === filter);

  // 获取日志级别图标
  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'info':
        return <Info className="w-4 h-4 text-blue-400" />;
      case 'warn':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      default:
        return <Info className="w-4 h-4 text-gray-400" />;
    }
  };

  // 获取日志级别颜色
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'info':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/30';
      case 'warn':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';
      case 'error':
        return 'text-red-400 bg-red-400/10 border-red-400/30';
      case 'success':
        return 'text-green-400 bg-green-400/10 border-green-400/30';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/30';
    }
  };

  // 切换日志展开状态
  const toggleLogExpansion = (logId: string) => {
    setExpandedLogs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(logId)) {
        newSet.delete(logId);
      } else {
        newSet.add(logId);
      }
      return newSet;
    });
  };

  // 导出日志
  const exportLogs = () => {
    const logData = filteredLogs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      message: log.message,
      data: log.data
    }));

    const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dual-core-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  // 格式化数据
  const formatData = (data: any) => {
    if (!data) return null;
    
    try {
      return JSON.stringify(data, null, 2);
    } catch {
      return String(data);
    }
  };

  return (
    <Card className={`bg-[#1F1F23] border-[#27272A] ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-gray-300 flex items-center">
            <Eye className="w-4 h-4 mr-2 text-purple-400" />
            实时操作日志
            <Badge variant="outline" className="ml-2 text-xs">
              {filteredLogs.length}/{logs.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {/* 过滤器 */}
            <div className="flex items-center space-x-1">
              <Filter className="w-3 h-3 text-gray-400" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="bg-[#27272A] border border-[#3F3F46] rounded px-2 py-1 text-xs text-gray-300"
              >
                <option value="all">全部</option>
                <option value="info">信息</option>
                <option value="warn">警告</option>
                <option value="error">错误</option>
                <option value="success">成功</option>
              </select>
            </div>

            {/* 自动滚动开关 */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoScroll(!autoScroll)}
              className={`text-xs ${autoScroll ? 'bg-blue-500/20 text-blue-400' : ''}`}
            >
              自动滚动
            </Button>

            {/* 导出按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={exportLogs}
              disabled={filteredLogs.length === 0}
            >
              <Download className="w-3 h-3 mr-1" />
              导出
            </Button>

            {/* 清空按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={onClearLogs}
              disabled={logs.length === 0}
            >
              <Trash2 className="w-3 h-3 mr-1" />
              清空
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <ScrollArea className="h-64" ref={scrollAreaRef}>
          <div className="p-4 space-y-2">
            {filteredLogs.length === 0 ? (
              <div className="text-center text-gray-400 py-8">
                <Eye className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">暂无日志记录</p>
              </div>
            ) : (
              filteredLogs.map((log) => {
                const isExpanded = expandedLogs.has(log.id);
                const hasData = log.data !== undefined && log.data !== null;
                
                return (
                  <div
                    key={log.id}
                    className="border border-[#27272A] rounded-lg p-3 hover:bg-[#27272A]/50 transition-colors"
                  >
                    <div className="flex items-start space-x-3">
                      {/* 级别图标 */}
                      <div className="flex-shrink-0 mt-0.5">
                        {getLevelIcon(log.level)}
                      </div>
                      
                      {/* 日志内容 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Badge className={`text-xs ${getLevelColor(log.level)}`}>
                              {log.level.toUpperCase()}
                            </Badge>
                            <span className="text-xs text-gray-400 font-mono">
                              {formatTimestamp(log.timestamp)}
                            </span>
                          </div>
                          
                          {hasData && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleLogExpansion(log.id)}
                              className="h-6 w-6 p-0"
                            >
                              {isExpanded ? (
                                <ChevronDown className="w-3 h-3" />
                              ) : (
                                <ChevronRight className="w-3 h-3" />
                              )}
                            </Button>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-300 mt-1 break-words">
                          {log.message}
                        </p>
                        
                        {hasData && isExpanded && (
                          <div className="mt-2 p-2 bg-[#0F0F11] rounded border border-[#27272A]">
                            <pre className="text-xs text-gray-400 whitespace-pre-wrap overflow-x-auto">
                              {formatData(log.data)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
