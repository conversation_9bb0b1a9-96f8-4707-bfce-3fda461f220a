"use client"

import { useState, useEffect } from "react"
import { BaseDebugger, useDebuggerState, BaseDebuggerConfig } from "./BaseDebugger"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Trash2, 
  RefreshCw, 
  Database, 
  AlertTriangle, 
  CheckCircle, 
  Download,
  Upload,
  HardDrive,
  MessageSquare,
  Lightbulb,
  FileText
} from "lucide-react"

// 数据统计接口
interface DataStats {
  chatMessages: {
    count: number
    size: number // bytes
    lastUpdated: string
  }
  dailyInsights: {
    count: number
    size: number
    lastUpdated: string
  }
  knowledgeBase: {
    files: number
    chunks: number
    size: number
    lastUpdated: string
  }
  vectorDatabase: {
    hotStore: {
      entries: number
      size: number
    }
    coldStore: {
      entries: number
      size: number
    }
  }
  totalSize: number
  lastBackup?: string
}

// 操作状态接口
interface OperationStatus {
  [key: string]: {
    loading: boolean
    success: boolean | null
    error: string | null
  }
}

interface UnifiedDataManagerProps {
  onStatsChange?: (stats: DataStats) => void
  onOperationComplete?: (operation: string, success: boolean) => void
}

export function UnifiedDataManager({ onStatsChange, onOperationComplete }: UnifiedDataManagerProps) {
  // 调试器状态管理
  const { state, setLoading, setError, setSuccess } = useDebuggerState()

  // 数据统计状态
  const [stats, setStats] = useState<DataStats>({
    chatMessages: { count: 0, size: 0, lastUpdated: '' },
    dailyInsights: { count: 0, size: 0, lastUpdated: '' },
    knowledgeBase: { files: 0, chunks: 0, size: 0, lastUpdated: '' },
    vectorDatabase: {
      hotStore: { entries: 0, size: 0 },
      coldStore: { entries: 0, size: 0 }
    },
    totalSize: 0
  })

  // 操作状态
  const [operationStatus, setOperationStatus] = useState<OperationStatus>({})

  // 调试器配置
  const debuggerConfig: BaseDebuggerConfig = {
    title: "统一数据管理器",
    description: "管理 SelfMirror 的所有数据，包括聊天记录、洞察、知识库和向量数据库",
    autoRefresh: true,
    refreshInterval: 30000,
    showLastUpdated: true,
    showStatus: true
  }

  // 加载数据统计
  const loadStats = async () => {
    try {
      // 并行获取各种数据统计
      const [chatResponse, insightResponse, vectorResponse] = await Promise.all([
        fetch('/api/debug/stats/chat'),
        fetch('/api/debug/stats/insights'),
        fetch('/api/debug/stats/vector')
      ])

      const chatData = chatResponse.ok ? await chatResponse.json() : { count: 0, size: 0, lastUpdated: '' }
      const insightData = insightResponse.ok ? await insightResponse.json() : { count: 0, size: 0, lastUpdated: '' }
      const vectorData = vectorResponse.ok ? await vectorResponse.json() : {
        hotStore: { entries: 0, size: 0 },
        coldStore: { entries: 0, size: 0 }
      }

      const newStats: DataStats = {
        chatMessages: chatData,
        dailyInsights: insightData,
        knowledgeBase: {
          files: vectorData.totalFiles || 0,
          chunks: vectorData.totalChunks || 0,
          size: vectorData.totalSize || 0,
          lastUpdated: vectorData.lastUpdated || ''
        },
        vectorDatabase: vectorData,
        totalSize: chatData.size + insightData.size + vectorData.totalSize
      }

      setStats(newStats)
      onStatsChange?.(newStats)
      return newStats
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '获取统计数据失败')
    }
  }

  // 执行操作的通用函数
  const executeOperation = async (
    operationKey: string,
    apiEndpoint: string,
    method: 'POST' | 'DELETE' = 'POST',
    confirmMessage?: string
  ) => {
    // 确认操作
    if (confirmMessage && !confirm(confirmMessage)) {
      return
    }

    // 设置操作状态
    setOperationStatus(prev => ({
      ...prev,
      [operationKey]: { loading: true, success: null, error: null }
    }))

    try {
      const response = await fetch(apiEndpoint, { method })
      
      if (response.ok) {
        setOperationStatus(prev => ({
          ...prev,
          [operationKey]: { loading: false, success: true, error: null }
        }))
        
        // 刷新统计数据
        await loadStats()
        onOperationComplete?.(operationKey, true)
      } else {
        throw new Error(`操作失败: ${response.statusText}`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '操作失败'
      setOperationStatus(prev => ({
        ...prev,
        [operationKey]: { loading: false, success: false, error: errorMessage }
      }))
      onOperationComplete?.(operationKey, false)
    }
  }

  // 清空聊天历史
  const clearChatHistory = () => executeOperation(
    'clearChat',
    '/api/debug/clear-chat',
    'POST',
    '确定要清空所有聊天历史吗？此操作不可撤销。'
  )

  // 重置每日洞察
  const resetDailyInsights = () => executeOperation(
    'resetInsights',
    '/api/debug/reset-insights',
    'POST',
    '确定要重置每日洞察数据吗？此操作不可撤销。'
  )

  // 清理知识库
  const clearKnowledgeBase = () => executeOperation(
    'clearKnowledge',
    '/api/debug/clear-knowledge',
    'POST',
    '确定要清空知识库吗？此操作不可撤销。'
  )

  // 重建向量数据库
  const rebuildVectorDB = () => executeOperation(
    'rebuildVector',
    '/api/debug/rebuild-vector',
    'POST',
    '确定要重建向量数据库吗？这可能需要较长时间。'
  )

  // 导出数据
  const exportData = async (dataType: 'all' | 'chat' | 'insights' | 'knowledge') => {
    try {
      const response = await fetch(`/api/debug/export/${dataType}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `selfmirror-${dataType}-${Date.now()}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      setError('导出失败')
    }
  }

  // 格式化文件大小
  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取操作状态指示器
  const getOperationIndicator = (operationKey: string) => {
    const status = operationStatus[operationKey]
    if (!status) return null

    if (status.loading) {
      return <Badge variant="outline">处理中...</Badge>
    }
    if (status.success === true) {
      return <Badge variant="default" className="flex items-center gap-1">
        <CheckCircle className="w-3 h-3" />
        成功
      </Badge>
    }
    if (status.success === false) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertTriangle className="w-3 h-3" />
        失败
      </Badge>
    }
    return null
  }

  // 初始化加载数据
  useEffect(() => {
    loadStats()
  }, [])

  // 调试器操作
  const actions = {
    onRefresh: async () => {
      await loadStats()
    }
  }

  // 头部操作按钮
  const headerActions = (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" onClick={() => exportData('all')}>
        <Download className="w-4 h-4" />
        导出全部
      </Button>
    </div>
  )

  return (
    <BaseDebugger
      config={debuggerConfig}
      state={state}
      actions={actions}
      headerActions={headerActions}
    >
      {/* 总体统计 */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold">{stats.chatMessages.count}</div>
          <div className="text-sm text-muted-foreground">聊天消息</div>
          <div className="text-xs text-muted-foreground">{formatSize(stats.chatMessages.size)}</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold">{stats.dailyInsights.count}</div>
          <div className="text-sm text-muted-foreground">每日洞察</div>
          <div className="text-xs text-muted-foreground">{formatSize(stats.dailyInsights.size)}</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold">{stats.knowledgeBase.files}</div>
          <div className="text-sm text-muted-foreground">知识文件</div>
          <div className="text-xs text-muted-foreground">{formatSize(stats.knowledgeBase.size)}</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold">{formatSize(stats.totalSize)}</div>
          <div className="text-sm text-muted-foreground">总存储</div>
          <div className="text-xs text-muted-foreground">
            {stats.vectorDatabase.hotStore.entries + stats.vectorDatabase.coldStore.entries} 向量
          </div>
        </div>
      </div>

      <Tabs defaultValue="chat" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="chat">聊天数据</TabsTrigger>
          <TabsTrigger value="insights">洞察数据</TabsTrigger>
          <TabsTrigger value="knowledge">知识库</TabsTrigger>
          <TabsTrigger value="vector">向量数据库</TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              <h4 className="font-medium">聊天历史管理</h4>
            </div>
            {getOperationIndicator('clearChat')}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">消息数量</div>
              <div className="text-2xl font-bold">{stats.chatMessages.count}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">存储大小</div>
              <div className="text-2xl font-bold">{formatSize(stats.chatMessages.size)}</div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="destructive"
              onClick={clearChatHistory}
              disabled={operationStatus.clearChat?.loading}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              清空聊天历史
            </Button>
            <Button variant="outline" onClick={() => exportData('chat')}>
              <Download className="w-4 h-4 mr-2" />
              导出聊天数据
            </Button>
          </div>

          {operationStatus.clearChat?.error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{operationStatus.clearChat.error}</AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Lightbulb className="w-5 h-5" />
              <h4 className="font-medium">洞察数据管理</h4>
            </div>
            {getOperationIndicator('resetInsights')}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">洞察数量</div>
              <div className="text-2xl font-bold">{stats.dailyInsights.count}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">存储大小</div>
              <div className="text-2xl font-bold">{formatSize(stats.dailyInsights.size)}</div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="destructive"
              onClick={resetDailyInsights}
              disabled={operationStatus.resetInsights?.loading}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              重置洞察数据
            </Button>
            <Button variant="outline" onClick={() => exportData('insights')}>
              <Download className="w-4 h-4 mr-2" />
              导出洞察数据
            </Button>
          </div>

          {operationStatus.resetInsights?.error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{operationStatus.resetInsights.error}</AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="knowledge" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              <h4 className="font-medium">知识库管理</h4>
            </div>
            {getOperationIndicator('clearKnowledge')}
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">文件数量</div>
              <div className="text-2xl font-bold">{stats.knowledgeBase.files}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">文本块数</div>
              <div className="text-2xl font-bold">{stats.knowledgeBase.chunks}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">存储大小</div>
              <div className="text-2xl font-bold">{formatSize(stats.knowledgeBase.size)}</div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="destructive"
              onClick={clearKnowledgeBase}
              disabled={operationStatus.clearKnowledge?.loading}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              清空知识库
            </Button>
            <Button variant="outline" onClick={() => exportData('knowledge')}>
              <Download className="w-4 h-4 mr-2" />
              导出知识库
            </Button>
          </div>

          {operationStatus.clearKnowledge?.error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{operationStatus.clearKnowledge.error}</AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="vector" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              <h4 className="font-medium">向量数据库管理</h4>
            </div>
            {getOperationIndicator('rebuildVector')}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <h5 className="font-medium">热存储 (Hot Store)</h5>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">条目数量</span>
                  <span className="font-medium">{stats.vectorDatabase.hotStore.entries}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">存储大小</span>
                  <span className="font-medium">{formatSize(stats.vectorDatabase.hotStore.size)}</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h5 className="font-medium">冷存储 (Cold Store)</h5>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">条目数量</span>
                  <span className="font-medium">{stats.vectorDatabase.coldStore.entries}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">存储大小</span>
                  <span className="font-medium">{formatSize(stats.vectorDatabase.coldStore.size)}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="destructive"
              onClick={rebuildVectorDB}
              disabled={operationStatus.rebuildVector?.loading}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              重建向量数据库
            </Button>
          </div>

          {operationStatus.rebuildVector?.error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{operationStatus.rebuildVector.error}</AlertDescription>
            </Alert>
          )}
        </TabsContent>
      </Tabs>
    </BaseDebugger>
  )
}
