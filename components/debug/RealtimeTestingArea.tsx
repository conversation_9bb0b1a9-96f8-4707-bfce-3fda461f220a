'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
// import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { TestResult, WeightAnalysis, PerformanceMetrics } from '@/types/rag';
import { RetrievalResultsViewer } from './RetrievalResultsViewer';
import { WeightAnalysisChart } from './WeightAnalysisChart';
import { PerformanceMetricsDisplay } from './PerformanceMetricsDisplay';

interface RealtimeTestingAreaProps {
  onRunTest: (query: string) => Promise<void>;
  isTesting: boolean;
  lastTestResult: TestResult | null;
  testHistory: TestResult[];
}

export function RealtimeTestingArea({
  onRunTest,
  isTesting,
  lastTestResult,
  testHistory
}: RealtimeTestingAreaProps) {
  const [testQuery, setTestQuery] = useState('');
  const [testMode, setTestMode] = useState<'single' | 'batch' | 'benchmark'>('single');
  const [selectedTestSuite, setSelectedTestSuite] = useState<string>('');
  const [testProgress, setTestProgress] = useState<any>(null);
  const [comparisonMode, setComparisonMode] = useState(false);
  const [selectedResults, setSelectedResults] = useState<string[]>([]);

  const handleRunTest = async () => {
    if (!testQuery.trim()) {
      alert('请输入测试查询');
      return;
    }
    
    await onRunTest(testQuery);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleRunTest();
    }
  };

  // 预设测试用例
  const testSuites = {
    emotional: {
      name: '情感记忆测试',
      queries: [
        '最近让我感到快乐的事情',
        '让我感到焦虑的经历',
        '令人感动的瞬间',
        '失望和挫折的记忆'
      ]
    },
    cognitive: {
      name: '认知分析测试',
      queries: [
        '我的学习方法和技巧',
        '重要的决策过程',
        '思维模式的转变',
        '解决问题的策略'
      ]
    },
    growth: {
      name: '成长轨迹测试',
      queries: [
        '个人能力的突破',
        '价值观的形成',
        '重要的人生转折',
        '未来的目标规划'
      ]
    },
    relationship: {
      name: '人际关系测试',
      queries: [
        '重要的人际互动',
        '亲密关系的发展',
        '冲突和和解',
        '社交模式的变化'
      ]
    }
  };

  // 运行批量测试
  const handleBatchTest = async (suiteKey: string) => {
    const suite = testSuites[suiteKey as keyof typeof testSuites];
    if (!suite) return;

    setTestProgress({
      total: suite.queries.length,
      current: 0,
      results: []
    });

    for (let i = 0; i < suite.queries.length; i++) {
      setTestProgress((prev: any) => ({ ...prev, current: i + 1 }));
      await onRunTest(suite.queries[i]);
      // 模拟测试间隔
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setTestProgress(null);
  };

  // 运行基准测试
  const handleBenchmarkTest = async () => {
    const benchmarkQueries = [
      '简单查询测试',
      '复杂语义查询测试',
      '多关键词组合查询',
      '模糊匹配查询测试',
      '长文本查询测试'
    ];

    setTestProgress({
      total: benchmarkQueries.length * 3, // 每个查询测试3次
      current: 0,
      results: []
    });

    for (const query of benchmarkQueries) {
      for (let i = 0; i < 3; i++) {
        setTestProgress((prev: any) => ({ ...prev, current: prev.current + 1 }));
        await onRunTest(`${query} (第${i + 1}次)`);
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }

    setTestProgress(null);
  };

  // 快速测试预设查询
  const quickTestQueries = [
    '最近的学习心得',
    '重要的人生感悟',
    '技能提升的经历',
    '情感状态的变化',
    '未来的计划目标'
  ];

  return (
    <div className="h-80 bg-[#161B22] border-t border-[#30363D]">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg text-white flex items-center justify-between">
          实时测试与预览
          <div className="flex items-center space-x-2">
            {testProgress && (
              <Badge variant="outline" className="text-yellow-400 border-yellow-400">
                进度: {testProgress.current}/{testProgress.total}
              </Badge>
            )}
            {lastTestResult && (
              <Badge variant="outline" className="text-blue-400 border-blue-400">
                最近测试: {new Date(lastTestResult.timestamp).toLocaleTimeString()}
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="h-full overflow-hidden">
        {/* 高级测试输入区域 */}
        <div className="space-y-3 mb-4">
          {/* 测试模式选择 */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Label className="text-sm text-gray-300">测试模式:</Label>
              <Select value={testMode} onValueChange={(value: any) => setTestMode(value)}>
                <SelectTrigger className="w-32 bg-[#21262D] border-[#30363D] text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#21262D] border-[#30363D]">
                  <SelectItem value="single" className="text-white">单次测试</SelectItem>
                  <SelectItem value="batch" className="text-white">批量测试</SelectItem>
                  <SelectItem value="benchmark" className="text-white">基准测试</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {testMode === 'batch' && (
              <div className="flex items-center space-x-2">
                <Label className="text-sm text-gray-300">测试套件:</Label>
                <Select value={selectedTestSuite} onValueChange={setSelectedTestSuite}>
                  <SelectTrigger className="w-40 bg-[#21262D] border-[#30363D] text-white">
                    <SelectValue placeholder="选择套件" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#21262D] border-[#30363D]">
                    {Object.entries(testSuites).map(([key, suite]) => (
                      <SelectItem key={key} value={key} className="text-white">
                        {suite.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="comparison"
                checked={comparisonMode}
                onChange={(e) => setComparisonMode(e.target.checked)}
                className="w-4 h-4"
              />
              <Label htmlFor="comparison" className="text-sm text-gray-300">对比模式</Label>
            </div>
          </div>

          {/* 测试输入 */}
          {testMode === 'single' ? (
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Input
                  placeholder="输入测试查询..."
                  value={testQuery}
                  onChange={(e) => setTestQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1 bg-[#21262D] border-[#30363D] text-white"
                />
                <Button
                  onClick={handleRunTest}
                  disabled={isTesting || !testQuery.trim()}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isTesting ? '测试中...' : '测试检索'}
                </Button>
              </div>

              {/* 快速测试按钮 */}
              <div className="flex flex-wrap gap-2">
                <span className="text-xs text-gray-400">快速测试:</span>
                {quickTestQueries.map((query, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setTestQuery(query);
                      onRunTest(query);
                    }}
                    className="text-xs text-gray-300 border-gray-600 hover:bg-gray-700"
                  >
                    {query}
                  </Button>
                ))}
              </div>
            </div>
          ) : testMode === 'batch' ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-300">
                  {selectedTestSuite && testSuites[selectedTestSuite as keyof typeof testSuites]
                    ? `${testSuites[selectedTestSuite as keyof typeof testSuites].name} (${testSuites[selectedTestSuite as keyof typeof testSuites].queries.length} 个查询)`
                    : '请选择测试套件'
                  }
                </span>
                <Button
                  onClick={() => selectedTestSuite && handleBatchTest(selectedTestSuite)}
                  disabled={isTesting || !selectedTestSuite}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {isTesting ? '批量测试中...' : '开始批量测试'}
                </Button>
              </div>

              {selectedTestSuite && testSuites[selectedTestSuite as keyof typeof testSuites] && (
                <div className="text-xs text-gray-400 space-y-1">
                  <div>测试查询预览:</div>
                  {testSuites[selectedTestSuite as keyof typeof testSuites].queries.map((query, index) => (
                    <div key={index} className="ml-2">• {query}</div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-300">基准测试 (5个查询 × 3次重复 = 15次测试)</span>
                <Button
                  onClick={handleBenchmarkTest}
                  disabled={isTesting}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  {isTesting ? '基准测试中...' : '开始基准测试'}
                </Button>
              </div>
              <div className="text-xs text-gray-400">
                将测试不同复杂度的查询以评估系统性能稳定性
              </div>
            </div>
          )}

          {/* 测试进度 */}
          {testProgress && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-300">测试进度</span>
                <span className="text-blue-400">{Math.round((testProgress.current / testProgress.total) * 100)}%</span>
              </div>
              <Progress value={(testProgress.current / testProgress.total) * 100} className="h-2" />
            </div>
          )}
        </div>

        {/* 测试结果展示 */}
        <div className="h-[calc(100%-140px)]">
          <Tabs defaultValue="results" className="h-full">
            <TabsList className="grid w-full grid-cols-5 bg-[#21262D]">
              <TabsTrigger value="results" className="text-gray-300">检索结果</TabsTrigger>
              <TabsTrigger value="weights" className="text-gray-300">权重分析</TabsTrigger>
              <TabsTrigger value="performance" className="text-gray-300">性能指标</TabsTrigger>
              <TabsTrigger value="comparison" className="text-gray-300">结果对比</TabsTrigger>
              <TabsTrigger value="history" className="text-gray-300">测试历史</TabsTrigger>
            </TabsList>

            <TabsContent value="results" className="h-[calc(100%-40px)] overflow-y-auto">
              {lastTestResult ? (
                <RetrievalResultsViewer results={lastTestResult.results} />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <div className="text-lg mb-2">🔍</div>
                    <div>运行测试查询以查看检索结果</div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="weights" className="h-[calc(100%-40px)] overflow-y-auto">
              {lastTestResult?.weightAnalysis ? (
                <WeightAnalysisChart data={lastTestResult.weightAnalysis} />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <div className="text-lg mb-2">⚖️</div>
                    <div>运行测试查询以查看权重分析</div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="performance" className="h-[calc(100%-40px)] overflow-y-auto">
              {lastTestResult?.performanceMetrics ? (
                <PerformanceMetricsDisplay metrics={lastTestResult.performanceMetrics} />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <div className="text-lg mb-2">📊</div>
                    <div>运行测试查询以查看性能指标</div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="comparison" className="h-[calc(100%-40px)] overflow-y-auto">
              {comparisonMode && testHistory.length > 1 ? (
                <div className="space-y-4">
                  {/* 对比选择 */}
                  <Card className="bg-[#0D1117] border-[#30363D]">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm text-gray-300">选择对比结果</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {testHistory.slice(0, 10).map((test, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`test-${index}`}
                              checked={selectedResults.includes(test.timestamp)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedResults(prev => [...prev, test.timestamp]);
                                } else {
                                  setSelectedResults(prev => prev.filter(t => t !== test.timestamp));
                                }
                              }}
                              className="w-4 h-4"
                            />
                            <Label htmlFor={`test-${index}`} className="text-xs text-gray-300 flex-1">
                              {test.query} - {new Date(test.timestamp).toLocaleTimeString()}
                            </Label>
                            <Badge variant="outline" className="text-xs text-blue-400 border-blue-400">
                              {test.results.length} 结果
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 对比分析 */}
                  {selectedResults.length >= 2 && (
                    <Card className="bg-[#0D1117] border-[#30363D]">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm text-gray-300">对比分析</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {/* 性能对比 */}
                          <div>
                            <div className="text-xs text-gray-400 mb-2">性能对比:</div>
                            <div className="grid grid-cols-3 gap-4 text-xs">
                              <div className="text-center">
                                <div className="text-gray-400">平均耗时</div>
                                <div className="text-white">
                                  {Math.round(
                                    testHistory
                                      .filter(t => selectedResults.includes(t.timestamp))
                                      .reduce((sum, t) => sum + t.performanceMetrics.totalTime, 0) /
                                    selectedResults.length
                                  )}ms
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-400">平均相关性</div>
                                <div className="text-white">
                                  {(
                                    testHistory
                                      .filter(t => selectedResults.includes(t.timestamp))
                                      .reduce((sum, t) => sum + t.performanceMetrics.averageRelevance, 0) /
                                    selectedResults.length
                                  ).toFixed(2)}
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-400">平均结果数</div>
                                <div className="text-white">
                                  {Math.round(
                                    testHistory
                                      .filter(t => selectedResults.includes(t.timestamp))
                                      .reduce((sum, t) => sum + t.results.length, 0) /
                                    selectedResults.length
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* 结果重叠分析 */}
                          <div>
                            <div className="text-xs text-gray-400 mb-2">结果重叠分析:</div>
                            <div className="space-y-2">
                              {selectedResults.map((timestamp, index) => {
                                const test = testHistory.find(t => t.timestamp === timestamp);
                                if (!test) return null;

                                return (
                                  <div key={timestamp} className="p-2 bg-[#21262D] rounded">
                                    <div className="flex items-center justify-between mb-1">
                                      <span className="text-xs text-white">{test.query}</span>
                                      <span className="text-xs text-gray-400">
                                        {new Date(test.timestamp).toLocaleTimeString()}
                                      </span>
                                    </div>
                                    <div className="text-xs text-gray-400">
                                      {test.results.length} 个结果,
                                      耗时 {test.performanceMetrics.totalTime}ms,
                                      相关性 {test.performanceMetrics.averageRelevance.toFixed(2)}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>

                          {/* 导出对比报告 */}
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const comparisonData = testHistory.filter(t => selectedResults.includes(t.timestamp));
                                const blob = new Blob([JSON.stringify(comparisonData, null, 2)], { type: 'application/json' });
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = `test-comparison-${Date.now()}.json`;
                                a.click();
                                URL.revokeObjectURL(url);
                              }}
                              className="text-gray-300 border-gray-600 hover:bg-gray-700"
                            >
                              导出对比报告
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedResults([])}
                              className="text-gray-300 border-gray-600 hover:bg-gray-700"
                            >
                              清除选择
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <div className="text-lg mb-2">📊</div>
                    <div>
                      {!comparisonMode
                        ? '启用对比模式以使用此功能'
                        : testHistory.length < 2
                        ? '需要至少2个测试结果才能进行对比'
                        : '选择要对比的测试结果'
                      }
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="h-[calc(100%-40px)] overflow-y-auto">
              {testHistory.length > 0 ? (
                <div className="space-y-4">
                  {/* 历史统计 */}
                  <Card className="bg-[#0D1117] border-[#30363D]">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm text-gray-300">测试统计</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-4 gap-4 text-center text-xs">
                        <div>
                          <div className="text-lg font-bold text-blue-400">{testHistory.length}</div>
                          <div className="text-gray-400">总测试数</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-green-400">
                            {Math.round(testHistory.reduce((sum, t) => sum + t.performanceMetrics.totalTime, 0) / testHistory.length)}ms
                          </div>
                          <div className="text-gray-400">平均耗时</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-purple-400">
                            {(testHistory.reduce((sum, t) => sum + t.performanceMetrics.averageRelevance, 0) / testHistory.length).toFixed(2)}
                          </div>
                          <div className="text-gray-400">平均相关性</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-yellow-400">
                            {Math.round(testHistory.reduce((sum, t) => sum + t.results.length, 0) / testHistory.length)}
                          </div>
                          <div className="text-gray-400">平均结果数</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 测试历史列表 */}
                  <div className="space-y-2">
                    {testHistory.map((test, index) => (
                      <Card key={index} className="bg-[#0D1117] border-[#30363D]">
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-sm text-white font-medium">
                              {test.query}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge
                                variant="outline"
                                className={`text-xs ${
                                  test.performanceMetrics.totalTime < 100
                                    ? 'text-green-400 border-green-400'
                                    : test.performanceMetrics.totalTime < 200
                                    ? 'text-yellow-400 border-yellow-400'
                                    : 'text-red-400 border-red-400'
                                }`}
                              >
                                {test.performanceMetrics.totalTime}ms
                              </Badge>
                              <div className="text-xs text-gray-400">
                                {new Date(test.timestamp).toLocaleString()}
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-4 gap-4 text-xs text-gray-400 mb-2">
                            <span>结果: {test.results.length}</span>
                            <span>相关性: {test.performanceMetrics.averageRelevance.toFixed(2)}</span>
                            <span>候选项: {test.performanceMetrics.candidatesFound}</span>
                            <span>过滤: {test.performanceMetrics.candidatesFiltered}</span>
                          </div>

                          {/* 展开详情 */}
                          <details className="mt-2">
                            <summary className="text-xs text-blue-400 cursor-pointer hover:text-blue-300">
                              查看详细信息
                            </summary>
                            <div className="mt-2 p-2 bg-[#21262D] rounded text-xs space-y-2">
                              <div>
                                <span className="text-gray-400">配置快照:</span>
                                <div className="ml-2 text-gray-300">
                                  检索模式: {test.configSnapshot.retrieval.retrievalMode}<br/>
                                  算法: {test.configSnapshot.retrieval.semanticAlgorithm}<br/>
                                  阈值: {test.configSnapshot.retrieval.similarityThreshold}<br/>
                                  权重规则: {test.configSnapshot.retrieval.weightingRules.length} 个
                                </div>
                              </div>

                              <div>
                                <span className="text-gray-400">时间分解:</span>
                                <div className="ml-2 text-gray-300">
                                  向量化: {test.performanceMetrics.timeBreakdown.vectorization}ms<br/>
                                  搜索: {test.performanceMetrics.timeBreakdown.search}ms<br/>
                                  权重计算: {test.performanceMetrics.timeBreakdown.weighting}ms<br/>
                                  过滤: {test.performanceMetrics.timeBreakdown.filtering}ms
                                </div>
                              </div>

                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const data = JSON.stringify(test, null, 2);
                                    const blob = new Blob([data], { type: 'application/json' });
                                    const url = URL.createObjectURL(blob);
                                    const a = document.createElement('a');
                                    a.href = url;
                                    a.download = `test-result-${test.timestamp}.json`;
                                    a.click();
                                    URL.revokeObjectURL(url);
                                  }}
                                  className="h-6 text-xs text-gray-300 border-gray-600 hover:bg-gray-700"
                                >
                                  导出
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setTestQuery(test.query);
                                    onRunTest(test.query);
                                  }}
                                  className="h-6 text-xs text-gray-300 border-gray-600 hover:bg-gray-700"
                                >
                                  重新测试
                                </Button>
                              </div>
                            </div>
                          </details>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* 批量操作 */}
                  <Card className="bg-[#0D1117] border-[#30363D]">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-300">批量操作</span>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const data = JSON.stringify(testHistory, null, 2);
                              const blob = new Blob([data], { type: 'application/json' });
                              const url = URL.createObjectURL(blob);
                              const a = document.createElement('a');
                              a.href = url;
                              a.download = `test-history-${Date.now()}.json`;
                              a.click();
                              URL.revokeObjectURL(url);
                            }}
                            className="text-gray-300 border-gray-600 hover:bg-gray-700"
                          >
                            导出全部
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (confirm('确定要清除所有测试历史吗？')) {
                                // 这里应该调用清除历史的回调
                                console.log('清除测试历史');
                              }
                            }}
                            className="text-red-400 border-red-600 hover:bg-red-900/20"
                          >
                            清除历史
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <div className="text-lg mb-2">📝</div>
                    <div>暂无测试历史</div>
                    <div className="text-xs mt-1">运行测试后将在此显示历史记录</div>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </div>
  );
}
