"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { RotateCcw, BarChart3, AlertTriangle } from "lucide-react"
import { WeightingParams } from "@/types/meaning-rag"

interface WeightingControlsProps {
  weightingParams: WeightingParams
  onWeightingChange: (params: WeightingParams) => void
}

export function WeightingControls({ 
  weightingParams, 
  onWeightingChange 
}: WeightingControlsProps) {
  const [localParams, setLocalParams] = useState<WeightingParams>(weightingParams)
  const [isNormalized, setIsNormalized] = useState(true)

  // 计算权重总和
  const totalWeight = Object.values(localParams).reduce((sum, weight) => sum + weight, 0)
  const isValidTotal = Math.abs(totalWeight - 1.0) < 0.01

  // 权重配置定义
  const weightConfigs = [
    {
      key: 'semanticRelevance' as keyof WeightingParams,
      label: '语义相关性',
      description: '基于向量相似度的语义匹配程度',
      color: 'bg-blue-500',
      icon: '🧠'
    },
    {
      key: 'emotionalMatch' as keyof WeightingParams,
      label: '情感匹配',
      description: '情感标签与用户当前状态的匹配度',
      color: 'bg-pink-500',
      icon: '💝'
    },
    {
      key: 'themeRelevance' as keyof WeightingParams,
      label: '主题相关性',
      description: '认知主题与用户画像的相关程度',
      color: 'bg-purple-500',
      icon: '🎯'
    },
    {
      key: 'importanceScore' as keyof WeightingParams,
      label: '重要性分数',
      description: '内容本身的重要性和价值评估',
      color: 'bg-orange-500',
      icon: '⭐'
    },
    {
      key: 'temporalDecay' as keyof WeightingParams,
      label: '时间衰减',
      description: '基于时间的记忆衰减权重',
      color: 'bg-green-500',
      icon: '⏰'
    },
    {
      key: 'profileMatch' as keyof WeightingParams,
      label: '画像匹配',
      description: '与用户个人画像的匹配程度',
      color: 'bg-cyan-500',
      icon: '👤'
    }
  ]

  // 同步外部变更
  useEffect(() => {
    setLocalParams(weightingParams)
  }, [weightingParams])

  // 更新单个权重
  const updateWeight = (key: keyof WeightingParams, value: number) => {
    const newParams = { ...localParams, [key]: value }
    setLocalParams(newParams)
    
    if (isNormalized) {
      // 自动归一化其他权重
      const otherKeys = Object.keys(newParams).filter(k => k !== key && k !== '_normalized') as (keyof WeightingParams)[]
      const otherTotal = otherKeys.reduce((sum, k) => {
        const value = newParams[k]
        return sum + (typeof value === 'number' ? value : 0)
      }, 0)
      const remaining = 1.0 - value
      
      if (otherTotal > 0 && remaining > 0) {
        const scale = remaining / otherTotal
        otherKeys.forEach(k => {
          const currentValue = newParams[k]
          if (typeof currentValue === 'number') {
            (newParams as any)[k] = currentValue * scale
          }
        })
      }
    }
    
    onWeightingChange(newParams)
  }

  // 归一化所有权重
  const normalizeWeights = () => {
    const numericEntries = Object.entries(localParams).filter(([key, value]) =>
      key !== '_normalized' && typeof value === 'number'
    )
    const total = numericEntries.reduce((sum, [, value]) => sum + (value as number), 0)

    if (total > 0) {
      const normalizedParams = { ...localParams }
      numericEntries.forEach(([key, value]) => {
        (normalizedParams as any)[key] = (value as number) / total
      })

      setLocalParams(normalizedParams)
      onWeightingChange(normalizedParams)
    }
  }

  // 重置为默认权重
  const resetToDefaults = () => {
    const defaultParams: WeightingParams = {
      semanticRelevance: 0.3,
      emotionalMatch: 0.2,
      themeRelevance: 0.2,
      importanceScore: 0.15,
      temporalDecay: 0.1,
      profileMatch: 0.05
    }
    
    setLocalParams(defaultParams)
    onWeightingChange(defaultParams)
  }

  // 应用预设配置
  const applyPreset = (presetName: string) => {
    const presets: Record<string, WeightingParams> = {
      'semantic': {
        semanticRelevance: 0.6,
        emotionalMatch: 0.1,
        themeRelevance: 0.15,
        importanceScore: 0.1,
        temporalDecay: 0.03,
        profileMatch: 0.02
      },
      'emotional': {
        semanticRelevance: 0.2,
        emotionalMatch: 0.4,
        themeRelevance: 0.15,
        importanceScore: 0.15,
        temporalDecay: 0.05,
        profileMatch: 0.05
      },
      'balanced': {
        semanticRelevance: 0.25,
        emotionalMatch: 0.2,
        themeRelevance: 0.2,
        importanceScore: 0.2,
        temporalDecay: 0.1,
        profileMatch: 0.05
      }
    }
    
    const preset = presets[presetName]
    if (preset) {
      setLocalParams(preset)
      onWeightingChange(preset)
    }
  }

  return (
    <Card className="bg-[#161B22] border-[#30363D]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-gray-200 flex items-center">
            <BarChart3 className="w-4 h-4 mr-2" />
            动态权重参数
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {/* 权重总和显示 */}
            <Badge 
              variant={isValidTotal ? "default" : "destructive"}
              className="text-xs"
            >
              总和: {totalWeight.toFixed(3)}
            </Badge>
            
            {/* 控制按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={normalizeWeights}
              disabled={isValidTotal}
            >
              归一化
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={resetToDefaults}
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              重置
            </Button>
          </div>
        </div>
        
        {/* 权重总和警告 */}
        {!isValidTotal && (
          <div className="flex items-center space-x-2 text-xs text-yellow-400">
            <AlertTriangle className="w-3 h-3" />
            <span>权重总和应为1.0，当前为{totalWeight.toFixed(3)}</span>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 预设配置 */}
        <div className="space-y-2">
          <label className="text-xs text-gray-400">快速预设</label>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => applyPreset('semantic')}
              className="text-xs"
            >
              语义优先
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => applyPreset('emotional')}
              className="text-xs"
            >
              情感优先
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => applyPreset('balanced')}
              className="text-xs"
            >
              均衡模式
            </Button>
          </div>
        </div>

        {/* 权重控制器 */}
        <div className="space-y-4">
          {weightConfigs.map((config) => {
            const value = localParams[config.key]
            const numericValue = typeof value === 'number' ? value : 0
            const percentage = (numericValue * 100).toFixed(1)
            
            return (
              <div key={config.key} className="space-y-2">
                {/* 权重标签和值 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{config.icon}</span>
                    <div>
                      <div className="text-xs text-gray-200">{config.label}</div>
                      <div className="text-xs text-gray-400">{config.description}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="text-xs min-w-[60px]">
                      {percentage}%
                    </Badge>
                    <Input
                      type="number"
                      value={numericValue.toFixed(3)}
                      onChange={(e) => {
                        const newValue = parseFloat(e.target.value) || 0
                        if (newValue >= 0 && newValue <= 1) {
                          updateWeight(config.key, newValue)
                        }
                      }}
                      min={0}
                      max={1}
                      step={0.01}
                      className="w-20 h-8 text-xs bg-[#0D1117] border-[#30363D] text-gray-200"
                    />
                  </div>
                </div>
                
                {/* 滑块控制 */}
                <div className="relative">
                  <Slider
                    value={[numericValue]}
                    onValueChange={([newValue]) => updateWeight(config.key, newValue)}
                    min={0}
                    max={1}
                    step={0.01}
                    className="w-full"
                  />
                  
                  {/* 权重可视化条 */}
                  <div className="mt-1 h-1 bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${config.color} transition-all duration-200`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* 权重分布可视化 */}
        <div className="space-y-2">
          <label className="text-xs text-gray-400">权重分布</label>
          <div className="h-4 bg-gray-700 rounded-full overflow-hidden flex">
            {weightConfigs.map((config, index) => {
              const value = localParams[config.key]
              const numericValue = typeof value === 'number' ? value : 0
              const width = (numericValue / Math.max(totalWeight, 1)) * 100

              return (
                <div
                  key={config.key}
                  className={`${config.color} transition-all duration-200`}
                  style={{ width: `${width}%` }}
                  title={`${config.label}: ${(numericValue * 100).toFixed(1)}%`}
                />
              )
            })}
          </div>
          
          {/* 图例 */}
          <div className="grid grid-cols-3 gap-2 text-xs">
            {weightConfigs.map((config) => (
              <div key={config.key} className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${config.color}`} />
                <span className="text-gray-400 truncate">{config.label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 归一化选项 */}
        <div className="flex items-center space-x-2 pt-2 border-t border-[#30363D]">
          <input
            type="checkbox"
            checked={isNormalized}
            onChange={(e) => setIsNormalized(e.target.checked)}
            className="rounded"
          />
          <label className="text-xs text-gray-400">
            自动归一化权重（调整一个权重时自动调整其他权重）
          </label>
        </div>
      </CardContent>
    </Card>
  )
}
