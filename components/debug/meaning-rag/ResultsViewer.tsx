"use client"

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Eye, 
  BarChart3, 
  Clock, 
  Target, 
  Filter,
  ChevronDown,
  ChevronRight,
  Copy,
  Download
} from "lucide-react"
import { DetailedRetrievalResult } from "@/types/meaning-rag"

interface ResultsViewerProps {
  result: DetailedRetrievalResult
}

export function ResultsViewer({ result }: ResultsViewerProps) {
  const [activeTab, setActiveTab] = useState("memories")
  const [expandedMemory, setExpandedMemory] = useState<string | null>(null)
  const [expandedCandidate, setExpandedCandidate] = useState<string | null>(null)

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 导出结果
  const exportResults = () => {
    const exportData = {
      query: result.query,
      timestamp: new Date().toISOString(),
      finalMemories: result.finalMemories,
      debugInfo: result.debugInfo,
      performance: result.performance
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `rag-results-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  // 格式化时间
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    return `${(ms / 1000).toFixed(2)}s`
  }

  return (
    <Card className="bg-[#161B22] border-[#30363D]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-gray-200 flex items-center">
            <Eye className="w-4 h-4 mr-2" />
            检索结果分析
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              {result.finalMemories.length} 个结果
            </Badge>
            <Badge variant="outline" className="text-xs">
              {formatTime(result.performance.totalTime)}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={exportResults}
            >
              <Download className="w-3 h-3 mr-1" />
              导出
            </Button>
          </div>
        </div>
        
        {/* 查询信息 */}
        <div className="text-xs text-gray-400 bg-[#0D1117] p-2 rounded">
          <span className="font-semibold">查询:</span> {result.query}
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4 bg-[#0D1117]">
            <TabsTrigger value="memories" className="data-[state=active]:bg-[#161B22]">
              最终结果
            </TabsTrigger>
            <TabsTrigger value="candidates" className="data-[state=active]:bg-[#161B22]">
              候选分析
            </TabsTrigger>
            <TabsTrigger value="weights" className="data-[state=active]:bg-[#161B22]">
              权重详情
            </TabsTrigger>
            <TabsTrigger value="performance" className="data-[state=active]:bg-[#161B22]">
              性能指标
            </TabsTrigger>
          </TabsList>

          {/* 最终结果 */}
          <TabsContent value="memories" className="space-y-3">
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {result.finalMemories.map((memory, index) => (
                  <Card key={memory.id} className="bg-[#0D1117] border-[#30363D]">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="secondary" className="text-xs">
                            #{index + 1}
                          </Badge>
                          <span className="text-xs text-gray-400">
                            重要性: {memory.importanceScore.toFixed(2)}
                          </span>
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setExpandedMemory(
                            expandedMemory === memory.id ? null : memory.id
                          )}
                        >
                          {expandedMemory === memory.id ? 
                            <ChevronDown className="w-3 h-3" /> : 
                            <ChevronRight className="w-3 h-3" />
                          }
                        </Button>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-2">
                      {/* 内容预览 */}
                      <div className="text-sm text-gray-200">
                        {expandedMemory === memory.id ? 
                          memory.content : 
                          memory.content.substring(0, 150) + (memory.content.length > 150 ? '...' : '')
                        }
                      </div>
                      
                      {/* 意义子块 */}
                      {memory.childChunk && (
                        <div className="text-xs text-blue-300 bg-blue-900/20 p-2 rounded">
                          <span className="font-semibold">意义摘要:</span> {memory.childChunk}
                        </div>
                      )}
                      
                      {/* 标签 */}
                      <div className="flex flex-wrap gap-1">
                        {memory.emotionalTags.map((tag, i) => (
                          <Badge key={i} variant="outline" className="text-xs bg-pink-900/20 text-pink-300">
                            {tag}
                          </Badge>
                        ))}
                        {memory.cognitiveThemes.map((theme, i) => (
                          <Badge key={i} variant="outline" className="text-xs bg-purple-900/20 text-purple-300">
                            {theme}
                          </Badge>
                        ))}
                      </div>
                      
                      {/* 展开详情 */}
                      {expandedMemory === memory.id && (
                        <div className="space-y-2 pt-2 border-t border-[#30363D]">
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="text-gray-400">创建时间:</span>
                              <span className="text-gray-300 ml-1">
                                {memory.createdAt.toLocaleString()}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-400">Token数:</span>
                              <span className="text-gray-300 ml-1">{memory.tokenCount}</span>
                            </div>
                          </div>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(memory.content)}
                            className="w-full"
                          >
                            <Copy className="w-3 h-3 mr-1" />
                            复制内容
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* 候选分析 */}
          <TabsContent value="candidates" className="space-y-3">
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="text-center p-2 bg-[#0D1117] rounded">
                <div className="text-blue-400 font-semibold">{result.debugInfo.totalCandidates}</div>
                <div className="text-gray-400">总候选数</div>
              </div>
              <div className="text-center p-2 bg-[#0D1117] rounded">
                <div className="text-green-400 font-semibold">{result.debugInfo.candidatesAfterFiltering}</div>
                <div className="text-gray-400">过滤后</div>
              </div>
              <div className="text-center p-2 bg-[#0D1117] rounded">
                <div className="text-purple-400 font-semibold">{result.finalMemories.length}</div>
                <div className="text-gray-400">最终结果</div>
              </div>
            </div>
            
            <ScrollArea className="h-80">
              <div className="space-y-2">
                {result.debugInfo.candidateBreakdown.slice(0, 10).map((candidate, index) => (
                  <Card key={candidate.chunk.id} className="bg-[#0D1117] border-[#30363D]">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant="secondary" className="text-xs">
                            排名 #{candidate.rank}
                          </Badge>
                          <span className="text-xs text-gray-400">
                            权重: {candidate.finalWeight.toFixed(3)}
                          </span>
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setExpandedCandidate(
                            expandedCandidate === candidate.chunk.id ? null : candidate.chunk.id
                          )}
                        >
                          {expandedCandidate === candidate.chunk.id ? 
                            <ChevronDown className="w-3 h-3" /> : 
                            <ChevronRight className="w-3 h-3" />
                          }
                        </Button>
                      </div>
                      
                      <div className="text-sm text-gray-300 mb-2">
                        {candidate.chunk.content.substring(0, 100)}...
                      </div>
                      
                      {expandedCandidate === candidate.chunk.id && (
                        <div className="space-y-2 pt-2 border-t border-[#30363D]">
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="text-gray-400">语义相关:</span>
                              <span className="text-blue-300 ml-1">
                                {candidate.scores.semanticRelevance.toFixed(3)}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-400">情感匹配:</span>
                              <span className="text-pink-300 ml-1">
                                {candidate.scores.emotionalMatch.toFixed(3)}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-400">主题相关:</span>
                              <span className="text-purple-300 ml-1">
                                {candidate.scores.themeRelevance.toFixed(3)}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-400">重要性:</span>
                              <span className="text-orange-300 ml-1">
                                {candidate.scores.importanceScore.toFixed(3)}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* 权重详情 */}
          <TabsContent value="weights" className="space-y-3">
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {result.debugInfo.weightBreakdown.slice(0, 5).map((breakdown, index) => (
                  <Card key={breakdown.chunkId} className="bg-[#0D1117] border-[#30363D]">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          块 #{index + 1}
                        </Badge>
                        <span className="text-xs text-gray-400">
                          最终权重: {breakdown.finalWeight.toFixed(3)}
                        </span>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-2">
                      {/* 权重分解 */}
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        {Object.entries(breakdown.weightedScores).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-gray-400">{key}:</span>
                            <span className="text-gray-300">{value.toFixed(3)}</span>
                          </div>
                        ))}
                      </div>
                      
                      {/* 说明 */}
                      {breakdown.explanation.length > 0 && (
                        <div className="space-y-1">
                          <div className="text-xs text-gray-400">权重说明:</div>
                          {breakdown.explanation.map((exp, i) => (
                            <div key={i} className="text-xs text-gray-300">
                              • {exp}
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* 性能指标 */}
          <TabsContent value="performance" className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              {/* 时间分析 */}
              <Card className="bg-[#0D1117] border-[#30363D]">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-gray-200 flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    时间分析
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-400">向量搜索:</span>
                      <span className="text-gray-300">{formatTime(result.performance.vectorSearchTime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">权重计算:</span>
                      <span className="text-gray-300">{formatTime(result.performance.weightingTime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">过滤处理:</span>
                      <span className="text-gray-300">{formatTime(result.performance.filteringTime)}</span>
                    </div>
                    <div className="flex justify-between border-t border-[#30363D] pt-1">
                      <span className="text-gray-400 font-semibold">总时间:</span>
                      <span className="text-blue-300 font-semibold">{formatTime(result.performance.totalTime)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 过滤统计 */}
              <Card className="bg-[#0D1117] border-[#30363D]">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-gray-200 flex items-center">
                    <Filter className="w-4 h-4 mr-2" />
                    过滤统计
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-400">阈值过滤:</span>
                      <span className="text-gray-300">{result.debugInfo.filteringStats.removedByThreshold}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">去重过滤:</span>
                      <span className="text-gray-300">{result.debugInfo.filteringStats.removedByDeduplication}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">时间过滤:</span>
                      <span className="text-gray-300">{result.debugInfo.filteringStats.removedByTemporal}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">重要性过滤:</span>
                      <span className="text-gray-300">{result.debugInfo.filteringStats.removedByImportance}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
