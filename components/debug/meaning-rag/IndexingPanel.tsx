"use client"

import { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Upload, Play, Square, FileText, Settings } from "lucide-react"
import { 
  IndexingConfig, 
  IndexingProgress, 
  IndexingResult,
  ChunkingMethod 
} from "@/types/meaning-rag"
import { ConfigEditor } from "./ConfigEditor"
import { ProgressMonitor } from "./ProgressMonitor"

interface IndexingPanelProps {
  onIndexingComplete?: (result: IndexingResult) => void
}

export function IndexingPanel({ onIndexingComplete }: IndexingPanelProps) {
  // 状态管理
  const [documentText, setDocumentText] = useState("")
  const [indexingConfig, setIndexingConfig] = useState<IndexingConfig>({
    chunkingRules: {
      method: 'recursive',
      chunkSize: 1000,
      overlap: 200,
      preserveStructure: true
    },
    childChunkPrompt: `请为以下文本生成一个简洁但信息丰富的意义摘要，保留核心概念和关键信息：

{content}

摘要应该：
1. 保持原文的主要意思
2. 提取关键概念和情感
3. 长度控制在100字以内
4. 使用清晰简洁的语言`,
    meaningAnnotationRules: {
      extractEmotions: true,
      extractThemes: true,
      extractImportance: true,
      extractKeywords: true,
      generateSummary: true
    },
    vectorModel: {
      modelName: 'Qwen/Qwen2.5-0.5B-Instruct',
      dimensions: 384
    },
    batchSize: 10
  })
  
  const [indexingProgress, setIndexingProgress] = useState<IndexingProgress>({
    status: 'idle',
    currentStep: '',
    progress: 0,
    processedChunks: 0,
    totalChunks: 0,
    logs: []
  })
  
  const [indexingResult, setIndexingResult] = useState<IndexingResult | null>(null)
  const [showConfigEditor, setShowConfigEditor] = useState(false)

  // 文件上传处理
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        setDocumentText(content)
      }
      reader.readAsText(file)
    }
  }, [])

  // 拖拽上传处理
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file && file.type === 'text/plain') {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        setDocumentText(content)
      }
      reader.readAsText(file)
    }
  }, [])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  // 开始索引处理
  const startIndexing = async () => {
    if (!documentText.trim()) {
      alert('请输入或上传文档内容')
      return
    }

    try {
      setIndexingProgress({
        status: 'processing',
        currentStep: '准备开始...',
        progress: 0,
        processedChunks: 0,
        totalChunks: 0,
        logs: [{ timestamp: new Date(), level: 'info', message: '开始索引处理...' }]
      })

      const response = await fetch('/api/rag/index', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentText,
          config: indexingConfig
        })
      })

      if (!response.ok) {
        throw new Error(`索引失败: ${response.statusText}`)
      }

      const result: IndexingResult = await response.json()
      
      setIndexingResult(result)
      setIndexingProgress({
        status: 'completed',
        currentStep: '索引完成',
        progress: 100,
        processedChunks: result.processedChunks,
        totalChunks: result.totalChunks,
        logs: [
          ...indexingProgress.logs,
          { timestamp: new Date(), level: 'info', message: `索引完成！处理了${result.processedChunks}个块` }
        ]
      })

      onIndexingComplete?.(result)

    } catch (error) {
      console.error('索引处理失败:', error)
      setIndexingProgress({
        status: 'error',
        currentStep: '索引失败',
        progress: 0,
        processedChunks: 0,
        totalChunks: 0,
        logs: [
          ...indexingProgress.logs,
          { 
            timestamp: new Date(), 
            level: 'error', 
            message: `索引失败: ${error instanceof Error ? error.message : '未知错误'}` 
          }
        ]
      })
    }
  }

  // 停止索引处理
  const stopIndexing = () => {
    // TODO: 实现停止索引的逻辑
    setIndexingProgress(prev => ({
      ...prev,
      status: 'idle',
      currentStep: '已停止'
    }))
  }

  // 配置更新处理
  const handleConfigUpdate = (newConfig: IndexingConfig) => {
    setIndexingConfig(newConfig)
    setShowConfigEditor(false)
  }

  const isProcessing = indexingProgress.status === 'processing'

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* 文档输入区域 */}
      <Card className="bg-[#161B22] border-[#30363D]">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm text-gray-200 flex items-center">
              <FileText className="w-4 h-4 mr-2" />
              文档输入
            </CardTitle>
            <div className="flex items-center space-x-2">
              <input
                type="file"
                accept=".txt,.md"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button variant="outline" size="sm" className="cursor-pointer" asChild>
                  <span>
                    <Upload className="w-3 h-3 mr-1" />
                    上传文件
                  </span>
                </Button>
              </label>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div
            className="relative"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            <Textarea
              value={documentText}
              onChange={(e) => setDocumentText(e.target.value)}
              placeholder="在此输入文档内容，或拖拽文本文件到此区域..."
              className="min-h-[200px] bg-[#0D1117] border-[#30363D] text-gray-200 resize-none"
              disabled={isProcessing}
            />
            {documentText && (
              <div className="absolute bottom-2 right-2">
                <Badge variant="secondary" className="text-xs">
                  {documentText.length} 字符
                </Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 索引配置区域 */}
      <Card className="bg-[#161B22] border-[#30363D]">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm text-gray-200 flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              索引配置
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowConfigEditor(true)}
              disabled={isProcessing}
            >
              高级配置
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 基础配置 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-xs text-gray-400">分块方法</label>
              <Select
                value={indexingConfig.chunkingRules.method}
                onValueChange={(value: ChunkingMethod) =>
                  setIndexingConfig(prev => ({
                    ...prev,
                    chunkingRules: { ...prev.chunkingRules, method: value }
                  }))
                }
                disabled={isProcessing}
              >
                <SelectTrigger className="bg-[#0D1117] border-[#30363D] text-gray-200">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recursive">递归分块</SelectItem>
                  <SelectItem value="sentence">句子分块</SelectItem>
                  <SelectItem value="paragraph">段落分块</SelectItem>
                  <SelectItem value="semantic">语义分块</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-xs text-gray-400">块大小</label>
              <Input
                type="number"
                value={indexingConfig.chunkingRules.chunkSize}
                onChange={(e) =>
                  setIndexingConfig(prev => ({
                    ...prev,
                    chunkingRules: { 
                      ...prev.chunkingRules, 
                      chunkSize: parseInt(e.target.value) || 1000 
                    }
                  }))
                }
                min={100}
                max={5000}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
                disabled={isProcessing}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-xs text-gray-400">重叠字符数</label>
              <Input
                type="number"
                value={indexingConfig.chunkingRules.overlap}
                onChange={(e) =>
                  setIndexingConfig(prev => ({
                    ...prev,
                    chunkingRules: { 
                      ...prev.chunkingRules, 
                      overlap: parseInt(e.target.value) || 200 
                    }
                  }))
                }
                min={0}
                max={1000}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
                disabled={isProcessing}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-xs text-gray-400">批处理大小</label>
              <Input
                type="number"
                value={indexingConfig.batchSize}
                onChange={(e) =>
                  setIndexingConfig(prev => ({
                    ...prev,
                    batchSize: parseInt(e.target.value) || 10
                  }))
                }
                min={1}
                max={50}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
                disabled={isProcessing}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 执行控制区域 */}
      <Card className="bg-[#161B22] border-[#30363D]">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                onClick={startIndexing}
                disabled={isProcessing || !documentText.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Play className="w-4 h-4 mr-2" />
                {isProcessing ? '处理中...' : '开始索引'}
              </Button>
              
              {isProcessing && (
                <Button
                  onClick={stopIndexing}
                  variant="outline"
                  size="sm"
                >
                  <Square className="w-3 h-3 mr-1" />
                  停止
                </Button>
              )}
            </div>
            
            {indexingProgress.status !== 'idle' && (
              <div className="text-xs text-gray-400">
                状态: {indexingProgress.currentStep}
              </div>
            )}
          </div>
          
          {/* 进度条 */}
          {isProcessing && (
            <div className="mt-4 space-y-2">
              <Progress value={indexingProgress.progress} className="w-full" />
              <div className="flex justify-between text-xs text-gray-400">
                <span>{indexingProgress.processedChunks}/{indexingProgress.totalChunks} 块</span>
                <span>{indexingProgress.progress.toFixed(1)}%</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 进度监控 */}
      <ProgressMonitor progress={indexingProgress} />

      {/* 配置编辑器模态框 */}
      {showConfigEditor && (
        <ConfigEditor
          config={indexingConfig}
          onSave={handleConfigUpdate}
          onCancel={() => setShowConfigEditor(false)}
        />
      )}
    </div>
  )
}
