"use client"

import { useEffect, useRef } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Activity, CheckCircle, XCircle, AlertCircle, Clock } from "lucide-react"
import { IndexingProgress } from "@/types/meaning-rag"

interface ProgressMonitorProps {
  progress: IndexingProgress
}

export function ProgressMonitor({ progress }: ProgressMonitorProps) {
  const logsEndRef = useRef<HTMLDivElement>(null)

  // 自动滚动到最新日志
  useEffect(() => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [progress.logs])

  // 状态图标
  const getStatusIcon = () => {
    switch (progress.status) {
      case 'idle':
        return <Clock className="w-4 h-4 text-gray-400" />
      case 'processing':
        return <Activity className="w-4 h-4 text-blue-400 animate-pulse" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-400" />
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-400" />
    }
  }

  // 状态颜色
  const getStatusColor = () => {
    switch (progress.status) {
      case 'idle':
        return 'text-gray-400'
      case 'processing':
        return 'text-blue-400'
      case 'completed':
        return 'text-green-400'
      case 'error':
        return 'text-red-400'
      default:
        return 'text-yellow-400'
    }
  }

  // 状态文本
  const getStatusText = () => {
    switch (progress.status) {
      case 'idle':
        return '待机'
      case 'processing':
        return '处理中'
      case 'completed':
        return '已完成'
      case 'error':
        return '错误'
      default:
        return '未知状态'
    }
  }

  // 日志级别图标
  const getLogIcon = (level: 'info' | 'warn' | 'error') => {
    switch (level) {
      case 'info':
        return <CheckCircle className="w-3 h-3 text-blue-400" />
      case 'warn':
        return <AlertCircle className="w-3 h-3 text-yellow-400" />
      case 'error':
        return <XCircle className="w-3 h-3 text-red-400" />
    }
  }

  // 日志级别颜色
  const getLogColor = (level: 'info' | 'warn' | 'error') => {
    switch (level) {
      case 'info':
        return 'text-gray-300'
      case 'warn':
        return 'text-yellow-300'
      case 'error':
        return 'text-red-300'
    }
  }

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 计算预计剩余时间
  const getEstimatedTimeRemaining = () => {
    if (progress.estimatedTimeRemaining) {
      const minutes = Math.floor(progress.estimatedTimeRemaining / 60)
      const seconds = progress.estimatedTimeRemaining % 60
      return minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`
    }
    return null
  }

  // 如果是空闲状态且没有日志，不显示组件
  if (progress.status === 'idle' && progress.logs.length === 0) {
    return null
  }

  return (
    <Card className="bg-[#161B22] border-[#30363D]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-gray-200 flex items-center">
            {getStatusIcon()}
            <span className="ml-2">处理状态</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge 
              variant="secondary" 
              className={`text-xs ${getStatusColor()}`}
            >
              {getStatusText()}
            </Badge>
            
            {progress.status === 'processing' && (
              <Badge variant="outline" className="text-xs">
                {progress.progress.toFixed(1)}%
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 当前步骤和进度 */}
        {progress.status === 'processing' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-300">{progress.currentStep}</span>
              <span className="text-gray-400">
                {progress.processedChunks}/{progress.totalChunks}
              </span>
            </div>
            
            {/* 进度条 */}
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.progress}%` }}
              />
            </div>
            
            {/* 预计剩余时间 */}
            {getEstimatedTimeRemaining() && (
              <div className="text-xs text-gray-400 text-center">
                预计剩余时间: {getEstimatedTimeRemaining()}
              </div>
            )}
          </div>
        )}

        {/* 完成状态统计 */}
        {progress.status === 'completed' && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="text-center p-2 bg-[#0D1117] rounded">
              <div className="text-green-400 font-semibold">{progress.processedChunks}</div>
              <div className="text-gray-400 text-xs">已处理块数</div>
            </div>
            <div className="text-center p-2 bg-[#0D1117] rounded">
              <div className="text-blue-400 font-semibold">{progress.totalChunks}</div>
              <div className="text-gray-400 text-xs">总块数</div>
            </div>
          </div>
        )}

        {/* 实时日志 */}
        {progress.logs.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-xs text-gray-400">实时日志</label>
              <Badge variant="secondary" className="text-xs">
                {progress.logs.length} 条记录
              </Badge>
            </div>
            
            <ScrollArea className="h-32 w-full rounded border border-[#30363D] bg-[#0D1117]">
              <div className="p-3 space-y-1">
                {progress.logs.map((log, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-2 text-xs"
                  >
                    {getLogIcon(log.level)}
                    <span className="text-gray-500 min-w-[60px]">
                      {formatTime(log.timestamp)}
                    </span>
                    <span className={getLogColor(log.level)}>
                      {log.message}
                    </span>
                  </div>
                ))}
                <div ref={logsEndRef} />
              </div>
            </ScrollArea>
          </div>
        )}

        {/* 错误状态显示 */}
        {progress.status === 'error' && (
          <div className="p-3 bg-red-900/20 border border-red-700 rounded">
            <div className="flex items-center space-x-2 text-red-300">
              <XCircle className="w-4 h-4" />
              <span className="font-semibold">处理失败</span>
            </div>
            {progress.logs.length > 0 && (
              <div className="mt-2 text-sm text-red-200">
                {progress.logs[progress.logs.length - 1]?.message}
              </div>
            )}
          </div>
        )}

        {/* 性能指标 */}
        {(progress.status === 'completed' || progress.status === 'processing') && (
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center p-2 bg-[#0D1117] rounded">
              <div className="text-gray-300">
                {progress.processedChunks > 0 ? 
                  Math.round((progress.processedChunks / (Date.now() - (progress.logs[0]?.timestamp.getTime() || Date.now()))) * 60000) : 0
                }
              </div>
              <div className="text-gray-500">块/分钟</div>
            </div>
            
            <div className="text-center p-2 bg-[#0D1117] rounded">
              <div className="text-gray-300">
                {progress.totalChunks > 0 ? 
                  Math.round((progress.processedChunks / progress.totalChunks) * 100) : 0
                }%
              </div>
              <div className="text-gray-500">完成度</div>
            </div>
            
            <div className="text-center p-2 bg-[#0D1117] rounded">
              <div className="text-gray-300">
                {progress.logs.length > 0 ? 
                  Math.round((Date.now() - progress.logs[0].timestamp.getTime()) / 1000) : 0
                }s
              </div>
              <div className="text-gray-500">已用时</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
