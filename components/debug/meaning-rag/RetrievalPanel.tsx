"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, Settings, BarChart3, Clock } from "lucide-react"
import { 
  EnhancedRetrievalConfig, 
  DetailedRetrievalResult,
  RetrievalMode,
  RetrievalTestState 
} from "@/types/meaning-rag"
import { WeightingControls } from "./WeightingControls"
import { ResultsViewer } from "./ResultsViewer"

interface RetrievalPanelProps {
  onConfigChange?: (config: EnhancedRetrievalConfig) => void
}

export function RetrievalPanel({ onConfigChange }: RetrievalPanelProps) {
  // 检索配置状态
  const [retrievalConfig, setRetrievalConfig] = useState<EnhancedRetrievalConfig>({
    mode: 'hybrid',
    vectorSearch: {
      topK: 100,
      similarityThreshold: 0.7
    },
    keywordSearch: {
      enabled: true,
      boost: 1.2
    },
    weightingParams: {
      semanticRelevance: 0.3,
      emotionalMatch: 0.2,
      themeRelevance: 0.2,
      importanceScore: 0.15,
      temporalDecay: 0.1,
      profileMatch: 0.05
    },
    filterRules: {
      minWeightThreshold: 0.1,
      maxResults: 50,
      deduplication: {
        enabled: true,
        similarityThreshold: 0.85
      },
      temporalFilter: {
        enabled: false,
        maxAge: 30
      },
      importanceFilter: {
        enabled: false,
        minImportance: 0.5
      }
    },
    finalCount: 5,
    debug: {
      enabled: true,
      includeScores: true,
      includeWeightBreakdown: true
    }
  })

  // 测试状态
  const [testState, setTestState] = useState<RetrievalTestState>({
    isRunning: false,
    testHistory: []
  })

  const [testQuery, setTestQuery] = useState("")
  const [testResult, setTestResult] = useState<DetailedRetrievalResult | null>(null)

  // 配置变更通知
  useEffect(() => {
    onConfigChange?.(retrievalConfig)
  }, [retrievalConfig, onConfigChange])

  // 更新配置
  const updateConfig = (updates: Partial<EnhancedRetrievalConfig>) => {
    setRetrievalConfig(prev => ({ ...prev, ...updates }))
  }

  // 执行检索测试
  const runRetrievalTest = async () => {
    if (!testQuery.trim()) {
      alert('请输入测试查询')
      return
    }

    setTestState(prev => ({ ...prev, isRunning: true }))

    try {
      const response = await fetch('/api/rag/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: testQuery,
          config: retrievalConfig,
          context: {
            userProfile: '用户画像内容...', // TODO: 从实际数据获取
            dailyInsight: '每日洞察内容...' // TODO: 从实际数据获取
          },
          options: {
            includeDebugInfo: true
          }
        })
      })

      if (!response.ok) {
        throw new Error(`检索失败: ${response.statusText}`)
      }

      const result: DetailedRetrievalResult = await response.json()
      
      setTestResult(result)
      setTestState(prev => ({
        ...prev,
        isRunning: false,
        lastQuery: testQuery,
        lastResult: result,
        testHistory: [
          ...prev.testHistory.slice(-9), // 保留最近10条记录
          {
            query: testQuery,
            timestamp: new Date(),
            resultCount: result.finalMemories.length,
            processingTime: result.performance.totalTime
          }
        ]
      }))

    } catch (error) {
      console.error('检索测试失败:', error)
      setTestState(prev => ({
        ...prev,
        isRunning: false,
        lastError: error instanceof Error ? error.message : '未知错误'
      }))
    }
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* 检索模式配置 */}
      <Card className="bg-[#161B22] border-[#30363D]">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-gray-200 flex items-center">
            <Settings className="w-4 h-4 mr-2" />
            检索模式配置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-xs text-gray-400">检索模式</label>
              <Select
                value={retrievalConfig.mode}
                onValueChange={(value: RetrievalMode) =>
                  updateConfig({ mode: value })
                }
              >
                <SelectTrigger className="bg-[#0D1117] border-[#30363D] text-gray-200">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vector">纯向量搜索</SelectItem>
                  <SelectItem value="keyword">关键词搜索</SelectItem>
                  <SelectItem value="hybrid">混合搜索</SelectItem>
                  <SelectItem value="semantic">语义搜索</SelectItem>
                  <SelectItem value="meaning">意义搜索</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-xs text-gray-400">最终结果数量</label>
              <Input
                type="number"
                value={retrievalConfig.finalCount}
                onChange={(e) =>
                  updateConfig({ finalCount: parseInt(e.target.value) || 5 })
                }
                min={1}
                max={20}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-xs text-gray-400">Top-K 候选数</label>
              <Input
                type="number"
                value={retrievalConfig.vectorSearch.topK}
                onChange={(e) =>
                  updateConfig({
                    vectorSearch: {
                      ...retrievalConfig.vectorSearch,
                      topK: parseInt(e.target.value) || 100
                    }
                  })
                }
                min={10}
                max={500}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
              />
            </div>

            <div className="space-y-2">
              <label className="text-xs text-gray-400">相似度阈值</label>
              <Input
                type="number"
                value={retrievalConfig.vectorSearch.similarityThreshold}
                onChange={(e) =>
                  updateConfig({
                    vectorSearch: {
                      ...retrievalConfig.vectorSearch,
                      similarityThreshold: parseFloat(e.target.value) || 0.7
                    }
                  })
                }
                min={0}
                max={1}
                step={0.1}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 权重控制 */}
      <WeightingControls
        weightingParams={retrievalConfig.weightingParams}
        onWeightingChange={(params) =>
          updateConfig({ weightingParams: params })
        }
      />

      {/* 过滤规则配置 */}
      <Card className="bg-[#161B22] border-[#30363D]">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-gray-200">过滤规则</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-xs text-gray-400">最低权重阈值</label>
              <Input
                type="number"
                value={retrievalConfig.filterRules.minWeightThreshold}
                onChange={(e) =>
                  updateConfig({
                    filterRules: {
                      ...retrievalConfig.filterRules,
                      minWeightThreshold: parseFloat(e.target.value) || 0.1
                    }
                  })
                }
                min={0}
                max={1}
                step={0.05}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
              />
            </div>

            <div className="space-y-2">
              <label className="text-xs text-gray-400">最大结果数</label>
              <Input
                type="number"
                value={retrievalConfig.filterRules.maxResults}
                onChange={(e) =>
                  updateConfig({
                    filterRules: {
                      ...retrievalConfig.filterRules,
                      maxResults: parseInt(e.target.value) || 50
                    }
                  })
                }
                min={5}
                max={200}
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
              />
            </div>
          </div>

          {/* 去重设置 */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={retrievalConfig.filterRules.deduplication.enabled}
                onChange={(e) =>
                  updateConfig({
                    filterRules: {
                      ...retrievalConfig.filterRules,
                      deduplication: {
                        ...retrievalConfig.filterRules.deduplication,
                        enabled: e.target.checked
                      }
                    }
                  })
                }
                className="rounded"
              />
              <label className="text-xs text-gray-400">启用去重</label>
            </div>
            
            {retrievalConfig.filterRules.deduplication.enabled && (
              <Input
                type="number"
                value={retrievalConfig.filterRules.deduplication.similarityThreshold}
                onChange={(e) =>
                  updateConfig({
                    filterRules: {
                      ...retrievalConfig.filterRules,
                      deduplication: {
                        ...retrievalConfig.filterRules.deduplication,
                        similarityThreshold: parseFloat(e.target.value) || 0.85
                      }
                    }
                  })
                }
                min={0}
                max={1}
                step={0.05}
                placeholder="去重相似度阈值"
                className="bg-[#0D1117] border-[#30363D] text-gray-200"
              />
            )}
          </div>
        </CardContent>
      </Card>

      {/* 实时测试区域 */}
      <Card className="bg-[#161B22] border-[#30363D] flex-1">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm text-gray-200 flex items-center">
              <Search className="w-4 h-4 mr-2" />
              实时检索测试
            </CardTitle>
            {testState.testHistory.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {testState.testHistory.length} 次测试
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 测试查询输入 */}
          <div className="space-y-2">
            <label className="text-xs text-gray-400">测试查询</label>
            <div className="flex space-x-2">
              <Textarea
                value={testQuery}
                onChange={(e) => setTestQuery(e.target.value)}
                placeholder="输入测试查询..."
                className="flex-1 bg-[#0D1117] border-[#30363D] text-gray-200 min-h-[80px]"
                disabled={testState.isRunning}
              />
              <Button
                onClick={runRetrievalTest}
                disabled={testState.isRunning || !testQuery.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Search className="w-4 h-4 mr-2" />
                {testState.isRunning ? '检索中...' : '测试'}
              </Button>
            </div>
          </div>

          {/* 测试历史 */}
          {testState.testHistory.length > 0 && (
            <div className="space-y-2">
              <label className="text-xs text-gray-400 flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                测试历史
              </label>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {testState.testHistory.slice(-5).map((test, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-[#0D1117] rounded text-xs"
                  >
                    <span className="text-gray-300 truncate flex-1">
                      {test.query}
                    </span>
                    <div className="flex items-center space-x-2 text-gray-400">
                      <span>{test.resultCount} 结果</span>
                      <span>{test.processingTime}ms</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 错误显示 */}
          {testState.lastError && (
            <div className="p-3 bg-red-900/20 border border-red-700 rounded">
              <div className="text-xs text-red-300">
                错误: {testState.lastError}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 检索结果查看器 */}
      {testResult && (
        <ResultsViewer result={testResult} />
      )}
    </div>
  )
}
