"use client"

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Save, X, <PERSON>, Settings, Brain } from "lucide-react"
import { IndexingConfig } from "@/types/meaning-rag"

interface ConfigEditorProps {
  config: IndexingConfig
  onSave: (config: IndexingConfig) => void
  onCancel: () => void
}

export function ConfigEditor({ config, onSave, onCancel }: ConfigEditorProps) {
  const [editedConfig, setEditedConfig] = useState<IndexingConfig>(JSON.parse(JSON.stringify(config)))
  const [activeTab, setActiveTab] = useState("chunking")

  // 更新配置
  const updateConfig = (updates: Partial<IndexingConfig>) => {
    setEditedConfig(prev => ({ ...prev, ...updates }))
  }

  // 保存配置
  const handleSave = () => {
    onSave(editedConfig)
  }

  // 重置配置
  const handleReset = () => {
    setEditedConfig(JSON.parse(JSON.stringify(config)))
  }

  return (
    <Dialog open={true} onOpenChange={() => onCancel()}>
      <DialogContent className="max-w-4xl max-h-[80vh] bg-[#161B22] border-[#30363D] text-gray-200">
        <DialogHeader>
          <DialogTitle className="flex items-center text-gray-200">
            <Settings className="w-5 h-5 mr-2" />
            高级索引配置
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#0D1117]">
              <TabsTrigger value="chunking" className="data-[state=active]:bg-[#161B22]">
                分块配置
              </TabsTrigger>
              <TabsTrigger value="annotation" className="data-[state=active]:bg-[#161B22]">
                意义批注
              </TabsTrigger>
              <TabsTrigger value="prompt" className="data-[state=active]:bg-[#161B22]">
                提示词
              </TabsTrigger>
            </TabsList>

            <div className="mt-4 h-[500px] overflow-y-auto">
              {/* 分块配置 */}
              <TabsContent value="chunking" className="space-y-4">
                <Card className="bg-[#0D1117] border-[#30363D]">
                  <CardHeader>
                    <CardTitle className="text-sm text-gray-200">文档分块规则</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-xs text-gray-400">块大小（字符数）</label>
                        <Input
                          type="number"
                          value={editedConfig.chunkingRules.chunkSize}
                          onChange={(e) =>
                            updateConfig({
                              chunkingRules: {
                                ...editedConfig.chunkingRules,
                                chunkSize: parseInt(e.target.value) || 1000
                              }
                            })
                          }
                          min={100}
                          max={10000}
                          className="bg-[#161B22] border-[#30363D] text-gray-200"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-xs text-gray-400">重叠字符数</label>
                        <Input
                          type="number"
                          value={editedConfig.chunkingRules.overlap}
                          onChange={(e) =>
                            updateConfig({
                              chunkingRules: {
                                ...editedConfig.chunkingRules,
                                overlap: parseInt(e.target.value) || 200
                              }
                            })
                          }
                          min={0}
                          max={2000}
                          className="bg-[#161B22] border-[#30363D] text-gray-200"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={editedConfig.chunkingRules.preserveStructure}
                          onCheckedChange={(checked) =>
                            updateConfig({
                              chunkingRules: {
                                ...editedConfig.chunkingRules,
                                preserveStructure: checked
                              }
                            })
                          }
                        />
                        <label className="text-xs text-gray-400">保持文档结构</label>
                      </div>
                      <div className="text-xs text-gray-500">
                        启用后会尽量保持段落、标题等文档结构完整性
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-gray-400">自定义分隔符</label>
                      <Input
                        value={editedConfig.chunkingRules.separators?.join(', ') || ''}
                        onChange={(e) =>
                          updateConfig({
                            chunkingRules: {
                              ...editedConfig.chunkingRules,
                              separators: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                            }
                          })
                        }
                        placeholder="例如: \\n\\n, \\n, 。, ！, ？"
                        className="bg-[#161B22] border-[#30363D] text-gray-200"
                      />
                      <div className="text-xs text-gray-500">
                        用逗号分隔多个分隔符，支持转义字符
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-[#0D1117] border-[#30363D]">
                  <CardHeader>
                    <CardTitle className="text-sm text-gray-200">向量模型配置</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-xs text-gray-400">模型名称</label>
                        <Input
                          value={editedConfig.vectorModel.modelName}
                          onChange={(e) =>
                            updateConfig({
                              vectorModel: {
                                ...editedConfig.vectorModel,
                                modelName: e.target.value
                              }
                            })
                          }
                          className="bg-[#161B22] border-[#30363D] text-gray-200"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-xs text-gray-400">向量维度</label>
                        <Input
                          type="number"
                          value={editedConfig.vectorModel.dimensions}
                          onChange={(e) =>
                            updateConfig({
                              vectorModel: {
                                ...editedConfig.vectorModel,
                                dimensions: parseInt(e.target.value) || 384
                              }
                            })
                          }
                          min={128}
                          max={4096}
                          className="bg-[#161B22] border-[#30363D] text-gray-200"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-gray-400">批处理大小</label>
                      <Input
                        type="number"
                        value={editedConfig.batchSize}
                        onChange={(e) =>
                          updateConfig({
                            batchSize: parseInt(e.target.value) || 10
                          })
                        }
                        min={1}
                        max={100}
                        className="bg-[#161B22] border-[#30363D] text-gray-200"
                      />
                      <div className="text-xs text-gray-500">
                        同时处理的块数量，影响处理速度和内存使用
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 意义批注配置 */}
              <TabsContent value="annotation" className="space-y-4">
                <Card className="bg-[#0D1117] border-[#30363D]">
                  <CardHeader>
                    <CardTitle className="text-sm text-gray-200 flex items-center">
                      <Brain className="w-4 h-4 mr-2" />
                      意义批注规则
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {[
                      { key: 'extractEmotions', label: '提取情感标签', desc: '分析文本中的情感倾向和情感词汇' },
                      { key: 'extractThemes', label: '提取认知主题', desc: '识别文本的主要话题和认知领域' },
                      { key: 'extractImportance', label: '计算重要性分数', desc: '评估内容的重要性和价值' },
                      { key: 'extractKeywords', label: '提取关键词', desc: '识别文本中的核心概念和关键术语' },
                      { key: 'generateSummary', label: '生成摘要', desc: '创建简洁的内容摘要' }
                    ].map((item) => (
                      <div key={item.key} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={editedConfig.meaningAnnotationRules[item.key as keyof typeof editedConfig.meaningAnnotationRules]}
                            onCheckedChange={(checked) =>
                              updateConfig({
                                meaningAnnotationRules: {
                                  ...editedConfig.meaningAnnotationRules,
                                  [item.key]: checked
                                }
                              })
                            }
                          />
                          <div>
                            <div className="text-sm text-gray-200">{item.label}</div>
                            <div className="text-xs text-gray-400">{item.desc}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 提示词配置 */}
              <TabsContent value="prompt" className="space-y-4">
                <Card className="bg-[#0D1117] border-[#30363D]">
                  <CardHeader>
                    <CardTitle className="text-sm text-gray-200 flex items-center">
                      <Code className="w-4 h-4 mr-2" />
                      意义子块生成提示词
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <label className="text-xs text-gray-400">提示词模板</label>
                        <Badge variant="secondary" className="text-xs">
                          {editedConfig.childChunkPrompt.length} 字符
                        </Badge>
                      </div>
                      <Textarea
                        value={editedConfig.childChunkPrompt}
                        onChange={(e) =>
                          updateConfig({
                            childChunkPrompt: e.target.value
                          })
                        }
                        className="min-h-[300px] bg-[#161B22] border-[#30363D] text-gray-200 font-mono text-sm"
                        placeholder="输入意义子块生成的提示词..."
                      />
                      <div className="text-xs text-gray-500">
                        使用 {'{content}'} 作为原文内容的占位符
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-gray-400">提示词预设</label>
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateConfig({
                              childChunkPrompt: `请为以下文本生成一个简洁但信息丰富的意义摘要：

{content}

要求：
1. 保留核心概念和关键信息
2. 提取主要情感和态度
3. 长度控制在100字以内
4. 使用清晰简洁的语言`
                            })
                          }
                          className="text-xs"
                        >
                          简洁摘要
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateConfig({
                              childChunkPrompt: `分析以下文本的深层含义和情感内容：

{content}

请提取：
1. 核心观点和主要论述
2. 情感色彩和态度倾向
3. 隐含的价值观和信念
4. 与个人成长相关的洞察`
                            })
                          }
                          className="text-xs"
                        >
                          深度分析
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={handleReset}
            className="text-gray-400"
          >
            重置
          </Button>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={onCancel}
            >
              <X className="w-4 h-4 mr-2" />
              取消
            </Button>
            <Button
              onClick={handleSave}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              保存配置
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
