"use client"

import { useState, useEffect } from "react"
import { BaseDebugger, useDebuggerState, BaseDebuggerConfig } from "./BaseDebugger"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Play, Download, Upload } from "lucide-react"

// 统一的 RAG 调试参数接口
interface UnifiedRAGDebugParams {
  mode: 'vector_only' | 'keyword_only' | 'hybrid_weighted'
  semanticAlgorithm: 'cosine' | 'euclidean' | 'dot_product'
  similarityThreshold: number
  topK: number
  finalCount: number
  weights: {
    semantic: number
    keyword: number
    importance: number
    temporal: number
    emotional: number
  }
  enableSemanticFiltering: boolean
  enableReranking: boolean
}

// RAG 上下文接口
interface RAGContext {
  retrievedMemories: Array<{
    id: string
    file: string
    content: string
    relevanceScore: number
    metadata?: Record<string, any>
  }>
  contextMetadata: {
    totalFiles: number
    totalChunks: number
    avgRelevanceScore: number
    processingTime: number
  }
  systemPrompt: string
  timestamp: string
}

// 调试结果接口
interface RAGDebugResult {
  query: string
  context: RAGContext
  performance: {
    retrievalTime: number
    totalTime: number
    candidatesCount: number
    finalCount: number
  }
  debugInfo: {
    weightingFactors: Record<string, number>
    filteringSteps: string[]
    rankingChanges: Array<{
      step: string
      changes: number
    }>
  }
}

interface UnifiedRAGDebuggerProps {
  onParamsChange?: (params: UnifiedRAGDebugParams) => void
  onDebugResult?: (result: RAGDebugResult) => void
}

export function UnifiedRAGDebugger({ onParamsChange, onDebugResult }: UnifiedRAGDebuggerProps) {
  // 调试器状态管理
  const { state, setLoading, setError, setSuccess } = useDebuggerState()

  // RAG 调试参数
  const [debugParams, setDebugParams] = useState<UnifiedRAGDebugParams>({
    mode: 'hybrid_weighted',
    semanticAlgorithm: 'cosine',
    similarityThreshold: 0.3,
    topK: 100,
    finalCount: 5,
    weights: {
      semantic: 0.4,
      keyword: 0.2,
      importance: 0.2,
      temporal: 0.1,
      emotional: 0.1
    },
    enableSemanticFiltering: true,
    enableReranking: true
  })

  // 调试结果和上下文
  const [debugResult, setDebugResult] = useState<RAGDebugResult | null>(null)
  const [ragContext, setRagContext] = useState<RAGContext | null>(null)
  const [testQuery, setTestQuery] = useState("")

  // 调试器配置
  const config: BaseDebuggerConfig = {
    title: "统一 RAG 调试器",
    description: "调试和优化 RAG 检索性能，支持多种检索模式和参数调优",
    autoRefresh: true,
    refreshInterval: 30000,
    showLastUpdated: true,
    showStatus: true
  }

  // 获取最新的 RAG 上下文
  const fetchRAGContext = async () => {
    try {
      const response = await fetch('/api/debug/last-context')
      if (response.ok) {
        const data = await response.json()
        setRagContext(data)
        return data
      }
      throw new Error('获取 RAG 上下文失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '网络错误')
    }
  }

  // 执行 RAG 调试
  const executeRAGDebug = async () => {
    if (!testQuery.trim()) {
      setError('请输入测试查询')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/debug/rag-debug', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: testQuery,
          params: debugParams
        })
      })

      if (response.ok) {
        const result = await response.json()
        setDebugResult(result)
        onDebugResult?.(result)
        setSuccess()
      } else {
        throw new Error('调试执行失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '调试失败')
    }
  }

  // 参数变更处理
  const handleParamsChange = (updates: Partial<UnifiedRAGDebugParams>) => {
    const newParams = { ...debugParams, ...updates }
    setDebugParams(newParams)
    onParamsChange?.(newParams)
  }

  // 权重变更处理
  const handleWeightChange = (key: keyof UnifiedRAGDebugParams['weights'], value: number) => {
    const newWeights = { ...debugParams.weights, [key]: value }
    handleParamsChange({ weights: newWeights })
  }

  // 导出配置
  const exportConfig = async () => {
    const configData = {
      params: debugParams,
      timestamp: new Date().toISOString()
    }
    const blob = new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `rag-debug-config-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  // 导入配置
  const importConfig = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target?.result as string)
            if (data.params) {
              setDebugParams(data.params)
              setSuccess()
            }
          } catch (error) {
            setError('配置文件格式错误')
          }
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  // 调试器操作
  const actions = {
    onRefresh: fetchRAGContext
  }

  // 头部操作按钮
  const headerActions = (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" onClick={exportConfig}>
        <Download className="w-4 h-4" />
        导出
      </Button>
      <Button variant="outline" size="sm" onClick={importConfig}>
        <Upload className="w-4 h-4" />
        导入
      </Button>
    </div>
  )

  return (
    <BaseDebugger
      config={config}
      state={state}
      actions={actions}
      headerActions={headerActions}
    >
      <Tabs defaultValue="params" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="params">参数配置</TabsTrigger>
          <TabsTrigger value="test">测试执行</TabsTrigger>
          <TabsTrigger value="context">上下文查看</TabsTrigger>
          <TabsTrigger value="results">结果分析</TabsTrigger>
        </TabsList>

        <TabsContent value="params" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {/* 基础配置 */}
            <div className="space-y-3">
              <h4 className="font-medium">基础配置</h4>
              
              <div className="space-y-2">
                <Label>检索模式</Label>
                <Select
                  value={debugParams.mode}
                  onValueChange={(value) => handleParamsChange({ mode: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vector_only">仅向量</SelectItem>
                    <SelectItem value="keyword_only">仅关键词</SelectItem>
                    <SelectItem value="hybrid_weighted">混合加权</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>语义算法</Label>
                <Select
                  value={debugParams.semanticAlgorithm}
                  onValueChange={(value) => handleParamsChange({ semanticAlgorithm: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cosine">余弦相似度</SelectItem>
                    <SelectItem value="euclidean">欧几里得距离</SelectItem>
                    <SelectItem value="dot_product">点积</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>相似度阈值: {debugParams.similarityThreshold}</Label>
                <Slider
                  value={[debugParams.similarityThreshold]}
                  onValueChange={([value]) => handleParamsChange({ similarityThreshold: value })}
                  min={0}
                  max={1}
                  step={0.01}
                />
              </div>
            </div>

            {/* 权重配置 */}
            <div className="space-y-3">
              <h4 className="font-medium">权重配置</h4>
              
              {Object.entries(debugParams.weights).map(([key, value]) => (
                <div key={key} className="space-y-2">
                  <Label>{key}: {value.toFixed(2)}</Label>
                  <Slider
                    value={[value]}
                    onValueChange={([newValue]) => handleWeightChange(key as any, newValue)}
                    min={0}
                    max={1}
                    step={0.01}
                  />
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <div className="space-y-3">
            <Label>测试查询</Label>
            <Input
              value={testQuery}
              onChange={(e) => setTestQuery(e.target.value)}
              placeholder="输入测试查询..."
            />
            <Button onClick={executeRAGDebug} disabled={state.isLoading}>
              <Play className="w-4 h-4 mr-2" />
              执行调试
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="context" className="space-y-4">
          {ragContext ? (
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{ragContext.contextMetadata.totalFiles}</div>
                  <div className="text-sm text-muted-foreground">文件数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{ragContext.contextMetadata.totalChunks}</div>
                  <div className="text-sm text-muted-foreground">块数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{ragContext.contextMetadata.avgRelevanceScore.toFixed(2)}</div>
                  <div className="text-sm text-muted-foreground">平均相关性</div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h4 className="font-medium">检索到的记忆</h4>
                {ragContext.retrievedMemories.slice(0, 3).map((memory, index) => (
                  <div key={index} className="p-3 border rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <Badge variant="outline">{memory.file}</Badge>
                      <Badge>{memory.relevanceScore.toFixed(3)}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {memory.content}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              暂无上下文数据
            </div>
          )}
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {debugResult ? (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{debugResult.performance.retrievalTime}ms</div>
                  <div className="text-sm text-muted-foreground">检索时间</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{debugResult.performance.candidatesCount}</div>
                  <div className="text-sm text-muted-foreground">候选数量</div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h4 className="font-medium">调试信息</h4>
                <div className="space-y-1">
                  {debugResult.debugInfo.filteringSteps.map((step, index) => (
                    <div key={index} className="text-sm text-muted-foreground">
                      {index + 1}. {step}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              暂无调试结果
            </div>
          )}
        </TabsContent>
      </Tabs>
    </BaseDebugger>
  )
}
