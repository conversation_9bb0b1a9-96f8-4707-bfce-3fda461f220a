"use client"

import { useState, useEffect, ReactNode } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { RefreshCw, CheckCircle, AlertTriangle, Loader2 } from "lucide-react"

// 通用的调试器配置接口
export interface BaseDebuggerConfig {
  title: string
  description?: string
  autoRefresh?: boolean
  refreshInterval?: number
  showLastUpdated?: boolean
  showStatus?: boolean
}

// 通用的状态接口
export interface BaseDebuggerState {
  isLoading: boolean
  lastUpdated: Date | null
  error: string | null
  status: 'idle' | 'loading' | 'success' | 'error'
}

// 通用的操作接口
export interface BaseDebuggerActions {
  onRefresh?: () => Promise<void>
  onReset?: () => Promise<void>
  onExport?: () => Promise<void>
  onImport?: (data: any) => Promise<void>
}

// 基础调试器组件的 Props
export interface BaseDebuggerProps {
  config: BaseDebuggerConfig
  state: BaseDebuggerState
  actions?: BaseDebuggerActions
  children: ReactNode
  headerActions?: ReactNode
  footerActions?: ReactNode
}

/**
 * 基础调试器组件
 * 提供通用的调试器界面结构和状态管理
 */
export function BaseDebugger({
  config,
  state,
  actions,
  children,
  headerActions,
  footerActions
}: BaseDebuggerProps) {
  const [internalState, setInternalState] = useState<BaseDebuggerState>(state)

  // 自动刷新逻辑
  useEffect(() => {
    if (config.autoRefresh && actions?.onRefresh) {
      const interval = setInterval(() => {
        if (!internalState.isLoading) {
          actions.onRefresh?.()
        }
      }, config.refreshInterval || 10000)

      return () => clearInterval(interval)
    }
  }, [config.autoRefresh, config.refreshInterval, actions?.onRefresh, internalState.isLoading])

  // 同步外部状态
  useEffect(() => {
    setInternalState(state)
  }, [state])

  // 处理刷新操作
  const handleRefresh = async () => {
    if (actions?.onRefresh) {
      setInternalState(prev => ({ ...prev, isLoading: true, error: null }))
      try {
        await actions.onRefresh()
        setInternalState(prev => ({ 
          ...prev, 
          isLoading: false, 
          lastUpdated: new Date(),
          status: 'success',
          error: null
        }))
      } catch (error) {
        setInternalState(prev => ({ 
          ...prev, 
          isLoading: false, 
          status: 'error',
          error: error instanceof Error ? error.message : '操作失败'
        }))
      }
    }
  }

  // 处理重置操作
  const handleReset = async () => {
    if (actions?.onReset) {
      const confirmed = confirm('确定要重置吗？此操作不可撤销。')
      if (!confirmed) return

      setInternalState(prev => ({ ...prev, isLoading: true, error: null }))
      try {
        await actions.onReset()
        setInternalState(prev => ({ 
          ...prev, 
          isLoading: false, 
          lastUpdated: new Date(),
          status: 'success',
          error: null
        }))
      } catch (error) {
        setInternalState(prev => ({ 
          ...prev, 
          isLoading: false, 
          status: 'error',
          error: error instanceof Error ? error.message : '重置失败'
        }))
      }
    }
  }

  // 渲染状态指示器
  const renderStatusIndicator = () => {
    if (!config.showStatus) return null

    const statusConfig = {
      idle: { icon: null, color: 'default', text: '就绪' },
      loading: { icon: Loader2, color: 'default', text: '加载中...' },
      success: { icon: CheckCircle, color: 'default', text: '成功' },
      error: { icon: AlertTriangle, color: 'destructive', text: '错误' }
    }

    const { icon: Icon, color, text } = statusConfig[internalState.status]

    return (
      <Badge variant={color as any} className="flex items-center gap-1">
        {Icon && <Icon className="w-3 h-3" />}
        {text}
      </Badge>
    )
  }

  // 渲染最后更新时间
  const renderLastUpdated = () => {
    if (!config.showLastUpdated || !internalState.lastUpdated) return null

    return (
      <div className="text-xs text-muted-foreground">
        最后更新: {internalState.lastUpdated.toLocaleString()}
      </div>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {config.title}
              {renderStatusIndicator()}
            </CardTitle>
            {config.description && (
              <p className="text-sm text-muted-foreground mt-1">
                {config.description}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {actions?.onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={internalState.isLoading}
              >
                <RefreshCw className={`w-4 h-4 ${internalState.isLoading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            )}
            {headerActions}
          </div>
        </div>
        {renderLastUpdated()}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 错误提示 */}
        {internalState.error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{internalState.error}</AlertDescription>
          </Alert>
        )}

        {/* 主要内容 */}
        <div className={internalState.isLoading ? 'opacity-50 pointer-events-none' : ''}>
          {children}
        </div>

        {/* 底部操作 */}
        {footerActions && (
          <div className="flex items-center justify-between pt-4 border-t">
            {footerActions}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 自定义 Hook：管理调试器状态
 */
export function useDebuggerState(initialState?: Partial<BaseDebuggerState>) {
  const [state, setState] = useState<BaseDebuggerState>({
    isLoading: false,
    lastUpdated: null,
    error: null,
    status: 'idle',
    ...initialState
  })

  const updateState = (updates: Partial<BaseDebuggerState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }

  const setLoading = (loading: boolean) => {
    updateState({ 
      isLoading: loading, 
      status: loading ? 'loading' : 'idle',
      error: loading ? null : state.error
    })
  }

  const setError = (error: string | null) => {
    updateState({ 
      error, 
      status: error ? 'error' : 'success',
      isLoading: false
    })
  }

  const setSuccess = () => {
    updateState({ 
      status: 'success', 
      error: null, 
      isLoading: false,
      lastUpdated: new Date()
    })
  }

  return {
    state,
    updateState,
    setLoading,
    setError,
    setSuccess
  }
}
