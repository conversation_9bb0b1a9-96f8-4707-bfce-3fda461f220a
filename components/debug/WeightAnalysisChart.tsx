'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { WeightAnalysis } from '@/types/rag';

interface WeightAnalysisChartProps {
  data: WeightAnalysis[];
}

export function WeightAnalysisChart({ data }: WeightAnalysisChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400">
        <div className="text-center">
          <div className="text-lg mb-2">⚖️</div>
          <div>没有权重分析数据</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {data.map((analysis, index) => (
        <Card key={analysis.candidateId} className="bg-[#0D1117] border-[#30363D]">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm text-white">
                候选项 #{index + 1}
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-blue-400 border-blue-400">
                  原始: {analysis.originalScore.toFixed(3)}
                </Badge>
                <Badge variant="outline" className="text-green-400 border-green-400">
                  最终: {analysis.finalScore.toFixed(3)}
                </Badge>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* 分数分解 */}
            <div>
              <div className="text-xs text-gray-400 mb-2">分数分解:</div>
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-400">语义分数:</span>
                    <span className="text-white">{analysis.scoreBreakdown.semanticScore.toFixed(3)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">标签分数:</span>
                    <span className="text-white">{analysis.scoreBreakdown.tagScore.toFixed(3)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">重要性分数:</span>
                    <span className="text-white">{analysis.scoreBreakdown.importanceScore.toFixed(3)}</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-400">时间分数:</span>
                    <span className="text-white">{analysis.scoreBreakdown.temporalScore.toFixed(3)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">动态加权:</span>
                    <span className="text-green-400">+{analysis.scoreBreakdown.dynamicBoosts.toFixed(3)}</span>
                  </div>
                  <div className="flex justify-between border-t border-[#30363D] pt-1">
                    <span className="text-gray-300 font-medium">最终分数:</span>
                    <span className="text-green-400 font-medium">{analysis.scoreBreakdown.finalScore.toFixed(3)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 应用的规则 */}
            {analysis.appliedRules.length > 0 && (
              <div>
                <div className="text-xs text-gray-400 mb-2">应用的权重规则:</div>
                <div className="space-y-2">
                  {analysis.appliedRules.map((rule, ruleIndex) => (
                    <div 
                      key={ruleIndex} 
                      className={`p-2 rounded border ${
                        rule.matched 
                          ? 'bg-green-900/20 border-green-700' 
                          : 'bg-gray-900/20 border-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium text-white">
                          {rule.ruleName}
                        </span>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant="outline" 
                            className={rule.matched ? 'text-green-400 border-green-400' : 'text-gray-400 border-gray-600'}
                          >
                            {rule.matched ? '✓ 匹配' : '✗ 未匹配'}
                          </Badge>
                          {rule.matched && (
                            <Badge variant="outline" className="text-blue-400 border-blue-400">
                              +{rule.contribution.toFixed(3)}
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      {rule.matched && (
                        <div className="text-xs text-gray-400">
                          权重提升: {rule.boost}x → 贡献: +{rule.contribution.toFixed(3)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 分数变化可视化 */}
            <div>
              <div className="text-xs text-gray-400 mb-2">分数变化:</div>
              <div className="relative">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-[#21262D] rounded-full h-2 overflow-hidden">
                    <div 
                      className="h-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${analysis.originalScore * 100}%` }}
                    />
                  </div>
                  <span className="text-xs text-blue-400 w-12">
                    {analysis.originalScore.toFixed(2)}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2 mt-1">
                  <div className="flex-1 bg-[#21262D] rounded-full h-2 overflow-hidden">
                    <div 
                      className="h-full bg-green-500 transition-all duration-300"
                      style={{ width: `${analysis.finalScore * 100}%` }}
                    />
                  </div>
                  <span className="text-xs text-green-400 w-12">
                    {analysis.finalScore.toFixed(2)}
                  </span>
                </div>
                
                <div className="text-xs text-gray-400 mt-1">
                  变化: {analysis.finalScore > analysis.originalScore ? '+' : ''}{((analysis.finalScore - analysis.originalScore) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
