'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { IndexingConfig } from '@/types/rag';
import { ragConfigManager } from '@/lib/services/rag-config-manager';
import { PromptEditor } from './PromptEditor';

interface IndexingPipelineControllerProps {
  config: IndexingConfig;
  onConfigChange: (newConfig: Partial<IndexingConfig>) => void;
  onStartIndexing: (documentText: string) => Promise<void>;
  isIndexing: boolean;
  indexingProgress: number;
}

export function IndexingPipelineController({
  config,
  onConfigChange,
  onStartIndexing,
  isIndexing,
  indexingProgress
}: IndexingPipelineControllerProps) {
  const [documentInput, setDocumentInput] = useState('');
  const [validationResult, setValidationResult] = useState<any>(null);
  const [chunkPreview, setChunkPreview] = useState<string[]>([]);
  const [showChunkPreview, setShowChunkPreview] = useState(false);
  const [selectedPromptTemplate, setSelectedPromptTemplate] = useState<string>('');
  const [indexingResults, setIndexingResults] = useState<any>(null);

  // 配置验证
  const validateConfig = useCallback(() => {
    const result = ragConfigManager.validateIndexingConfig(config);
    setValidationResult(result);
    return result;
  }, [config]);

  // 处理配置变更
  const handleConfigChange = useCallback((field: keyof IndexingConfig, value: any) => {
    onConfigChange({ [field]: value });
  }, [onConfigChange]);

  // 启动索引
  const handleStartIndexing = useCallback(async () => {
    const validation = validateConfig();
    if (!validation.isValid) {
      alert('配置验证失败，请检查配置项');
      return;
    }

    if (!documentInput.trim()) {
      alert('请输入要索引的文档内容');
      return;
    }

    // 模拟索引结果
    const startTime = Date.now();
    const chunks = simulateChunking(documentInput, config);

    try {
      await onStartIndexing(documentInput);

      // 生成模拟的索引结果
      const processingTime = Date.now() - startTime;
      const mockResults = {
        totalChunks: chunks.length,
        successfulChunks: Math.max(0, chunks.length - Math.floor(Math.random() * 2)),
        processingTime,
        avgChunkLength: Math.round(chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length),
        vectorDimension: 2560, // Qwen3-Embedding-4B 的向量维度
        meaningExtractionRate: Math.round(85 + Math.random() * 10),
        errorRate: Math.round(Math.random() * 5),
        errors: Math.random() > 0.7 ? ['部分块处理超时', '网络连接不稳定'] : []
      };

      setIndexingResults(mockResults);
    } catch (error) {
      console.error('索引失败:', error);
    }
  }, [documentInput, validateConfig, onStartIndexing, config]);

  // 模拟分块逻辑
  const simulateChunking = useCallback((text: string, config: IndexingConfig): string[] => {
    const chunks: string[] = [];
    const { chunkSize, chunkOverlap, chunkingStrategy } = config;

    switch (chunkingStrategy) {
      case 'recursive':
        // 递归分块
        const paragraphs = text.split(/\n\s*\n/);
        for (const paragraph of paragraphs) {
          if (paragraph.length <= chunkSize) {
            chunks.push(paragraph.trim());
          } else {
            // 按句子分割
            const sentences = paragraph.split(/[。！？.!?]/);
            let currentChunk = '';
            for (const sentence of sentences) {
              if ((currentChunk + sentence).length <= chunkSize) {
                currentChunk += sentence + '。';
              } else {
                if (currentChunk) chunks.push(currentChunk.trim());
                currentChunk = sentence + '。';
              }
            }
            if (currentChunk) chunks.push(currentChunk.trim());
          }
        }
        break;

      case 'sentence':
        // 按句子分块
        const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
        let currentChunk = '';
        for (const sentence of sentences) {
          const sentenceWithPunctuation = sentence.trim() + '。';
          if ((currentChunk + sentenceWithPunctuation).length <= chunkSize) {
            currentChunk += sentenceWithPunctuation;
          } else {
            if (currentChunk) chunks.push(currentChunk.trim());
            currentChunk = sentenceWithPunctuation;
          }
        }
        if (currentChunk) chunks.push(currentChunk.trim());
        break;

      case 'paragraph':
        // 按段落分块
        const paras = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        for (const para of paras) {
          if (para.length <= chunkSize) {
            chunks.push(para.trim());
          } else {
            // 段落太长，递归分块
            const subChunks = simulateChunking(para, { ...config, chunkingStrategy: 'recursive' });
            chunks.push(...subChunks);
          }
        }
        break;

      default:
        // 简单固定长度分块
        for (let i = 0; i < text.length; i += chunkSize - chunkOverlap) {
          const chunk = text.slice(i, i + chunkSize);
          if (chunk.length >= 50) chunks.push(chunk);
        }
    }

    return chunks.filter(chunk => chunk.length > 0);
  }, []);

  // 分块预览
  const handleChunkPreview = useCallback(() => {
    if (!documentInput.trim()) {
      alert('请先输入文档内容');
      return;
    }

    const chunks = simulateChunking(documentInput, config);
    setChunkPreview(chunks);
    setShowChunkPreview(true);
  }, [documentInput, config, simulateChunking]);

  // 测试提示词
  const handleTestPrompt = useCallback(async () => {
    if (!config.meaningGenerationPrompt) {
      alert('请先设置意义生成提示词');
      return;
    }

    const sampleText = documentInput.slice(0, 500) || '这是一个测试文本，用于验证提示词的效果。';
    const testPrompt = config.meaningGenerationPrompt.replace('{chunk}', sampleText);

    console.log('🧪 测试提示词:', testPrompt);
    alert('提示词测试结果已输出到控制台');
  }, [config.meaningGenerationPrompt, documentInput]);

  // 提示词模板
  const promptTemplates = {
    default: `你是一个专业的记忆意义提取专家。请将以下记忆片段转换为简洁而深刻的意义摘要。

## 任务要求：
1. 提取记忆片段的核心意义和情感价值
2. 保留关键的情感状态和认知洞察
3. 使用简洁、准确的语言
4. 长度控制在50-100字之间
5. 突出记忆的深层含义，而非表面事实

## 原始记忆片段：
{chunk}

## 请生成意义摘要：`,

    emotional: `作为情感分析专家，请深度分析以下记忆片段的情感内核。

## 分析重点：
1. 识别主要情感状态和强度
2. 挖掘潜在的情感冲突或转变
3. 提取情感背后的深层需求
4. 总结情感体验的意义价值

## 记忆片段：
{chunk}

## 情感意义摘要：`,

    cognitive: `作为认知心理学专家，请分析以下记忆片段的认知模式和思维特征。

## 分析维度：
1. 思维模式和认知风格
2. 决策过程和推理逻辑
3. 学习和成长的认知洞察
4. 认知偏差和思维盲点

## 记忆片段：
{chunk}

## 认知意义摘要：`,

    growth: `作为个人成长导师，请提取以下记忆片段中的成长价值和启示。

## 提取重点：
1. 个人成长的关键时刻
2. 能力提升和突破点
3. 价值观形成和转变
4. 未来发展的启示意义

## 记忆片段：
{chunk}

## 成长意义摘要：`
  };

  // 应用提示词模板
  const handleApplyTemplate = useCallback((templateKey: string) => {
    const template = promptTemplates[templateKey as keyof typeof promptTemplates];
    if (template) {
      handleConfigChange('meaningGenerationPrompt', template);
      setSelectedPromptTemplate(templateKey);
    }
  }, [handleConfigChange]);

  return (
    <div className="h-full flex flex-col bg-[#161B22]">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg text-white flex items-center justify-between">
          索引流水线控制器
          <Badge variant="outline" className="text-blue-400 border-blue-400">
            {config.chunkingStrategy}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto space-y-4">
        <Tabs defaultValue="chunking" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-[#21262D]">
            <TabsTrigger value="chunking" className="text-gray-300">分块配置</TabsTrigger>
            <TabsTrigger value="prompts" className="text-gray-300">提示词</TabsTrigger>
            <TabsTrigger value="execution" className="text-gray-300">执行控制</TabsTrigger>
          </TabsList>

          {/* 分块策略配置 */}
          <TabsContent value="chunking" className="space-y-4">
            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">文档分块配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-gray-300">分块策略</Label>
                  <Select
                    value={config.chunkingStrategy}
                    onValueChange={(value: IndexingConfig['chunkingStrategy']) => 
                      handleConfigChange('chunkingStrategy', value)
                    }
                  >
                    <SelectTrigger className="bg-[#21262D] border-[#30363D] text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-[#21262D] border-[#30363D]">
                      <SelectItem value="recursive" className="text-white">递归字符分块</SelectItem>
                      <SelectItem value="sentence" className="text-white">按句子分块</SelectItem>
                      <SelectItem value="paragraph" className="text-white">按段落分块</SelectItem>
                      <SelectItem value="semantic" className="text-white">语义分块（AI辅助）</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-gray-300">块大小</Label>
                    <Input
                      type="number"
                      min={100}
                      max={2000}
                      step={50}
                      value={config.chunkSize}
                      onChange={(e) => handleConfigChange('chunkSize', parseInt(e.target.value))}
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-gray-300">块重叠</Label>
                    <Input
                      type="number"
                      min={0}
                      max={500}
                      step={25}
                      value={config.chunkOverlap}
                      onChange={(e) => handleConfigChange('chunkOverlap', parseInt(e.target.value))}
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-gray-300">批处理大小</Label>
                    <Input
                      type="number"
                      min={1}
                      max={100}
                      value={config.batchSize}
                      onChange={(e) => handleConfigChange('batchSize', parseInt(e.target.value))}
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-gray-300">最大并发数</Label>
                    <Input
                      type="number"
                      min={1}
                      max={10}
                      value={config.maxConcurrency}
                      onChange={(e) => handleConfigChange('maxConcurrency', parseInt(e.target.value))}
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-gray-300">启用缓存</Label>
                  <Switch
                    checked={config.enableCaching}
                    onCheckedChange={(checked) => handleConfigChange('enableCaching', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 分块预览 */}
            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center justify-between">
                  分块预览
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleChunkPreview}
                    disabled={!documentInput.trim()}
                    className="text-gray-300 border-gray-600 hover:bg-gray-700"
                  >
                    预览分块
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {showChunkPreview && chunkPreview.length > 0 ? (
                  <div className="space-y-3">
                    <div className="text-xs text-gray-400 mb-2">
                      共生成 {chunkPreview.length} 个块，平均长度: {Math.round(chunkPreview.reduce((sum, chunk) => sum + chunk.length, 0) / chunkPreview.length)} 字符
                    </div>
                    <div className="max-h-60 overflow-y-auto space-y-2">
                      {chunkPreview.slice(0, 5).map((chunk, index) => (
                        <div key={index} className="p-3 bg-[#21262D] rounded border border-[#30363D]">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs text-blue-400">块 #{index + 1}</span>
                            <span className="text-xs text-gray-400">{chunk.length} 字符</span>
                          </div>
                          <div className="text-xs text-gray-300 leading-relaxed">
                            {chunk.length > 200 ? chunk.substring(0, 200) + '...' : chunk}
                          </div>
                        </div>
                      ))}
                      {chunkPreview.length > 5 && (
                        <div className="text-xs text-gray-400 text-center py-2">
                          还有 {chunkPreview.length - 5} 个块未显示...
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-400 py-8">
                    <div className="text-lg mb-2">📄</div>
                    <div>输入文档内容后点击"预览分块"查看分块效果</div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 提示词编辑 */}
          <TabsContent value="prompts" className="space-y-4">
            {/* 提示词模板选择 */}
            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">提示词模板</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={selectedPromptTemplate === 'default' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleApplyTemplate('default')}
                    className="text-xs"
                  >
                    🔧 通用模板
                  </Button>
                  <Button
                    variant={selectedPromptTemplate === 'emotional' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleApplyTemplate('emotional')}
                    className="text-xs"
                  >
                    💝 情感分析
                  </Button>
                  <Button
                    variant={selectedPromptTemplate === 'cognitive' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleApplyTemplate('cognitive')}
                    className="text-xs"
                  >
                    🧠 认知分析
                  </Button>
                  <Button
                    variant={selectedPromptTemplate === 'growth' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleApplyTemplate('growth')}
                    className="text-xs"
                  >
                    🌱 成长导向
                  </Button>
                </div>

                {selectedPromptTemplate && (
                  <div className="text-xs text-gray-400 p-2 bg-[#21262D] rounded">
                    当前模板: {
                      selectedPromptTemplate === 'default' ? '通用意义提取模板' :
                      selectedPromptTemplate === 'emotional' ? '情感分析专用模板' :
                      selectedPromptTemplate === 'cognitive' ? '认知分析专用模板' :
                      selectedPromptTemplate === 'growth' ? '个人成长专用模板' : '自定义模板'
                    }
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300 flex items-center justify-between">
                  意义子块生成提示词
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.enableAIEnhancement}
                      onCheckedChange={(checked) => handleConfigChange('enableAIEnhancement', checked)}
                    />
                    <Label className="text-xs text-gray-400">AI增强</Label>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  value={config.meaningGenerationPrompt}
                  onChange={(e) => handleConfigChange('meaningGenerationPrompt', e.target.value)}
                  className="min-h-[200px]"
                  placeholder="输入意义生成提示词..."
                />
                
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={validateConfig}
                    className="text-gray-300 border-gray-600 hover:bg-gray-700"
                  >
                    验证提示词
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleTestPrompt}
                    className="text-gray-300 border-gray-600 hover:bg-gray-700"
                  >
                    测试样本
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">连接标签生成提示词</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={config.connectionTaggingPrompt}
                  onChange={(e) => handleConfigChange('connectionTaggingPrompt', e.target.value)}
                  className="min-h-[150px]"
                  placeholder="输入连接标签生成提示词..."
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* 执行控制 */}
          <TabsContent value="execution" className="space-y-4">
            {/* 文档输入方式选择 */}
            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">输入方式</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="text" className="w-full">
                  <TabsList className="grid w-full grid-cols-3 bg-[#21262D]">
                    <TabsTrigger value="text" className="text-gray-300 text-xs">文本输入</TabsTrigger>
                    <TabsTrigger value="file" className="text-gray-300 text-xs">文件上传</TabsTrigger>
                    <TabsTrigger value="batch" className="text-gray-300 text-xs">批量处理</TabsTrigger>
                  </TabsList>

                  <TabsContent value="text" className="mt-4">
                    <div className="space-y-4">
                      <Textarea
                        placeholder="粘贴要索引的文档内容..."
                        value={documentInput}
                        onChange={(e) => setDocumentInput(e.target.value)}
                        rows={8}
                        className="bg-[#21262D] border-[#30363D] text-white font-mono text-sm"
                      />

                      <div className="flex items-center justify-between">
                        <div className="text-xs text-gray-400">
                          字符数: {documentInput.length}
                          {documentInput.length > 0 && (
                            <span className="ml-2">
                              预估块数: {Math.ceil(documentInput.length / config.chunkSize)}
                            </span>
                          )}
                        </div>

                        <Button
                          onClick={handleStartIndexing}
                          disabled={isIndexing || !documentInput.trim()}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          {isIndexing ? '索引中...' : '启动索引与向量化'}
                        </Button>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="file" className="mt-4">
                    <div className="space-y-4">
                      <div className="border-2 border-dashed border-[#30363D] rounded-lg p-8 text-center">
                        <div className="text-gray-400 mb-4">
                          <div className="text-2xl mb-2">📁</div>
                          <div>拖拽文件到此处或点击选择</div>
                          <div className="text-xs mt-1">支持 .txt, .md, .docx 格式</div>
                        </div>
                        <input
                          type="file"
                          accept=".txt,.md,.docx"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const reader = new FileReader();
                              reader.onload = (e) => {
                                const content = e.target?.result as string;
                                setDocumentInput(content);
                              };
                              reader.readAsText(file);
                            }
                          }}
                          className="hidden"
                          id="file-upload"
                        />
                        <Button
                          variant="outline"
                          onClick={() => document.getElementById('file-upload')?.click()}
                          className="text-gray-300 border-gray-600 hover:bg-gray-700"
                        >
                          选择文件
                        </Button>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="batch" className="mt-4">
                    <div className="space-y-4">
                      <div className="text-sm text-gray-300 mb-2">批量文档处理</div>
                      <div className="border border-[#30363D] rounded p-4 bg-[#21262D]">
                        <div className="text-xs text-gray-400 mb-2">功能开发中...</div>
                        <div className="text-xs text-gray-500">
                          • 支持多文件同时上传<br/>
                          • 批量索引进度跟踪<br/>
                          • 结果统计和分析<br/>
                          • 错误处理和重试
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* 索引进度和状态 */}
            {isIndexing && (
              <Card className="bg-[#0D1117] border-[#30363D]">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm text-gray-300">索引进度</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-300">总体进度</span>
                      <span className="text-blue-400">{indexingProgress}%</span>
                    </div>
                    <Progress value={indexingProgress} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div className="space-y-1">
                      <div className="text-gray-400">当前阶段:</div>
                      <div className="text-white">
                        {indexingProgress < 20 ? '文档分块' :
                         indexingProgress < 60 ? '向量生成' :
                         indexingProgress < 90 ? '意义提取' : '数据存储'}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-gray-400">预估剩余:</div>
                      <div className="text-white">
                        {Math.max(0, Math.round((100 - indexingProgress) * 0.5))}秒
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">文档输入</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="粘贴要索引的文档内容..."
                  value={documentInput}
                  onChange={(e) => setDocumentInput(e.target.value)}
                  rows={8}
                  className="bg-[#21262D] border-[#30363D] text-white font-mono text-sm"
                />
                
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-400">
                    字符数: {documentInput.length}
                    {documentInput.length > 0 && (
                      <span className="ml-2">
                        预估块数: {Math.ceil(documentInput.length / config.chunkSize)}
                      </span>
                    )}
                  </div>
                  
                  <Button
                    onClick={handleStartIndexing}
                    disabled={isIndexing || !documentInput.trim()}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    {isIndexing ? '索引中...' : '启动索引与向量化'}
                  </Button>
                </div>

                {isIndexing && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-300">索引进度</span>
                      <span className="text-blue-400">{indexingProgress}%</span>
                    </div>
                    <Progress value={indexingProgress} className="h-2" />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 索引结果分析 */}
            {indexingResults && (
              <Card className="bg-[#0D1117] border-[#30363D]">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm text-gray-300">索引结果分析</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-lg font-bold text-blue-400">{indexingResults.totalChunks || 0}</div>
                        <div className="text-xs text-gray-400">总块数</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-400">{indexingResults.successfulChunks || 0}</div>
                        <div className="text-xs text-gray-400">成功处理</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-yellow-400">{indexingResults.processingTime || 0}ms</div>
                        <div className="text-xs text-gray-400">处理时间</div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="text-xs text-gray-400">处理统计:</div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-400">平均块长度:</span>
                          <span className="text-white">{indexingResults.avgChunkLength || 0} 字符</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">向量维度:</span>
                          <span className="text-white">{indexingResults.vectorDimension || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">意义提取率:</span>
                          <span className="text-white">{indexingResults.meaningExtractionRate || 0}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">错误率:</span>
                          <span className="text-white">{indexingResults.errorRate || 0}%</span>
                        </div>
                      </div>
                    </div>

                    {indexingResults.errors && indexingResults.errors.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-xs text-red-400">处理错误:</div>
                        <div className="max-h-32 overflow-y-auto space-y-1">
                          {indexingResults.errors.map((error: string, index: number) => (
                            <div key={index} className="text-xs text-red-300 p-2 bg-red-900/20 rounded">
                              {error}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const data = JSON.stringify(indexingResults, null, 2);
                          const blob = new Blob([data], { type: 'application/json' });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `indexing-results-${Date.now()}.json`;
                          a.click();
                          URL.revokeObjectURL(url);
                        }}
                        className="text-gray-300 border-gray-600 hover:bg-gray-700"
                      >
                        导出结果
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIndexingResults(null)}
                        className="text-gray-300 border-gray-600 hover:bg-gray-700"
                      >
                        清除结果
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 配置验证结果 */}
            {validationResult && (
              <Card className="bg-[#0D1117] border-[#30363D]">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm text-gray-300">配置验证结果</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={validationResult.isValid ? "default" : "destructive"}
                        className={validationResult.isValid ? "bg-green-600" : "bg-red-600"}
                      >
                        {validationResult.isValid ? '✅ 配置有效' : '❌ 配置无效'}
                      </Badge>
                    </div>

                    {validationResult.errors?.length > 0 && (
                      <div className="space-y-1">
                        <div className="text-xs text-red-400">错误:</div>
                        {validationResult.errors.map((error: any, index: number) => (
                          <div key={index} className="text-xs text-red-300 ml-2">
                            • {error.field}: {error.message}
                          </div>
                        ))}
                      </div>
                    )}

                    {validationResult.warnings?.length > 0 && (
                      <div className="space-y-1">
                        <div className="text-xs text-yellow-400">警告:</div>
                        {validationResult.warnings.map((warning: any, index: number) => (
                          <div key={index} className="text-xs text-yellow-300 ml-2">
                            • {warning.field}: {warning.message}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </div>
  );
}
