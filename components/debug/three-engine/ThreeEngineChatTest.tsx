"use client"

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Send, 
  MessageSquare, 
  Bot, 
  User, 
  Zap, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: {
    workflowId?: string;
    totalTime?: number;
    quality?: number;
    engineTimes?: {
      navigator: number;
      contextRetriever: number;
      integrationGenerator: number;
    };
    cacheUsed?: boolean;
  };
}

interface ChatSession {
  sessionId: string;
  messages: Message[];
  startTime: string;
  topics: string[];
}

export function ThreeEngineChatTest() {
  const [session, setSession] = useState<ChatSession>({
    sessionId: `session_${Date.now()}`,
    messages: [],
    startTime: new Date().toISOString(),
    topics: []
  });
  
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [useThreeEngine, setUseThreeEngine] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [streamingMessage, setStreamingMessage] = useState('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [session.messages, streamingMessage]);

  // 发送消息
  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date().toISOString()
    };

    // 添加用户消息
    setSession(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage]
    }));

    const currentInput = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);
    setError(null);
    setStreamingMessage('');

    try {
      const requestBody = {
        messages: [
          ...session.messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          { role: 'user', content: currentInput }
        ],
        useThreeEngine,
        userProfile: '测试用户画像：喜欢深度思考，关注技术发展',
        sessionInfo: {
          sessionId: session.sessionId,
          startTime: session.startTime,
          topics: session.topics,
          emotionalState: 'curious'
        }
      };

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': useThreeEngine ? 'text/stream' : 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (useThreeEngine && response.headers.get('content-type')?.includes('text/stream')) {
        // 处理流式响应
        await handleStreamResponse(response);
      } else {
        // 处理普通响应
        const data = await response.json();
        
        const assistantMessage: Message = {
          id: `msg_${Date.now()}_assistant`,
          role: 'assistant',
          content: data.response || data.content || '抱歉，我无法生成回复。',
          timestamp: new Date().toISOString(),
          metadata: data.metadata
        };

        setSession(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage]
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送消息失败');
      console.error('发送消息失败:', err);
    } finally {
      setIsLoading(false);
      setStreamingMessage('');
    }
  };

  // 处理流式响应
  const handleStreamResponse = async (response: Response) => {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('无法读取响应流');

    const decoder = new TextDecoder();
    let accumulatedContent = '';
    let workflowMetadata: any = null;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim());

        for (const line of lines) {
          try {
            const data = JSON.parse(line);
            
            switch (data.type) {
              case 'navigator_complete':
                console.log('Navigator完成:', data.data);
                break;
              case 'retrieval_complete':
                console.log('检索完成:', data);
                break;
              case 'generation_chunk':
                if (data.data?.content) {
                  accumulatedContent += data.data.content;
                  setStreamingMessage(accumulatedContent);
                }
                if (data.metadata) {
                  workflowMetadata = data.metadata;
                }
                break;
              case 'workflow_complete':
                console.log('工作流完成');
                break;
              case 'workflow_error':
                throw new Error(data.error);
            }
          } catch (parseError) {
            console.warn('解析流数据失败:', parseError, line);
          }
        }
      }

      // 创建最终的助手消息
      if (accumulatedContent) {
        const assistantMessage: Message = {
          id: `msg_${Date.now()}_assistant`,
          role: 'assistant',
          content: accumulatedContent,
          timestamp: new Date().toISOString(),
          metadata: workflowMetadata
        };

        setSession(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage]
        }));
      }
    } finally {
      reader.releaseLock();
    }
  };

  // 清空对话
  const clearChat = () => {
    setSession({
      sessionId: `session_${Date.now()}`,
      messages: [],
      startTime: new Date().toISOString(),
      topics: []
    });
    setError(null);
    setStreamingMessage('');
  };

  // 处理回车发送
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className="h-full flex flex-col bg-[#18181B] text-gray-100">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between p-4 border-b border-[#27272A]">
        <div className="flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-blue-400" />
          <h2 className="text-lg font-semibold text-white">三引擎聊天测试</h2>
          <Badge variant="outline" className="text-xs">
            {session.messages.length} 条消息
          </Badge>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="three-engine-mode"
              checked={useThreeEngine}
              onCheckedChange={setUseThreeEngine}
            />
            <Label htmlFor="three-engine-mode" className="text-sm">
              三引擎模式
            </Label>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={clearChat}
            disabled={isLoading}
          >
            清空对话
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert className="m-4 border-red-500/30 bg-red-500/10">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-red-400">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* 消息列表 */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {session.messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-[#1F1F23] border border-[#27272A] text-gray-100'
                }`}
              >
                <div className="flex items-center space-x-2 mb-1">
                  {message.role === 'user' ? (
                    <User className="w-4 h-4" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                  <span className="text-xs opacity-70">
                    {formatTime(message.timestamp)}
                  </span>
                  {message.metadata?.workflowId && (
                    <Badge variant="outline" className="text-xs">
                      {useThreeEngine ? '三引擎' : '传统'}
                    </Badge>
                  )}
                </div>
                <div className="text-sm whitespace-pre-wrap">
                  {message.content}
                </div>
                
                {/* 显示性能指标 */}
                {message.metadata && (
                  <div className="mt-2 pt-2 border-t border-gray-600/30">
                    <div className="flex items-center space-x-4 text-xs text-gray-400">
                      {message.metadata.totalTime && (
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{message.metadata.totalTime}ms</span>
                        </div>
                      )}
                      {message.metadata.quality && (
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="w-3 h-3" />
                          <span>{(message.metadata.quality * 100).toFixed(0)}%</span>
                        </div>
                      )}
                      {message.metadata.cacheUsed && (
                        <div className="flex items-center space-x-1">
                          <CheckCircle className="w-3 h-3 text-green-400" />
                          <span>缓存命中</span>
                        </div>
                      )}
                    </div>
                    
                    {/* 引擎时间分解 */}
                    {message.metadata.engineTimes && (
                      <div className="mt-1 text-xs text-gray-500">
                        Navigator: {message.metadata.engineTimes.navigator}ms | 
                        Retriever: {message.metadata.engineTimes.contextRetriever}ms | 
                        Generator: {message.metadata.engineTimes.integrationGenerator}ms
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* 流式消息显示 */}
          {streamingMessage && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg p-3 bg-[#1F1F23] border border-[#27272A] text-gray-100">
                <div className="flex items-center space-x-2 mb-1">
                  <Bot className="w-4 h-4" />
                  <span className="text-xs opacity-70">正在生成...</span>
                  <Loader2 className="w-3 h-3 animate-spin" />
                </div>
                <div className="text-sm whitespace-pre-wrap">
                  {streamingMessage}
                  <span className="inline-block w-2 h-4 bg-blue-400 animate-pulse ml-1" />
                </div>
              </div>
            </div>
          )}

          {/* 加载指示器 */}
          {isLoading && !streamingMessage && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg p-3 bg-[#1F1F23] border border-[#27272A] text-gray-100">
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4" />
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-400">
                    {useThreeEngine ? '三引擎正在处理...' : 'AI正在思考...'}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
        <div ref={messagesEndRef} />
      </ScrollArea>

      {/* 输入区域 */}
      <div className="p-4 border-t border-[#27272A]">
        <div className="flex space-x-2">
          <Input
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={useThreeEngine ? "测试三引擎系统..." : "发送消息..."}
            disabled={isLoading}
            className="flex-1 bg-[#27272A] border-[#3F3F46] text-white placeholder-gray-400"
          />
          <Button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
        
        {/* 状态指示 */}
        <div className="flex items-center justify-between mt-2 text-xs text-gray-400">
          <div className="flex items-center space-x-2">
            <Zap className={`w-3 h-3 ${useThreeEngine ? 'text-green-400' : 'text-gray-500'}`} />
            <span>
              {useThreeEngine ? '三引擎模式已启用' : '传统模式'}
            </span>
          </div>
          <div>
            会话ID: {session.sessionId.slice(-8)}
          </div>
        </div>
      </div>
    </div>
  );
}
