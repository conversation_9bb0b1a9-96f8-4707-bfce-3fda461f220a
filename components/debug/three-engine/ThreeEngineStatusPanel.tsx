"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Activity, 
  Brain, 
  Search, 
  Zap, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Wifi,
  WifiOff
} from 'lucide-react';
import {
  useThreeEngineDataContext,
  ConnectionStatusIndicator,
  DataRefreshControls,
  DataSummary
} from './ThreeEngineDataProvider';
import { ThreeEngineTestUtils } from './ThreeEngineTestUtils';

export function ThreeEngineStatusPanel() {
  const {
    stats,
    latestResult,
    derivedData,
    error,
    hasData,
    isStale,
    connectionStatus,
    formatTime,
    formatDuration,
    formatPercentage
  } = useThreeEngineDataContext();

  // 获取引擎状态
  const getEngineStatus = () => {
    const baseStatus = derivedData.systemHealth === 'excellent' ? 'healthy' : 
                      derivedData.systemHealth === 'good' ? 'healthy' :
                      derivedData.systemHealth === 'fair' ? 'degraded' : 'unhealthy';

    return {
      navigator: {
        name: 'Navigator Engine',
        status: baseStatus,
        model: 'doubao/doubao-lite-4k',
        description: '战略规划引擎',
        color: 'text-purple-400',
        icon: Brain
      },
      contextRetriever: {
        name: 'Context Retriever',
        status: baseStatus,
        model: 'gemini/gemini-2.5-flash-preview-05-20',
        description: '轻量级检索引擎',
        color: 'text-blue-400',
        icon: Search
      },
      integrationGenerator: {
        name: 'Integration Generator',
        status: baseStatus,
        model: 'gemini/gemini-2.5-flash-preview-05-20',
        description: '集成生成引擎',
        color: 'text-green-400',
        icon: Zap
      }
    };
  };

  // 状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'unhealthy':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  // 状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'degraded':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'unhealthy':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const engineStatus = getEngineStatus();

  return (
    <div className="space-y-4">
      {/* 顶部状态栏 */}
      <div className="flex items-center justify-between p-4 bg-[#1F1F23] border border-[#27272A] rounded-lg">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">三引擎系统状态</h3>
          </div>
          <ConnectionStatusIndicator />
        </div>
        <DataRefreshControls />
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert className="border-red-500/30 bg-red-500/10">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-red-400">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* 数据状态提示 */}
      {!hasData && (
        <Alert className="border-yellow-500/30 bg-yellow-500/10">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-yellow-400">
            暂无三引擎数据，请在聊天测试区域进行一些对话以生成监控数据
          </AlertDescription>
        </Alert>
      )}

      {isStale && hasData && (
        <Alert className="border-orange-500/30 bg-orange-500/10">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-orange-400">
            数据可能已过期，建议刷新获取最新状态
          </AlertDescription>
        </Alert>
      )}

      {/* 测试工具 */}
      {!hasData && (
        <ThreeEngineTestUtils />
      )}

      {/* 系统概览 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* 引擎状态卡片 */}
        <Card className="bg-[#1F1F23] border-[#27272A]">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-300 flex items-center">
              <Activity className="w-4 h-4 mr-2 text-blue-400" />
              引擎状态
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {Object.entries(engineStatus).map(([key, engine]) => {
              const IconComponent = engine.icon;
              return (
                <div key={key} className="flex items-center justify-between p-2 rounded border border-[#27272A]">
                  <div className="flex items-center space-x-3">
                    <IconComponent className={`w-4 h-4 ${engine.color}`} />
                    <div>
                      <div className="text-sm text-gray-300 font-medium">{engine.name}</div>
                      <div className="text-xs text-gray-500">{engine.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(engine.status)}
                    <Badge className={`text-xs ${getStatusColor(engine.status)}`}>
                      {engine.status}
                    </Badge>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* 性能概览卡片 */}
        <Card className="bg-[#1F1F23] border-[#27272A]">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-300 flex items-center">
              <TrendingUp className="w-4 h-4 mr-2 text-green-400" />
              性能概览
            </CardTitle>
          </CardHeader>
          <CardContent>
            <DataSummary />
          </CardContent>
        </Card>
      </div>

      {/* 最新工作流结果 */}
      {latestResult?.workflowResult && (
        <Card className="bg-[#1F1F23] border-[#27272A]">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-300 flex items-center justify-between">
              <div className="flex items-center">
                <Zap className="w-4 h-4 mr-2 text-yellow-400" />
                最新工作流结果
              </div>
              <Badge variant="outline" className="text-xs">
                {formatTime(latestResult.workflowResult.metadata.timestamp)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 核心指标 */}
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-white">
                  {formatDuration(latestResult.workflowResult.totalTime)}
                </div>
                <div className="text-xs text-gray-400">总耗时</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-white">
                  {formatPercentage(latestResult.workflowResult.quality.overall)}
                </div>
                <div className="text-xs text-gray-400">质量分数</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-white">
                  {formatPercentage(latestResult.workflowResult.performance.parallelEfficiency)}
                </div>
                <div className="text-xs text-gray-400">并行效率</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-white">
                  {latestResult.workflowResult.performance.cacheUtilization.used ? '是' : '否'}
                </div>
                <div className="text-xs text-gray-400">使用缓存</div>
              </div>
            </div>

            {/* 质量分解 */}
            <div className="space-y-2">
              <div className="text-sm text-gray-300 mb-2">质量分解:</div>
              {Object.entries(latestResult.workflowResult.quality)
                .filter(([key]) => key !== 'overall')
                .map(([key, value]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <span className="text-xs text-gray-400 w-16 capitalize">{key}</span>
                    <Progress value={(value as number) * 100} className="flex-1 h-2" />
                    <span className="text-xs text-gray-300 w-12">
                      {formatPercentage(value as number)}
                    </span>
                  </div>
                ))}
            </div>

            {/* 引擎时间分解 */}
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <div className="text-gray-300 mb-1">引擎耗时:</div>
                <div className="space-y-1 text-gray-400">
                  <div>Navigator: {formatDuration(latestResult.workflowResult.performance.engineTimes.navigator)}</div>
                  <div>Retriever: {formatDuration(latestResult.workflowResult.performance.engineTimes.contextRetriever)}</div>
                </div>
              </div>
              <div>
                <div className="text-gray-300 mb-1">其他指标:</div>
                <div className="space-y-1 text-gray-400">
                  <div>Generator: {formatDuration(latestResult.workflowResult.performance.engineTimes.integrationGenerator)}</div>
                  <div>Cold Store: {formatDuration(latestResult.workflowResult.performance.engineTimes.coldStoreRetrieval)}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 系统健康指标 */}
      {stats && (
        <Card className="bg-[#1F1F23] border-[#27272A]">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-300 flex items-center">
              <Activity className="w-4 h-4 mr-2 text-blue-400" />
              系统健康指标
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">系统健康</span>
                  <Badge className={`text-xs ${getStatusColor(derivedData.systemHealth)}`}>
                    {derivedData.systemHealth}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">缓存效率</span>
                  <span className="text-sm text-white">{formatPercentage(stats.cacheHitRate)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">并行效率</span>
                  <span className="text-sm text-white">
                    {stats.enginePerformance?.parallelEfficiency ? 
                      formatPercentage(stats.enginePerformance.parallelEfficiency) : '-'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">活跃状态</span>
                  <div className="flex items-center space-x-1">
                    {derivedData.isActive ? (
                      <Wifi className="w-3 h-3 text-green-400" />
                    ) : (
                      <WifiOff className="w-3 h-3 text-gray-400" />
                    )}
                    <span className="text-xs text-gray-400">
                      {derivedData.isActive ? '活跃' : '非活跃'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
