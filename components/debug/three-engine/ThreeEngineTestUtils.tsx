"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  TestTube, 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle
} from 'lucide-react';

// 模拟三引擎工作流结果生成器
export function ThreeEngineTestUtils() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastGenerated, setLastGenerated] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 生成模拟的三引擎工作流结果
  const generateMockWorkflowResult = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      // 模拟工作流执行时间
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      const workflowId = `test_workflow_${Date.now()}`;
      const mockResult = {
        response: "这是一个模拟的三引擎协作响应。Navigator引擎分析了用户意图，Context Retriever检索了相关上下文，Integration Generator生成了这个综合回复。",
        engineResults: {
          navigator: {
            searchInstructions: {
              strategy: {
                searchType: "semantic",
                priority: "relevance",
                scope: "comprehensive"
              },
              keywords: {
                primary: ["测试", "三引擎"],
                secondary: ["调试", "监控"],
                emotional: ["好奇", "探索"],
                contextual: ["系统", "性能"]
              },
              targets: {
                hotStoreQueries: ["最近的测试对话", "用户反馈"],
                coldStoreQueries: ["历史性能数据", "系统日志"],
                crossReferences: ["相关配置", "错误记录"]
              }
            },
            processingTime: 300 + Math.random() * 200
          },
          contextRetriever: {
            contexts: [
              {
                id: "ctx_001",
                type: "recent_dialogue",
                content: "用户最近询问了系统性能相关问题",
                relevanceScore: 0.85,
                timestamp: new Date(Date.now() - 3600000).toISOString()
              },
              {
                id: "ctx_002", 
                type: "emotional_context",
                content: "用户表现出对技术细节的浓厚兴趣",
                relevanceScore: 0.78,
                timestamp: new Date(Date.now() - 1800000).toISOString()
              }
            ],
            summary: {
              totalResults: 2,
              relevanceScore: 0.82,
              emotionalState: "curious",
              keyTopics: ["性能", "技术", "系统"],
              timeSpan: "2小时内"
            },
            processingTime: 150 + Math.random() * 100
          },
          integrationGenerator: {
            response: "基于Navigator的分析和Context Retriever的检索结果，我为您生成了这个综合性回复。",
            quality: {
              coherence: 0.85 + Math.random() * 0.1,
              relevance: 0.88 + Math.random() * 0.1,
              empathy: 0.82 + Math.random() * 0.1,
              insight: 0.79 + Math.random() * 0.15,
              overall: 0.84 + Math.random() * 0.1
            },
            processingTime: 800 + Math.random() * 400
          }
        },
        performance: {
          totalTime: 1250 + Math.random() * 500,
          engineTimes: {
            navigator: 300 + Math.random() * 200,
            contextRetriever: 150 + Math.random() * 100,
            integrationGenerator: 800 + Math.random() * 400,
            coldStoreRetrieval: 200 + Math.random() * 150
          },
          parallelEfficiency: 0.75 + Math.random() * 0.2,
          cacheUtilization: {
            used: Math.random() > 0.3,
            hitRate: 0.6 + Math.random() * 0.3,
            timeSaved: Math.random() > 0.3 ? 200 + Math.random() * 300 : 0
          }
        },
        quality: {
          overall: 0.84 + Math.random() * 0.1,
          coherence: 0.85 + Math.random() * 0.1,
          relevance: 0.88 + Math.random() * 0.1,
          empathy: 0.82 + Math.random() * 0.1,
          insight: 0.79 + Math.random() * 0.15
        },
        sourceAttribution: {
          userInputId: `input_${Date.now()}`,
          hotStoreContributions: ["recent_chat_001", "user_profile_002"],
          coldStoreContributions: ["knowledge_base_003", "historical_data_004"],
          totalSources: 4
        },
        metadata: {
          workflowId,
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          environment: "test"
        }
      };

      // 发送到调试API
      const response = await fetch('/api/debug/three-engine-result', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          workflowResult: mockResult,
          inputParams: {
            userInput: "测试三引擎系统",
            useThreeEngine: true,
            timestamp: new Date().toISOString()
          },
          timestamp: new Date().toISOString()
        })
      });

      if (response.ok) {
        setLastGenerated(workflowId);
        console.log('✅ 模拟工作流结果已生成:', workflowId);
      } else {
        throw new Error(`API响应错误: ${response.status}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '生成模拟数据失败');
      console.error('❌ 生成模拟数据失败:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  // 清空调试数据
  const clearDebugData = async () => {
    try {
      const response = await fetch('/api/debug/three-engine-result', {
        method: 'DELETE'
      });

      if (response.ok) {
        setLastGenerated(null);
        setError(null);
        console.log('✅ 调试数据已清空');
      } else {
        throw new Error(`清空失败: ${response.status}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '清空数据失败');
    }
  };

  return (
    <Card className="bg-[#1F1F23] border-[#27272A]">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm text-gray-300 flex items-center">
          <TestTube className="w-4 h-4 mr-2 text-blue-400" />
          三引擎测试工具
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 错误提示 */}
        {error && (
          <Alert className="border-red-500/30 bg-red-500/10">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-red-400">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* 状态显示 */}
        {lastGenerated && (
          <Alert className="border-green-500/30 bg-green-500/10">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="text-green-400">
              最后生成的工作流: {lastGenerated}
            </AlertDescription>
          </Alert>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          <Button
            onClick={generateMockWorkflowResult}
            disabled={isGenerating}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600"
            size="sm"
          >
            {isGenerating ? (
              <>
                <Clock className="w-4 h-4 mr-1 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-1" />
                生成模拟数据
              </>
            )}
          </Button>

          <Button
            onClick={clearDebugData}
            variant="outline"
            size="sm"
            disabled={isGenerating}
          >
            <XCircle className="w-4 h-4 mr-1" />
            清空数据
          </Button>
        </div>

        {/* 说明文字 */}
        <div className="text-xs text-gray-400 space-y-1">
          <p>• 点击"生成模拟数据"创建测试用的三引擎工作流结果</p>
          <p>• 生成的数据会自动发送到调试API，监控面板会实时更新</p>
          <p>• 可以多次生成来测试历史记录和统计功能</p>
        </div>
      </CardContent>
    </Card>
  );
}
