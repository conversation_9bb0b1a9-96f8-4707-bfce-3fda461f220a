"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useThreeEngineData } from '@/hooks/useThreeEngineData';
// TODO: 重构后重新启用
// import { useThreeEngineDebugNotifications } from '@/lib/services/three-engine-debug-notifier';

interface ThreeEngineDataContextType {
  // 数据
  stats: any;
  latestResult: any;
  history: any[];
  derivedData: any;
  
  // 状态
  isLoading: boolean;
  error: string | null;
  lastUpdate: string | null;
  connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
  
  // 操作
  refreshData: () => Promise<void>;
  clearData: () => Promise<boolean>;
  
  // 设置
  autoRefresh: boolean;
  setAutoRefresh: (enabled: boolean) => void;
  refreshInterval: number;
  setRefreshInterval: (interval: number) => void;
  
  // 实用函数
  formatTime: (timestamp: string) => string;
  formatDuration: (ms: number) => string;
  formatPercentage: (value: number) => string;
  
  // 数据验证
  hasData: boolean;
  isStale: boolean;
}

const ThreeEngineDataContext = createContext<ThreeEngineDataContextType | null>(null);

interface ThreeEngineDataProviderProps {
  children: React.ReactNode;
  defaultAutoRefresh?: boolean;
  defaultRefreshInterval?: number;
}

export function ThreeEngineDataProvider({ 
  children, 
  defaultAutoRefresh = true,
  defaultRefreshInterval = 5000 
}: ThreeEngineDataProviderProps) {
  const [autoRefresh, setAutoRefresh] = useState(defaultAutoRefresh);
  const [refreshInterval, setRefreshInterval] = useState(defaultRefreshInterval);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting' | 'error'>('disconnected');

  const {
    stats,
    latestResult,
    history,
    derivedData,
    isLoading,
    error,
    lastUpdate,
    refreshData,
    clearData,
    hasData,
    isStale,
    formatTime,
    formatDuration,
    formatPercentage
  } = useThreeEngineData({
    autoRefresh,
    refreshInterval,
    enableWebSocket: false // 暂时禁用WebSocket
  });

  // 增强的刷新函数，包含状态管理
  const enhancedRefreshData = useCallback(async () => {
    setConnectionStatus('connecting');
    try {
      await refreshData();
      setConnectionStatus('connected');
    } catch (err) {
      setConnectionStatus('error');
      throw err;
    }
  }, [refreshData]);

  // TODO: 重构后重新启用实时通知
  // const {
  //   notifications,
  //   latestNotification,
  //   clearNotifications
  // } = useThreeEngineDebugNotifications();
  const notifications = [];
  const latestNotification = null;
  const clearNotifications = () => {};

  // 处理实时通知
  useEffect(() => {
    if (latestNotification && typeof latestNotification === 'object' && 'type' in latestNotification) {
      switch ((latestNotification as any).type) {
        case 'workflow_complete':
          // 工作流完成时自动刷新数据
          enhancedRefreshData();
          break;
        case 'workflow_error':
          console.error('工作流错误通知:', (latestNotification as any).data);
          break;
        case 'stats_update':
          // 统计数据更新时可以直接使用通知中的数据
          break;
      }
    }
  }, [latestNotification]);

  // 监控连接状态
  useEffect(() => {
    if (isLoading) {
      setConnectionStatus('connecting');
    } else if (error) {
      setConnectionStatus('error');
    } else if (hasData && !isStale) {
      setConnectionStatus('connected');
    } else {
      setConnectionStatus('disconnected');
    }
  }, [isLoading, error, hasData, isStale]);



  // 增强的清理函数
  const enhancedClearData = useCallback(async () => {
    try {
      const result = await clearData();
      if (result) {
        setConnectionStatus('disconnected');
      }
      return result;
    } catch (err) {
      setConnectionStatus('error');
      return false;
    }
  }, [clearData]);

  const contextValue: ThreeEngineDataContextType = {
    // 数据
    stats,
    latestResult,
    history,
    derivedData,
    
    // 状态
    isLoading,
    error,
    lastUpdate,
    connectionStatus,
    
    // 操作
    refreshData: enhancedRefreshData,
    clearData: enhancedClearData,
    
    // 设置
    autoRefresh,
    setAutoRefresh,
    refreshInterval,
    setRefreshInterval,
    
    // 实用函数
    formatTime,
    formatDuration,
    formatPercentage,
    
    // 数据验证
    hasData,
    isStale
  };

  return (
    <ThreeEngineDataContext.Provider value={contextValue}>
      {children}
    </ThreeEngineDataContext.Provider>
  );
}

export function useThreeEngineDataContext() {
  const context = useContext(ThreeEngineDataContext);
  if (!context) {
    throw new Error('useThreeEngineDataContext must be used within a ThreeEngineDataProvider');
  }
  return context;
}

// 连接状态指示器组件
export function ConnectionStatusIndicator() {
  const { connectionStatus, lastUpdate, error } = useThreeEngineDataContext();

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          color: 'text-green-400',
          bgColor: 'bg-green-500/20',
          borderColor: 'border-green-500/30',
          icon: '●',
          text: '已连接'
        };
      case 'connecting':
        return {
          color: 'text-blue-400',
          bgColor: 'bg-blue-500/20',
          borderColor: 'border-blue-500/30',
          icon: '◐',
          text: '连接中'
        };
      case 'error':
        return {
          color: 'text-red-400',
          bgColor: 'bg-red-500/20',
          borderColor: 'border-red-500/30',
          icon: '●',
          text: '连接错误'
        };
      default:
        return {
          color: 'text-gray-400',
          bgColor: 'bg-gray-500/20',
          borderColor: 'border-gray-500/30',
          icon: '○',
          text: '未连接'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`flex items-center space-x-2 px-2 py-1 rounded-md border ${config.bgColor} ${config.borderColor}`}>
      <span className={`${config.color} animate-pulse`}>{config.icon}</span>
      <span className={`text-xs ${config.color}`}>{config.text}</span>
      {lastUpdate && connectionStatus === 'connected' && (
        <span className="text-xs text-gray-500">
          {new Date(lastUpdate).toLocaleTimeString()}
        </span>
      )}
      {error && connectionStatus === 'error' && (
        <span className="text-xs text-red-400 truncate max-w-32" title={error}>
          {error}
        </span>
      )}
    </div>
  );
}

// 数据刷新控制组件
export function DataRefreshControls() {
  const { 
    autoRefresh, 
    setAutoRefresh, 
    refreshInterval, 
    setRefreshInterval,
    refreshData,
    isLoading 
  } = useThreeEngineDataContext();

  const intervalOptions = [
    { value: 1000, label: '1秒' },
    { value: 3000, label: '3秒' },
    { value: 5000, label: '5秒' },
    { value: 10000, label: '10秒' },
    { value: 30000, label: '30秒' }
  ];

  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="auto-refresh"
          checked={autoRefresh}
          onChange={(e) => setAutoRefresh(e.target.checked)}
          className="rounded border-gray-600 bg-gray-800 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="auto-refresh" className="text-sm text-gray-300">
          自动刷新
        </label>
      </div>

      {autoRefresh && (
        <select
          value={refreshInterval}
          onChange={(e) => setRefreshInterval(Number(e.target.value))}
          className="text-xs bg-gray-800 border border-gray-600 rounded px-2 py-1 text-gray-300"
        >
          {intervalOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )}

      <button
        onClick={refreshData}
        disabled={isLoading}
        className="text-xs px-2 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded text-white"
      >
        {isLoading ? '刷新中...' : '手动刷新'}
      </button>
    </div>
  );
}

// 数据统计摘要组件
export function DataSummary() {
  const { stats, latestResult, history, hasData } = useThreeEngineDataContext();

  if (!hasData) {
    return (
      <div className="text-center text-gray-400 py-4">
        <p className="text-sm">暂无数据</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-4 gap-4 text-center">
      <div>
        <div className="text-lg font-semibold text-white">
          {stats?.totalWorkflows || 0}
        </div>
        <div className="text-xs text-gray-400">总工作流</div>
      </div>
      <div>
        <div className="text-lg font-semibold text-white">
          {stats ? `${Math.round(stats.averageTime)}ms` : '-'}
        </div>
        <div className="text-xs text-gray-400">平均时间</div>
      </div>
      <div>
        <div className="text-lg font-semibold text-white">
          {stats ? `${(stats.averageQuality * 100).toFixed(0)}%` : '-'}
        </div>
        <div className="text-xs text-gray-400">平均质量</div>
      </div>
      <div>
        <div className="text-lg font-semibold text-white">
          {history.length}
        </div>
        <div className="text-xs text-gray-400">历史记录</div>
      </div>
    </div>
  );
}
