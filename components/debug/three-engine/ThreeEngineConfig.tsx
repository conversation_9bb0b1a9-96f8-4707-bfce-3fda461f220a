"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Settings, 
  Brain, 
  Search, 
  Zap, 
  Database,
  Save,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Cpu,
  Timer,
  MemoryStick
} from 'lucide-react';

interface EngineConfig {
  modelId: string;
  provider: 'doubao' | 'gemini' | 'openai';
  modelName: string;
  temperature: number;
  maxTokens: number;
  costOptimized?: boolean;
  speedOptimized?: boolean;
  qualityOptimized?: boolean;
}

interface WorkflowConfig {
  enableParallelExecution: boolean;
  maxConcurrentEngines: number;
  engineTimeout: number;
  totalTimeout: number;
}

interface CacheConfig {
  enabled: boolean;
  ttl: number;
  maxSize: number;
  keyStrategy: 'content_hash' | 'semantic_hash' | 'simple';
}

interface ThreeEngineConfiguration {
  navigator: EngineConfig;
  contextRetriever: EngineConfig;
  integrationGenerator: EngineConfig;
  workflow: WorkflowConfig;
  cache: CacheConfig;
  global: {
    enableFallback: boolean;
    fallbackProvider: string;
    timeout: number;
    retryAttempts: number;
    enableCaching: boolean;
  };
}

export function ThreeEngineConfig() {
  const [config, setConfig] = useState<ThreeEngineConfiguration | null>(null);
  const [originalConfig, setOriginalConfig] = useState<ThreeEngineConfiguration | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // 可用的模型选项
  const modelOptions = {
    doubao: [
      'doubao-lite-4k',
      'doubao-pro-4k',
      'doubao-pro-32k'
    ],
    gemini: [
      'gemini-2.5-flash-preview-05-20',
      'gemini-1.5-pro',
      'gemini-1.5-flash'
    ],
    openai: [
      'gpt-4o',
      'gpt-4o-mini',
      'gpt-3.5-turbo'
    ]
  };

  // 加载配置
  const loadConfig = async () => {
    setIsLoading(true);
    try {
      // 模拟API调用 - 实际应该调用三引擎配置API
      const mockConfig: ThreeEngineConfiguration = {
        navigator: {
          modelId: 'navigator_doubao',
          provider: 'doubao',
          modelName: 'doubao-lite-4k',
          temperature: 0.3,
          maxTokens: 1000,
          costOptimized: true
        },
        contextRetriever: {
          modelId: 'retriever_gemini',
          provider: 'gemini',
          modelName: 'gemini-2.5-flash-preview-05-20',
          temperature: 0.1,
          maxTokens: 500,
          speedOptimized: true
        },
        integrationGenerator: {
          modelId: 'generator_gemini',
          provider: 'gemini',
          modelName: 'gemini-2.5-flash-preview-05-20',
          temperature: 0.7,
          maxTokens: 2000,
          qualityOptimized: true
        },
        workflow: {
          enableParallelExecution: true,
          maxConcurrentEngines: 3,
          engineTimeout: 20000,
          totalTimeout: 45000
        },
        cache: {
          enabled: true,
          ttl: 600000,
          maxSize: 200,
          keyStrategy: 'semantic_hash'
        },
        global: {
          enableFallback: true,
          fallbackProvider: 'gemini',
          timeout: 30000,
          retryAttempts: 3,
          enableCaching: true
        }
      };

      setConfig(mockConfig);
      setOriginalConfig(JSON.parse(JSON.stringify(mockConfig)));
      setHasChanges(false);
    } catch (error) {
      setMessage({ type: 'error', text: '加载配置失败' });
    } finally {
      setIsLoading(false);
    }
  };

  // 保存配置
  const saveConfig = async () => {
    if (!config) return;

    setIsSaving(true);
    try {
      // 模拟API调用 - 实际应该调用三引擎配置更新API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setOriginalConfig(JSON.parse(JSON.stringify(config)));
      setHasChanges(false);
      setMessage({ type: 'success', text: '配置保存成功' });
    } catch (error) {
      setMessage({ type: 'error', text: '保存配置失败' });
    } finally {
      setIsSaving(false);
    }
  };

  // 重置配置
  const resetConfig = () => {
    if (originalConfig) {
      setConfig(JSON.parse(JSON.stringify(originalConfig)));
      setHasChanges(false);
      setMessage({ type: 'success', text: '配置已重置' });
    }
  };

  // 更新引擎配置
  const updateEngineConfig = (engine: keyof Pick<ThreeEngineConfiguration, 'navigator' | 'contextRetriever' | 'integrationGenerator'>, field: keyof EngineConfig, value: any) => {
    if (!config) return;

    const newConfig = { ...config };
    (newConfig[engine] as any)[field] = value;
    
    // 如果更改了provider，重置modelName
    if (field === 'provider') {
      newConfig[engine].modelName = modelOptions[value as keyof typeof modelOptions][0];
    }
    
    setConfig(newConfig);
    setHasChanges(true);
  };

  // 更新工作流配置
  const updateWorkflowConfig = (field: keyof WorkflowConfig, value: any) => {
    if (!config) return;

    const newConfig = { ...config };
    (newConfig.workflow as any)[field] = value;
    setConfig(newConfig);
    setHasChanges(true);
  };

  // 更新缓存配置
  const updateCacheConfig = (field: keyof CacheConfig, value: any) => {
    if (!config) return;

    const newConfig = { ...config };
    (newConfig.cache as any)[field] = value;
    setConfig(newConfig);
    setHasChanges(true);
  };

  useEffect(() => {
    loadConfig();
  }, []);

  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (isLoading || !config) {
    return (
      <div className="h-full flex items-center justify-center bg-[#18181B] text-gray-100">
        <div className="text-center">
          <Settings className="w-8 h-8 mx-auto mb-4 animate-spin text-blue-400" />
          <p>加载配置中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col space-y-4 p-4 bg-[#18181B] text-gray-100">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-blue-400" />
          <h2 className="text-lg font-semibold text-white">三引擎配置管理</h2>
          {hasChanges && (
            <Badge variant="outline" className="text-yellow-400 border-yellow-400">
              有未保存的更改
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={resetConfig}
            disabled={!hasChanges || isSaving}
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            重置
          </Button>
          <Button
            size="sm"
            onClick={saveConfig}
            disabled={!hasChanges || isSaving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="w-4 h-4 mr-1" />
            {isSaving ? '保存中...' : '保存配置'}
          </Button>
        </div>
      </div>

      {/* 消息提示 */}
      {message && (
        <Alert className={`border-${message.type === 'success' ? 'green' : 'red'}-500/30 bg-${message.type === 'success' ? 'green' : 'red'}-500/10`}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertTriangle className="h-4 w-4" />
          )}
          <AlertDescription className={`text-${message.type === 'success' ? 'green' : 'red'}-400`}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="engines" className="flex-1 flex flex-col">
        <TabsList className="bg-[#27272A] border-b border-[#27272A] rounded-none w-full justify-start">
          <TabsTrigger value="engines" className="text-sm">引擎配置</TabsTrigger>
          <TabsTrigger value="workflow" className="text-sm">工作流设置</TabsTrigger>
          <TabsTrigger value="cache" className="text-sm">缓存策略</TabsTrigger>
          <TabsTrigger value="global" className="text-sm">全局设置</TabsTrigger>
        </TabsList>

        {/* 引擎配置 */}
        <TabsContent value="engines" className="flex-1 space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {/* Navigator Engine */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-4">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <Brain className="w-4 h-4 mr-2 text-purple-400" />
                  Navigator Engine - 战略规划引擎
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">AI提供商</Label>
                    <Select
                      value={config.navigator.provider}
                      onValueChange={(value) => updateEngineConfig('navigator', 'provider', value)}
                    >
                      <SelectTrigger className="bg-[#27272A] border-[#3F3F46]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="doubao">豆包 (Doubao)</SelectItem>
                        <SelectItem value="gemini">Gemini</SelectItem>
                        <SelectItem value="openai">OpenAI</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">模型名称</Label>
                    <Select
                      value={config.navigator.modelName}
                      onValueChange={(value) => updateEngineConfig('navigator', 'modelName', value)}
                    >
                      <SelectTrigger className="bg-[#27272A] border-[#3F3F46]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {modelOptions[config.navigator.provider].map((model) => (
                          <SelectItem key={model} value={model}>{model}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">温度 ({config.navigator.temperature})</Label>
                    <Slider
                      value={[config.navigator.temperature]}
                      onValueChange={([value]) => updateEngineConfig('navigator', 'temperature', value)}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">最大Token数</Label>
                    <Input
                      type="number"
                      value={config.navigator.maxTokens}
                      onChange={(e) => updateEngineConfig('navigator', 'maxTokens', parseInt(e.target.value))}
                      className="bg-[#27272A] border-[#3F3F46]"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Context Retriever */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-4">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <Search className="w-4 h-4 mr-2 text-blue-400" />
                  Context Retriever - 轻量级检索引擎
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">AI提供商</Label>
                    <Select
                      value={config.contextRetriever.provider}
                      onValueChange={(value) => updateEngineConfig('contextRetriever', 'provider', value)}
                    >
                      <SelectTrigger className="bg-[#27272A] border-[#3F3F46]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="doubao">豆包 (Doubao)</SelectItem>
                        <SelectItem value="gemini">Gemini</SelectItem>
                        <SelectItem value="openai">OpenAI</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">模型名称</Label>
                    <Select
                      value={config.contextRetriever.modelName}
                      onValueChange={(value) => updateEngineConfig('contextRetriever', 'modelName', value)}
                    >
                      <SelectTrigger className="bg-[#27272A] border-[#3F3F46]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {modelOptions[config.contextRetriever.provider].map((model) => (
                          <SelectItem key={model} value={model}>{model}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">温度 ({config.contextRetriever.temperature})</Label>
                    <Slider
                      value={[config.contextRetriever.temperature]}
                      onValueChange={([value]) => updateEngineConfig('contextRetriever', 'temperature', value)}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">最大Token数</Label>
                    <Input
                      type="number"
                      value={config.contextRetriever.maxTokens}
                      onChange={(e) => updateEngineConfig('contextRetriever', 'maxTokens', parseInt(e.target.value))}
                      className="bg-[#27272A] border-[#3F3F46]"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Integration Generator */}
            <Card className="bg-[#1F1F23] border-[#27272A]">
              <CardHeader className="pb-4">
                <CardTitle className="text-sm text-gray-300 flex items-center">
                  <Zap className="w-4 h-4 mr-2 text-green-400" />
                  Integration Generator - 集成生成引擎
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">AI提供商</Label>
                    <Select
                      value={config.integrationGenerator.provider}
                      onValueChange={(value) => updateEngineConfig('integrationGenerator', 'provider', value)}
                    >
                      <SelectTrigger className="bg-[#27272A] border-[#3F3F46]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="doubao">豆包 (Doubao)</SelectItem>
                        <SelectItem value="gemini">Gemini</SelectItem>
                        <SelectItem value="openai">OpenAI</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">模型名称</Label>
                    <Select
                      value={config.integrationGenerator.modelName}
                      onValueChange={(value) => updateEngineConfig('integrationGenerator', 'modelName', value)}
                    >
                      <SelectTrigger className="bg-[#27272A] border-[#3F3F46]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {modelOptions[config.integrationGenerator.provider].map((model) => (
                          <SelectItem key={model} value={model}>{model}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">温度 ({config.integrationGenerator.temperature})</Label>
                    <Slider
                      value={[config.integrationGenerator.temperature]}
                      onValueChange={([value]) => updateEngineConfig('integrationGenerator', 'temperature', value)}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">最大Token数</Label>
                    <Input
                      type="number"
                      value={config.integrationGenerator.maxTokens}
                      onChange={(e) => updateEngineConfig('integrationGenerator', 'maxTokens', parseInt(e.target.value))}
                      className="bg-[#27272A] border-[#3F3F46]"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 工作流设置 */}
        <TabsContent value="workflow" className="flex-1 space-y-4">
          <Card className="bg-[#1F1F23] border-[#27272A]">
            <CardHeader className="pb-4">
              <CardTitle className="text-sm text-gray-300 flex items-center">
                <Cpu className="w-4 h-4 mr-2 text-orange-400" />
                工作流执行设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-gray-300">启用并行执行</Label>
                  <p className="text-xs text-gray-400">允许多个引擎并行处理以提高性能</p>
                </div>
                <Switch
                  checked={config.workflow.enableParallelExecution}
                  onCheckedChange={(checked) => updateWorkflowConfig('enableParallelExecution', checked)}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">最大并发引擎数</Label>
                  <Input
                    type="number"
                    value={config.workflow.maxConcurrentEngines}
                    onChange={(e) => updateWorkflowConfig('maxConcurrentEngines', parseInt(e.target.value))}
                    className="bg-[#27272A] border-[#3F3F46]"
                    min={1}
                    max={5}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">单引擎超时 (ms)</Label>
                  <Input
                    type="number"
                    value={config.workflow.engineTimeout}
                    onChange={(e) => updateWorkflowConfig('engineTimeout', parseInt(e.target.value))}
                    className="bg-[#27272A] border-[#3F3F46]"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm text-gray-300">总工作流超时 (ms)</Label>
                <Input
                  type="number"
                  value={config.workflow.totalTimeout}
                  onChange={(e) => updateWorkflowConfig('totalTimeout', parseInt(e.target.value))}
                  className="bg-[#27272A] border-[#3F3F46]"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 缓存策略 */}
        <TabsContent value="cache" className="flex-1 space-y-4">
          <Card className="bg-[#1F1F23] border-[#27272A]">
            <CardHeader className="pb-4">
              <CardTitle className="text-sm text-gray-300 flex items-center">
                <MemoryStick className="w-4 h-4 mr-2 text-cyan-400" />
                智能缓存配置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-gray-300">启用缓存</Label>
                  <p className="text-xs text-gray-400">启用智能缓存以提高重复查询性能</p>
                </div>
                <Switch
                  checked={config.cache.enabled}
                  onCheckedChange={(checked) => updateCacheConfig('enabled', checked)}
                />
              </div>
              
              {config.cache.enabled && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-300">缓存TTL (ms)</Label>
                      <Input
                        type="number"
                        value={config.cache.ttl}
                        onChange={(e) => updateCacheConfig('ttl', parseInt(e.target.value))}
                        className="bg-[#27272A] border-[#3F3F46]"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-300">最大缓存条目数</Label>
                      <Input
                        type="number"
                        value={config.cache.maxSize}
                        onChange={(e) => updateCacheConfig('maxSize', parseInt(e.target.value))}
                        className="bg-[#27272A] border-[#3F3F46]"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-300">缓存键策略</Label>
                    <Select
                      value={config.cache.keyStrategy}
                      onValueChange={(value) => updateCacheConfig('keyStrategy', value)}
                    >
                      <SelectTrigger className="bg-[#27272A] border-[#3F3F46]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="content_hash">内容哈希</SelectItem>
                        <SelectItem value="semantic_hash">语义哈希</SelectItem>
                        <SelectItem value="simple">简单策略</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 全局设置 */}
        <TabsContent value="global" className="flex-1 space-y-4">
          <Card className="bg-[#1F1F23] border-[#27272A]">
            <CardHeader className="pb-4">
              <CardTitle className="text-sm text-gray-300 flex items-center">
                <Timer className="w-4 h-4 mr-2 text-red-400" />
                全局系统设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm text-gray-300">启用降级机制</Label>
                  <p className="text-xs text-gray-400">在主要模型失败时自动切换到备用模型</p>
                </div>
                <Switch
                  checked={config.global.enableFallback}
                  onCheckedChange={(checked) => setConfig(prev => prev ? {...prev, global: {...prev.global, enableFallback: checked}} : null)}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">全局超时 (ms)</Label>
                  <Input
                    type="number"
                    value={config.global.timeout}
                    onChange={(e) => setConfig(prev => prev ? {...prev, global: {...prev.global, timeout: parseInt(e.target.value)}} : null)}
                    className="bg-[#27272A] border-[#3F3F46]"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm text-gray-300">重试次数</Label>
                  <Input
                    type="number"
                    value={config.global.retryAttempts}
                    onChange={(e) => setConfig(prev => prev ? {...prev, global: {...prev.global, retryAttempts: parseInt(e.target.value)}} : null)}
                    className="bg-[#27272A] border-[#3F3F46]"
                    min={0}
                    max={5}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
