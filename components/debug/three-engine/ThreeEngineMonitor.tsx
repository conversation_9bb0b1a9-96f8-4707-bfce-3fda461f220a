"use client"

import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Database, TrendingUp } from 'lucide-react';
import {
  ThreeEngineDataProvider,
  useThreeEngineDataContext
} from './ThreeEngineDataProvider';
import { ThreeEngineStatusPanel } from './ThreeEngineStatusPanel';

interface EngineStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'not_loaded';
  model: string;
  lastUpdate: string;
}

interface PerformanceMetrics {
  totalRequests: number;
  averageProcessingTime: number;
  successRate: number;
  cacheHitRate?: number;
  averageQualityScore?: number;
}

interface ThreeEngineStats {
  totalWorkflows: number;
  averageTotalTime: number;
  averageParallelEfficiency: number;
  averageQualityScore: number;
  cacheHitRate: number;
  successRate: number;
  engineMetrics: {
    navigator: PerformanceMetrics;
    contextRetriever: PerformanceMetrics;
    integrationGenerator: PerformanceMetrics;
  };
}

interface WorkflowResult {
  workflowId: string;
  totalTime: number;
  quality: {
    overall: number;
    coherence: number;
    relevance: number;
    empathy: number;
    insight: number;
  };
  performance: {
    engineTimes: {
      navigator: number;
      contextRetriever: number;
      integrationGenerator: number;
      coldStoreRetrieval: number;
    };
    parallelEfficiency: number;
    cacheUtilization: {
      used: boolean;
      hitRate: number;
      timeSaved: number;
    };
  };
  timestamp: string;
}

// 内部监控组件
function ThreeEngineMonitorContent() {
  const { clearData } = useThreeEngineDataContext();

  return (
    <div className="h-full flex flex-col bg-[#18181B] text-gray-100">
      <Tabs defaultValue="overview" className="flex-1 flex flex-col">
        <TabsList className="bg-[#27272A] border-b border-[#27272A] rounded-none w-full justify-start">
          <TabsTrigger value="overview" className="text-sm">系统概览</TabsTrigger>
          <TabsTrigger value="performance" className="text-sm">性能分析</TabsTrigger>
          <TabsTrigger value="workflow" className="text-sm">工作流监控</TabsTrigger>
        </TabsList>

        {/* 系统概览 */}
        <TabsContent value="overview" className="flex-1 p-4">
          <ThreeEngineStatusPanel />
        </TabsContent>

        {/* 性能分析 */}
        <TabsContent value="performance" className="flex-1">
          <div className="text-center text-gray-400 mt-8">
            <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>性能分析图表功能开发中...</p>
            <p className="text-sm mt-2">将包含时间序列图表、性能趋势分析等</p>
          </div>
        </TabsContent>

        {/* 工作流监控 */}
        <TabsContent value="workflow" className="flex-1">
          <div className="text-center text-gray-400 mt-8">
            <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>工作流监控功能开发中...</p>
            <p className="text-sm mt-2">将包含实时工作流状态、执行日志等</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// 主导出组件，包含数据提供者
export function ThreeEngineMonitor() {
  return (
    <ThreeEngineDataProvider>
      <ThreeEngineMonitorContent />
    </ThreeEngineDataProvider>
  );
}


