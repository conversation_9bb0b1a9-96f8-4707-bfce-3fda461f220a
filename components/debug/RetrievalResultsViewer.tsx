'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RetrievalResult } from '@/types/rag';

interface RetrievalResultsViewerProps {
  results: RetrievalResult[];
}

export function RetrievalResultsViewer({ results }: RetrievalResultsViewerProps) {
  if (!results || results.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400">
        <div className="text-center">
          <div className="text-lg mb-2">🔍</div>
          <div>没有找到匹配的结果</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {results.map((result, index) => (
        <Card key={index} className="bg-[#0D1117] border-[#30363D]">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm text-white">
                结果 #{index + 1}
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Badge
                  variant="outline"
                  className="text-blue-400 border-blue-400"
                >
                  记忆片段: {result.memories.length}
                </Badge>
                <Badge
                  variant="outline"
                  className={`${
                    result.metadata.averageRelevanceScore >= 0.8
                      ? 'text-green-400 border-green-400'
                      : result.metadata.averageRelevanceScore >= 0.6
                      ? 'text-yellow-400 border-yellow-400'
                      : 'text-red-400 border-red-400'
                  }`}
                >
                  平均相关性: {result.metadata.averageRelevanceScore.toFixed(3)}
                </Badge>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="space-y-2">
              {result.memories.map((memory, memoryIndex) => (
                <div key={memoryIndex} className="text-sm text-gray-300 leading-relaxed p-2 bg-[#161B22] rounded border border-[#30363D]">
                  {memory}
                </div>
              ))}
            </div>
            
            {result.metadata && Object.keys(result.metadata).length > 1 && (
              <div className="mt-3 pt-3 border-t border-[#30363D]">
                <div className="text-xs text-gray-400 mb-2">元数据:</div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {Object.entries(result.metadata).map(([key, value]) => {
                    if (key === 'source') return null; // 已在上方显示
                    return (
                      <div key={key} className="text-gray-400">
                        <span className="text-gray-500">{key}:</span> {String(value)}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
