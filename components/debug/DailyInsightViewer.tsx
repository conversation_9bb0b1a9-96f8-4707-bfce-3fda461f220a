"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface InsightEntry {
  date: string
  content: string
  timestamp: string
}

export function DailyInsightViewer() {
  const [insights, setInsights] = useState<InsightEntry[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState<string>("today")

  useEffect(() => {
    loadInsights()
  }, [selectedDate])

  const loadInsights = async () => {
    setIsLoading(true)
    try {
      // 加载今日洞察
      if (selectedDate === "today") {
        const response = await fetch(`/api/memory/local?path=${encodeURIComponent("每日洞察今天.md")}`)
        if (response.ok) {
          const data = await response.json()
          const content = data.content || ""
          setInsights([{
            date: "今天",
            content: content,
            timestamp: new Date().toLocaleString()
          }])
        }
      } else {
        // 加载历史洞察
        const response = await fetch(`/api/memory/local?path=${encodeURIComponent("每日洞察归档.md")}`)
        if (response.ok) {
          const data = await response.json()
          const content = data.content || ""
          // 简单解析归档内容
          const entries = content.split('\n\n').filter(Boolean).map((entry: string, index: number) => ({
            date: `归档 ${index + 1}`,
            content: entry,
            timestamp: new Date().toLocaleString()
          }))
          setInsights(entries)
        }
      }
    } catch (error) {
      console.error("加载洞察失败:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const refreshInsights = () => {
    loadInsights()
  }

  return (
    <Card className="h-full bg-[#161B22] border-[#30363D] flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-gray-200">每日洞察日志</CardTitle>
          <div className="flex items-center space-x-2">
            <select
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="bg-[#0D1117] text-gray-200 text-xs px-2 py-1 rounded border border-[#30363D] focus:outline-none focus:ring-2 focus:ring-[#2563EB]"
            >
              <option value="today">今日洞察</option>
              <option value="archive">历史归档</option>
            </select>
            <Button
              onClick={refreshInsights}
              size="sm"
              variant="outline"
              className="text-xs"
            >
              刷新
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          <div className="text-gray-400 text-center">加载中...</div>
        ) : insights.length === 0 ? (
          <div className="text-gray-400 text-center">暂无洞察记录</div>
        ) : (
          <div className="space-y-4">
            {insights.map((insight, index) => (
              <div key={index} className="border-l-2 border-[#2563EB] pl-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs text-gray-400">{insight.date}</span>
                  <span className="text-xs text-gray-500">{insight.timestamp}</span>
                </div>
                <div className="text-sm text-gray-200 whitespace-pre-wrap font-mono">
                  {insight.content}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
