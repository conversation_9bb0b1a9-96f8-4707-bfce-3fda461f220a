"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface PromptEditorProps {
  fileName: string
  filePath: string
}

export function PromptEditor({ fileName, filePath }: PromptEditorProps) {
  const [content, setContent] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 加载文件内容
  useEffect(() => {
    loadFileContent()
  }, [filePath])

  const loadFileContent = async () => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/memory/local?path=${encodeURIComponent(filePath)}`)
      if (response.ok) {
        const data = await response.json()
        setContent(data.content || "")
      } else {
        setError("文件加载失败")
      }
    } catch (err) {
      setError("网络错误")
      console.error("加载文件失败:", err)
    } finally {
      setIsLoading(false)
    }
  }

  const saveFileContent = async () => {
    setIsSaving(true)
    setError(null)
    try {
      const response = await fetch(`/api/memory/local`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          path: filePath,
          content: content
        })
      })

      if (response.ok) {
        setLastSaved(new Date())
      } else {
        setError("保存失败")
      }
    } catch (err) {
      setError("保存时发生网络错误")
      console.error("保存文件失败:", err)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <Card className="h-full bg-[#161B22] border-[#30363D]">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-gray-400">加载中...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="h-full bg-[#161B22] border-[#30363D] flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-gray-200">{fileName}</CardTitle>
          <div className="flex items-center space-x-2">
            {lastSaved && (
              <span className="text-xs text-green-400">
                已保存 {lastSaved.toLocaleTimeString()}
              </span>
            )}
            <Button
              onClick={saveFileContent}
              disabled={isSaving}
              size="sm"
              className="bg-[#2563EB] hover:bg-[#1D4ED8] text-white"
            >
              {isSaving ? "保存中..." : "保存"}
            </Button>
          </div>
        </div>
        {error && (
          <div className="text-red-400 text-xs">{error}</div>
        )}
      </CardHeader>
      <CardContent className="flex-1 p-0">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="w-full h-full resize-none bg-[#0D1117] text-[#E6EDF3] p-4 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-[#2563EB] focus:ring-opacity-50 border-none"
          placeholder="编辑提示词内容..."
        />
      </CardContent>
    </Card>
  )
}
