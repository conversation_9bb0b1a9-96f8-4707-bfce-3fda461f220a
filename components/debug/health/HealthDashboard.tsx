"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { 
  Heart, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Play,
  Settings,
  Zap,
  Activity,
  Clock,
  TrendingUp
} from 'lucide-react'

interface ComponentHealth {
  component: string
  status: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'DOWN'
  score: number
  checks: Array<{
    id: string
    name: string
    status: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'DOWN'
    lastCheck?: Date
    message?: string
  }>
}

interface SystemHealthReport {
  overallStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'DOWN'
  overallScore: number
  uptime: number
  alerts: Array<{
    level: 'info' | 'warning' | 'critical'
    component: string
    message: string
    timestamp: Date
  }>
  recommendations: string[]
}

interface RecoveryAction {
  id: string
  name: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export default function HealthDashboard() {
  const [healthReport, setHealthReport] = useState<SystemHealthReport | null>(null)
  const [componentHealth, setComponentHealth] = useState<ComponentHealth[]>([])
  const [recoveryActions, setRecoveryActions] = useState<RecoveryAction[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  useEffect(() => {
    fetchHealthData()
    const interval = setInterval(fetchHealthData, 30000) // 每30秒更新
    return () => clearInterval(interval)
  }, [])

  const fetchHealthData = async () => {
    try {
      setLoading(true)
      
      // 获取系统健康报告
      const reportResponse = await fetch('/api/debug/health?action=report')
      const reportData = await reportResponse.json()
      
      if (reportData.success) {
        setHealthReport(reportData.data)
      }

      // 获取组件健康检查
      const checksResponse = await fetch('/api/debug/health?action=checks')
      const checksData = await checksResponse.json()
      
      if (checksData.success) {
        setComponentHealth(checksData.data.components || [])
      }

      // 获取恢复操作
      const actionsResponse = await fetch('/api/debug/health?action=actions')
      const actionsData = await actionsResponse.json()
      
      if (actionsData.success) {
        setRecoveryActions(actionsData.data.recoveryActions || [])
      }

      // 获取运行状态
      const statusResponse = await fetch('/api/debug/health?action=status')
      const statusData = await statusResponse.json()
      
      if (statusData.success) {
        setIsRunning(statusData.data.isRunning)
      }

      setLastUpdate(new Date())
    } catch (error) {
      console.error('获取健康数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const startHealthChecking = async () => {
    try {
      const response = await fetch('/api/debug/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'start' })
      })
      
      const data = await response.json()
      if (data.success) {
        setIsRunning(true)
        fetchHealthData()
      }
    } catch (error) {
      console.error('启动健康检查失败:', error)
    }
  }

  const stopHealthChecking = async () => {
    try {
      const response = await fetch('/api/debug/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' })
      })
      
      const data = await response.json()
      if (data.success) {
        setIsRunning(false)
      }
    } catch (error) {
      console.error('停止健康检查失败:', error)
    }
  }

  const runHealthChecks = async () => {
    try {
      const response = await fetch('/api/debug/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'runChecks' })
      })
      
      const data = await response.json()
      if (data.success) {
        console.log('健康检查完成:', data.data)
        fetchHealthData()
      }
    } catch (error) {
      console.error('运行健康检查失败:', error)
    }
  }

  const executeRecoveryAction = async (actionId: string) => {
    try {
      const response = await fetch('/api/debug/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'executeRecovery', actionId })
      })
      
      const data = await response.json()
      if (data.success) {
        console.log('恢复操作完成:', data.data)
        fetchHealthData()
      }
    } catch (error) {
      console.error('执行恢复操作失败:', error)
    }
  }

  const performSystemDiagnostic = async () => {
    try {
      const response = await fetch('/api/debug/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'systemDiagnostic' })
      })
      
      const data = await response.json()
      if (data.success) {
        console.log('系统诊断完成:', data.data)
        fetchHealthData()
      }
    } catch (error) {
      console.error('系统诊断失败:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'WARNING':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'CRITICAL':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'DOWN':
        return <XCircle className="h-5 w-5 text-red-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return <Badge variant="default" className="bg-green-500">健康</Badge>
      case 'WARNING':
        return <Badge variant="secondary" className="bg-yellow-500">警告</Badge>
      case 'CRITICAL':
        return <Badge variant="destructive">严重</Badge>
      case 'DOWN':
        return <Badge variant="destructive" className="bg-red-600">宕机</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'low':
        return <Badge variant="outline">低</Badge>
      case 'medium':
        return <Badge variant="secondary">中</Badge>
      case 'high':
        return <Badge variant="destructive">高</Badge>
      case 'critical':
        return <Badge variant="destructive" className="bg-red-600">严重</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  if (loading && !healthReport) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-sm text-muted-foreground">加载健康数据...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 系统健康概览 */}
      {healthReport && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center space-x-2">
                <Heart className="h-6 w-6 text-red-500" />
                <span>系统健康状态</span>
              </CardTitle>
              <div className="flex items-center space-x-2">
                {getStatusBadge(healthReport.overallStatus)}
                <span className="text-sm text-muted-foreground">
                  {lastUpdate?.toLocaleTimeString()}
                </span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-500">
                  {healthReport.overallScore}
                </div>
                <div className="text-sm text-muted-foreground">健康分数</div>
                <Progress value={healthReport.overallScore} className="mt-2" />
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-500">
                  {Math.floor(healthReport.uptime / 3600)}h
                </div>
                <div className="text-sm text-muted-foreground">运行时间</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-500">
                  {healthReport.alerts.length}
                </div>
                <div className="text-sm text-muted-foreground">活跃警报</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-500">
                  {componentHealth.length}
                </div>
                <div className="text-sm text-muted-foreground">监控组件</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 控制面板 */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <div className={`h-3 w-3 rounded-full ${isRunning ? 'bg-green-500' : 'bg-gray-500'}`} />
          <span className="text-sm font-medium">
            健康检查: {isRunning ? '运行中' : '已停止'}
          </span>
        </div>
        
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={runHealthChecks}
          >
            <Play className="h-4 w-4 mr-2" />
            运行检查
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={performSystemDiagnostic}
          >
            <Activity className="h-4 w-4 mr-2" />
            系统诊断
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={fetchHealthData}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          
          {isRunning ? (
            <Button
              variant="destructive"
              size="sm"
              onClick={stopHealthChecking}
            >
              停止监控
            </Button>
          ) : (
            <Button
              size="sm"
              onClick={startHealthChecking}
            >
              启动监控
            </Button>
          )}
        </div>
      </div>

      {/* 主要内容 */}
      <Tabs defaultValue="components" className="space-y-4">
        <TabsList>
          <TabsTrigger value="components">组件状态</TabsTrigger>
          <TabsTrigger value="alerts">警报管理</TabsTrigger>
          <TabsTrigger value="recovery">自动恢复</TabsTrigger>
          <TabsTrigger value="recommendations">优化建议</TabsTrigger>
        </TabsList>

        <TabsContent value="components" className="space-y-4">
          <div className="grid gap-4">
            {componentHealth.map((component) => (
              <Card key={component.component}>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(component.status)}
                      <div>
                        <CardTitle className="text-lg">{component.component}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          {getStatusBadge(component.status)}
                          <span className="text-sm text-muted-foreground">
                            分数: {component.score}/100
                          </span>
                        </div>
                      </div>
                    </div>
                    <Progress value={component.score} className="w-24" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {component.checks.map((check) => (
                      <div key={check.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(check.status)}
                          <span className="text-sm font-medium">{check.name}</span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {check.lastCheck?.toLocaleTimeString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          {healthReport?.alerts && healthReport.alerts.length > 0 ? (
            <div className="space-y-3">
              {healthReport.alerts.map((alert, index) => (
                <Alert key={index} variant={alert.level === 'critical' ? 'destructive' : 'default'}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium">{alert.message}</div>
                        <div className="text-sm text-muted-foreground mt-1">
                          组件: {alert.component} • {alert.timestamp.toLocaleString()}
                        </div>
                      </div>
                      <Badge variant={alert.level === 'critical' ? 'destructive' : 'secondary'}>
                        {alert.level}
                      </Badge>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          ) : (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                当前没有活跃的健康警报。系统运行正常。
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="recovery" className="space-y-4">
          <div className="grid gap-4">
            {recoveryActions.map((action) => (
              <Card key={action.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Zap className="h-5 w-5 text-blue-500" />
                        <span className="font-medium">{action.name}</span>
                        {getSeverityBadge(action.severity)}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {action.description}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => executeRecoveryAction(action.id)}
                      className="ml-4"
                    >
                      执行
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-green-500" />
                <span>系统优化建议</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {healthReport?.recommendations && healthReport.recommendations.length > 0 ? (
                <div className="space-y-3">
                  {healthReport.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <div className="text-sm">{recommendation}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  当前没有特别的优化建议。系统运行良好。
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
