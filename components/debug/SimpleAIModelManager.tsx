"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Zap, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Settings,
  Eye,
  EyeOff
} from "lucide-react"

// 简化的 AI 模型状态接口
interface SimpleAIModelStatus {
  provider: 'gemini' | 'doubao'
  isConnected: boolean
  lastUsed: string
  hasApiKey: boolean
}

export function SimpleAIModelManager() {
  const [currentProvider, setCurrentProvider] = useState<'gemini' | 'doubao'>('gemini')
  const [modelStatus, setModelStatus] = useState<Record<string, SimpleAIModelStatus>>({
    gemini: {
      provider: 'gemini',
      isConnected: true,
      lastUsed: '2分钟前',
      hasApiKey: true
    },
    doubao: {
      provider: 'doubao',
      isConnected: false,
      lastUsed: '1小时前',
      hasApiKey: false
    }
  })
  const [isSwitching, setIsSwitching] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [showApiKeys, setShowApiKeys] = useState(false)
  const [apiKeys, setApiKeys] = useState({
    gemini: '',
    doubao: ''
  })

  // 切换 AI 模型提供商
  const switchProvider = async (provider: 'gemini' | 'doubao') => {
    if (!modelStatus[provider].hasApiKey) {
      setShowSettings(true)
      return
    }

    setIsSwitching(true)
    try {
      const response = await fetch('/api/debug/ai-model/switch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      })

      if (response.ok) {
        setCurrentProvider(provider)
        // 更新状态
        setModelStatus(prev => ({
          ...prev,
          [provider]: {
            ...prev[provider],
            isConnected: true,
            lastUsed: '刚刚'
          }
        }))
        console.log(`✅ 已切换到 ${provider}`)
      } else {
        throw new Error('切换失败')
      }
    } catch (error) {
      console.error('❌ 切换模型失败:', error)
    } finally {
      setIsSwitching(false)
    }
  }

  // 测试连接
  const testConnection = async (provider: 'gemini' | 'doubao') => {
    try {
      const response = await fetch('/api/debug/ai-model/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      })

      const result = await response.json()
      
      setModelStatus(prev => ({
        ...prev,
        [provider]: {
          ...prev[provider],
          isConnected: result.success
        }
      }))

      if (result.success) {
        console.log(`✅ ${provider} 连接测试成功`)
      } else {
        console.error(`❌ ${provider} 连接测试失败`)
      }
    } catch (error) {
      console.error(`❌ ${provider} 连接测试失败:`, error)
    }
  }

  // 保存 API 密钥
  const saveApiKey = async (provider: 'gemini' | 'doubao', apiKey: string) => {
    try {
      const response = await fetch('/api/debug/ai-model/config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          provider, 
          config: { apiKey } 
        })
      })

      if (response.ok) {
        setModelStatus(prev => ({
          ...prev,
          [provider]: {
            ...prev[provider],
            hasApiKey: !!apiKey
          }
        }))
        console.log(`✅ ${provider} API 密钥已保存`)
      }
    } catch (error) {
      console.error(`❌ 保存 ${provider} API 密钥失败:`, error)
    }
  }

  // 渲染模型状态卡片
  const renderModelCard = (provider: 'gemini' | 'doubao') => {
    const status = modelStatus[provider]
    const isActive = currentProvider === provider
    const displayName = provider === 'gemini' ? 'Gemini' : '豆包'

    return (
      <Card key={provider} className={`cursor-pointer transition-all ${isActive ? 'ring-2 ring-primary' : ''}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              {provider === 'gemini' ? '🤖' : '🚀'} {displayName}
              {isActive && <Badge>当前使用</Badge>}
            </CardTitle>
            <div className="flex items-center gap-1">
              {status.isConnected ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm text-muted-foreground">
                {status.isConnected ? '已连接' : '未连接'}
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm text-muted-foreground">
            最后使用: {status.lastUsed}
          </div>
          
          {!status.hasApiKey && (
            <Alert>
              <AlertDescription>
                需要配置 API 密钥才能使用此模型
              </AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button
              onClick={() => switchProvider(provider)}
              disabled={isSwitching || isActive}
              className="flex-1"
            >
              {isSwitching ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Zap className="h-4 w-4 mr-2" />
              )}
              {isActive ? '当前使用' : '切换使用'}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => testConnection(provider)}
              disabled={!status.hasApiKey}
            >
              测试
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">AI 模型切换</h2>
        <Button
          variant="outline"
          onClick={() => setShowSettings(!showSettings)}
        >
          <Settings className="h-4 w-4 mr-2" />
          设置
        </Button>
      </div>

      {/* 当前状态 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-muted-foreground">当前使用模型</div>
              <div className="text-xl font-semibold">
                {currentProvider === 'gemini' ? '🤖 Gemini' : '🚀 豆包'}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {modelStatus[currentProvider].isConnected ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span className="text-sm">
                {modelStatus[currentProvider].isConnected ? '运行正常' : '连接异常'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 模型选择 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {renderModelCard('gemini')}
        {renderModelCard('doubao')}
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <Card>
          <CardHeader>
            <CardTitle>API 密钥设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Gemini API 密钥 */}
            <div className="space-y-2">
              <Label htmlFor="gemini-key">Gemini API 密钥</Label>
              <div className="flex gap-2">
                <Input
                  id="gemini-key"
                  type={showApiKeys ? "text" : "password"}
                  value={apiKeys.gemini}
                  onChange={(e) => setApiKeys(prev => ({ ...prev, gemini: e.target.value }))}
                  placeholder="输入 Gemini API 密钥"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowApiKeys(!showApiKeys)}
                >
                  {showApiKeys ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button
                  onClick={() => saveApiKey('gemini', apiKeys.gemini)}
                  disabled={!apiKeys.gemini}
                >
                  保存
                </Button>
              </div>
            </div>

            {/* 豆包 API 密钥 */}
            <div className="space-y-2">
              <Label htmlFor="doubao-key">豆包 API 密钥</Label>
              <div className="flex gap-2">
                <Input
                  id="doubao-key"
                  type={showApiKeys ? "text" : "password"}
                  value={apiKeys.doubao}
                  onChange={(e) => setApiKeys(prev => ({ ...prev, doubao: e.target.value }))}
                  placeholder="输入豆包 API 密钥"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowApiKeys(!showApiKeys)}
                >
                  {showApiKeys ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button
                  onClick={() => saveApiKey('doubao', apiKeys.doubao)}
                  disabled={!apiKeys.doubao}
                >
                  保存
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
