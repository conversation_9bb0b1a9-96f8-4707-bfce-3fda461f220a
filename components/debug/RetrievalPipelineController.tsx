'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RetrievalConfig, DynamicWeightRule, FilterRule } from '@/types/rag';
import { ragConfigManager } from '@/lib/services/rag-config-manager';
import { PromptEditor } from './PromptEditor';

interface RetrievalPipelineControllerProps {
  config: RetrievalConfig;
  onConfigChange: (newConfig: Partial<RetrievalConfig>) => void;
}

export function RetrievalPipelineController({
  config,
  onConfigChange
}: RetrievalPipelineControllerProps) {
  const [validationResult, setValidationResult] = useState<any>(null);
  const [showStrategyPreview, setShowStrategyPreview] = useState(false);
  const [strategyPreviewData, setStrategyPreviewData] = useState<any>(null);
  const [performanceAnalysis, setPerformanceAnalysis] = useState<any>(null);
  const [selectedRuleForEdit, setSelectedRuleForEdit] = useState<string | null>(null);
  const [retrievalPreview, setRetrievalPreview] = useState<any>(null);

  // 配置验证
  const validateConfig = useCallback(() => {
    const result = ragConfigManager.validateRetrievalConfig(config);
    setValidationResult(result);
    return result;
  }, [config]);

  // 处理配置变更
  const handleConfigChange = useCallback((field: keyof RetrievalConfig, value: any) => {
    onConfigChange({ [field]: value });
  }, [onConfigChange]);

  return (
    <div className="h-full flex flex-col bg-[#161B22] rounded-xl border border-[#30363D] shadow-sm">
      <div className="pb-3 px-6 pt-6">
        <div className="text-lg text-white flex items-center justify-between font-semibold">
          检索流水线控制器
          <Badge variant="outline" className="text-green-400 border-green-400">
            {config.retrievalMode}
          </Badge>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto space-y-4 px-6 pb-6">
        <Tabs defaultValue="retrieval" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-[#21262D]">
            <TabsTrigger value="retrieval" className="text-gray-300">检索配置</TabsTrigger>
            <TabsTrigger value="weighting" className="text-gray-300">动态权重</TabsTrigger>
            <TabsTrigger value="filtering" className="text-gray-300">过滤规则</TabsTrigger>
          </TabsList>

          {/* 检索模式配置 */}
          <TabsContent value="retrieval" className="space-y-4">
            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">检索模式配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-gray-300">检索模式</Label>
                  <Select
                    value={config.retrievalMode}
                    onValueChange={(value: RetrievalConfig['retrievalMode']) => 
                      handleConfigChange('retrievalMode', value)
                    }
                  >
                    <SelectTrigger className="bg-[#21262D] border-[#30363D] text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-[#21262D] border-[#30363D]">
                      <SelectItem value="vector_only" className="text-white">仅向量检索</SelectItem>
                      <SelectItem value="keyword_only" className="text-white">仅关键词检索</SelectItem>
                      <SelectItem value="hybrid_weighted" className="text-white">混合权重检索</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-300">语义匹配算法</Label>
                  <Select
                    value={config.semanticAlgorithm}
                    onValueChange={(value: RetrievalConfig['semanticAlgorithm']) => 
                      handleConfigChange('semanticAlgorithm', value)
                    }
                  >
                    <SelectTrigger className="bg-[#21262D] border-[#30363D] text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-[#21262D] border-[#30363D]">
                      <SelectItem value="cosine" className="text-white">余弦相似度</SelectItem>
                      <SelectItem value="euclidean" className="text-white">欧氏距离</SelectItem>
                      <SelectItem value="dot_product" className="text-white">点积算法</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-300">
                    相似度阈值: {config.similarityThreshold.toFixed(2)}
                  </Label>
                  <Slider
                    value={[config.similarityThreshold]}
                    onValueChange={([value]) => handleConfigChange('similarityThreshold', value)}
                    max={1}
                    min={0}
                    step={0.05}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>0.0</span>
                    <span>0.5</span>
                    <span>1.0</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-gray-300">候选项数量</Label>
                    <Input
                      type="number"
                      min={1}
                      max={1000}
                      value={config.topK}
                      onChange={(e) => handleConfigChange('topK', parseInt(e.target.value))}
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-gray-300">最终返回数量</Label>
                    <Input
                      type="number"
                      min={1}
                      max={50}
                      value={config.finalCount}
                      onChange={(e) => handleConfigChange('finalCount', parseInt(e.target.value))}
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-gray-300">启用重排序</Label>
                    <Switch
                      checked={config.enableReranking}
                      onCheckedChange={(checked) => handleConfigChange('enableReranking', checked)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-300">
                    多样性因子: {config.diversityFactor.toFixed(2)}
                  </Label>
                  <Slider
                    value={[config.diversityFactor]}
                    onValueChange={([value]) => handleConfigChange('diversityFactor', value)}
                    max={1}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 动态权重规则 */}
          <TabsContent value="weighting" className="space-y-4">
            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">权重规则概览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center text-gray-400 py-8">
                  <div className="text-lg mb-2">⚖️</div>
                  <div>权重规则功能开发中...</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 过滤规则配置 */}
          <TabsContent value="filtering" className="space-y-4">
            <Card className="bg-[#0D1117] border-[#30363D]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-gray-300">智能过滤规则</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center text-gray-400 py-8">
                  <div className="text-lg mb-2">🔍</div>
                  <div>过滤规则功能开发中...</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
