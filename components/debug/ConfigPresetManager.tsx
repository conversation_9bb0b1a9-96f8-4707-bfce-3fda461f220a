'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ConfigPreset } from '@/types/rag';

interface ConfigPresetManagerProps {
  presets: ConfigPreset[];
  selectedPreset: string | null;
  onLoadPreset: (presetId: string) => void;
  onSaveAsPreset: (name: string, description: string, category: ConfigPreset['category']) => void;
  onRefreshPresets: () => void;
}

export function ConfigPresetManager({
  presets,
  selectedPreset,
  onLoadPreset,
  onSaveAsPreset,
  onRefreshPresets
}: ConfigPresetManagerProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newPresetName, setNewPresetName] = useState('');
  const [newPresetDescription, setNewPresetDescription] = useState('');
  const [newPresetCategory, setNewPresetCategory] = useState<ConfigPreset['category']>('custom');

  const handleSavePreset = () => {
    if (!newPresetName.trim()) {
      alert('请输入预设名称');
      return;
    }

    onSaveAsPreset(newPresetName, newPresetDescription, newPresetCategory);
    
    // 重置表单
    setNewPresetName('');
    setNewPresetDescription('');
    setNewPresetCategory('custom');
    setIsCreateDialogOpen(false);
  };

  // 按类别分组预设
  const presetsByCategory = presets.reduce((acc, preset) => {
    if (!acc[preset.category]) {
      acc[preset.category] = [];
    }
    acc[preset.category].push(preset);
    return acc;
  }, {} as Record<ConfigPreset['category'], ConfigPreset[]>);

  const categoryNames: Record<ConfigPreset['category'], string> = {
    default: '默认配置',
    emotional: '情感聚焦',
    technical: '技术分析',
    creative: '创意模式',
    custom: '自定义'
  };

  const categoryColors: Record<ConfigPreset['category'], string> = {
    default: 'text-blue-400 border-blue-400',
    emotional: 'text-pink-400 border-pink-400',
    technical: 'text-green-400 border-green-400',
    creative: 'text-purple-400 border-purple-400',
    custom: 'text-gray-400 border-gray-400'
  };

  return (
    <Card className="bg-[#161B22] border-[#30363D]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-white">配置预设管理</CardTitle>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefreshPresets}
              className="text-gray-300 border-gray-600 hover:bg-gray-700"
            >
              刷新
            </Button>
            
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-gray-300 border-gray-600 hover:bg-gray-700"
                >
                  保存预设
                </Button>
              </DialogTrigger>
              
              <DialogContent className="bg-[#161B22] border-[#30363D] text-white">
                <DialogHeader>
                  <DialogTitle>保存配置预设</DialogTitle>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-gray-300">预设名称</Label>
                    <Input
                      value={newPresetName}
                      onChange={(e) => setNewPresetName(e.target.value)}
                      placeholder="输入预设名称..."
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-gray-300">描述</Label>
                    <Textarea
                      value={newPresetDescription}
                      onChange={(e) => setNewPresetDescription(e.target.value)}
                      placeholder="输入预设描述..."
                      rows={3}
                      className="bg-[#21262D] border-[#30363D] text-white"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-gray-300">类别</Label>
                    <Select
                      value={newPresetCategory}
                      onValueChange={(value: ConfigPreset['category']) => setNewPresetCategory(value)}
                    >
                      <SelectTrigger className="bg-[#21262D] border-[#30363D] text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[#21262D] border-[#30363D]">
                        <SelectItem value="custom" className="text-white">自定义</SelectItem>
                        <SelectItem value="emotional" className="text-white">情感聚焦</SelectItem>
                        <SelectItem value="technical" className="text-white">技术分析</SelectItem>
                        <SelectItem value="creative" className="text-white">创意模式</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsCreateDialogOpen(false)}
                      className="text-gray-300 border-gray-600 hover:bg-gray-700"
                    >
                      取消
                    </Button>
                    <Button
                      onClick={handleSavePreset}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      保存
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {Object.entries(presetsByCategory).map(([category, categoryPresets]) => (
          <div key={category} className="space-y-2">
            <div className="text-xs text-gray-400 font-medium">
              {categoryNames[category as ConfigPreset['category']]}
            </div>
            
            <div className="space-y-2">
              {categoryPresets.map((preset) => (
                <Card 
                  key={preset.id} 
                  className={`bg-[#0D1117] border-[#30363D] cursor-pointer transition-colors ${
                    selectedPreset === preset.id ? 'ring-1 ring-blue-500' : 'hover:bg-[#161B22]'
                  }`}
                  onClick={() => onLoadPreset(preset.id)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-sm font-medium text-white">
                        {preset.name}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${categoryColors[preset.category]}`}
                        >
                          {categoryNames[preset.category]}
                        </Badge>
                        {selectedPreset === preset.id && (
                          <Badge variant="outline" className="text-blue-400 border-blue-400 text-xs">
                            当前
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {preset.description && (
                      <div className="text-xs text-gray-400 mb-2">
                        {preset.description}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>使用次数: {preset.usageCount}</span>
                      <span>版本: {preset.version}</span>
                    </div>
                    
                    {preset.benchmarkResults && (
                      <div className="mt-2 pt-2 border-t border-[#30363D]">
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div className="text-center">
                            <div className="text-white">{preset.benchmarkResults.averageRetrievalTime}ms</div>
                            <div className="text-gray-500">平均耗时</div>
                          </div>
                          <div className="text-center">
                            <div className="text-white">{preset.benchmarkResults.averageRelevance.toFixed(2)}</div>
                            <div className="text-gray-500">平均相关性</div>
                          </div>
                          <div className="text-center">
                            <div className="text-white">{preset.benchmarkResults.testCases}</div>
                            <div className="text-gray-500">测试用例</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
        
        {presets.length === 0 && (
          <div className="text-center text-gray-400 py-8">
            <div className="text-lg mb-2">📋</div>
            <div>暂无配置预设</div>
            <div className="text-xs mt-1">点击"保存预设"创建第一个配置预设</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
