'use client';

import { useState } from 'react';
import { PlusIcon, ArrowUpTrayIcon } from '@heroicons/react/24/solid';
import { ModelSelector } from './ModelSelector';

// Mock data for models
const MODELS = [
  { id: 'gpt-4o-mini', name: 'GPT 4o-mini' },
  { id: 'gpt-4o', name: 'GPT 4o' },
  { id: 'claude-3', name: '<PERSON> 3' },
];

export function AssistantLayout({ children }: { children: React.ReactNode }) {
  const [selectedModel, setSelectedModel] = useState(MODELS[0]);

  return (
    <div className="flex h-screen bg-background-custom text-primary-text-custom">
      {/* Sidebar */}
      <div className="w-64 border-r border-border-custom p-4 flex flex-col">
        <div className="flex items-center mb-6">
          <span className="text-xl font-semibold">assistant-ui</span>
        </div>
        <button className="flex items-center justify-center w-full bg-accent-custom hover:opacity-90 text-white font-bold py-2 px-4 rounded-lg mb-4">
          <PlusIcon className="h-5 w-5 mr-2" />
          New Thread
        </button>
        {/* Thread history can be added here */}
        <div className="flex-1"></div>
        {/* Export button at bottom */}
        <button className="flex items-center justify-center w-full border border-border-custom hover:bg-content-background-custom text-primary-text-custom py-2 px-4 rounded-lg mt-4">
          <ArrowUpTrayIcon className="h-5 w-5 mr-2" />
          Export
        </button>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header with model selector */}
        <div className="border-b border-border-custom p-4">
          <div className="max-w-xs mx-auto">
            <ModelSelector 
              models={MODELS} 
              selectedModel={selectedModel} 
              onSelectModel={setSelectedModel} 
            />
          </div>
        </div>
        
        {/* Chat content */}
        <div className="flex-1 overflow-hidden">
          {children}
        </div>
      </div>
    </div>
  );
}