'use client';

import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/solid';

interface Model {
  id: string;
  name: string;
}

interface ModelSelectorProps {
  models: Model[];
  selectedModel: Model;
  onSelectModel: (model: Model) => void;
}

export function ModelSelector({ 
  models, 
  selectedModel, 
  onSelectModel 
}: ModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full bg-content-background-custom border border-border-custom rounded-lg px-4 py-2 text-primary-text-custom"
      >
        <div className="flex items-center">
          <span className="mr-2">🤖</span>
          <span>{selectedModel.name}</span>
        </div>
        {isOpen ? (
          <ChevronUpIcon className="h-5 w-5 text-secondary-text-custom" />
        ) : (
          <ChevronDownIcon className="h-5 w-5 text-secondary-text-custom" />
        )}
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-content-background-custom border border-border-custom rounded-lg shadow-lg">
          <ul>
            {models.map((model) => (
              <li key={model.id}>
                <button
                  onClick={() => {
                    onSelectModel(model);
                    setIsOpen(false);
                  }}
                  className={`flex items-center w-full px-4 py-2 text-left hover:bg-background-custom ${selectedModel.id === model.id ? 'bg-background-custom' : ''}`}
                >
                  <span className="mr-2">🤖</span>
                  <span>{model.name}</span>
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}