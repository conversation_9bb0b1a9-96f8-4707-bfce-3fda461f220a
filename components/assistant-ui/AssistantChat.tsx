'use client';

import { useState, useRef, useEffect } from 'react';
import { useChat } from 'ai/react';
import { Message } from '@/types/conversation';
import { MessageBubble } from '@/components/chat/MessageBubble';
import { ChatInput } from './ChatInput';

export function AssistantChat() {
  const { messages, input, handleInputChange, handleSubmit, isLoading, error } = useChat({
    api: '/api/chat',
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = (message: string) => {
    const syntheticEvent = {
      preventDefault: () => {},
    } as React.FormEvent;
    
    // Manually set the input value and then submit
    handleInputChange({ target: { value: message } } as React.ChangeEvent<HTMLTextAreaElement>);
    setTimeout(() => handleSubmit(syntheticEvent), 0);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <h1 className="text-2xl text-secondary-text-custom mb-4">How can I help you today?</h1>
            {/* Suggestion cards */}
            <div className="flex space-x-4 mb-8">
              <button 
                onClick={() => handleSendMessage("What is the weather in Tokyo?")}
                className="bg-content-background-custom p-4 rounded-lg border border-border-custom w-64 text-left hover:bg-background-custom transition-colors"
              >
                <p>What is the weather in Tokyo?</p>
              </button>
              <button 
                onClick={() => handleSendMessage("What is assistant-ui?")}
                className="bg-content-background-custom p-4 rounded-lg border border-border-custom w-64 text-left hover:bg-background-custom transition-colors"
              >
                <p>What is assistant-ui?</p>
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((m) => (
              <MessageBubble
                key={m.id}
                message={{
                  id: m.id,
                  role: m.role,
                  content: m.content,
                  timestamp: new Date().toISOString() // UIMessage doesn't have timestamp, so we add current time
                } as Message}
              />
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Input area */}
      <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />
      {error && <p className="text-red-500 px-4 py-2 text-sm">Error: {error.message}</p>}
    </div>
  );
}