'use client';

import { Message } from '@/types/conversation';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface MessageBubbleProps {
  message: Message;
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user';

  // Base classes for all bubbles
  const bubbleClasses = 'max-w-[85%] md:max-w-[75%] px-4 py-3 rounded-lg shadow-sm';

  // Role-specific classes
  const userBubbleClasses = 'bg-accent-custom text-primary-text-custom';
  const aiBubbleClasses = 'bg-content-background-custom text-primary-text-custom';

  const parseMessageContent = (content: string) => {
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        parts.push({ type: 'text', content: content.slice(lastIndex, match.index) });
      }
      parts.push({ type: 'code', language: match[1] || 'text', content: match[2] });
      lastIndex = match.index + match[0].length;
    }

    if (lastIndex < content.length) {
      parts.push({ type: 'text', content: content.slice(lastIndex) });
    }

    return parts.length > 0 ? parts : [{ type: 'text', content }];
  };

  const contentParts = parseMessageContent(message.content);

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`${bubbleClasses} ${isUser ? userBubbleClasses : aiBubbleClasses}`}>
        <div className="space-y-2">
          {contentParts.map((part, index) => (
            <div key={index}>
              {part.type === 'text' ? (
                <div className="whitespace-pre-wrap leading-relaxed font-sans">
                  {part.content}
                </div>
              ) : (
                <div className="my-2 font-mono text-sm">
                  <SyntaxHighlighter
                    language={part.language}
                    style={vscDarkPlus} // A good dark theme for code
                    customStyle={{
                      margin: 0,
                      padding: '1rem',
                      borderRadius: '0.5rem',
                      backgroundColor: 'var(--background-custom)', // Use the deepest background for contrast
                    }}
                    wrapLongLines={true}
                    codeTagProps={{ style: { fontFamily: 'var(--font-mono)' } }}
                  >
                    {part.content.trim()}
                  </SyntaxHighlighter>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
