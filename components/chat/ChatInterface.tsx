"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Send } from "lucide-react"
import { Button } from "@/components/ui/button"


interface Message {
  id: string
  content: string
  sender: "user" | "ai"
  timestamp: Date
}

export default function SelfMirrorChat() {
  // RAG调试功能已移除
  const debugParams = null
  const isDebugMode = false

  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: "你好，我是小镜。我在这里陪伴你，倾听你的想法和感受。无论你想分享什么，我都会用心聆听。",
      sender: "ai",
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = "auto"
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px"
    }
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [inputValue])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setIsLoading(true)

    try {
      // 构建消息历史，转换为API期望的格式
      const apiMessages = [...messages, userMessage].map(msg => ({
        role: msg.sender === "user" ? "user" : "assistant",
        content: msg.content
      }))

      // 调用真实的AI API
      console.log('🚀 发送API请求到 /api/chat')
      console.log('📝 请求数据:', { messages: apiMessages, isInInsightMode: false, ragParams: debugParams })
      console.log('🔧 RAG调试模式:', isDebugMode, debugParams)

      // 使用绝对URL确保不通过代理
      const apiUrl = `${window.location.origin}/api/chat`
      console.log('🌐 API URL:', apiUrl)

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: apiMessages,
          isInInsightMode: false,
          ragParams: isDebugMode ? debugParams : undefined
        }),
      })

      console.log('📡 API响应状态:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API错误响应:', errorText)
        throw new Error(`API请求失败: ${response.status} - ${errorText}`)
      }

      // 实现真正的流式响应处理
      console.log('📡 开始处理流式响应')

      // 创建AI消息占位符
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "",
        sender: "ai",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, aiMessage])
      console.log('💬 创建AI消息占位符:', aiMessage.id)

      try {
        // 获取流式读取器
        const reader = response.body?.getReader()
        const decoder = new TextDecoder()
        let aiResponseContent = ""
        let buffer = ""

        if (!reader) {
          throw new Error('无法获取响应流')
        }

        console.log('📖 开始实时读取流式数据')

        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            console.log('✅ 流式数据读取完成')
            break
          }

          // 解码数据块
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          // 按行分割处理
          const lines = buffer.split('\n')
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || ""

          // 处理完整的行
          for (const line of lines) {
            if (line.trim() === '') continue

            console.log('📝 处理流式行:', line.substring(0, 100))

            // SSE格式：data: {"type":"text","content":"文本内容"}
            if (line.startsWith('data: ')) {
              try {
                const jsonData = line.slice(6) // 移除 "data: " 前缀
                const parsed = JSON.parse(jsonData)
                console.log('✅ 实时解析到数据:', parsed)

                if (parsed.type === 'text' && parsed.content) {
                  // 立即更新AI消息内容
                  aiResponseContent += parsed.content
                  setMessages((prev) =>
                    prev.map(msg =>
                      msg.id === aiMessage.id
                        ? { ...msg, content: aiResponseContent }
                        : msg
                    )
                  )
                } else if (parsed.type === 'end') {
                  console.log('✅ 流式响应结束')
                } else if (parsed.type === 'error') {
                  console.error('❌ 服务器错误:', parsed.error)
                  throw new Error(parsed.error)
                }
              } catch (parseError) {
                console.warn('⚠️ 流式解析失败:', parseError, 'Line:', line)
              }
            }
          }
        }

        // 处理缓冲区中剩余的数据
        if (buffer.trim()) {
          console.log('📝 处理缓冲区剩余数据:', buffer)
          if (buffer.startsWith('data: ')) {
            try {
              const jsonData = buffer.slice(6)
              const parsed = JSON.parse(jsonData)
              if (parsed.type === 'text' && parsed.content) {
                aiResponseContent += parsed.content
                setMessages((prev) =>
                  prev.map(msg =>
                    msg.id === aiMessage.id
                      ? { ...msg, content: aiResponseContent }
                      : msg
                  )
                )
              }
            } catch (parseError) {
              console.warn('⚠️ 缓冲区解析失败:', parseError)
            }
          }
        }

        console.log('✅ 流式响应处理完成，总长度:', aiResponseContent.length)

        // 如果没有解析到任何内容，显示错误消息
        if (!aiResponseContent) {
          setMessages((prev) =>
            prev.map(msg =>
              msg.id === aiMessage.id
                ? { ...msg, content: "抱歉，我现在无法正确处理你的消息。请稍后再试。" }
                : msg
            )
          )
        }

      } catch (parseError) {
        console.error('❌ 流式响应处理失败:', parseError)
        setMessages((prev) =>
          prev.map(msg =>
            msg.id === aiMessage.id
              ? { ...msg, content: "抱歉，响应处理出现问题。请稍后再试。" }
              : msg
          )
        )
      }

      setIsLoading(false)

    } catch (error) {
      console.error('发送消息失败:', error)

      // 显示错误消息
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "抱歉，我现在无法回应。请检查网络连接或稍后再试。",
        sender: "ai",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  return (
    <div className="min-h-screen bg-[#18181B] flex flex-col">
      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto px-4 py-8">
        <div className="max-w-2xl mx-auto space-y-6">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}>
              <div
                className={`max-w-[80%] px-4 py-3 rounded-lg ${
                  message.sender === "user" ? "bg-[#2563EB] text-white" : "bg-[#27272A] text-gray-100"
                }`}
              >
                <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-[#27272A] text-gray-100 px-4 py-3 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div
                    className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.1s" }}
                  ></div>
                  <div
                    className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.2s" }}
                  ></div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-[#27272A] bg-[#18181B]">
        <div className="max-w-2xl mx-auto px-4 py-4">
          <form onSubmit={handleSubmit} className="relative">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="告诉我你的感受..."
              className="w-full bg-[#27272A] text-gray-100 placeholder-gray-500 rounded-lg px-4 py-3 pr-12 resize-none border-none outline-none focus:ring-2 focus:ring-[#2563EB] focus:ring-opacity-50 transition-all duration-200"
              rows={1}
              style={{ minHeight: "48px", maxHeight: "120px" }}
            />
            <Button
              type="submit"
              size="icon"
              disabled={!inputValue.trim() || isLoading}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 bg-[#2563EB] hover:bg-[#1D4ED8] disabled:bg-gray-600 disabled:opacity-50"
            >
              <Send className="h-4 w-4" />
              <span className="sr-only">发送消息</span>
            </Button>
          </form>

          {/* Footer Hint */}
          <div className="mt-3 text-center">
            <p className="text-xs text-gray-600">按 Enter 发送，Shift + Enter 换行 • 所有对话都只存储在本地</p>
          </div>
        </div>
      </div>
    </div>
  )
}