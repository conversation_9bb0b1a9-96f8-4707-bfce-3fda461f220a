/**
 * 测试向量数据库的全局ID集成功能
 */

const { dualVectorSystem, globalIdManager } = require('./lib/services/vector-database');

async function testVectorDatabaseGlobalIdIntegration() {
  console.log('🧪 开始测试向量数据库的全局ID集成...');
  
  try {
    // 1. 初始化系统
    console.log('📋 初始化向量数据库和全局ID管理器...');
    await globalIdManager.initialize();
    await dualVectorSystem.initialize();
    
    // 2. 测试单个向量添加（Hot Store）
    console.log('\n🔥 测试Hot Store向量添加...');
    
    const testVector1 = Array.from({length: 384}, () => Math.random());
    const hotMetadata = {
      chunkId: 'test-hot-chunk-1',
      sourceDocumentType: 'daily_insight_hot',
      sourceDocumentId: '20250703-T001',
      parentChunk: '这是一个测试的父块内容',
      childChunk: '这是对应的意义子块内容',
      createdAt: new Date().toISOString()
    };
    
    const hotChunkId = await dualVectorSystem.addVector(testVector1, hotMetadata);
    console.log(`✅ Hot Store添加成功: ${hotChunkId}`);
    
    // 3. 测试单个向量添加（Cold Store）
    console.log('\n❄️ 测试Cold Store向量添加...');
    
    const testVector2 = Array.from({length: 384}, () => Math.random());
    const coldMetadata = {
      chunkId: 'test-cold-chunk-1',
      sourceDocumentType: 'user_profile',
      sourceDocumentId: '20250703-T002',
      parentChunk: '这是用户画像的父块内容',
      childChunk: '这是用户画像的意义子块内容',
      createdAt: new Date().toISOString()
    };
    
    const coldChunkId = await dualVectorSystem.addVector(testVector2, coldMetadata);
    console.log(`✅ Cold Store添加成功: ${coldChunkId}`);
    
    // 4. 测试批量向量添加
    console.log('\n📦 测试批量向量添加...');
    
    const batchVectors = [];
    for (let i = 0; i < 3; i++) {
      batchVectors.push({
        vector: Array.from({length: 384}, () => Math.random()),
        metadata: {
          chunkId: `test-batch-chunk-${i + 1}`,
          sourceDocumentType: 'dialogue_history',
          sourceDocumentId: `20250703-T00${i + 3}`,
          parentChunk: `批量测试父块 ${i + 1}`,
          childChunk: `批量测试意义子块 ${i + 1}`,
          createdAt: new Date().toISOString()
        }
      });
    }
    
    const batchChunkIds = await dualVectorSystem.addBatch(batchVectors);
    console.log(`✅ 批量添加成功: ${batchChunkIds.join(', ')}`);
    
    // 5. 验证全局ID生成
    console.log('\n🔍 验证全局ID生成...');
    
    // 获取今日统计
    const todayStats = globalIdManager.getTodayStats();
    console.log('📊 今日全局ID统计:', {
      date: todayStats?.date,
      currentTurn: todayStats?.currentTurn,
      totalInputs: todayStats?.totalInputs,
      totalDerivations: todayStats?.totalDerivations
    });
    
    // 6. 测试向量搜索
    console.log('\n🔍 测试向量搜索...');
    
    const queryVector = Array.from({length: 384}, () => Math.random());
    const searchResults = await dualVectorSystem.searchVector(queryVector, {
      maxResults: 3,
      searchStrategy: 'parallel'
    });
    
    console.log(`✅ 搜索完成，找到 ${searchResults.mergedResults.length} 个结果`);

    // 验证搜索结果中的全局ID
    searchResults.mergedResults.forEach((result, index) => {
      console.log(`  结果 ${index + 1}:`);
      console.log(`    ChunkId: ${result.metadata.chunkId}`);
      console.log(`    GlobalId: ${result.metadata.globalId}`);
      console.log(`    相似度: ${result.similarity.toFixed(4)}`);
      console.log(`    来源: ${result.metadata.sourceDocumentType}`);
    });
    
    // 7. 测试向量数据库统计
    console.log('\n📈 向量数据库统计信息:');
    const stats = await dualVectorSystem.getStats();
    console.log('  Hot Store:', {
      totalVectors: stats.hotStore.totalVectors,
      averageTemperature: stats.hotStore.averageTemperature?.toFixed(2)
    });
    console.log('  Cold Store:', {
      totalVectors: stats.coldStore.totalVectors,
      indexNodes: stats.coldStore.indexNodes
    });
    console.log('  搜索统计:', {
      totalSearches: stats.searchStats.totalSearches,
      averageSearchTime: stats.searchStats.averageSearchTime?.toFixed(2) + 'ms'
    });
    
    // 8. 验证ID血缘关系
    console.log('\n🔗 验证ID血缘关系...');
    
    // 随机选择一个结果验证血缘关系
    if (searchResults.mergedResults.length > 0) {
      const firstResult = searchResults.mergedResults[0];
      const globalId = firstResult.metadata.globalId;
      
      if (globalId) {
        const metadata = globalIdManager.getIdMetadata(globalId);
        const sourceChain = globalIdManager.getSourceChain(globalId);
        
        console.log(`  ID: ${globalId}`);
        console.log(`  类型: ${metadata?.type}`);
        console.log(`  父ID: ${metadata?.parentId}`);
        console.log(`  来源链: ${sourceChain.join(' -> ')}`);
        console.log(`  内容长度: ${metadata?.contentLength}`);
        console.log(`  创建时间: ${metadata?.createdAt}`);
      }
    }
    
    console.log('\n🎉 向量数据库全局ID集成测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testVectorDatabaseGlobalIdIntegration();
}

module.exports = { testVectorDatabaseGlobalIdIntegration };
