# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
**/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
.selfmirror-data/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files (根据需要决定是否跟踪)
# .env*.local
# .env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# YoYo AI version control directory
.yoyo/

# AI Models (large files should not be in git)
public/models/
*.gguf
*.bin
*.safetensors
*.pt
*.pth
*.h5
*.pkl
*.model
