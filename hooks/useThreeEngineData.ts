"use client"

import { useState, useEffect, useCallback, useRef } from 'react';

interface ThreeEngineStats {
  totalWorkflows: number;
  validWorkflows: number;
  averageTime: number;
  averageQuality: number;
  cacheHitRate: number;
  enginePerformance: {
    averageTimes: {
      navigator: number;
      contextRetriever: number;
      integrationGenerator: number;
      coldStoreRetrieval: number;
    };
    parallelEfficiency: number;
  };
  qualityBreakdown: {
    coherence: number;
    relevance: number;
    empathy: number;
    insight: number;
  };
}

interface LatestResult {
  workflowResult: {
    workflowId: string;
    totalTime: number;
    quality: {
      overall: number;
      coherence: number;
      relevance: number;
      empathy: number;
      insight: number;
    };
    performance: {
      engineTimes: {
        navigator: number;
        contextRetriever: number;
        integrationGenerator: number;
        coldStoreRetrieval: number;
      };
      parallelEfficiency: number;
      cacheUtilization: {
        used: boolean;
        hitRate: number;
        timeSaved: number;
      };
    };
    sourceAttribution: {
      userInputId: string;
      hotStoreContributions: string[];
      coldStoreContributions: string[];
      totalSources: number;
    };
    metadata: {
      workflowId: string;
      timestamp: string;
    };
  };
  timestamp: string;
}

interface UseThreeEngineDataOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableWebSocket?: boolean;
}

export function useThreeEngineData(options: UseThreeEngineDataOptions = {}) {
  const {
    autoRefresh = true,
    refreshInterval = 5000,
    enableWebSocket = false
  } = options;

  const [stats, setStats] = useState<ThreeEngineStats | null>(null);
  const [latestResult, setLatestResult] = useState<LatestResult | null>(null);
  const [history, setHistory] = useState<LatestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // 获取统计数据
  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch('/api/debug/three-engine-result?type=stats');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.data);
          setError(null);
        }
      }
    } catch (err) {
      console.warn('获取统计数据失败:', err);
      setError(err instanceof Error ? err.message : '获取统计数据失败');
    }
  }, []);

  // 获取最新结果
  const fetchLatestResult = useCallback(async () => {
    try {
      const response = await fetch('/api/debug/three-engine-result?type=latest');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setLatestResult(data.data);
          setError(null);
        }
      }
    } catch (err) {
      console.warn('获取最新结果失败:', err);
      setError(err instanceof Error ? err.message : '获取最新结果失败');
    }
  }, []);

  // 获取历史记录
  const fetchHistory = useCallback(async (limit: number = 10) => {
    try {
      const response = await fetch(`/api/debug/three-engine-result?type=history&limit=${limit}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setHistory(data.data);
          setError(null);
        }
      }
    } catch (err) {
      console.warn('获取历史记录失败:', err);
      setError(err instanceof Error ? err.message : '获取历史记录失败');
    }
  }, []);

  // 刷新所有数据
  const refreshData = useCallback(async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        fetchStats(),
        fetchLatestResult(),
        fetchHistory()
      ]);
      setLastUpdate(new Date().toISOString());
    } catch (err) {
      setError(err instanceof Error ? err.message : '刷新数据失败');
    } finally {
      setIsLoading(false);
    }
  }, [fetchStats, fetchLatestResult, fetchHistory]);

  // 清空调试数据
  const clearData = useCallback(async () => {
    try {
      const response = await fetch('/api/debug/three-engine-result', {
        method: 'DELETE'
      });
      if (response.ok) {
        setStats(null);
        setLatestResult(null);
        setHistory([]);
        setError(null);
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : '清空数据失败');
      return false;
    }
  }, []);

  // WebSocket连接管理
  const connectWebSocket = useCallback(() => {
    if (!enableWebSocket || wsRef.current) return;

    try {
      // 注意：这里需要根据实际的WebSocket端点进行调整
      const wsUrl = `ws://${window.location.host}/api/debug/three-engine-ws`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('三引擎WebSocket连接已建立');
        setError(null);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          switch (data.type) {
            case 'stats_update':
              setStats(data.payload);
              break;
            case 'latest_result':
              setLatestResult(data.payload);
              break;
            case 'new_workflow':
              // 新工作流完成，刷新数据
              refreshData();
              break;
            default:
              console.log('未知WebSocket消息类型:', data.type);
          }
          
          setLastUpdate(new Date().toISOString());
        } catch (err) {
          console.warn('解析WebSocket消息失败:', err);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setError('WebSocket连接错误');
      };

      ws.onclose = () => {
        console.log('WebSocket连接已关闭');
        wsRef.current = null;
        
        // 尝试重连
        if (enableWebSocket) {
          setTimeout(connectWebSocket, 5000);
        }
      };

      wsRef.current = ws;
    } catch (err) {
      console.error('WebSocket连接失败:', err);
      setError('WebSocket连接失败');
    }
  }, [enableWebSocket, refreshData]);

  // 断开WebSocket连接
  const disconnectWebSocket = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  }, []);

  // 设置自动刷新
  useEffect(() => {
    if (autoRefresh && !enableWebSocket) {
      intervalRef.current = setInterval(refreshData, refreshInterval);
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, enableWebSocket, refreshInterval, refreshData]);

  // WebSocket连接管理
  useEffect(() => {
    if (enableWebSocket) {
      connectWebSocket();
      return disconnectWebSocket;
    }
  }, [enableWebSocket, connectWebSocket, disconnectWebSocket]);

  // 初始数据加载
  useEffect(() => {
    refreshData();
  }, []);

  // 清理函数
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      disconnectWebSocket();
    };
  }, [disconnectWebSocket]);

  // 计算衍生数据
  const derivedData = {
    // 性能趋势（基于历史数据）
    performanceTrend: history.length > 1 ? 
      (history[0]?.workflowResult.totalTime || 0) - (history[1]?.workflowResult.totalTime || 0) : 0,
    
    // 质量趋势
    qualityTrend: history.length > 1 ? 
      (history[0]?.workflowResult.quality.overall || 0) - (history[1]?.workflowResult.quality.overall || 0) : 0,
    
    // 缓存效率
    cacheEfficiency: stats ? stats.cacheHitRate : 0,
    
    // 系统健康状态
    systemHealth: (() => {
      if (!stats) return 'unknown';
      if (stats.averageQuality > 0.8 && stats.cacheHitRate > 0.6) return 'excellent';
      if (stats.averageQuality > 0.6 && stats.cacheHitRate > 0.4) return 'good';
      if (stats.averageQuality > 0.4) return 'fair';
      return 'poor';
    })(),
    
    // 最近活动状态
    isActive: lastUpdate ? (Date.now() - new Date(lastUpdate).getTime()) < 30000 : false
  };

  return {
    // 数据
    stats,
    latestResult,
    history,
    derivedData,
    
    // 状态
    isLoading,
    error,
    lastUpdate,
    
    // 操作
    refreshData,
    clearData,
    fetchHistory,
    
    // WebSocket控制
    connectWebSocket,
    disconnectWebSocket,
    isWebSocketConnected: !!wsRef.current,
    
    // 实用函数
    formatTime: (timestamp: string) => new Date(timestamp).toLocaleTimeString(),
    formatDuration: (ms: number) => `${ms}ms`,
    formatPercentage: (value: number) => `${(value * 100).toFixed(1)}%`,
    
    // 数据验证
    hasData: !!(stats || latestResult),
    isStale: lastUpdate ? (Date.now() - new Date(lastUpdate).getTime()) > 60000 : true
  };
}
