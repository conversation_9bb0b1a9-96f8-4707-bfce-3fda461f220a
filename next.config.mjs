/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  webpack: (config, { isServer }) => {
    // 配置WebAssembly模块处理
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
      layers: true,
    };

    // 为WebAssembly文件添加规则
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'webassembly/async',
    });

    // 解决 @xenova/transformers 的 WASM 加载问题
    config.resolve.alias = {
      ...config.resolve.alias,
      'sharp$': false,
      'onnxruntime-node$': false,
    };

    // 排除服务端不需要的包
    if (isServer) {
      config.externals = [...(config.externals || []), 'sharp', 'onnxruntime-node'];
    }

    // 处理 node-llama-cpp 的动态导入
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
    };

    return config;
  },

  // 禁用严格模式以避免某些包的兼容性问题
  reactStrictMode: false,
};

export default nextConfig;
