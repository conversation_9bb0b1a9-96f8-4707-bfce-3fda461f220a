# SelfMirror 双核抽象层生产环境配置
# 请根据实际部署环境调整这些配置值

# ===========================================
# 双核抽象层核心配置
# ===========================================

# 调试模式 - 生产环境建议设置为 false
DUAL_CORE_DEBUG_MODE=false

# 智能缓存层配置
DUAL_CORE_CACHE_SIZE=50
DUAL_CORE_CACHE_ELIMINATION_THRESHOLD=0.8
DUAL_CORE_CACHE_MIN_RETAINED_IDS=10
DUAL_CORE_CACHE_RESET_INTERVAL_HOURS=24

# 上下文打包工厂配置
DUAL_CORE_MAX_CONTEXT_LENGTH=4000
DUAL_CORE_MAX_SEGMENTS=20
DUAL_CORE_ENABLE_QUALITY_ASSESSMENT=true

# 性能监控配置
DUAL_CORE_PERFORMANCE_MONITORING=true
DUAL_CORE_PERFORMANCE_LOG_LEVEL=warn
DUAL_CORE_MAX_PROCESSING_TIME_MS=200

# ===========================================
# 安全和限制配置
# ===========================================

# 内存安全限制
DUAL_CORE_MAX_MEMORY_USAGE_MB=100
DUAL_CORE_MAX_CONCURRENT_OPERATIONS=10
DUAL_CORE_OPERATION_TIMEOUT_MS=30000

# 日志配置
DUAL_CORE_LOG_LEVEL=info
DUAL_CORE_LOG_RETENTION_DAYS=7
DUAL_CORE_ENABLE_DETAILED_LOGS=false

# 错误处理配置
DUAL_CORE_ENABLE_FALLBACK=true
DUAL_CORE_MAX_RETRY_ATTEMPTS=3
DUAL_CORE_RETRY_DELAY_MS=1000

# ===========================================
# 调试控制台配置
# ===========================================

# 调试控制台访问控制
DUAL_CORE_DEBUG_CONSOLE_ENABLED=true
DUAL_CORE_DEBUG_CONSOLE_AUTH_REQUIRED=true
DUAL_CORE_DEBUG_CONSOLE_MAX_LOG_ENTRIES=1000

# 实时功能配置
DUAL_CORE_REALTIME_UPDATES_ENABLED=true
DUAL_CORE_REALTIME_UPDATE_INTERVAL_MS=30000
DUAL_CORE_WEBSOCKET_ENABLED=false

# ===========================================
# 性能优化配置
# ===========================================

# 缓存优化
DUAL_CORE_ENABLE_MEMORY_OPTIMIZATION=true
DUAL_CORE_GC_INTERVAL_MS=300000
DUAL_CORE_CACHE_COMPRESSION=false

# 并发控制
DUAL_CORE_MAX_PARALLEL_CACHE_OPERATIONS=5
DUAL_CORE_MAX_PARALLEL_PACKAGING_OPERATIONS=3

# ===========================================
# 监控和告警配置
# ===========================================

# 性能告警阈值
DUAL_CORE_ALERT_PROCESSING_TIME_MS=500
DUAL_CORE_ALERT_MEMORY_USAGE_MB=80
DUAL_CORE_ALERT_ERROR_RATE_THRESHOLD=0.1

# 健康检查配置
DUAL_CORE_HEALTH_CHECK_ENABLED=true
DUAL_CORE_HEALTH_CHECK_INTERVAL_MS=60000

# ===========================================
# 开发和测试配置 (生产环境应禁用)
# ===========================================

# 测试模式 - 生产环境必须设置为 false
DUAL_CORE_TEST_MODE=false
DUAL_CORE_MOCK_DATA_ENABLED=false
DUAL_CORE_SIMULATION_MODE=false

# ===========================================
# 数据持久化配置
# ===========================================

# 策略存储配置
DUAL_CORE_STRATEGY_STORAGE_ENABLED=true
DUAL_CORE_STRATEGY_STORAGE_PATH=/data/dual-core/strategies
DUAL_CORE_STRATEGY_BACKUP_ENABLED=true

# 性能数据存储
DUAL_CORE_PERFORMANCE_DATA_RETENTION_DAYS=30
DUAL_CORE_PERFORMANCE_DATA_COMPRESSION=true

# ===========================================
# 集成配置
# ===========================================

# 三引擎集成配置
DUAL_CORE_THREE_ENGINE_INTEGRATION=true
DUAL_CORE_FALLBACK_TO_LEGACY=true
DUAL_CORE_LEGACY_COMPATIBILITY_MODE=false

# API配置
DUAL_CORE_API_RATE_LIMIT=100
DUAL_CORE_API_TIMEOUT_MS=10000

# ===========================================
# 环境特定配置
# ===========================================

# 生产环境标识
NODE_ENV=production
DUAL_CORE_ENVIRONMENT=production

# 版本信息
DUAL_CORE_VERSION=2.1.0
DUAL_CORE_BUILD_DATE=2025-07-01

# ===========================================
# 注意事项
# ===========================================

# 1. 请根据服务器资源调整内存和并发限制
# 2. 在高流量环境中，建议禁用详细日志以提高性能
# 3. 定期监控性能指标，根据实际情况调整配置
# 4. 确保有足够的磁盘空间用于日志和数据存储
# 5. 在生产环境中，建议启用身份验证和访问控制
