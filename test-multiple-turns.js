/**
 * 测试多轮对话的全局ID生成
 */

async function testMultipleTurns() {
  console.log('🧪 开始测试多轮对话的全局ID生成...');
  
  const conversations = [
    '第一轮对话：你好！',
    '第二轮对话：请介绍一下你自己。',
    '第三轮对话：谢谢你的介绍。'
  ];

  const results = [];

  for (let i = 0; i < conversations.length; i++) {
    console.log(`\n🔄 开始第 ${i + 1} 轮对话...`);
    
    try {
      const response = await fetch('http://localhost:3000/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: conversations[i]
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 读取流式响应
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let userInputId = '';
      let responseId = '';
      let responseLength = 0;

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.type === 'text' && data.metadata) {
                  userInputId = data.metadata.userInputId;
                  responseId = data.metadata.responseId;
                } else if (data.type === 'end' && data.metadata) {
                  responseLength = data.metadata.totalLength;
                }
              } catch (parseError) {
                // 忽略解析错误
              }
            }
          }
        }
      }

      results.push({
        turn: i + 1,
        userInput: conversations[i],
        userInputId,
        responseId,
        responseLength
      });

      console.log(`✅ 第 ${i + 1} 轮对话完成:`);
      console.log(`  用户输入ID: ${userInputId}`);
      console.log(`  AI响应ID: ${responseId}`);
      console.log(`  响应长度: ${responseLength} 字符`);

    } catch (error) {
      console.error(`❌ 第 ${i + 1} 轮对话失败:`, error);
    }

    // 等待一秒再进行下一轮
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n📊 多轮对话测试结果总结:');
  console.log('┌─────┬─────────────────┬─────────────────┬──────────┐');
  console.log('│ 轮次 │    用户输入ID    │    AI响应ID     │ 响应长度  │');
  console.log('├─────┼─────────────────┼─────────────────┼──────────┤');
  
  results.forEach(result => {
    console.log(`│  ${result.turn}  │ ${result.userInputId} │ ${result.responseId} │   ${result.responseLength}   │`);
  });
  
  console.log('└─────┴─────────────────┴─────────────────┴──────────┘');

  // 验证ID递增规律
  console.log('\n🔍 ID递增规律验证:');
  for (let i = 0; i < results.length; i++) {
    const expectedTurn = String(i + 1).padStart(3, '0');
    const actualTurn = results[i].userInputId.split('-T')[1];
    const isCorrect = actualTurn === expectedTurn;
    
    console.log(`  第${i + 1}轮: 期望T${expectedTurn}, 实际T${actualTurn} ${isCorrect ? '✅' : '❌'}`);
  }

  // 验证衍生ID关系
  console.log('\n🔗 衍生ID关系验证:');
  results.forEach(result => {
    const userTurn = result.userInputId.split('-T')[1];
    const responseTurn = result.responseId.split('-T')[1].split('-D')[0];
    const isCorrect = userTurn === responseTurn;
    
    console.log(`  第${result.turn}轮: ${result.userInputId} -> ${result.responseId} ${isCorrect ? '✅' : '❌'}`);
  });

  console.log('\n🎉 多轮对话全局ID测试完成！');
}

// 运行测试
testMultipleTurns();
