/**
 * Jest 测试设置文件 - 双核抽象层专用
 */

// 扩展 Jest 匹配器
import '@testing-library/jest-dom';

// 全局测试配置
global.console = {
  ...console,
  // 在测试期间减少日志输出
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// 模拟 Next.js 环境变量
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_APP_ENV = 'test';

// 设置测试超时
jest.setTimeout(30000);

// 全局测试工具函数
global.testUtils = {
  /**
   * 等待指定时间
   */
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  /**
   * 生成随机字符串
   */
  randomString: (length = 8) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  /**
   * 生成测试用的父块ID
   */
  generateParentChunkIds: (count = 5, prefix = 'test') => {
    return Array.from({ length: count }, (_, i) => `${prefix}_chunk_${i}_${Date.now()}`);
  },
  
  /**
   * 生成测试用的历史对话ID
   */
  generateHistoryDialogueIds: (count = 3) => {
    const today = new Date();
    return Array.from({ length: count }, (_, i) => {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
      return `${dateStr}-T${String(i + 1).padStart(3, '0')}_conv`;
    });
  },
  
  /**
   * 验证性能指标
   */
  validatePerformanceMetrics: (metrics, thresholds = {}) => {
    const defaultThresholds = {
      maxAverageTime: 100,
      minSuccessRate: 0.9,
      minThroughput: 1,
      maxMemoryUsage: 50 * 1024 * 1024, // 50MB
    };
    
    const finalThresholds = { ...defaultThresholds, ...thresholds };
    
    expect(metrics.averageTime).toBeLessThanOrEqual(finalThresholds.maxAverageTime);
    expect(metrics.successRate).toBeGreaterThanOrEqual(finalThresholds.minSuccessRate);
    expect(metrics.throughput).toBeGreaterThanOrEqual(finalThresholds.minThroughput);
    expect(metrics.memoryUsage).toBeLessThanOrEqual(finalThresholds.maxMemoryUsage);
  },
  
  /**
   * 创建模拟的检索结果
   */
  createMockRetrievalResult: (options = {}) => {
    const defaults = {
      parentChunkIds: global.testUtils.generateParentChunkIds(5),
      queryContext: '测试查询上下文',
      timestamp: new Date().toISOString(),
    };
    
    return { ...defaults, ...options };
  },
  
  /**
   * 创建模拟的上下文打包输入
   */
  createMockPackagingInputs: (options = {}) => {
    const defaults = {
      userProfile: {
        id: 'test_user',
        content: '测试用户画像',
        preferences: { responseStyle: 'conversational' },
        timestamp: new Date().toISOString(),
      },
      deepHistoryMemory: {
        source: 'test',
        ids: global.testUtils.generateParentChunkIds(3),
        relevanceScores: {},
        retrievalMethod: 'hybrid',
        processingTime: 10,
        metadata: { contentMap: {} },
      },
      recentDialogue: {
        contexts: [
          {
            id: 'recent_1',
            content: '最近的对话内容',
            timestamp: new Date().toISOString(),
            relevanceScore: 0.8,
          },
        ],
        summary: { emotionalState: 'neutral' },
        emotionalState: 'neutral',
      },
      completeHistory: {
        fullConversation: ['消息1', '消息2'],
        sessionInfo: { sessionId: 'test_session' },
        totalTurns: 2,
        sessionDuration: 120000,
      },
    };
    
    return { ...defaults, ...options };
  },
};

// 模拟浏览器 API（如果需要）
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 在每个测试前清理
beforeEach(() => {
  // 清理所有模拟
  jest.clearAllMocks();
  
  // 重置控制台模拟
  global.console.log.mockClear();
  global.console.debug.mockClear();
  global.console.info.mockClear();
  global.console.warn.mockClear();
  global.console.error.mockClear();
});

// 在每个测试后清理
afterEach(() => {
  // 清理定时器
  jest.clearAllTimers();
  
  // 强制垃圾回收（如果可用）
  if (global.gc) {
    global.gc();
  }
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
  // 在测试环境中，我们希望测试失败而不是静默忽略错误
  throw reason;
});

// 性能监控（仅在性能测试时启用）
if (process.env.PERFORMANCE_TEST === 'true') {
  const { performance, PerformanceObserver } = require('perf_hooks');
  
  global.performance = performance;
  
  // 监控性能指标
  const obs = new PerformanceObserver((items) => {
    items.getEntries().forEach((entry) => {
      if (entry.duration > 100) { // 超过100ms的操作
        console.warn(`慢操作检测: ${entry.name} 耗时 ${entry.duration.toFixed(2)}ms`);
      }
    });
  });
  
  obs.observe({ entryTypes: ['measure'] });
  
  // 在测试结束时清理
  afterAll(() => {
    obs.disconnect();
  });
}

console.log('🧪 双核抽象层测试环境已初始化');
