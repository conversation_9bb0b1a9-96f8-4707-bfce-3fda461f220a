/**
 * Jest配置文件 - 专门用于双核抽象层测试
 */

const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  // 测试环境
  testEnvironment: 'node',
  
  // 测试文件匹配模式
  testMatch: [
    '**/__tests__/dual-core*.test.ts',
    '**/__tests__/dual-core*.test.js'
  ],
  
  // 模块路径映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  
  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/jest.setup.dual-core.js'],
  
  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    'lib/services/intelligent-cache-layer/**/*.{ts,tsx}',
    'lib/services/context-packaging-factory/**/*.{ts,tsx}',
    'lib/services/three-engine/intelligent-cache-adapter.ts',
    'lib/services/dual-core-debug-controller.ts',
    'components/debug/dual-core/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  coverageDirectory: 'coverage/dual-core',
  coverageReporters: ['text', 'lcov', 'html'],
  
  // 测试超时
  testTimeout: 30000,
  
  // 并发测试
  maxConcurrency: 5,
  
  // 详细输出
  verbose: true,
  
  // 错误时停止
  bail: false,
  
  // 清理模拟
  clearMocks: true,
  restoreMocks: true,
  
  // TypeScript 支持
  preset: 'ts-jest',
  
  // 全局变量
  globals: {
    'ts-jest': {
      tsconfig: {
        jsx: 'react-jsx',
      },
    },
  },
  
  // 模块文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // 转换配置
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  
  // 忽略转换的模块
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))',
  ],
  
  // 测试报告器
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './coverage/dual-core/html-report',
        filename: 'report.html',
        expand: true,
        hideIcon: false,
        pageTitle: '双核抽象层测试报告',
      },
    ],
  ],
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
