# SelfMirror MVP 技术架构白皮书

## 一、项目总览与核心哲学

### 1.1 产品定位
SelfMirror 是一个基于AI的个人认知伙伴，专注于通过深度对话帮助用户进行自我探索和情感理解。不同于传统的AI助手，SelfMirror 致力于成为用户内心世界的"镜子"，通过温和的探针式共情和持续的记忆积累，陪伴用户的心理成长历程。

### 1.2 核心哲学：意义距离 > 语义距离
SelfMirror 的设计哲学建立在"意义距离比语义距离更重要"的理念之上：
- **语义距离**：关注词汇、句法的表面相似性
- **意义距离**：关注情感内核、认知模式、价值观念的深层共鸣
- **实现方式**：通过四文件记忆系统和探针式共情，构建用户的深层认知画像

### 1.3 技术原则

#### 本地化优先 (Local-First)
- 所有用户数据存储在本地，确保隐私安全
- 采用Markdown文件系统，数据格式透明可读
- 支持离线使用，减少对外部服务的依赖

#### 隐私至上 (Privacy-First)
- 零数据上传：用户画像、对话历史完全本地化
- 透明存储：采用人类可读的Markdown格式
- 用户控制：提供完整的数据导出和删除能力

#### 大道至简 (Simplicity-First)
- 极简架构：避免过度工程化
- 渐进增强：核心功能优先，高级特性可选
- 维护友好：代码结构清晰，文档完整

## 二、系统架构总览图
graph TB
    %% 用户界面层
    subgraph "🎨 前端层 (Frontend Layer)"
        UI1[app/page.tsx<br/>应用入口]
        UI2[components/chat/ChatInterface.tsx<br/>核心对话界面<br/>流式响应 + 自动滚动]
        UI3[components/emotional-space/<br/>情绪可视化组件<br/>⚠️ 未集成]
        UI4[app/debug/page.tsx<br/>调试工具]
    end
    
    %% API层
    subgraph "🚀 API层 (API Layer)"
        API1[app/api/chat/route.ts<br/>快速响应模式<br/>thinkingBudget=0<br/>响应时间 < 2秒]
        API2[app/api/daily-reflection/route.ts<br/>每日沉淀模式<br/>每6轮对话触发<br/>后台异步处理]
        API3[app/api/memory/archive/route.ts<br/>记忆归档管理]
    end
    
    %% 业务逻辑层
    subgraph "⚙️ 业务逻辑层 (Business Logic Layer)"
        BL1[lib/services/conversation.ts<br/>对话管理服务<br/>上下文窗口：正常6轮/沉淀8轮<br/>触发机制：每6轮对话]
        BL2[lib/storage/simple-memory.ts<br/>文件存储管理<br/>Markdown读写操作]
    end
    
    %% 四文件记忆系统
    subgraph "📝 四文件记忆系统 (Four-File Memory System)"
        MEM1[memory/用户画像.md<br/>认知特征 + 价值观念]
        MEM2[memory/关键事件.md<br/>重要事件记录]
        MEM3[memory/每日洞察今天.md<br/>当天洞察热日志]
        MEM4[memory/每日洞察归档.md<br/>历史洞察冷日志]
        MEM5[memory/对话历史.md<br/>完整对话记录]
    end
    
    %% 提示词系统
    subgraph "📋 提示词系统 (Prompt System)"
        PROMPT1[memory/prompts/系统提示词.md<br/>基础人格设定]
        PROMPT2[memory/prompts/日常对话模式.md<br/>快速响应策略]
        PROMPT3[memory/prompts/每日沉淀模式.md<br/>深度分析策略]
    end
    
    %% AI引擎
    subgraph "🤖 AI引擎 (AI Engine)"
        AI1[Google Gemini 2.5 Flash<br/>AI推理模型]
        AI2[自定义Fetch + ProxyAgent<br/>网络层 + 代理支持<br/>thinking控制]
    end
    
    %% 主要数据流
    UI2 --> BL1
    BL1 --> API1
    API1 --> BL2
    BL2 --> MEM1
    BL2 --> MEM2
    BL2 --> MEM5
    
    %% 深度分析流
    BL1 -.->|每6轮触发| API2
    API2 --> BL2
    API2 --> MEM3
    
    %% 记忆上下文加载
    API1 --> BL2
    API2 --> BL2
    BL2 --> MEM1
    BL2 --> MEM2
    BL2 --> MEM3
    BL2 --> MEM4
    
    %% 提示词加载
    API1 --> PROMPT1
    API1 --> PROMPT2
    API2 --> PROMPT1
    API2 --> PROMPT3
    
    %% AI调用
    API1 --> AI2
    API2 --> AI2
    AI2 --> AI1
    
    %% 归档流
    UI4 --> API3
    API3 --> BL2
    BL2 --> MEM4
    
    %% 样式定义
    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef business fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef memory fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef prompt fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef ai fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    
    class UI1,UI2,UI3,UI4 frontend
    class API1,API2,API3 api
    class BL1,BL2 business
    class MEM1,MEM2,MEM3,MEM4,MEM5 memory
    class PROMPT1,PROMPT2,PROMPT3 prompt
    class AI1,AI2 ai  <svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 3037.08984375 1095" style="max-width: 3037.08984375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26"><style>#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .error-icon{fill:#a44141;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edge-thickness-normal{stroke-width:1px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .marker.cross{stroke:lightgrey;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 p{margin:0;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .cluster-label text{fill:#F9FFFE;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .cluster-label span{color:#F9FFFE;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .cluster-label span p{background-color:transparent;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .label text,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 span{fill:#ccc;color:#ccc;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node rect,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node circle,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node ellipse,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node polygon,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .rough-node .label text,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node .label text,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .image-shape .label,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .icon-shape .label{text-anchor:middle;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .rough-node .label,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node .label,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .image-shape .label,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .icon-shape .label{text-align:center;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .node.clickable{cursor:pointer;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .arrowheadPath{fill:lightgrey;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .cluster text{fill:#F9FFFE;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .cluster span{color:#F9FFFE;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 rect.text{fill:none;stroke-width:0;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .icon-shape,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .icon-shape p,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .icon-shape rect,#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .frontend&gt;*{fill:#e3f2fd!important;stroke:#1976d2!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .frontend span{fill:#e3f2fd!important;stroke:#1976d2!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .api&gt;*{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .api span{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .business&gt;*{fill:#e8f5e8!important;stroke:#388e3c!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .business span{fill:#e8f5e8!important;stroke:#388e3c!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .memory&gt;*{fill:#fff8e1!important;stroke:#f57c00!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .memory span{fill:#fff8e1!important;stroke:#f57c00!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .prompt&gt;*{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .prompt span{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .ai&gt;*{fill:#f1f8e9!important;stroke:#689f38!important;stroke-width:2px!important;}#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26 .ai span{fill:#f1f8e9!important;stroke:#689f38!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph5" class="cluster"><rect height="354" width="316.328125" y="733" x="8" style=""></rect><g transform="translate(91.921875, 733)" class="cluster-label"><foreignObject height="24" width="148.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 AI引擎 (AI Engine)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="176" width="950" y="733" x="344.328125" style=""></rect><g transform="translate(719.328125, 733)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 提示词系统 (Prompt System)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="128" width="1426.2265625" y="959" x="1602.86328125" style=""></rect><g transform="translate(2215.9765625, 959)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📝 四文件记忆系统 (Four-File Memory System)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="675" width="895.724609375" y="234" x="1686.697265625" style=""></rect><g transform="translate(2034.5595703125, 234)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ 业务逻辑层 (Business Logic Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="200" width="1600.169921875" y="483" x="63.16015625" style=""></rect><g transform="translate(792.0966796875, 483)" class="cluster-label"><foreignObject height="24" width="142.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🚀 API层 (API Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="176" width="1162.2890625" y="8" x="1129.654296875" style=""></rect><g transform="translate(1613.685546875, 8)" class="cluster-label"><foreignObject height="24" width="194.2265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎨 前端层 (Frontend Layer)</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI2_BL1_0" d="M2097.072,147L2097.072,153.167C2097.072,159.333,2097.072,171.667,2097.072,182C2097.072,192.333,2097.072,200.667,2097.072,209C2097.072,217.333,2097.072,225.667,2097.072,233.333C2097.072,241,2097.072,248,2097.072,251.5L2097.072,255"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL1_API1_1" d="M1966.189,399.429L1950.663,407.191C1935.137,414.953,1904.084,430.476,1888.558,444.405C1873.031,458.333,1873.031,470.667,1716.43,491.683C1559.829,512.699,1246.627,542.398,1090.026,557.247L933.425,572.096"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API1_BL2_2" d="M929.443,610.254L978.828,622.378C1028.212,634.502,1126.981,658.751,1260.184,675.042C1393.387,691.333,1561.023,699.667,1644.842,708C1728.66,716.333,1728.66,724.667,1777.856,738.923C1827.052,753.179,1925.444,773.357,1974.64,783.446L2023.835,793.536"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM1_3" d="M2027.754,848.195L1979.309,858.329C1930.865,868.463,1833.975,888.732,1785.531,903.032C1737.086,917.333,1737.086,925.667,1737.086,934C1737.086,942.333,1737.086,950.667,1737.634,958.341C1738.182,966.016,1739.278,973.032,1739.827,976.54L1740.375,980.048"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM2_4" d="M2027.754,863.936L2005.013,871.446C1982.272,878.957,1936.79,893.979,1914.049,905.656C1891.309,917.333,1891.309,925.667,1891.309,934C1891.309,942.333,1891.309,950.667,1898.803,958.695C1906.298,966.723,1921.288,974.445,1928.782,978.307L1936.277,982.168"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM5_5" d="M2287.754,851.367L2328.874,860.973C2369.994,870.578,2452.234,889.789,2551.753,903.561C2651.272,917.333,2768.07,925.667,2826.468,934C2884.867,942.333,2884.867,950.667,2884.867,958.333C2884.867,966,2884.867,973,2884.867,976.5L2884.867,980"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL1_API2_6" d="M2115.344,409L2116.846,415.167C2118.348,421.333,2121.353,433.667,2122.855,446C2124.357,458.333,2124.357,470.667,1982.451,491.234C1840.545,511.801,1556.732,540.603,1414.825,555.003L1272.919,569.404"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_BL2_7" d="M1219.047,658L1223.498,662.167C1227.948,666.333,1236.849,674.667,1325.118,683C1413.387,691.333,1581.023,699.667,1664.842,708C1748.66,716.333,1748.66,724.667,1794.524,738.699C1840.388,752.732,1932.116,772.463,1977.979,782.329L2023.843,792.195"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_MEM3_8" d="M1268.939,615.581L1313.774,626.817C1358.608,638.054,1448.277,660.527,1675.786,675.93C1903.296,691.333,2268.646,699.667,2451.321,708C2633.996,716.333,2633.996,724.667,2633.996,743.5C2633.996,762.333,2633.996,791.667,2633.996,821C2633.996,850.333,2633.996,879.667,2576.66,898.5C2519.323,917.333,2404.65,925.667,2347.313,934C2289.977,942.333,2289.977,950.667,2290.525,958.341C2291.073,966.016,2292.169,973.032,2292.717,976.54L2293.265,980.048"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API1_BL2_9" d="M929.443,607.817L985.494,620.347C1041.546,632.878,1153.648,657.939,1384.999,674.636C1616.35,691.333,1966.951,699.667,2142.251,708C2317.551,716.333,2317.551,724.667,2310.569,732.678C2303.586,740.69,2289.622,748.38,2282.64,752.225L2275.658,756.07"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_BL2_10" d="M1249.047,658L1255.164,662.167C1261.282,666.333,1273.516,674.667,1484.485,683C1695.454,691.333,2105.159,699.667,2310.011,708C2514.863,716.333,2514.863,724.667,2477.659,738.001C2440.455,751.336,2366.046,769.672,2328.842,778.84L2291.638,788.008"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM1_11" d="M2027.754,860.938L2001.68,868.948C1975.605,876.959,1923.457,892.979,1897.383,905.156C1871.309,917.333,1871.309,925.667,1871.309,934C1871.309,942.333,1871.309,950.667,1863.814,958.695C1856.319,966.723,1841.329,974.445,1833.835,978.307L1826.34,982.168"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM2_12" d="M2087.911,884L2083.292,888.167C2078.672,892.333,2069.434,900.667,2064.815,909C2060.195,917.333,2060.195,925.667,2060.195,934C2060.195,942.333,2060.195,950.667,2057.669,958.453C2055.143,966.24,2050.09,973.48,2047.564,977.1L2045.038,980.72"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM3_13" d="M2196.61,884L2199.18,888.167C2201.75,892.333,2206.89,900.667,2247.488,909C2288.086,917.333,2364.143,925.667,2402.171,934C2440.199,942.333,2440.199,950.667,2431.677,958.723C2423.154,966.78,2406.109,974.559,2397.586,978.449L2389.064,982.339"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM4_14" d="M2210.928,884L2214.445,888.167C2217.962,892.333,2224.996,900.667,2266.541,909C2308.086,917.333,2384.143,925.667,2422.171,934C2460.199,942.333,2460.199,950.667,2468.722,958.723C2477.244,966.78,2494.29,974.559,2502.812,978.449L2511.335,982.339"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API1_PROMPT1_15" d="M707.428,612.872L663.994,624.56C620.561,636.248,533.695,659.624,490.261,675.479C446.828,691.333,446.828,699.667,446.828,708C446.828,716.333,446.828,724.667,450.822,734.456C454.816,744.246,462.803,755.493,466.797,761.116L470.79,766.739"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API1_PROMPT2_16" d="M818.998,646L819.053,652.167C819.108,658.333,819.218,670.667,819.273,681C819.328,691.333,819.328,699.667,819.328,708C819.328,716.333,819.328,724.667,819.328,734.333C819.328,744,819.328,755,819.328,760.5L819.328,766"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_PROMPT1_17" d="M1008.939,609.826L949.838,622.021C890.736,634.217,772.532,658.609,713.43,674.971C654.328,691.333,654.328,699.667,654.328,708C654.328,716.333,654.328,724.667,644.737,734.654C635.146,744.642,615.964,756.283,606.373,762.104L596.782,767.925"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_PROMPT3_18" d="M1131.731,658L1131.33,662.167C1130.93,666.333,1130.129,674.667,1129.729,683C1129.328,691.333,1129.328,699.667,1129.328,708C1129.328,716.333,1129.328,724.667,1129.328,734.333C1129.328,744,1129.328,755,1129.328,760.5L1129.328,766"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API1_AI2_19" d="M707.428,599.762L615.55,613.635C523.673,627.508,339.919,655.254,248.041,673.294C156.164,691.333,156.164,699.667,156.164,708C156.164,716.333,156.164,724.667,156.79,734.338C157.415,744.009,158.666,755.017,159.291,760.521L159.917,766.026"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_AI2_20" d="M1008.939,597.385L879.991,611.655C751.042,625.924,493.144,654.462,364.195,672.898C235.246,691.333,235.246,699.667,235.246,708C235.246,716.333,235.246,724.667,230.817,734.476C226.387,744.285,217.529,755.569,213.099,761.211L208.67,766.854"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AI2_AI1_21" d="M166.164,872L166.164,878.167C166.164,884.333,166.164,896.667,166.164,907C166.164,917.333,166.164,925.667,166.164,934C166.164,942.333,166.164,950.667,166.164,958.333C166.164,966,166.164,973,166.164,976.5L166.164,980"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI4_API3_22" d="M1473.635,135L1473.635,143.167C1473.635,151.333,1473.635,167.667,1473.635,180C1473.635,192.333,1473.635,200.667,1473.635,209C1473.635,217.333,1473.635,225.667,1473.635,246.5C1473.635,267.333,1473.635,300.667,1473.635,336C1473.635,371.333,1473.635,408.667,1473.635,433.5C1473.635,458.333,1473.635,470.667,1473.635,486.333C1473.635,502,1473.635,521,1473.635,530.5L1473.635,540"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API3_BL2_23" d="M1461.725,622L1458.621,632.167C1455.516,642.333,1449.307,662.667,1628.163,677C1807.02,691.333,2170.941,699.667,2352.902,708C2534.863,716.333,2534.863,724.667,2494.328,738.292C2453.792,751.918,2372.721,770.837,2332.185,780.296L2291.649,789.755"></path><path marker-end="url(#mermaid-a31ae214-2644-4e4b-ac56-ba961cecdd26_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BL2_MEM4_24" d="M2287.754,871.958L2303.504,878.132C2319.253,884.305,2350.753,896.653,2424.901,906.993C2499.049,917.333,2615.847,925.667,2674.246,934C2732.645,942.333,2732.645,950.667,2724.636,958.71C2716.628,966.752,2700.612,974.505,2692.604,978.381L2684.595,982.257"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2124.357421875, 446)" class="edgeLabel"><g transform="translate(-36.1953125, -12)" class="label"><foreignObject height="24" width="72.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>每6轮触发</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1242.361328125, 96)" id="flowchart-UI1-200" class="node default frontend"><rect height="78" width="155.4140625" y="-39" x="-77.70703125" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-47.70703125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="95.4140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>app/page.tsx<br>应用入口</p></span></div></foreignObject></g></g><g transform="translate(2097.072265625, 96)" id="flowchart-UI2-201" class="node default frontend"><rect height="102" width="319.7421875" y="-51" x="-159.87109375" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-129.87109375, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="259.7421875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>components/chat/ChatInterface.tsx<br>核心对话界面<br>流式响应 + 自动滚动</p></span></div></foreignObject></g></g><g transform="translate(1757.201171875, 96)" id="flowchart-UI3-202" class="node default frontend"><rect height="126" width="260" y="-63" x="-130" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>components/emotional-space/<br>情绪可视化组件<br>⚠️ 未集成</p></span></div></foreignObject></g></g><g transform="translate(1473.634765625, 96)" id="flowchart-UI4-203" class="node default frontend"><rect height="78" width="207.1328125" y="-39" x="-103.56640625" style="fill:#e3f2fd !important;stroke:#1976d2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-73.56640625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="147.1328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>app/debug/page.tsx<br>调试工具</p></span></div></foreignObject></g></g><g transform="translate(818.435546875, 583)" id="flowchart-API1-204" class="node default api"><rect height="126" width="222.015625" y="-63" x="-111.0078125" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-81.0078125, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="162.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>app/api/chat/route.ts<br>快速响应模式<br>thinkingBudget=0<br>响应时间 &lt; 2秒</p></span></div></foreignObject></g></g><g transform="translate(1138.939453125, 583)" id="flowchart-API2-205" class="node default api"><rect height="150" width="260" y="-75" x="-130" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>app/api/daily-reflection/route.ts<br>每日沉淀模式<br>每6轮对话触发<br>后台异步处理</p></span></div></foreignObject></g></g><g transform="translate(1473.634765625, 583)" id="flowchart-API3-206" class="node default api"><rect height="78" width="309.390625" y="-39" x="-154.6953125" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-124.6953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="249.390625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>app/api/memory/archive/route.ts<br>记忆归档管理</p></span></div></foreignObject></g></g><g transform="translate(2097.072265625, 334)" id="flowchart-BL1-207" class="node default business"><rect height="150" width="261.765625" y="-75" x="-130.8828125" style="fill:#e8f5e8 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100.8828125, -60)" style="" class="label"><rect></rect><foreignObject height="120" width="201.765625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lib/services/conversation.ts<br>对话管理服务<br>上下文窗口：正常6轮/沉淀8轮<br>触发机制：每6轮对话</p></span></div></foreignObject></g></g><g transform="translate(2157.75390625, 821)" id="flowchart-BL2-208" class="node default business"><rect height="126" width="260" y="-63" x="-130" style="fill:#e8f5e8 !important;stroke:#388e3c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lib/storage/simple-memory.ts<br>文件存储管理<br>Markdown读写操作</p></span></div></foreignObject></g></g><g transform="translate(1747.0859375, 1023)" id="flowchart-MEM1-209" class="node default memory"><rect height="78" width="218.4453125" y="-39" x="-109.22265625" style="fill:#fff8e1 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-79.22265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="158.4453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/用户画像.md<br>认知特征 + 价值观念</p></span></div></foreignObject></g></g><g transform="translate(2015.53125, 1023)" id="flowchart-MEM2-210" class="node default memory"><rect height="78" width="218.4453125" y="-39" x="-109.22265625" style="fill:#fff8e1 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-79.22265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="158.4453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/关键事件.md<br>重要事件记录</p></span></div></foreignObject></g></g><g transform="translate(2299.9765625, 1023)" id="flowchart-MEM3-211" class="node default memory"><rect height="78" width="250.4453125" y="-39" x="-125.22265625" style="fill:#fff8e1 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-95.22265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="190.4453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/每日洞察今天.md<br>当天洞察热日志</p></span></div></foreignObject></g></g><g transform="translate(2600.421875, 1023)" id="flowchart-MEM4-212" class="node default memory"><rect height="78" width="250.4453125" y="-39" x="-125.22265625" style="fill:#fff8e1 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-95.22265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="190.4453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/每日洞察归档.md<br>历史洞察冷日志</p></span></div></foreignObject></g></g><g transform="translate(2884.8671875, 1023)" id="flowchart-MEM5-213" class="node default memory"><rect height="78" width="218.4453125" y="-39" x="-109.22265625" style="fill:#fff8e1 !important;stroke:#f57c00 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-79.22265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="158.4453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/对话历史.md<br>完整对话记录</p></span></div></foreignObject></g></g><g transform="translate(509.328125, 821)" id="flowchart-PROMPT1-214" class="node default prompt"><rect height="102" width="260" y="-51" x="-130" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/prompts/系统提示词.md<br>基础人格设定</p></span></div></foreignObject></g></g><g transform="translate(819.328125, 821)" id="flowchart-PROMPT2-215" class="node default prompt"><rect height="102" width="260" y="-51" x="-130" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/prompts/日常对话模式.md<br>快速响应策略</p></span></div></foreignObject></g></g><g transform="translate(1129.328125, 821)" id="flowchart-PROMPT3-216" class="node default prompt"><rect height="102" width="260" y="-51" x="-130" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>memory/prompts/每日沉淀模式.md<br>深度分析策略</p></span></div></foreignObject></g></g><g transform="translate(166.1640625, 1023)" id="flowchart-AI1-217" class="node default ai"><rect height="78" width="234.0234375" y="-39" x="-117.01171875" style="fill:#f1f8e9 !important;stroke:#689f38 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-87.01171875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="174.0234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Google Gemini 2.5 Flash<br>AI推理模型</p></span></div></foreignObject></g></g><g transform="translate(166.1640625, 821)" id="flowchart-AI2-218" class="node default ai"><rect height="102" width="246.328125" y="-51" x="-123.1640625" style="fill:#f1f8e9 !important;stroke:#689f38 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-93.1640625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="186.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>自定义Fetch + ProxyAgent<br>网络层 + 代理支持<br>thinking控制</p></span></div></foreignObject></g></g></g></g></g></svg>  三、核心技术架构
3.1 双模式对话架构
SelfMirror 采用创新的双模式架构，平衡用户体验与深度分析：
快速响应模式 (Fast Response Mode)
触发条件: 每次用户输入
响应时间: < 2秒
技术实现: thinkingBudget=0，关闭AI思考过程
上下文窗口: 正常情况下6轮对话
核心目标: 保证对话流畅性，提供即时情感支持
每日沉淀模式 (Daily Reflection Mode)
触发条件: 每6轮对话自动触发
处理方式: 后台异步执行，不影响前端体验
上下文窗口: 沉淀期间扩展至8轮对话
核心目标: 深度分析用户认知模式，更新记忆系统
3.2 四文件记忆系统
采用Markdown格式的文件系统，实现结构化的用户认知建模：
热日志系统
每日洞察今天.md: 当天的实时洞察记录
对话历史.md: 完整的对话流水记录
冷日志系统
用户画像.md: 长期积累的认知特征和价值观念
关键事件.md: 重要生活事件和情感节点
每日洞察归档.md: 历史洞察的结构化存档
设计优势
人类可读: Markdown格式，用户可直接查看和编辑
版本控制友好: 支持Git等版本控制系统
隐私安全: 完全本地存储，无数据泄露风险
扩展性强: 易于添加新的记忆维度
3.3 提示词工程架构
分层式提示词系统，支持不同场景的AI行为定制：
系统层提示词
系统提示词.md: 定义AI的基础人格和价值观
核心特征: 温暖、共情、非评判性
模式层提示词
日常对话模式.md: 快速响应的对话策略
每日沉淀模式.md: 深度分析的思维框架
动态上下文
实时加载用户画像、关键事件等记忆文件
构建个性化的对话上下文

四、技术实现细节
4.1 前端架构
核心组件
import { ChatInterface } from '@/components/chat/ChatInterface';

export default function Home() {
  return <ChatInterface />;
} 
对话界面实现  export function ChatInterface() {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleSend = async () => {
    // 处理消息并获取AI响应
    const response = await conversationManager.processUserMessage(userInput);
    setMessages(conversationManager.getMessages());
  };  4.2 API层架构  快速响应API
export async function POST(req: Request) {
  // 加载记忆上下文
  const memory = await loadMemoryContext();
  
  // 构建完整的系统提示词
  const fullSystemPrompt = `
${systemPrompt}
${dailyModePrompt}
## 当前记忆上下文
### 用户画像
${memory.userProfile || '暂无用户画像'}
`;  网络层配置
const createCustomFetch = () => {
  if (process.env.NODE_ENV === 'development') {
    const { ProxyAgent, fetch: undiciFetch } = require('undici');
    const proxyAgent = new ProxyAgent(PROXY_URL);
    
    return async (url: string, options: any) => {
      // 添加thinkingConfig到generationConfig
      body.generationConfig.thinkingConfig = {
        thinkingBudget: 0
      };  4.3 业务逻辑层
对话管理服务
export class ConversationManager {
  // 检查是否需要触发每日沉淀
  private shouldTriggerAnalysis(): boolean {
    // 每6个对话块触发一次沉淀
    return this.dialogueBlocks.length >= 6 && this.dialogueBlocks.length % 6 === 0;
  }
  
  // 根据沉淀状态动态调整上下文窗口大小
  const windowSize = this.isRefining ? 8 : 6;  文件存储管理
// 读取所有记忆文件用于上下文
export async function loadMemoryContext(): Promise<{
  userProfile: string;
  keyEvents: string;
  dailyInsights: string;
  dailyInsightsToday: string;
}> {
  const [userProfile, keyEvents, dailyInsightsArchive, dailyInsightsToday] = await Promise.all([
    readMarkdownFile('用户画像.md'),
    readMarkdownFile('关键事件.md'),
    readMarkdownFile('每日洞察归档.md'),
    readMarkdownFile('每日洞察今天.md')
  ]);  五、数据流与状态管理
5.1 主要数据流
用户输入 → 对话管理服务 → 快速响应API
记忆加载 → 文件存储管理 → 四文件系统
AI响应 → 流式输出 → 前端界面更新
对话记录 → 文件存储 → 对话历史.md
5.2 深度分析流
触发条件 → 每6轮对话自动触发
后台处理 → 每日沉淀API → 深度分析
记忆更新 → 用户画像.md + 关键事件.md + 每日洞察今天.md
5.3 状态管理策略
前端状态: React useState管理UI状态
对话状态: ConversationManager管理对话历史和上下文
持久化状态: Markdown文件系统管理长期记忆
六、性能与优化
6.1 响应时间优化
快速响应: thinkingBudget=0，确保<2秒响应
流式输出: 实时显示AI生成内容，提升用户体验
异步处理: 深度分析在后台执行，不阻塞用户交互
6.2 内存管理
上下文窗口: 动态调整（正常6轮/沉淀8轮）
对话归档: 定期清理过期对话数据
文件缓存: 智能加载记忆文件，避免重复读取
6.3 网络优化
代理支持: 开发环境自动使用代理
错误重试: API调用失败时的降级处理
连接复用: undici库提供高效的HTTP连接管理
七、安全与隐私
7.1 数据安全
本地存储: 所有用户数据存储在本地文件系统
无数据上传: 除AI API调用外，无任何数据传输
透明格式: Markdown格式，用户可完全控制数据
7.2 API安全
密钥管理: API密钥硬编码（仅开发环境）
HTTPS传输: 所有AI API调用使用加密传输
代理保护: 支持代理访问，保护网络隐私
7.3 隐私保护
零追踪: 无用户行为追踪和分析
数据控制: 用户可随时查看、编辑、删除所有数据
离线能力: 核心功能支持离线使用
八、部署与运维
8.1 技术栈
前端框架: Next.js 15 + React + TypeScript
样式系统: Tailwind CSS
AI集成: Google Gemini 2.5 Flash + AI SDK
网络层: undici + ProxyAgent
存储: 本地文件系统 (Markdown)
8.2 环境配置
开发环境: 自动启用代理支持
生产环境: 直连模式
依赖管理: npm/yarn 标准流程
8.3 监控与调试
调试页面: /debug 提供系统状态查看
日志系统: 控制台输出关键操作日志
错误处理: 完善的错误捕获和用户提示
九、项目现状与下一步计划
9.1 已完成功能 ✅
核心对话功能: 完整的对话界面和流式响应
双模式架构: 快速响应 + 每日沉淀机制
四文件记忆系统: 完整的Markdown存储架构
提示词系统: 分层式提示词管理
网络层: 代理支持和thinking控制
调试工具: 基础的系统管理功能
9.2 待完成功能 ⚠️
情绪可视化: components/emotional-space/ 组件未集成到主界面
深度分析优化: 每日沉淀的AI分析逻辑需要完善
IndexedDB集成: 客户端缓存和离线支持
错误处理增强: 更完善的错误重试和降级机制
9.3 优先级排序
🔥 高优先级（立即执行）
情绪可视化集成: 将情绪线条动画集成到ChatInterface
深度分析完善: 优化每日沉淀的用户画像提取逻辑
🔶 中优先级（近期规划）
错误处理增强: 添加API重试和网络错误处理
性能监控: 添加响应时间和对话质量监控
🔵 低优先级（长期规划）
IndexedDB集成: 实现客户端缓存和离线模式
多用户支持: 支持多个用户画像切换
十、技术决策记录
10.1 架构决策
选择Markdown over JSON: 提高数据透明度和用户控制能力
选择文件系统 over 数据库: 简化部署，提高隐私安全
选择双模式 over 单一模式: 平衡响应速度和分析深度
10.2 技术选型
Next.js: 提供全栈开发能力，简化API开发
Gemini 2.5 Flash: 平衡性能和成本的AI模型选择
undici: 提供代理支持和高性能HTTP客户端
10.3 设计权衡
响应速度 vs 分析深度: 通过双模式架构解决
隐私安全 vs 功能丰富: 优先保证隐私安全
开发复杂度 vs 维护成本: 选择简单可维护的架构

文档版本: v1.0 最后更新: 2025-06-20 状态: 权威版本 - 单一事实来源
这份白皮书基于项目的真实代码状态撰写，是SelfMirror MVP项目的权威技术文档。所有开发决策应以此文档为准，其他零散文档仅作历史参考。

